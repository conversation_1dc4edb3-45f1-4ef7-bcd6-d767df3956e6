/**
 * اختبار التحديث الفوري لواجهة المستخدم
 * 
 * هذا الملف يحتوي على اختبارات لمحاكاة التحديث الفوري
 * لبطاقات الأرباح في واجهة المستخدم
 */

// محاكاة نظام الأحداث
class MockEventSystem {
  constructor() {
    this.listeners = new Map();
  }

  addEventListener(eventType, listener) {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, []);
    }
    this.listeners.get(eventType).push(listener);
  }

  removeEventListener(eventType, listener) {
    if (this.listeners.has(eventType)) {
      const listeners = this.listeners.get(eventType);
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  dispatchEvent(eventType, data) {
    if (this.listeners.has(eventType)) {
      this.listeners.get(eventType).forEach(listener => {
        try {
          listener({ detail: data });
        } catch (error) {
          console.error(`خطأ في معالج الحدث ${eventType}:`, error);
        }
      });
    }
  }
}

// محاكاة مكون FinancialSalesReport
class MockFinancialSalesReport {
  constructor() {
    this.eventSystem = new MockEventSystem();
    this.cachedProfitValues = { quarterly: 0, halfYearly: 0, threeQuarters: 0, yearly: 0 };
    this.refreshKey = 0;
    this.localTransactions = [];
    
    this.setupEventListeners();
  }

  setupEventListeners() {
    // مستمع لتحديث الأرباح
    this.eventSystem.addEventListener('profits-updated', async (event) => {
      console.log('[MOCK-UI] تم استلام حدث تحديث الأرباح:', event.detail);
      await this.handleProfitsUpdate(event.detail);
    });

    // مستمع للتحديث المباشر
    this.eventSystem.addEventListener('direct-update', async (event) => {
      console.log('[MOCK-UI] تم استلام حدث تحديث مباشر:', event.detail);
      await this.handleDirectUpdate(event.detail);
    });

    // مستمع لتحديث المعاملات
    this.eventSystem.addEventListener('transactions-refreshed', async (event) => {
      console.log('[MOCK-UI] تم استلام حدث تحديث المعاملات:', event.detail);
      await this.handleTransactionsUpdate(event.detail);
    });
  }

  async handleProfitsUpdate(data) {
    try {
      // محاكاة إعادة تحميل المعاملات
      await this.reloadTransactions();
      
      // إعادة حساب الأرباح
      setTimeout(() => {
        this.calculateProfits();
        this.refreshKey++;
        console.log('[MOCK-UI] تم تحديث الأرباح فورياً:', this.cachedProfitValues);
      }, 50);
    } catch (error) {
      console.error('[MOCK-UI] خطأ في معالجة تحديث الأرباح:', error);
    }
  }

  async handleDirectUpdate(data) {
    try {
      // تحديث فوري
      await this.reloadTransactions();
      this.calculateProfits();
      this.refreshKey++;
      console.log('[MOCK-UI] تم التحديث المباشر:', this.cachedProfitValues);
    } catch (error) {
      console.error('[MOCK-UI] خطأ في التحديث المباشر:', error);
    }
  }

  async handleTransactionsUpdate(data) {
    try {
      await this.reloadTransactions();
      this.calculateProfits();
      this.refreshKey++;
      console.log('[MOCK-UI] تم تحديث المعاملات:', this.cachedProfitValues);
    } catch (error) {
      console.error('[MOCK-UI] خطأ في تحديث المعاملات:', error);
    }
  }

  async reloadTransactions() {
    // محاكاة إعادة تحميل المعاملات من قاعدة البيانات
    return new Promise(resolve => {
      setTimeout(() => {
        console.log('[MOCK-UI] تم إعادة تحميل المعاملات');
        resolve();
      }, 10);
    });
  }

  calculateProfits() {
    // محاكاة حساب الأرباح
    const { calculateQuarterlyProfits } = require('./src/utils/profitCalculator.js');
    
    if (this.localTransactions.length > 0) {
      const newProfitValues = calculateQuarterlyProfits(this.localTransactions);
      this.cachedProfitValues = newProfitValues;
    }
  }

  // محاكاة إضافة معاملة جديدة
  addTransaction(transaction) {
    this.localTransactions.push(transaction);
    console.log('[MOCK-UI] تم إضافة معاملة جديدة:', transaction.transaction_type);
  }

  // الحصول على قيم الأرباح الحالية
  getCurrentProfits() {
    return this.cachedProfitValues;
  }
}

// اختبار التحديث الفوري
async function testInstantUpdate() {
  console.log('🚀 بدء اختبار التحديث الفوري لواجهة المستخدم...');
  
  const mockComponent = new MockFinancialSalesReport();
  
  // إضافة معاملة بيع
  const saleTransaction = {
    id: 1,
    transaction_type: 'sale',
    profit: 100,
    transaction_date: new Date(2025, 0, 15).toISOString()
  };
  
  mockComponent.addTransaction(saleTransaction);
  
  // محاكاة حدث تحديث الأرباح
  console.log('\n📊 محاكاة حدث تحديث الأرباح...');
  mockComponent.eventSystem.dispatchEvent('profits-updated', {
    transaction_type: 'sale',
    profit: 100,
    auto_update: true,
    timestamp: new Date().toISOString()
  });
  
  // انتظار قصير للتحديث
  await new Promise(resolve => setTimeout(resolve, 100));
  
  // التحقق من التحديث
  const profitsAfterSale = mockComponent.getCurrentProfits();
  console.log('💰 الأرباح بعد البيع:', profitsAfterSale);
  
  // إضافة معاملة إرجاع
  const returnTransaction = {
    id: 2,
    transaction_type: 'return',
    profit: -50,
    transaction_date: new Date(2025, 0, 20).toISOString()
  };
  
  mockComponent.addTransaction(returnTransaction);
  
  // محاكاة حدث تحديث مباشر
  console.log('\n🔄 محاكاة حدث تحديث مباشر...');
  mockComponent.eventSystem.dispatchEvent('direct-update', {
    transaction_type: 'return',
    profit: -50,
    timestamp: new Date().toISOString()
  });
  
  // انتظار قصير للتحديث
  await new Promise(resolve => setTimeout(resolve, 100));
  
  // التحقق من التحديث النهائي
  const finalProfits = mockComponent.getCurrentProfits();
  console.log('💰 الأرباح النهائية:', finalProfits);
  
  // التحقق من صحة النتائج
  const expectedProfit = 50; // 100 - 50 = 50
  if (finalProfits.quarterly === expectedProfit) {
    console.log('✅ اختبار التحديث الفوري نجح!');
    return true;
  } else {
    console.log('❌ اختبار التحديث الفوري فشل!');
    console.log(`المتوقع: ${expectedProfit}, الفعلي: ${finalProfits.quarterly}`);
    return false;
  }
}

// اختبار الأداء
async function testPerformance() {
  console.log('\n⚡ اختبار أداء التحديث الفوري...');
  
  const mockComponent = new MockFinancialSalesReport();
  const startTime = Date.now();
  
  // محاكاة 10 معاملات متتالية
  for (let i = 1; i <= 10; i++) {
    const transaction = {
      id: i,
      transaction_type: i % 2 === 0 ? 'sale' : 'return',
      profit: i % 2 === 0 ? 50 : -25,
      transaction_date: new Date(2025, 0, i).toISOString()
    };
    
    mockComponent.addTransaction(transaction);
    
    // إرسال حدث تحديث
    mockComponent.eventSystem.dispatchEvent('direct-update', {
      transaction_type: transaction.transaction_type,
      profit: transaction.profit,
      timestamp: new Date().toISOString()
    });
    
    // انتظار قصير
    await new Promise(resolve => setTimeout(resolve, 10));
  }
  
  const endTime = Date.now();
  const duration = endTime - startTime;
  
  console.log(`⏱️  وقت معالجة 10 معاملات: ${duration}ms`);
  console.log(`📈 متوسط الوقت لكل معاملة: ${duration / 10}ms`);
  
  if (duration < 1000) {
    console.log('✅ اختبار الأداء نجح (أقل من ثانية واحدة)');
    return true;
  } else {
    console.log('⚠️  اختبار الأداء بطيء (أكثر من ثانية واحدة)');
    return false;
  }
}

// تشغيل جميع الاختبارات
async function runAllUITests() {
  console.log('🎯 بدء اختبارات واجهة المستخدم...');
  console.log('=' .repeat(60));
  
  const tests = [
    { name: 'التحديث الفوري', func: testInstantUpdate },
    { name: 'اختبار الأداء', func: testPerformance }
  ];
  
  let passedTests = 0;
  
  for (const test of tests) {
    console.log(`\n🧪 ${test.name}:`);
    try {
      const result = await test.func();
      if (result) {
        passedTests++;
      }
    } catch (error) {
      console.error(`❌ خطأ في ${test.name}:`, error.message);
    }
  }
  
  console.log('\n' + '=' .repeat(60));
  console.log('📋 ملخص اختبارات واجهة المستخدم:');
  console.log(`   ✅ نجح: ${passedTests}/${tests.length}`);
  console.log(`   ❌ فشل: ${tests.length - passedTests}/${tests.length}`);
  
  if (passedTests === tests.length) {
    console.log('🎉 جميع اختبارات واجهة المستخدم نجحت!');
  } else {
    console.log('⚠️  بعض اختبارات واجهة المستخدم فشلت.');
  }
  
  return passedTests === tests.length;
}

// تشغيل الاختبارات
if (require.main === module) {
  runAllUITests().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = {
  MockFinancialSalesReport,
  testInstantUpdate,
  testPerformance,
  runAllUITests
};
