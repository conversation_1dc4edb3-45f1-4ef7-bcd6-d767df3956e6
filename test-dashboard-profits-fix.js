/**
 * اختبار إصلاح تحديث خانة الأرباح في Dashboard
 * هذا الملف يحتوي على اختبارات للتأكد من أن خانة الأرباح تتحدث بشكل صحيح وفوري
 */

console.log('🧪 بدء اختبار إصلاح تحديث خانة الأرباح في Dashboard...');

// دالة لمحاكاة عملية بيع
const simulateSaleTransaction = () => {
  console.log('🛒 محاكاة عملية بيع...');
  
  // إرسال حدث تحديث الأرباح
  const profitsEvent = new CustomEvent('profits-updated', {
    detail: {
      transaction_type: 'sale',
      amount: 1000,
      profit: 200,
      total_profit: 4950,
      auto_update: true,
      timestamp: new Date().toISOString()
    }
  });
  
  console.log('📡 إرسال حدث profits-updated');
  window.dispatchEvent(profitsEvent);
  
  // إرسال حدث تحديث تلقائي
  setTimeout(() => {
    const autoEvent = new CustomEvent('auto-profits-updated', {
      detail: {
        transaction_type: 'sale',
        amount: 1000,
        profit: 200,
        total_profit: 4950,
        auto_update: true,
        timestamp: new Date().toISOString()
      }
    });
    
    console.log('📡 إرسال حدث auto-profits-updated');
    window.dispatchEvent(autoEvent);
  }, 100);
  
  // إرسال حدث تحديث الخزينة
  setTimeout(() => {
    const cashboxEvent = new CustomEvent('cashbox-updated-ui', {
      detail: {
        current_balance: 15000,
        profit_total: 4950,
        sales_total: 25500,
        transaction_type: 'sale',
        success: true,
        instant_update: true
      }
    });
    
    console.log('💰 إرسال حدث cashbox-updated-ui');
    window.dispatchEvent(cashboxEvent);
  }, 200);
  
  // إرسال حدث تحديث مباشر
  setTimeout(() => {
    const directEvent = new CustomEvent('direct-update', {
      detail: {
        transaction_type: 'sale',
        amount: 1000,
        profit: 200,
        total_profit: 4950,
        timestamp: new Date().toISOString()
      }
    });
    
    console.log('⚡ إرسال حدث direct-update');
    window.dispatchEvent(directEvent);
  }, 300);
};

// دالة لمحاكاة تحديث Dashboard
const simulateDashboardUpdate = () => {
  console.log('📊 محاكاة تحديث Dashboard...');
  
  const dashboardEvent = new CustomEvent('dashboard-profits-updated', {
    detail: {
      quarterly: 1237.5,
      halfYearly: 2475,
      threeQuarters: 3712.5,
      yearly: 4950,
      source: 'dashboard-test',
      timestamp: new Date().toISOString()
    }
  });
  
  console.log('📈 إرسال حدث dashboard-profits-updated');
  window.dispatchEvent(dashboardEvent);
};

// دالة لمحاكاة تحديث مالي
const simulateFinancialUpdate = () => {
  console.log('💹 محاكاة تحديث مالي...');
  
  const financialEvent = new CustomEvent('financial-profits-updated', {
    detail: {
      quarterly: 1237.5,
      halfYearly: 2475,
      threeQuarters: 3712.5,
      yearly: 4950,
      source: 'financial-test',
      timestamp: new Date().toISOString()
    }
  });
  
  console.log('💰 إرسال حدث financial-profits-updated');
  window.dispatchEvent(financialEvent);
};

// دالة لمحاكاة تحديث فوري
const simulateRealTimeUpdate = () => {
  console.log('⚡ محاكاة تحديث فوري...');
  
  const realTimeEvent = new CustomEvent('real-time-profits-updated', {
    detail: {
      quarterly: 1237.5,
      halfYearly: 2475,
      threeQuarters: 3712.5,
      yearly: 4950,
      source: 'real-time-test',
      timestamp: new Date().toISOString()
    }
  });
  
  console.log('🔄 إرسال حدث real-time-profits-updated');
  window.dispatchEvent(realTimeEvent);
};

// دالة لفحص قيمة الأرباح المعروضة
const checkProfitDisplay = () => {
  console.log('🔍 فحص قيمة الأرباح المعروضة...');
  
  // البحث عن عنصر الأرباح
  const profitElements = document.querySelectorAll('[class*="stat-card-value"]');
  
  profitElements.forEach((element, index) => {
    const text = element.textContent || element.innerText || '';
    if (text.includes('4,950') || text.includes('4950')) {
      console.log(`✅ تم العثور على القيمة الصحيحة في العنصر ${index + 1}: ${text}`);
    } else if (text.includes('10,000') || text.includes('10000')) {
      console.log(`❌ تم العثور على القيمة الخاطئة في العنصر ${index + 1}: ${text}`);
    } else {
      console.log(`ℹ️ عنصر ${index + 1}: ${text}`);
    }
  });
  
  // البحث عن بطاقة الأرباح تحديداً
  const profitCards = document.querySelectorAll('[class*="profit"], [data-testid*="profit"]');
  console.log(`📊 تم العثور على ${profitCards.length} بطاقة أرباح`);
  
  profitCards.forEach((card, index) => {
    const text = card.textContent || card.innerText || '';
    console.log(`💳 بطاقة أرباح ${index + 1}: ${text.substring(0, 100)}...`);
  });
};

// دالة لتشغيل جميع الاختبارات
const runAllTests = () => {
  console.log('🚀 تشغيل جميع اختبارات إصلاح الأرباح...');
  
  // فحص أولي
  checkProfitDisplay();
  
  // تشغيل الاختبارات بتأخير
  setTimeout(() => {
    console.log('\n--- اختبار 1: محاكاة عملية بيع ---');
    simulateSaleTransaction();
  }, 1000);
  
  setTimeout(() => {
    console.log('\n--- اختبار 2: محاكاة تحديث Dashboard ---');
    simulateDashboardUpdate();
  }, 3000);
  
  setTimeout(() => {
    console.log('\n--- اختبار 3: محاكاة تحديث مالي ---');
    simulateFinancialUpdate();
  }, 5000);
  
  setTimeout(() => {
    console.log('\n--- اختبار 4: محاكاة تحديث فوري ---');
    simulateRealTimeUpdate();
  }, 7000);
  
  setTimeout(() => {
    console.log('\n--- فحص نهائي للنتائج ---');
    checkProfitDisplay();
  }, 9000);
  
  console.log('⏱️ تم جدولة جميع الاختبارات');
};

// دالة لمراقبة الأحداث
const monitorEvents = () => {
  console.log('👁️ بدء مراقبة أحداث الأرباح...');
  
  const eventsToMonitor = [
    'profits-updated',
    'auto-profits-updated',
    'dashboard-profits-updated',
    'financial-profits-updated',
    'real-time-profits-updated',
    'cashbox-updated-ui',
    'direct-update'
  ];
  
  eventsToMonitor.forEach(eventName => {
    window.addEventListener(eventName, (event) => {
      console.log(`📥 تم استلام حدث ${eventName}:`, event.detail);
      
      // فحص القيمة بعد كل حدث
      setTimeout(() => {
        checkProfitDisplay();
      }, 500);
    });
  });
  
  console.log('✅ تم تسجيل مراقبين للأحداث');
};

// تصدير الدوال للاستخدام الخارجي
if (typeof window !== 'undefined') {
  window.dashboardProfitsTests = {
    simulateSaleTransaction,
    simulateDashboardUpdate,
    simulateFinancialUpdate,
    simulateRealTimeUpdate,
    checkProfitDisplay,
    runAllTests,
    monitorEvents
  };
  
  console.log('✅ تم تحميل اختبارات إصلاح أرباح Dashboard بنجاح');
  console.log('📝 استخدم window.dashboardProfitsTests.runAllTests() لتشغيل جميع الاختبارات');
  console.log('📝 استخدم window.dashboardProfitsTests.monitorEvents() لمراقبة الأحداث');
  console.log('📝 استخدم window.dashboardProfitsTests.checkProfitDisplay() لفحص القيم المعروضة');
}

// تشغيل مراقبة الأحداث تلقائياً
if (typeof window !== 'undefined') {
  setTimeout(() => {
    monitorEvents();
  }, 1000);
}
