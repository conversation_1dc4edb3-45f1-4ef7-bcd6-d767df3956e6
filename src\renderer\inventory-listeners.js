/**
 * مستمعي أحداث المخزون
 * يقوم بإعداد مستمعي الأحداث للتحديثات التلقائية للمخزون
 */

// تسجيل بدء تحميل الملف
console.log('جاري تحميل مستمعي أحداث المخزون...');

// مستمع لإضافة صنف جديد
function setupItemAddedListener() {
  if (!window.api || !window.api.on) {
    console.error('واجهة API غير متوفرة لإعداد مستمع إضافة الصنف');
    return;
  }

  // إضافة مستمع لحدث إضافة صنف جديد
  window.api.on('item-added', (data) => {
    console.log('تم استلام إشعار بإضافة صنف جديد:', data);

    // تحديث قائمة الأصناف مع تمرير بيانات الصنف الجديد
    refreshItemsList(data);

    // تحديث قائمة المخزون
    refreshInventoryList();

    // إرسال حدث تحديث الأصناف مرة أخرى بعد فترة أطول للتأكد من التحديث
    setTimeout(() => {
      console.log('إرسال حدث تحديث الأصناف مرة أخرى بعد فترة أطول...');
      refreshItemsList(data);
    }, 1000);

    // عرض إشعار للمستخدم
    showNotification(`تم إضافة الصنف "${data.name}" بنجاح وتحديث المخزون`, 'success');
  });

  console.log('تم إعداد مستمع إضافة الصنف بنجاح');
}

// تحديث قائمة الأصناف
function refreshItemsList(itemData = null) {
  // التحقق من وجود الدالة في النافذة الحالية
  if (typeof window.loadItems === 'function') {
    console.log('جاري تحديث قائمة الأصناف باستخدام window.loadItems...');
    window.loadItems(true); // تمرير true لتجاوز التخزين المؤقت
  } else {
    console.log('دالة تحديث قائمة الأصناف غير متوفرة في هذه الصفحة');
  }

  // محاولة تحديث قائمة الأصناف في جميع الصفحات بغض النظر عن وجود window.loadItems
  try {
    // تحديث قائمة الأصناف في جميع الصفحات المفتوحة
    if (window.api && window.api.items) {
      console.log('محاولة تحديث قائمة الأصناف في جميع الصفحات...');

      // مسح التخزين المؤقت للأصناف إذا كان ذلك ممكناً
      if (window.api.items.clearCache) {
        console.log('مسح التخزين المؤقت للأصناف قبل التحديث...');
        window.api.items.clearCache('refresh-items-list', itemData?.id);
      }

      // تحميل الأصناف المحدثة مباشرة
      window.api.items.getAll(true).then(updatedItems => {
        if (Array.isArray(updatedItems)) {
          console.log(`تم تحميل ${updatedItems.length} صنف مباشرة من API في refreshItemsList`);

          // إرسال حدث تحديث الأصناف مع البيانات المحدثة
          window.dispatchEvent(new CustomEvent('refresh-items-list', {
            detail: {
              forceRefresh: true,
              itemData: itemData,
              allItems: updatedItems,
              timestamp: new Date().toISOString()
            }
          }));
        }
      }).catch(error => {
        console.error('خطأ في تحميل الأصناف مباشرة من API في refreshItemsList:', error);

        // إرسال حدث تحديث الأصناف بدون البيانات المحدثة
        window.dispatchEvent(new CustomEvent('refresh-items-list', {
          detail: {
            forceRefresh: true,
            itemData: itemData,
            timestamp: new Date().toISOString()
          }
        }));
      });

      // إرسال حدث تحديث الأصناف مرة أخرى بعد فترة قصيرة للتأكد من التحديث
      setTimeout(() => {
        // تحميل الأصناف المحدثة مرة أخرى
        window.api.items.getAll(true).then(updatedItems => {
          if (Array.isArray(updatedItems)) {
            console.log(`تم تحميل ${updatedItems.length} صنف مباشرة من API في refreshItemsList (متأخر)`);

            // إرسال حدث تحديث الأصناف مع البيانات المحدثة
            window.dispatchEvent(new CustomEvent('refresh-items-list', {
              detail: {
                forceRefresh: true,
                itemData: itemData,
                allItems: updatedItems,
                timestamp: new Date().toISOString(),
                delayed: true
              }
            }));
          }
        }).catch(error => {
          console.error('خطأ في تحميل الأصناف مباشرة من API في refreshItemsList (متأخر):', error);

          // إرسال حدث تحديث الأصناف بدون البيانات المحدثة
          window.dispatchEvent(new CustomEvent('refresh-items-list', {
            detail: {
              forceRefresh: true,
              itemData: itemData,
              timestamp: new Date().toISOString(),
              delayed: true
            }
          }));
        });
      }, 1000);
    }
  } catch (error) {
    console.error('خطأ في محاولة تحديث قائمة الأصناف:', error);
  }
}

// تحديث قائمة المخزون
function refreshInventoryList() {
  // التحقق من وجود الدالة في النافذة الحالية
  if (typeof window.loadInventory === 'function') {
    console.log('جاري تحديث قائمة المخزون...');
    window.loadInventory(true); // تمرير true لتجاوز التخزين المؤقت
  } else {
    console.log('دالة تحديث قائمة المخزون غير متوفرة في هذه الصفحة');
  }
}

// عرض إشعار للمستخدم - تم تعطيله
function showNotification(message, type = 'info') {
  // تسجيل الإشعار في وحدة التحكم فقط دون عرضه للمستخدم
  console.log(`تم تجاهل الإشعار (${type}): ${message}`);

  // لا نقوم بعرض أي إشعارات للمستخدم
  // تم تعطيل جميع طرق عرض الإشعارات
}

// إعداد مستمع لحدث refresh-needed
function setupRefreshNeededListener() {
  if (!window.api || !window.api.on) {
    console.error('واجهة API غير متوفرة لإعداد مستمع refresh-needed');
    return;
  }

  // إضافة مستمع لحدث refresh-needed
  window.api.on('refresh-needed', (data) => {
    console.log('تم استلام حدث refresh-needed:', data);

    // التحقق من نوع التحديث المطلوب
    if (data.target === 'inventory') {
      refreshInventoryList();
    } else if (data.target === 'items') {
      refreshItemsList();

      // إذا كانت العملية هي إضافة صنف جديد، نقوم بإرسال حدث تحديث قائمة الأصناف
      if (data.operation && (data.operation === 'add-item' || data.operation === 'add-item-delayed')) {
        console.log('إرسال حدث تحديث قائمة الأصناف بعد إضافة صنف جديد');
        window.dispatchEvent(new CustomEvent('refresh-items-list', {
          detail: {
            forceRefresh: true,
            itemId: data.itemId,
            operation: data.operation
          }
        }));
      }
    } else if (data.target === 'transactions') {
      // تحديث قائمة المعاملات
      if (typeof window.loadTransactions === 'function') {
        window.loadTransactions();
      }
    } else if (data.target === 'all') {
      refreshInventoryList();
      refreshItemsList();

      // تحديث قائمة المعاملات
      if (typeof window.loadTransactions === 'function') {
        window.loadTransactions();
      }

      // إرسال حدث تحديث قائمة الأصناف
      console.log('إرسال حدث تحديث قائمة الأصناف بعد تحديث كل شيء');
      window.dispatchEvent(new CustomEvent('refresh-items-list', {
        detail: {
          forceRefresh: true,
          operation: data.operation
        }
      }));
    }
  });

  console.log('تم إعداد مستمع refresh-needed بنجاح');
}

// إعداد جميع المستمعين
function setupAllListeners() {
  setupItemAddedListener();
  setupRefreshNeededListener();
  console.log('تم إعداد جميع مستمعي أحداث المخزون بنجاح');
}

// إعداد المستمعين عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
  console.log('جاري إعداد مستمعي أحداث المخزون بعد تحميل الصفحة...');
  setupAllListeners();
});

// تصدير الدوال للاستخدام الخارجي
window.inventoryListeners = {
  setupAllListeners,
  refreshItemsList,
  refreshInventoryList
};

console.log('تم تحميل مستمعي أحداث المخزون بنجاح');
