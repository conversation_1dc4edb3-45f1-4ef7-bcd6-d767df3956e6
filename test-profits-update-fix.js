/**
 * اختبار إصلاح تحديث خانات الأرباح في واجهة المستخدم
 * هذا الملف يحتوي على اختبارات شاملة للتأكد من أن خانات الأرباح تتحدث بشكل صحيح
 */

console.log('🧪 بدء اختبار إصلاح تحديث خانات الأرباح...');

// دالة لمحاكاة تحديث الأرباح
const simulateProfitUpdate = (profitData, source = 'test') => {
  console.log(`📊 محاكاة تحديث الأرباح من ${source}:`, profitData);
  
  // إرسال جميع الأحداث المطلوبة
  const events = [
    'profits-updated',
    'auto-profits-updated',
    'dashboard-profits-updated',
    'financial-profits-updated',
    'real-time-profits-updated'
  ];

  events.forEach(eventName => {
    const event = new CustomEvent(eventName, {
      detail: {
        ...profitData,
        timestamp: new Date().toISOString(),
        source: source,
        test: true
      }
    });
    
    console.log(`📡 إرسال حدث: ${eventName}`);
    window.dispatchEvent(event);
  });
};

// دالة لاختبار تحديث الأرباح بعد عملية بيع
const testSaleTransaction = () => {
  console.log('🛒 اختبار تحديث الأرباح بعد عملية بيع...');
  
  const saleData = {
    quarterly: 1250,
    halfYearly: 2500,
    threeQuarters: 3750,
    yearly: 5000,
    transaction_type: 'sale',
    amount: 1000,
    profit: 200,
    auto_update: true
  };
  
  simulateProfitUpdate(saleData, 'sale-test');
  
  // محاكاة تحديث الخزينة
  setTimeout(() => {
    const cashboxEvent = new CustomEvent('cashbox-updated-ui', {
      detail: {
        current_balance: 15000,
        profit_total: 5000,
        sales_total: 25000,
        transaction_type: 'sale',
        success: true,
        test: true
      }
    });
    
    console.log('💰 إرسال حدث تحديث الخزينة');
    window.dispatchEvent(cashboxEvent);
  }, 100);
};

// دالة لاختبار تحديث الأرباح بعد عملية شراء
const testPurchaseTransaction = () => {
  console.log('🛍️ اختبار تحديث الأرباح بعد عملية شراء...');
  
  const purchaseData = {
    quarterly: 1200,
    halfYearly: 2400,
    threeQuarters: 3600,
    yearly: 4800,
    transaction_type: 'purchase',
    amount: 800,
    transport_cost: 50,
    auto_update: true
  };
  
  simulateProfitUpdate(purchaseData, 'purchase-test');
  
  // محاكاة تحديث الخزينة
  setTimeout(() => {
    const cashboxEvent = new CustomEvent('cashbox-updated-ui', {
      detail: {
        current_balance: 14150,
        profit_total: 4800,
        purchases_total: 15000,
        transaction_type: 'purchase',
        success: true,
        test: true
      }
    });
    
    console.log('💰 إرسال حدث تحديث الخزينة للشراء');
    window.dispatchEvent(cashboxEvent);
  }, 100);
};

// دالة لاختبار التحديث المباشر
const testDirectUpdate = () => {
  console.log('⚡ اختبار التحديث المباشر للأرباح...');
  
  const directData = {
    quarterly: 1300,
    halfYearly: 2600,
    threeQuarters: 3900,
    yearly: 5200,
    source: 'direct-update-test',
    auto_update: true
  };
  
  simulateProfitUpdate(directData, 'direct-test');
  
  // إرسال حدث تحديث مباشر إضافي
  const directEvent = new CustomEvent('direct-update', {
    detail: {
      ...directData,
      timestamp: new Date().toISOString(),
      test: true
    }
  });
  
  console.log('⚡ إرسال حدث التحديث المباشر');
  window.dispatchEvent(directEvent);
};

// دالة لاختبار تحديث متعدد المصادر
const testMultiSourceUpdate = () => {
  console.log('🔄 اختبار تحديث متعدد المصادر...');
  
  // محاكاة تحديث من مصادر مختلفة
  const sources = [
    { name: 'cashbox', data: { yearly: 5100, source: 'cashbox' } },
    { name: 'transactions', data: { yearly: 5150, source: 'transactions' } },
    { name: 'api', data: { yearly: 5200, source: 'api' } }
  ];
  
  sources.forEach((source, index) => {
    setTimeout(() => {
      simulateProfitUpdate(source.data, source.name);
    }, index * 200);
  });
};

// دالة لمراقبة الأحداث
const monitorEvents = () => {
  console.log('👁️ بدء مراقبة أحداث تحديث الأرباح...');
  
  const eventsToMonitor = [
    'profits-updated',
    'auto-profits-updated',
    'dashboard-profits-updated',
    'financial-profits-updated',
    'real-time-profits-updated',
    'cashbox-updated-ui',
    'direct-update'
  ];
  
  eventsToMonitor.forEach(eventName => {
    window.addEventListener(eventName, (event) => {
      console.log(`📥 تم استلام حدث ${eventName}:`, event.detail);
    });
  });
  
  console.log('✅ تم تسجيل مراقبين للأحداث');
};

// دالة لتشغيل جميع الاختبارات
const runAllTests = () => {
  console.log('🚀 تشغيل جميع اختبارات تحديث الأرباح...');
  
  // بدء مراقبة الأحداث
  monitorEvents();
  
  // تشغيل الاختبارات بتأخير
  setTimeout(() => testSaleTransaction(), 1000);
  setTimeout(() => testPurchaseTransaction(), 3000);
  setTimeout(() => testDirectUpdate(), 5000);
  setTimeout(() => testMultiSourceUpdate(), 7000);
  
  console.log('⏱️ تم جدولة جميع الاختبارات');
};

// دالة لفحص حالة المكونات
const checkComponentsStatus = () => {
  console.log('🔍 فحص حالة المكونات...');
  
  // فحص وجود Dashboard
  const dashboardElement = document.querySelector('.dashboard-page');
  console.log('📊 Dashboard موجود:', !!dashboardElement);
  
  // فحص وجود بطاقات الأرباح
  const profitCards = document.querySelectorAll('[class*="profit"], [class*="stat-card"]');
  console.log('💳 عدد بطاقات الأرباح الموجودة:', profitCards.length);
  
  // فحص وجود window.api
  console.log('🔌 window.api متوفر:', typeof window.api !== 'undefined');
  
  // فحص وجود مكونات التقارير المالية
  const reportsElement = document.querySelector('.financial-sales-report');
  console.log('📈 التقارير المالية موجودة:', !!reportsElement);
};

// تصدير الدوال للاستخدام الخارجي
if (typeof window !== 'undefined') {
  window.profitUpdateTests = {
    simulateProfitUpdate,
    testSaleTransaction,
    testPurchaseTransaction,
    testDirectUpdate,
    testMultiSourceUpdate,
    monitorEvents,
    runAllTests,
    checkComponentsStatus
  };
  
  console.log('✅ تم تحميل اختبارات تحديث الأرباح بنجاح');
  console.log('📝 استخدم window.profitUpdateTests.runAllTests() لتشغيل جميع الاختبارات');
  console.log('📝 استخدم window.profitUpdateTests.checkComponentsStatus() لفحص حالة المكونات');
}

// تشغيل فحص أولي
if (typeof window !== 'undefined') {
  setTimeout(() => {
    checkComponentsStatus();
  }, 1000);
}
