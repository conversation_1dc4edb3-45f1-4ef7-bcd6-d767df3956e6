import { useRef, useEffect, useCallback } from 'react';

/**
 * هوك مساعد لإدارة تتبع حالة المكون وضمان عمل النماذج بشكل صحيح
 * @param {Object} formRef - مرجع للنموذج
 */
const useFormReset = (formRef) => {
  // مرجع لتتبع ما إذا كان المكون لا يزال مثبتًا
  const isMounted = useRef(true);

  // مرجع لتتبع حالة تمكين الحقول
  const fieldsEnabled = useRef(false);

  // مرجع للمؤقت
  const timerRef = useRef(null);

  // إعادة تعيين حقول الإدخال عند تحميل المكون
  useEffect(() => {
    // تعيين isMounted إلى true عند تحميل المكون
    isMounted.current = true;

    // تنظيف عند إلغاء تحميل المكون
    return () => {
      isMounted.current = false;

      // تنظيف أي مؤقتات معلقة
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }
    };
  }, []);

  // وظيفة لإعادة تمكين حقول النموذج
  const enableFormFields = useCallback(() => {
    // تنظيف أي مؤقتات سابقة
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    // محاولة أولى سريعة لإعادة تمكين الحقول
    if (formRef.current && isMounted.current) {
      try {
        console.log('Quick attempt to enable form fields...');
        const inputs = formRef.current.querySelectorAll('input, select, textarea, button');
        inputs.forEach(input => {
          input.disabled = false;
          if (input.type === 'text' || input.type === 'number') {
            input.readOnly = false;
          }
          input.style.opacity = '1';
          input.style.pointerEvents = 'auto';
        });
      } catch (error) {
        console.error('Error in quick form fields enabling:', error);
      }
    }

    // إنشاء مؤقت جديد للمحاولة الثانية بعد تأخير
    timerRef.current = setTimeout(() => {
      if (formRef.current && isMounted.current) {
        try {
          console.log('Enabling form fields (delayed attempt)...');

          // الحصول على جميع حقول الإدخال في النموذج
          const inputs = formRef.current.querySelectorAll('input, select, textarea, button');

          // إعادة تمكين جميع الحقول
          inputs.forEach(input => {
            input.disabled = false;

            // إعادة تعيين خصائص إضافية للحقول
            if (input.type === 'text' || input.type === 'number') {
              input.readOnly = false;
            }

            // إزالة أي أنماط قد تشير إلى أن الحقل معطل
            input.style.opacity = '1';
            input.style.pointerEvents = 'auto';
            input.style.backgroundColor = '';
            input.style.color = '';

            // إضافة مستمع أحداث للتركيز لضمان أن الحقل يمكن التفاعل معه
            const focusHandler = () => {
              input.disabled = false;
              input.readOnly = false;
              input.style.opacity = '1';
              input.style.pointerEvents = 'auto';
            };

            // إزالة المستمع القديم إذا كان موجودًا ثم إضافة مستمع جديد
            input.removeEventListener('focus', focusHandler);
            input.addEventListener('focus', focusHandler);

            // إضافة مستمع أحداث للنقر لضمان أن الحقل يمكن التفاعل معه
            const clickHandler = () => {
              input.disabled = false;
              input.readOnly = false;
              input.style.opacity = '1';
              input.style.pointerEvents = 'auto';
            };

            // إزالة المستمع القديم إذا كان موجودًا ثم إضافة مستمع جديد
            input.removeEventListener('click', clickHandler);
            input.addEventListener('click', clickHandler);
          });

          // تعيين حالة تمكين الحقول إلى true
          fieldsEnabled.current = true;

          console.log('Form fields enabled successfully');
        } catch (error) {
          console.error('Error enabling form fields:', error);
        }
      }

      // تنظيف المؤقت
      timerRef.current = null;

      // إضافة محاولة ثالثة بعد تأخير أطول
      setTimeout(() => {
        if (formRef.current && isMounted.current) {
          try {
            console.log('Final attempt to enable form fields...');
            const inputs = formRef.current.querySelectorAll('input, select, textarea, button');
            inputs.forEach(input => {
              input.disabled = false;
              if (input.type === 'text' || input.type === 'number') {
                input.readOnly = false;
              }
              input.style.opacity = '1';
              input.style.pointerEvents = 'auto';
            });
          } catch (error) {
            console.error('Error in final form fields enabling:', error);
          }
        }
      }, 1000);

    }, 500); // زيادة التأخير لضمان تحميل النموذج بشكل كامل
  }, [formRef]);

  // وظيفة لإعادة تعيين النموذج بالكامل
  const resetFormCompletely = useCallback(() => {
    if (formRef.current && isMounted.current) {
      try {
        console.log('Resetting form completely...');

        // الحصول على جميع حقول الإدخال في النموذج
        const inputs = formRef.current.querySelectorAll('input, select, textarea');

        // إعادة تعيين جميع الحقول
        inputs.forEach(input => {
          // إعادة تعيين قيمة الحقل
          if (input.type === 'checkbox' || input.type === 'radio') {
            input.checked = false;
          } else if (input.type === 'number') {
            input.value = '0';
          } else {
            input.value = '';
          }

          // إعادة تمكين الحقل
          input.disabled = false;

          if (input.type === 'text' || input.type === 'number') {
            input.readOnly = false;
          }

          // إزالة أي أنماط قد تشير إلى أن الحقل معطل
          input.style.opacity = '1';
          input.style.pointerEvents = 'auto';
          input.style.backgroundColor = '';
          input.style.color = '';

          // إضافة مستمع أحداث للتركيز لضمان أن الحقل يمكن التفاعل معه
          const focusHandler = () => {
            input.disabled = false;
            input.readOnly = false;
            input.style.opacity = '1';
            input.style.pointerEvents = 'auto';
          };

          // إزالة المستمع القديم إذا كان موجودًا ثم إضافة مستمع جديد
          input.removeEventListener('focus', focusHandler);
          input.addEventListener('focus', focusHandler);

          // إضافة مستمع أحداث للنقر لضمان أن الحقل يمكن التفاعل معه
          const clickHandler = () => {
            input.disabled = false;
            input.readOnly = false;
            input.style.opacity = '1';
            input.style.pointerEvents = 'auto';
          };

          // إزالة المستمع القديم إذا كان موجودًا ثم إضافة مستمع جديد
          input.removeEventListener('click', clickHandler);
          input.addEventListener('click', clickHandler);
        });

        console.log('Form reset completely');
      } catch (error) {
        console.error('Error resetting form:', error);
      }
    }

    // استدعاء وظيفة تمكين الحقول بعد إعادة التعيين
    enableFormFields();

    // محاولة إضافية بعد تأخير
    setTimeout(() => {
      enableFormFields();
    }, 1000);
  }, [formRef, enableFormFields]);

  // وظيفة خاصة لإعادة تمكين حقول الإدخال بعد عمليات الحذف
  const enableFormFieldsAfterDelete = useCallback(() => {
    console.log('Enabling form fields after delete operation...');

    // تنظيف أي مؤقتات معلقة
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }

    // تمكين الحقول فورًا بدون تأخير
    // تعيين حالة تمكين الحقول إلى true
    fieldsEnabled.current = true;

    // محاولة فورية لتمكين الحقول
    if (formRef.current && isMounted.current) {
      try {
        console.log('Immediate attempt to enable form fields...');

        // الحصول على جميع حقول الإدخال في النموذج
        const inputs = formRef.current.querySelectorAll('input, select, textarea, button');

        // إعادة تمكين جميع الحقول
        inputs.forEach(input => {
          // إعادة تمكين الحقل بشكل كامل
          input.disabled = false;

          if (input.type === 'text' || input.type === 'number') {
            input.readOnly = false;
          }

          // إزالة أي أنماط قد تشير إلى أن الحقل معطل
          input.style.opacity = '1';
          input.style.pointerEvents = 'auto';
          input.style.backgroundColor = '';
          input.style.color = '';

          // إضافة مستمع أحداث للتركيز لضمان أن الحقل يمكن التفاعل معه
          const focusHandler = () => {
            input.disabled = false;
            input.readOnly = false;
            input.style.opacity = '1';
            input.style.pointerEvents = 'auto';
          };

          // إزالة المستمع القديم إذا كان موجودًا ثم إضافة مستمع جديد
          input.removeEventListener('focus', focusHandler);
          input.addEventListener('focus', focusHandler);

          // إضافة مستمع أحداث للنقر لضمان أن الحقل يمكن التفاعل معه
          const clickHandler = () => {
            input.disabled = false;
            input.readOnly = false;
            input.style.opacity = '1';
            input.style.pointerEvents = 'auto';
          };

          // إزالة المستمع القديم إذا كان موجودًا ثم إضافة مستمع جديد
          input.removeEventListener('click', clickHandler);
          input.addEventListener('click', clickHandler);
        });

        console.log('Form fields enabled successfully in immediate attempt');
      } catch (error) {
        console.error('Error in immediate form fields enabling attempt:', error);
      }
    }

    // محاولات إضافية بفواصل زمنية قصيرة جدًا
    const intervals = [50, 100, 200];

    intervals.forEach((interval, index) => {
      setTimeout(() => {
        if (formRef.current && isMounted.current) {
          try {
            console.log(`Quick enabling form fields (attempt ${index + 2}, ${interval}ms)...`);

            // الحصول على جميع حقول الإدخال في النموذج
            const inputs = formRef.current.querySelectorAll('input, select, textarea, button');

            // إعادة تمكين جميع الحقول
            inputs.forEach(input => {
              // إعادة تمكين الحقل بشكل كامل
              input.disabled = false;

              if (input.type === 'text' || input.type === 'number') {
                input.readOnly = false;
              }

              // إزالة أي أنماط قد تشير إلى أن الحقل معطل
              input.style.opacity = '1';
              input.style.pointerEvents = 'auto';
              input.style.backgroundColor = '';
              input.style.color = '';
            });

            console.log(`Form fields enabled successfully in quick attempt ${index + 2}`);
          } catch (error) {
            console.error(`Error in quick form fields enabling attempt ${index + 2}:`, error);
          }
        }
      }, interval);
    });

    return () => {
      // تنظيف المؤقت عند إلغاء تحميل المكون
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [formRef, isMounted]);

  return {
    isMounted,
    enableFormFields,
    resetFormCompletely,
    fieldsEnabled,
    enableFormFieldsAfterDelete
  };
};

export default useFormReset;
