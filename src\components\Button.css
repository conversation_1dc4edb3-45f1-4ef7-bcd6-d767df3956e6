/* أنماط مكون الزر */
.app-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  outline: none;
  position: relative;
  overflow: hidden;
  font-family: inherit;
  font-size: 1rem;
}

.app-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.app-button:active:not(:disabled) {
  transform: translateY(0);
}

/* أحجام الأزرار */
.app-button-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.app-button-md {
  padding: 0.5rem 1rem;
  font-size: 1rem;
}

.app-button-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1.1rem;
}

/* أنواع الأزرار */
.app-button-primary {
  background-color: var(--primary-color);
  color: white;
}

.app-button-primary:hover:not(:disabled) {
  background-color: var(--primary-dark);
}

.app-button-secondary {
  background-color: var(--accent-color);
  color: white;
}

.app-button-secondary:hover:not(:disabled) {
  background-color: #d35400;
}

.app-button-success {
  background-color: var(--success-color);
  color: white;
}

.app-button-success:hover:not(:disabled) {
  background-color: #219653;
}

.app-button-danger {
  background-color: var(--danger-color);
  color: white;
}

.app-button-danger:hover:not(:disabled) {
  background-color: #c0392b;
}

.app-button-warning {
  background-color: var(--warning-color);
  color: white;
}

.app-button-warning:hover:not(:disabled) {
  background-color: #e67e22;
}

.app-button-info {
  background-color: var(--info-color);
  color: white;
}

.app-button-info:hover:not(:disabled) {
  background-color: #2980b9;
}

.app-button-light {
  background-color: #f8f9fa;
  color: var(--text-dark);
  border: 1px solid #e9ecef;
}

.app-button-light:hover:not(:disabled) {
  background-color: #e9ecef;
}

.app-button-dark {
  background-color: #343a40;
  color: white;
}

.app-button-dark:hover:not(:disabled) {
  background-color: #212529;
}

/* أزرار محددة الخطوط */
.app-button-outlined {
  background-color: transparent;
  border: 1px solid currentColor;
}

.app-button-outlined.app-button-primary {
  color: var(--primary-color);
}

.app-button-outlined.app-button-secondary {
  color: var(--accent-color);
}

.app-button-outlined.app-button-success {
  color: var(--success-color);
}

.app-button-outlined.app-button-danger {
  color: var(--danger-color);
}

.app-button-outlined.app-button-warning {
  color: var(--warning-color);
}

.app-button-outlined.app-button-info {
  color: var(--info-color);
}

.app-button-outlined.app-button-light {
  color: #6c757d;
}

.app-button-outlined.app-button-dark {
  color: #343a40;
}

.app-button-outlined:hover:not(:disabled) {
  background-color: rgba(0, 0, 0, 0.05);
}

/* زر بعرض كامل */
.app-button-full-width {
  width: 100%;
}

/* زر مستدير */
.app-button-rounded {
  border-radius: 50px;
}

/* زر معطل */
.app-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* أيقونة الزر */
.app-button-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.app-button-icon-right {
  margin-right: 8px;
}

.app-button-icon-left {
  margin-left: 8px;
}

/* زر التحميل */
.app-button-loading {
  color: transparent !important;
}

.app-button-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 1.2em;
  height: 1.2em;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 0.8s linear infinite;
}

.app-button-outlined .app-button-spinner {
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-top-color: currentColor;
}

@keyframes spin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
