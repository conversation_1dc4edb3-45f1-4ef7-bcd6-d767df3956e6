/**
 * تعريف جدول الخزينة
 *
 * هذا الجدول يستخدم لتخزين بيانات الخزينة والمعاملات المالية
 */

const createCashboxTable = `
CREATE TABLE IF NOT EXISTS cashbox (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  initial_balance REAL NOT NULL,
  current_balance REAL NOT NULL,
  profit_total REAL DEFAULT 0,
  sales_total REAL DEFAULT 0,
  purchases_total REAL DEFAULT 0,
  returns_total REAL DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
`;

const createCashboxTransactionsTable = `
CREATE TABLE IF NOT EXISTS cashbox_transactions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  type TEXT NOT NULL,
  amount REAL NOT NULL,
  source TEXT NOT NULL,
  notes TEXT,
  user_id INTEGER,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIG<PERSON> KEY (user_id) REFERENCES users (id)
);

-- إنشاء فهرس لنوع المعاملة لتحسين أداء البحث
CREATE INDEX IF NOT EXISTS idx_cashbox_transactions_type ON cashbox_transactions(type);
-- إنشاء فهرس لتاريخ المعاملة لتحسين أداء البحث
CREATE INDEX IF NOT EXISTS idx_cashbox_transactions_created_at ON cashbox_transactions(created_at);
`;

// بيانات الخزينة الافتراضية (فارغة للإصدار التجاري)
const defaultCashbox = [];

module.exports = {
  createCashboxTable,
  createCashboxTransactionsTable,
  defaultCashbox
};
