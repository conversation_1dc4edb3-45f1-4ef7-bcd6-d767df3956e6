/**
 * أداة إصلاح شاملة لمشاكل حفظ الأرباح في نظام الخزينة
 * تقوم بإصلاح جميع المشاكل المتعلقة بحفظ وتحديث قيم الأرباح
 */

const { logError, logSystem } = require('./error-handler');
const DatabaseManager = require('./database-singleton');
const eventSystem = require('./event-system');

/**
 * إصلاح شامل لمشاكل حفظ الأرباح
 */
async function fixProfitSavingIssues() {
  console.log('🔧 بدء الإصلاح الشامل لمشاكل حفظ الأرباح...');
  
  const fixResult = {
    timestamp: new Date().toISOString(),
    success: false,
    steps: [],
    errors: [],
    finalState: {}
  };

  try {
    // الحصول على اتصال قاعدة البيانات
    const dbManager = DatabaseManager.getInstance();
    const db = dbManager.getConnection();

    if (!db) {
      throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
    }

    console.log('✅ تم الحصول على اتصال قاعدة البيانات');

    // 1. إصلاح بنية جدول cashbox
    await fixCashboxTableStructure(db, fixResult);

    // 2. إصلاح حسابات الأرباح في المعاملات
    await fixTransactionProfits(db, fixResult);

    // 3. إعادة حساب إجمالي الأرباح
    await recalculateAndSaveTotalProfits(db, fixResult);

    // 4. تطبيق آلية حفظ محسنة
    await implementImprovedSavingMechanism(db, fixResult);

    // 5. اختبار آلية الحفظ
    await testSavingMechanism(db, fixResult);

    // 6. إرسال إشعارات التحديث
    await sendUpdateNotifications(fixResult);

    fixResult.success = true;
    console.log('✅ تم إكمال الإصلاح الشامل لمشاكل حفظ الأرباح بنجاح');

  } catch (error) {
    console.error('❌ خطأ في الإصلاح الشامل:', error);
    fixResult.errors.push(`خطأ عام: ${error.message}`);
    logError(error, 'fixProfitSavingIssues');
  }

  return fixResult;
}

/**
 * إصلاح بنية جدول cashbox
 */
async function fixCashboxTableStructure(db, fixResult) {
  console.log('🔧 إصلاح بنية جدول cashbox...');

  try {
    // التحقق من وجود حقل profit_total
    const tableInfo = db.prepare("PRAGMA table_info(cashbox)").all();
    const profitColumn = tableInfo.find(col => col.name === 'profit_total');
    
    if (!profitColumn) {
      // إضافة حقل profit_total إذا لم يكن موجوداً
      db.prepare("ALTER TABLE cashbox ADD COLUMN profit_total REAL DEFAULT 0").run();
      fixResult.steps.push('✅ تم إضافة حقل profit_total إلى جدول cashbox');
      console.log('✅ تم إضافة حقل profit_total إلى جدول cashbox');
    } else {
      fixResult.steps.push('✅ حقل profit_total موجود في جدول cashbox');
      console.log('✅ حقل profit_total موجود في جدول cashbox');
    }

    // التحقق من وجود سجل في جدول cashbox
    const cashboxData = db.prepare('SELECT * FROM cashbox LIMIT 1').get();
    
    if (!cashboxData) {
      // إنشاء سجل خزينة افتراضي
      const insertStmt = db.prepare(`
        INSERT INTO cashbox (
          initial_balance, current_balance, profit_total, 
          sales_total, purchases_total, returns_total, transport_total,
          created_at, updated_at
        ) VALUES (0, 0, 0, 0, 0, 0, 0, ?, ?)
      `);
      
      const now = new Date().toISOString();
      insertStmt.run(now, now);
      
      fixResult.steps.push('✅ تم إنشاء سجل خزينة افتراضي');
      console.log('✅ تم إنشاء سجل خزينة افتراضي');
    }

  } catch (error) {
    const errorMsg = `خطأ في إصلاح بنية جدول cashbox: ${error.message}`;
    fixResult.errors.push(errorMsg);
    console.error('❌', errorMsg);
  }
}

/**
 * إصلاح حسابات الأرباح في المعاملات
 */
async function fixTransactionProfits(db, fixResult) {
  console.log('🔧 إصلاح حسابات الأرباح في المعاملات...');

  try {
    // البحث عن معاملات بيع بأرباح خاطئة
    const wrongProfitsQuery = db.prepare(`
      SELECT t.id, t.selling_price, t.quantity, i.avg_price, t.profit,
             (t.selling_price - i.avg_price) * t.quantity as correct_profit
      FROM transactions t
      JOIN inventory i ON t.item_id = i.item_id
      WHERE t.transaction_type = 'sale' 
        AND t.selling_price > 0 
        AND i.avg_price > 0
        AND ABS(t.profit - ((t.selling_price - i.avg_price) * t.quantity)) > 0.01
    `);

    const wrongProfits = wrongProfitsQuery.all();
    
    if (wrongProfits.length > 0) {
      console.log(`🔧 تم العثور على ${wrongProfits.length} معاملة بأرباح خاطئة`);
      
      // إصلاح الأرباح الخاطئة
      const updateProfitStmt = db.prepare(`
        UPDATE transactions 
        SET profit = ? 
        WHERE id = ?
      `);

      let fixedCount = 0;
      
      // بدء معاملة قاعدة البيانات
      const transaction = db.transaction(() => {
        for (const tx of wrongProfits) {
          try {
            updateProfitStmt.run(tx.correct_profit, tx.id);
            fixedCount++;
          } catch (error) {
            console.error(`خطأ في إصلاح الربح للمعاملة ${tx.id}:`, error);
          }
        }
      });

      transaction();
      
      fixResult.steps.push(`✅ تم إصلاح ${fixedCount} معاملة بأرباح خاطئة`);
      console.log(`✅ تم إصلاح ${fixedCount} معاملة بأرباح خاطئة`);
    } else {
      fixResult.steps.push('✅ جميع معاملات البيع لها أرباح صحيحة');
      console.log('✅ جميع معاملات البيع لها أرباح صحيحة');
    }

  } catch (error) {
    const errorMsg = `خطأ في إصلاح حسابات الأرباح: ${error.message}`;
    fixResult.errors.push(errorMsg);
    console.error('❌', errorMsg);
  }
}

/**
 * إعادة حساب وحفظ إجمالي الأرباح
 */
async function recalculateAndSaveTotalProfits(db, fixResult) {
  console.log('🔧 إعادة حساب وحفظ إجمالي الأرباح...');

  try {
    // حساب إجمالي الأرباح من المعاملات
    const profitQuery = db.prepare(`
      SELECT COALESCE(SUM(profit), 0) as total_profit
      FROM transactions
      WHERE transaction_type = 'sale'
    `);

    const result = profitQuery.get();
    const calculatedProfit = Number(result.total_profit || 0);
    
    console.log(`📊 إجمالي الأرباح المحسوب: ${calculatedProfit}`);

    // تحديث إجمالي الأرباح في الخزينة مع آلية محسنة
    const updateResult = await updateProfitTotalWithVerification(db, calculatedProfit);
    
    if (updateResult.success) {
      fixResult.steps.push(`✅ تم تحديث إجمالي الأرباح إلى: ${calculatedProfit}`);
      fixResult.finalState.profit_total = calculatedProfit;
      console.log(`✅ تم تحديث إجمالي الأرباح إلى: ${calculatedProfit}`);
    } else {
      throw new Error(updateResult.error || 'فشل في تحديث إجمالي الأرباح');
    }

  } catch (error) {
    const errorMsg = `خطأ في إعادة حساب إجمالي الأرباح: ${error.message}`;
    fixResult.errors.push(errorMsg);
    console.error('❌', errorMsg);
  }
}

/**
 * تحديث إجمالي الأرباح مع التحقق المحسن
 */
async function updateProfitTotalWithVerification(db, totalProfit) {
  try {
    console.log(`[PROFIT-SAVE] بدء تحديث profit_total إلى: ${totalProfit}`);

    // استخدام معاملة قاعدة البيانات لضمان الاتساق
    const updateTransaction = db.transaction(() => {
      // تحديث القيمة
      const updateStmt = db.prepare(`
        UPDATE cashbox 
        SET profit_total = ?, 
            updated_at = ? 
        WHERE id = 1
      `);

      const updateResult = updateStmt.run(totalProfit, new Date().toISOString());
      
      if (updateResult.changes === 0) {
        throw new Error('لم يتم تحديث أي صف في جدول cashbox');
      }

      // التحقق الفوري من حفظ القيمة
      const verifyStmt = db.prepare('SELECT profit_total FROM cashbox WHERE id = 1');
      const verifyResult = verifyStmt.get();
      
      if (!verifyResult) {
        throw new Error('فشل في استرجاع البيانات للتحقق');
      }

      const savedValue = Number(verifyResult.profit_total);
      
      if (Math.abs(savedValue - totalProfit) > 0.01) {
        throw new Error(`فشل في حفظ القيمة: متوقع ${totalProfit} لكن تم حفظ ${savedValue}`);
      }

      console.log(`[PROFIT-SAVE] تم حفظ القيمة بنجاح: ${savedValue}`);
      
      return {
        success: true,
        savedValue: savedValue,
        changes: updateResult.changes
      };
    });

    return updateTransaction();

  } catch (error) {
    console.error('[PROFIT-SAVE] خطأ في تحديث profit_total:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * تطبيق آلية حفظ محسنة
 */
async function implementImprovedSavingMechanism(db, fixResult) {
  console.log('🔧 تطبيق آلية حفظ محسنة...');

  try {
    // إضافة فهرس على profit_total إذا لم يكن موجوداً
    try {
      db.prepare("CREATE INDEX IF NOT EXISTS idx_cashbox_profit_total ON cashbox(profit_total)").run();
      fixResult.steps.push('✅ تم إضافة فهرس على profit_total');
    } catch (indexError) {
      console.log('ملاحظة: فهرس profit_total موجود مسبقاً أو لا يمكن إضافته');
    }

    // تحسين إعدادات قاعدة البيانات للكتابة
    db.prepare("PRAGMA synchronous = NORMAL").run();
    db.prepare("PRAGMA journal_mode = WAL").run();
    db.prepare("PRAGMA cache_size = 10000").run();
    
    fixResult.steps.push('✅ تم تحسين إعدادات قاعدة البيانات للكتابة');
    console.log('✅ تم تحسين إعدادات قاعدة البيانات للكتابة');

  } catch (error) {
    const errorMsg = `خطأ في تطبيق آلية الحفظ المحسنة: ${error.message}`;
    fixResult.errors.push(errorMsg);
    console.error('❌', errorMsg);
  }
}

/**
 * اختبار آلية الحفظ
 */
async function testSavingMechanism(db, fixResult) {
  console.log('🧪 اختبار آلية الحفظ...');

  try {
    // الحصول على القيمة الحالية
    const currentQuery = db.prepare('SELECT profit_total FROM cashbox WHERE id = 1');
    const currentResult = currentQuery.get();
    const originalValue = currentResult ? Number(currentResult.profit_total) : 0;

    // اختبار تحديث وحفظ قيمة جديدة
    const testValue = originalValue + 0.01;
    const updateResult = await updateProfitTotalWithVerification(db, testValue);

    if (updateResult.success) {
      // إعادة القيمة الأصلية
      await updateProfitTotalWithVerification(db, originalValue);
      
      fixResult.steps.push('✅ اختبار آلية الحفظ نجح');
      console.log('✅ اختبار آلية الحفظ نجح');
    } else {
      throw new Error(`فشل اختبار آلية الحفظ: ${updateResult.error}`);
    }

  } catch (error) {
    const errorMsg = `خطأ في اختبار آلية الحفظ: ${error.message}`;
    fixResult.errors.push(errorMsg);
    console.error('❌', errorMsg);
  }
}

/**
 * إرسال إشعارات التحديث
 */
async function sendUpdateNotifications(fixResult) {
  console.log('📢 إرسال إشعارات التحديث...');

  try {
    // إرسال إشعار بتحديث الخزينة
    eventSystem.notifyCashboxUpdated({
      operation: 'profit-saving-fix',
      profit_total: fixResult.finalState.profit_total,
      success: fixResult.success,
      timestamp: fixResult.timestamp,
      instant_update: true
    });

    // إرسال إشعار بتحديث الأرباح
    eventSystem.notifyProfitsUpdated({
      operation: 'profit-saving-fix',
      total_profit: fixResult.finalState.profit_total,
      success: fixResult.success,
      timestamp: fixResult.timestamp,
      instant_update: true
    });

    fixResult.steps.push('✅ تم إرسال إشعارات التحديث');
    console.log('✅ تم إرسال إشعارات التحديث');

  } catch (error) {
    const errorMsg = `خطأ في إرسال إشعارات التحديث: ${error.message}`;
    fixResult.errors.push(errorMsg);
    console.error('❌', errorMsg);
  }
}

/**
 * طباعة تقرير الإصلاح
 */
function printFixReport(fixResult) {
  console.log('\n' + '='.repeat(60));
  console.log('📋 تقرير الإصلاح الشامل لمشاكل حفظ الأرباح');
  console.log('='.repeat(60));
  
  console.log(`🕐 وقت الإصلاح: ${fixResult.timestamp}`);
  console.log(`✅ نجح الإصلاح: ${fixResult.success ? 'نعم' : 'لا'}`);
  console.log(`🔧 عدد خطوات الإصلاح: ${fixResult.steps.length}`);
  console.log(`❌ عدد الأخطاء: ${fixResult.errors.length}`);
  
  if (fixResult.steps.length > 0) {
    console.log('\n🔧 خطوات الإصلاح المنجزة:');
    fixResult.steps.forEach((step, index) => {
      console.log(`${index + 1}. ${step}`);
    });
  }
  
  if (fixResult.errors.length > 0) {
    console.log('\n❌ الأخطاء التي حدثت:');
    fixResult.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error}`);
    });
  }
  
  console.log('\n📊 الحالة النهائية:');
  console.log(JSON.stringify(fixResult.finalState, null, 2));
  
  console.log('\n' + '='.repeat(60));
}

module.exports = {
  fixProfitSavingIssues,
  updateProfitTotalWithVerification,
  printFixReport
};
