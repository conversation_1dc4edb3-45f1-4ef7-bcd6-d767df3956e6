/* أنماط شريط التنقل العلوي */
.top-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: linear-gradient(to left, #1e3243, #162330); /* تدرج من اللون الأزرق الداكن */
  color: white;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.top-navbar-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 20px;
  max-width: 1600px;
  margin: 0 auto;
}

/* الشعار واسم النظام */
.top-navbar-brand {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: opacity 0.2s;
}

.top-navbar-brand:hover {
  opacity: 0.9;
}

.top-logo {
  width: 36px;
  height: 36px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 12px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.logo-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.logo-text {
  color: #e67e22; /* اللون البرتقالي من الشعار */
  font-weight: bold;
  font-size: 0.9rem;
}

.top-navbar-brand h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  white-space: nowrap;
}

/* عناصر القائمة الرئيسية */
.top-navbar-menu {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  margin: 0 20px;
  overflow-x: auto;
  scrollbar-width: none; /* إخفاء شريط التمرير في Firefox */
}

.top-navbar-menu::-webkit-scrollbar {
  display: none; /* إخفاء شريط التمرير في Chrome/Safari */
}

.top-navbar-menu-item {
  height: 60px;
  display: flex;
  align-items: center;
  padding: 0 15px;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
  white-space: nowrap;
}

.top-navbar-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.top-navbar-menu-item.active {
  background-color: rgba(255, 255, 255, 0.15);
  font-weight: bold;
}

.top-navbar-menu-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background-color: #e67e22; /* اللون البرتقالي من الشعار */
}

.menu-icon {
  margin-left: 8px;
  font-size: 1rem;
}

.menu-text {
  font-size: 0.9rem;
}

/* قسم المستخدم والإشعارات */
.top-navbar-actions {
  display: flex;
  align-items: center;
  position: relative;
}

.notification-icon {
  position: relative;
  margin-left: 20px;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: background-color 0.2s, transform 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-icon:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

.notification-icon:active {
  transform: scale(0.95);
}

.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #e74c3c;
  color: white;
  font-size: 0.7rem;
  min-width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  padding: 0 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
  }
  70% {
    transform: scale(1.1);
    box-shadow: 0 0 0 5px rgba(231, 76, 60, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
  }
}

@keyframes bell-shake {
  0% { transform: rotate(0); }
  10% { transform: rotate(10deg); }
  20% { transform: rotate(-10deg); }
  30% { transform: rotate(6deg); }
  40% { transform: rotate(-6deg); }
  50% { transform: rotate(3deg); }
  60% { transform: rotate(-3deg); }
  70% { transform: rotate(1deg); }
  80% { transform: rotate(-1deg); }
  90% { transform: rotate(0); }
  100% { transform: rotate(0); }
}

.bell-shake {
  animation: bell-shake 0.8s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
  transform-origin: top center;
}

.user-profile {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 20px;
  transition: background-color 0.2s;
}

.user-profile:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.user-avatar {
  font-size: 1.5rem;
  margin-left: 8px;
}

.user-name {
  font-size: 0.9rem;
  font-weight: 500;
}

/* قائمة المستخدم المنسدلة */
.user-dropdown {
  position: absolute;
  top: 60px;
  left: 0;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  width: 250px;
  z-index: 1001;
  overflow: hidden;
  animation: dropdown-fade 0.2s ease-out;
}

@keyframes dropdown-fade {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-dropdown-header {
  padding: 15px;
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
}

.user-dropdown-avatar {
  font-size: 2.5rem;
  margin-left: 15px;
  color: #1e3243;
}

.user-dropdown-info {
  flex: 1;
}

.user-dropdown-name {
  font-weight: bold;
  color: #1e3243;
  margin-bottom: 3px;
}

.user-dropdown-role {
  font-size: 0.8rem;
  color: #6c757d;
}

.user-dropdown-divider {
  height: 1px;
  background-color: #e9ecef;
  margin: 0;
}

.user-dropdown-item {
  padding: 12px 15px;
  display: flex;
  align-items: center;
  color: #343a40;
  cursor: pointer;
  transition: background-color 0.2s;
}

.user-dropdown-item:hover {
  background-color: #f8f9fa;
}

.user-dropdown-icon {
  margin-left: 12px;
  font-size: 1rem;
  color: #6c757d;
}

.user-dropdown-item.logout {
  color: #e74c3c;
}

.user-dropdown-item.logout .user-dropdown-icon {
  color: #e74c3c;
}

/* قائمة الإشعارات المنسدلة */
.notifications-dropdown {
  position: absolute;
  top: 60px;
  left: 0;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  width: 320px;
  z-index: 1001;
  overflow: hidden;
  animation: dropdown-fade 0.2s ease-out;
}

.notifications-header {
  padding: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.notifications-header h4 {
  margin: 0;
  font-size: 1rem;
  color: #343a40;
}

.mark-all-read {
  font-size: 0.8rem;
  color: #007bff;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: color 0.2s;
}

.mark-all-read:hover {
  color: #0056b3;
}

.mark-read-icon {
  margin-left: 5px;
  font-size: 0.7rem;
}

.notification-mark-read {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #007bff;
  font-size: 0.8rem;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 5px;
  opacity: 0;
  transition: opacity 0.2s, background-color 0.2s;
}

.notification-item:hover .notification-mark-read {
  opacity: 1;
  background-color: rgba(0, 123, 255, 0.1);
}

.notification-mark-read:hover {
  background-color: rgba(0, 123, 255, 0.2);
}

.notifications-list {
  max-height: 350px;
  overflow-y: auto;
}

.notification-item {
  padding: 12px 15px;
  display: flex;
  align-items: flex-start;
  border-bottom: 1px solid #f1f3f5;
  transition: background-color 0.2s;
  cursor: pointer;
}

.notification-item:hover {
  background-color: #f8f9fa;
}

.notification-item.unread {
  background-color: #f0f7ff;
}

.notification-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-left: 12px;
  margin-top: 5px;
  flex-shrink: 0;
}

.notification-indicator.success {
  background-color: #28a745;
}

.notification-indicator.warning {
  background-color: #ffc107;
}

.notification-indicator.danger {
  background-color: #dc3545;
}

.notification-indicator.info {
  background-color: #17a2b8;
}

.notification-content {
  flex: 1;
}

.notification-message {
  font-size: 0.9rem;
  color: #343a40;
  margin-bottom: 5px;
}

.notification-time {
  font-size: 0.75rem;
  color: #6c757d;
}

.no-notifications {
  padding: 20px;
  text-align: center;
  color: #6c757d;
  font-size: 0.9rem;
}

.notifications-footer {
  padding: 10px 15px;
  text-align: center;
  border-top: 1px solid #e9ecef;
  background-color: #f8f9fa;
}

.view-all-notifications {
  font-size: 0.85rem;
  color: #007bff;
  cursor: pointer;
}

.view-all-notifications:hover {
  text-decoration: underline;
}

/* زر القائمة للشاشات الصغيرة */
.mobile-menu-toggle {
  display: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.mobile-menu-toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* مساحة فارغة لتعويض ارتفاع شريط التنقل */
.top-navbar-spacer {
  height: 60px;
}

/* تنسيقات للشاشات المتوسطة والصغيرة */
@media (max-width: 1200px) {
  .top-navbar-menu-item {
    padding: 0 10px;
  }

  .menu-text {
    font-size: 0.85rem;
  }
}

@media (max-width: 992px) {
  .top-navbar-brand h3 {
    display: none;
  }

  .user-name {
    display: none;
  }
}

@media (max-width: 768px) {
  .mobile-menu-toggle {
    display: block;
  }

  .top-navbar-menu {
    position: fixed;
    top: 60px;
    right: -100%;
    width: 250px;
    height: calc(100vh - 60px);
    background-color: #1e3243;
    flex-direction: column;
    align-items: flex-start;
    padding: 10px 0;
    transition: right 0.3s ease;
    margin: 0;
    overflow-y: auto;
  }

  .top-navbar-menu.mobile-open {
    right: 0;
  }

  .top-navbar-menu-item {
    width: 100%;
    height: auto;
    padding: 12px 20px;
  }

  .top-navbar-menu-item.active::after {
    display: none;
  }

  .top-navbar-menu-item.active {
    background-color: rgba(255, 255, 255, 0.15);
    border-right: 3px solid #e67e22;
  }

  .notifications-dropdown,
  .user-dropdown {
    width: 280px;
    left: auto;
    right: 0;
  }
}
