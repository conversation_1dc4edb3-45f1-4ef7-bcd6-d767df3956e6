/* أنماط صفحة المشتريات */
.purchases-page {
  padding: 20px;
}

.purchases-header {
  margin-bottom: 30px;
}

.purchases-header h1 {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 10px;
}

.purchases-header p {
  font-size: 1rem;
  color: var(--text-light);
}

/* أنماط البحث */
.search-container {
  position: relative;
  margin-bottom: 20px;
  max-width: 500px;
}

.search-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-light);
}

.search-input {
  width: 100%;
  padding: 10px 15px 10px 40px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  font-size: 0.95rem;
}

.search-input:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

/* أنماط قائمة الأصناف */
.items-table-container {
  margin-bottom: 30px;
}

.item-name {
  font-weight: 600;
  color: var(--text-dark);
}

.item-unit {
  color: var(--text-light);
  font-size: 0.9rem;
}

.item-price {
  font-weight: 500;
}

.item-min-quantity {
  font-size: 0.9rem;
  color: var(--text-dark);
  font-weight: 500;
}

.item-actions {
  display: flex;
  gap: 5px;
}

/* أنماط جدول المشتريات */
.purchases-table-container {
  margin-bottom: 30px;
}

.purchase-date {
  font-size: 0.9rem;
  color: var(--text-dark);
}

.purchase-item {
  font-weight: 600;
  color: var(--text-dark);
}

.purchase-quantity {
  font-weight: 500;
}

.purchase-price {
  font-weight: 500;
}

.purchase-total {
  font-weight: 700;
  color: var(--primary-color);
}

.purchase-min-quantity {
  font-size: 0.9rem;
  color: var(--text-dark);
  font-weight: 500;
}

.purchase-invoice {
  font-size: 0.9rem;
  color: var(--text-dark);
  font-weight: 500;
}

.purchase-notes {
  font-size: 0.9rem;
  color: var(--text-light);
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* أنماط نافذة إضافة عملية شراء */
.purchase-form {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group.full-width {
  grid-column: span 2;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-dark);
}

.form-control {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: 0.95rem;
}

.form-control:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.form-text {
  font-size: 0.8rem;
  color: var(--text-light);
  margin-top: 5px;
}

.form-actions {
  grid-column: span 2;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 10px;
}

.total-price-display {
  padding: 15px;
  background-color: rgba(var(--primary-rgb), 0.05);
  border: 1px solid rgba(var(--primary-rgb), 0.1);
  border-radius: var(--border-radius-sm);
}

.total-breakdown {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.total-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.95rem;
  color: var(--text-dark);
}

.total-item span {
  font-weight: 500;
}

.total-final {
  border-top: 1px solid rgba(var(--primary-rgb), 0.2);
  padding-top: 8px;
  margin-top: 5px;
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--primary-color);
}

.required {
  color: var(--danger-color);
  margin-right: 3px;
}

/* تنسيقات للشاشات الصغيرة */
@media (max-width: 768px) {
  .purchase-form {
    grid-template-columns: 1fr;
  }

  .form-group.full-width {
    grid-column: span 1;
  }

  .form-actions {
    grid-column: span 1;
  }
}
