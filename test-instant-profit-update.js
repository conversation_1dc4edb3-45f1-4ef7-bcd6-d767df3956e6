/**
 * اختبار التحديث الفوري لبطاقات الأرباح
 * 
 * هذا الملف يحتوي على اختبارات للتأكد من أن بطاقات الأرباح
 * تتحدث فورياً بعد كل معاملة بيع أو شراء
 */

const { app, BrowserWindow } = require('electron');
const path = require('path');

// محاكاة معاملة بيع
function simulateSaleTransaction() {
  console.log('🔄 محاكاة معاملة بيع...');
  
  const saleData = {
    item_id: 1,
    item_name: 'منتج تجريبي',
    quantity: 2,
    price: 100,
    selling_price: 150,
    total_price: 300,
    profit: 100, // (150 - 100) * 2
    transaction_type: 'sale',
    transaction_date: new Date().toISOString(),
    customer_id: null,
    notes: 'اختبار التحديث الفوري'
  };

  // إرسال الحدث للنافذة الرئيسية
  if (global.mainWindow && global.mainWindow.webContents) {
    global.mainWindow.webContents.send('transaction-added', saleData);
    global.mainWindow.webContents.send('profits-updated', {
      transaction_type: 'sale',
      amount: saleData.total_price,
      profit: saleData.profit,
      auto_update: true,
      timestamp: new Date().toISOString()
    });
    global.mainWindow.webContents.send('direct-update', {
      transaction_type: 'sale',
      amount: saleData.total_price,
      profit: saleData.profit,
      timestamp: new Date().toISOString()
    });
  }

  console.log('✅ تم إرسال أحداث معاملة البيع');
}

// محاكاة معاملة شراء
function simulatePurchaseTransaction() {
  console.log('🔄 محاكاة معاملة شراء...');
  
  const purchaseData = {
    item_id: 1,
    item_name: 'منتج تجريبي',
    quantity: 5,
    price: 80,
    total_price: 400,
    profit: 0,
    transaction_type: 'purchase',
    transaction_date: new Date().toISOString(),
    supplier_id: null,
    notes: 'اختبار التحديث الفوري'
  };

  // إرسال الحدث للنافذة الرئيسية
  if (global.mainWindow && global.mainWindow.webContents) {
    global.mainWindow.webContents.send('transaction-added', purchaseData);
    global.mainWindow.webContents.send('cashbox-updated-ui', {
      transaction_type: 'purchase',
      amount: purchaseData.total_price,
      timestamp: new Date().toISOString()
    });
    global.mainWindow.webContents.send('direct-update', {
      transaction_type: 'purchase',
      amount: purchaseData.total_price,
      timestamp: new Date().toISOString()
    });
  }

  console.log('✅ تم إرسال أحداث معاملة الشراء');
}

// محاكاة معاملة إرجاع
function simulateReturnTransaction() {
  console.log('🔄 محاكاة معاملة إرجاع...');
  
  const returnData = {
    item_id: 1,
    item_name: 'منتج تجريبي',
    quantity: 1,
    price: 150,
    total_price: 150,
    profit: -50, // خصم من الأرباح
    transaction_type: 'return',
    transaction_date: new Date().toISOString(),
    customer_id: null,
    notes: 'اختبار التحديث الفوري'
  };

  // إرسال الحدث للنافذة الرئيسية
  if (global.mainWindow && global.mainWindow.webContents) {
    global.mainWindow.webContents.send('transaction-added', returnData);
    global.mainWindow.webContents.send('profits-updated', {
      transaction_type: 'return',
      amount: returnData.total_price,
      profit: returnData.profit,
      auto_update: true,
      timestamp: new Date().toISOString()
    });
    global.mainWindow.webContents.send('direct-update', {
      transaction_type: 'return',
      amount: returnData.total_price,
      profit: returnData.profit,
      timestamp: new Date().toISOString()
    });
  }

  console.log('✅ تم إرسال أحداث معاملة الإرجاع');
}

// اختبار شامل للتحديث الفوري
function runInstantUpdateTest() {
  console.log('🚀 بدء اختبار التحديث الفوري لبطاقات الأرباح...');
  
  // انتظار 2 ثانية ثم محاكاة معاملة بيع
  setTimeout(() => {
    simulateSaleTransaction();
    
    // انتظار 3 ثواني ثم محاكاة معاملة شراء
    setTimeout(() => {
      simulatePurchaseTransaction();
      
      // انتظار 3 ثواني ثم محاكاة معاملة إرجاع
      setTimeout(() => {
        simulateReturnTransaction();
        
        console.log('✅ تم إكمال اختبار التحديث الفوري');
        console.log('📋 تحقق من تحديث بطاقات الأرباح في واجهة المستخدم');
      }, 3000);
    }, 3000);
  }, 2000);
}

// تصدير الدوال للاستخدام الخارجي
module.exports = {
  simulateSaleTransaction,
  simulatePurchaseTransaction,
  simulateReturnTransaction,
  runInstantUpdateTest
};

// تشغيل الاختبار إذا تم تشغيل الملف مباشرة
if (require.main === module) {
  console.log('⚠️  هذا الملف مخصص للاختبار فقط');
  console.log('📝 استخدم الدوال المصدرة في التطبيق الرئيسي');
}
