.infinite-scroll-container {
  max-height: 600px;
  overflow-y: auto;
  position: relative;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.infinite-scroll-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
}

.infinite-scroll-table thead {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: var(--primary-color);
  color: white;
}

.infinite-scroll-table th {
  padding: 12px 15px;
  text-align: right;
  font-weight: bold;
  border-bottom: 1px solid #e0e0e0;
}

.infinite-scroll-table td {
  padding: 10px 15px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
}

.infinite-scroll-table tr:hover td {
  background-color: #f9f9f9;
}

.infinite-scroll-table tr.clickable {
  cursor: pointer;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: var(--text-light);
}

.spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.empty-message, .end-message {
  text-align: center;
  padding: 20px;
  color: var(--text-light);
  font-style: italic;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
  .infinite-scroll-container {
    max-height: 400px;
  }
  
  .infinite-scroll-table th,
  .infinite-scroll-table td {
    padding: 8px 10px;
    font-size: 14px;
  }
}
