/* أنماط مكون إدارة التحديثات */
.update-manager {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.update-section {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.update-section-title {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  margin: 0;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-size: 1.1rem;
  color: #333;
}

.update-icon {
  margin-left: 10px;
  color: #4a6da7;
}

.update-content {
  padding: 20px;
}

.update-info {
  margin-bottom: 20px;
}

.update-status {
  display: flex;
  align-items: center;
  margin: 10px 0;
  padding: 10px;
  border-radius: 4px;
  background-color: #f8f9fa;
}

.update-status.error {
  background-color: #f8d7da;
  color: #721c24;
}

.update-status.available {
  background-color: #fff3cd;
  color: #856404;
}

.update-status.up-to-date {
  background-color: #d4edda;
  color: #155724;
}

.update-status svg {
  margin-left: 8px;
}

.release-notes, .db-changes {
  margin-top: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-right: 3px solid #4a6da7;
}

.release-notes h4, .db-changes h4 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 0.95rem;
  color: #333;
}

.db-changes ul {
  margin: 0;
  padding-right: 20px;
}

.db-changes li {
  margin-bottom: 5px;
}

.update-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.update-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 15px;
  border: none;
  border-radius: 4px;
  background-color: #4a6da7;
  color: white;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.update-button:hover {
  background-color: #3a5a8f;
}

.update-button:disabled {
  background-color: #a0aec0;
  cursor: not-allowed;
}

.update-button svg {
  margin-left: 8px;
}

.update-button.check {
  background-color: #6c757d;
}

.update-button.check:hover {
  background-color: #5a6268;
}

.update-button.upload {
  background-color: #fd7e14;
}

.update-button.upload:hover {
  background-color: #e76b00;
}

.update-button.download {
  background-color: #28a745;
}

.update-button.download:hover {
  background-color: #218838;
}

.update-button.apply {
  background-color: #17a2b8;
}

.update-button.apply:hover {
  background-color: #138496;
}

.update-button.refresh {
  background-color: #6c757d;
}

.update-button.refresh:hover {
  background-color: #5a6268;
}

.progress-container {
  margin: 15px 0;
}

.progress-label {
  margin-bottom: 5px;
  font-size: 0.9rem;
  color: #495057;
}

.progress-bar {
  height: 10px;
  background-color: #e9ecef;
  border-radius: 5px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #4a6da7;
  transition: width 0.3s ease;
}

.auto-update-settings {
  margin-top: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.auto-update-label {
  display: flex;
  align-items: center;
  font-weight: 500;
  cursor: pointer;
}

.auto-update-label input {
  margin-left: 8px;
}

.auto-update-note, .db-update-note {
  display: flex;
  align-items: flex-start;
  margin-top: 8px;
  font-size: 0.85rem;
  color: #6c757d;
}

.auto-update-note svg, .db-update-note svg {
  margin-left: 5px;
  margin-top: 3px;
  flex-shrink: 0;
}

.db-update-note {
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  margin-top: 15px;
}

.spin-icon {
  animation: spin 1.5s linear infinite;
}

/* أنماط قسم النسخ الاحتياطية */
.backups-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.backups-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.backups-title h4 {
  margin: 0;
  font-size: 1rem;
}

.empty-backups {
  padding: 20px;
  text-align: center;
  background-color: #f8f9fa;
  border-radius: 4px;
  color: #6c757d;
}

.backups-table-container {
  margin: 15px 0;
  overflow-x: auto;
}

.backups-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.backups-table th,
.backups-table td {
  padding: 10px;
  text-align: right;
  border-bottom: 1px solid #e9ecef;
}

.backups-table th {
  background-color: #f8f9fa;
  font-weight: 600;
}

.backups-table tr:hover {
  background-color: #f8f9fa;
}

.backup-actions {
  display: flex;
  gap: 5px;
}

.backup-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border: none;
  border-radius: 4px;
  background-color: #6c757d;
  color: white;
  cursor: pointer;
  transition: background-color 0.2s;
}

.backup-button:hover {
  opacity: 0.9;
}

.backup-button:disabled {
  background-color: #a0aec0;
  cursor: not-allowed;
}

.backup-button.restore {
  background-color: #17a2b8;
}

.backup-button.delete {
  background-color: #dc3545;
}

.restoring-message {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 15px 0;
  padding: 10px;
  background-color: #cff4fc;
  color: #055160;
  border-radius: 4px;
}

.backups-note {
  display: flex;
  align-items: flex-start;
  margin-top: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  font-size: 0.85rem;
  color: #6c757d;
}

.backups-note svg {
  margin-left: 5px;
  margin-top: 3px;
  flex-shrink: 0;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* أنماط معالجة الأخطاء الحرجة */
.critical-error {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 20px;
  margin: 20px 0;
  background-color: #f8d7da;
  border-radius: 8px;
  border-right: 4px solid #dc3545;
  color: #721c24;
}

.critical-error svg {
  font-size: 2rem;
  color: #dc3545;
  flex-shrink: 0;
}

.critical-error h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 1.2rem;
}

.critical-error p {
  margin-bottom: 15px;
}

.error-state {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}
