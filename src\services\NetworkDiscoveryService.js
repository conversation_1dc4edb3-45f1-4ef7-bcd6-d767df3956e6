/**
 * خدمة اكتشاف الأجهزة على الشبكة المحلية
 * تستخدم لاكتشاف خوادم منظومة المبيعات على الشبكة المحلية
 */
import axios from 'axios';
import salesApiService from './salesApi';

/**
 * اكتشاف الأجهزة على الشبكة المحلية
 * @param {string} subnet - الشبكة الفرعية للبحث (مثال: 192.168.1)
 * @param {number} startPort - بداية نطاق المنافذ للبحث
 * @param {number} endPort - نهاية نطاق المنافذ للبحث
 * @returns {Promise<Array>} - قائمة بالأجهزة المكتشفة
 */
export const discoverDevices = async (subnet = '192.168.1', startPort = 3000, endPort = 3010) => {
  try {
    console.log(`بدء اكتشاف الأجهزة على الشبكة ${subnet}.* والمنافذ ${startPort}-${endPort}`);

    // قائمة بالأجهزة المكتشفة
    const discoveredDevices = [];

    // استخدام الـ IPC للاتصال بالعملية الرئيسية لاكتشاف الأجهزة
    // في بيئة الإنتاج، يجب استخدام وظيفة اكتشاف الشبكة الفعلية
    const devices = await window.api.invoke('discover-network-devices', {
      subnet,
      startPort,
      endPort
    });

    // إضافة الأجهزة المكتشفة إلى القائمة
    if (Array.isArray(devices)) {
      devices.forEach(device => {
        discoveredDevices.push(device);
      });
    }

    console.log(`تم اكتشاف ${discoveredDevices.length} جهاز`);

    return discoveredDevices;
  } catch (error) {
    console.error('Device discovery error:', error);
    return [];
  }
};

/**
 * اختبار الاتصال بجهاز مكتشف
 * @param {Object} device - معلومات الجهاز
 * @param {string} apiKey - مفتاح API للمصادقة
 * @returns {Promise<{success: boolean, message: string}>} - نتيجة اختبار الاتصال
 */
export const testDeviceConnection = async (device, apiKey) => {
  try {
    if (!device || !device.ip || !device.port) {
      return { success: false, message: 'معلومات الجهاز غير مكتملة' };
    }

    const url = `http://${device.ip}:${device.port}`;

    console.log(`اختبار الاتصال بالجهاز: ${url}`);

    // تحديث إعدادات خدمة API
    salesApiService.updateConfig({
      baseUrl: url,
      apiKey: apiKey
    });

    // استخدام خدمة API لاختبار الاتصال
    try {
      const result = await salesApiService.testConnection();

      if (result.success) {
        return {
          success: true,
          message: `تم الاتصال بنجاح بالجهاز ${device.name || device.ip}`,
          device: device
        };
      } else {
        return {
          success: false,
          message: result.message || `فشل الاتصال بالجهاز ${device.name || device.ip}`,
          device: device
        };
      }
    } catch (apiError) {
      console.error('API connection error:', apiError);
      return {
        success: false,
        message: `فشل الاتصال بالجهاز: ${apiError.message || 'خطأ غير معروف'}`,
        device: device
      };
    }
  } catch (error) {
    console.error('Test device connection error:', error);
    return {
      success: false,
      message: `فشل الاتصال بالجهاز: ${error.message || 'خطأ غير معروف'}`
    };
  }
};
