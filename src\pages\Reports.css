/* أنماط صفحة التقارير */
.reports-page {
  padding: 20px;
}

.reports-header {
  margin-bottom: 30px;
}

.reports-header h1 {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 10px;
}

.reports-header p {
  font-size: 1rem;
  color: var(--text-light);
}

.report-header-section {
  margin-bottom: 25px;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 15px;
}

.report-header-section h2 {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 10px;
}

.report-subtitle {
  font-size: 1rem;
  color: var(--text-light);
  display: flex;
  align-items: center;
}

.section-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid rgba(var(--primary-rgb), 0.1);
  padding-bottom: 8px;
}

.profit-section {
  margin-bottom: 30px;
}

/* أنماط التبويبات */
.tabs-container {
  margin-bottom: 20px;
  border-bottom: 1px solid var(--border-color);
}

.tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.tab-btn {
  padding: 10px 15px;
  border: none;
  background-color: transparent;
  color: var(--text-light);
  font-weight: 500;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tab-btn:hover {
  color: var(--primary-color);
}

.tab-btn.active {
  color: var(--primary-color);
  border-bottom: 2px solid var(--primary-color);
}

.tab-btn svg {
  font-size: 1.1rem;
}

/* أنماط أدوات التصفية */
.filters-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: var(--bg-light);
  border-radius: var(--border-radius-md);
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-label {
  font-weight: 500;
  color: var(--text-dark);
  white-space: nowrap;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  background-color: white;
  color: var(--text-dark);
  min-width: 150px;
}

.date-input-container {
  position: relative;
}

.date-input {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  background-color: white;
  color: var(--text-dark);
  width: 150px;
}

/* أنماط أدوات التصدير */
.export-tools {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  justify-content: flex-end;
}

.export-tools button {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 15px;
  border-radius: 6px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.export-tools button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.export-tools .btn-primary {
  background-color: var(--primary-color);
  color: white;
  border: none;
}

.export-tools .btn-secondary {
  background-color: #6c757d;
  color: white;
  border: none;
}

/* أنماط الإحصائيات */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background-color: white;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  padding: 20px;
  text-align: center;
  transition: transform 0.2s, box-shadow 0.2s;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(var(--primary-rgb), 0.1);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
}

.stat-card-icon {
  font-size: 2.2rem;
  color: var(--primary-color);
  margin-bottom: 15px;
  opacity: 0.9;
}

.stat-card-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 8px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.stat-card-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-light);
  position: relative;
  padding-bottom: 5px;
}

.stat-card-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 2px;
  background-color: var(--accent-color);
  opacity: 0.7;
}

.stat-card-subtitle {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-top: 8px;
}

/* أنماط خاصة بتقرير الأرباح */
.profits-container {
  background-color: white;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  padding: 25px;
  margin-bottom: 30px;
  border: 1px solid rgba(var(--primary-rgb), 0.1);
  position: relative;
}

.profits-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
}

.profits-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  border-bottom: 1px solid rgba(var(--primary-rgb), 0.1);
  padding-bottom: 15px;
}

.profits-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
}

.profits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.profit-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 15px;
  background-color: rgba(var(--primary-rgb), 0.05);
  border-radius: var(--border-radius-sm);
  border-right: 4px solid var(--primary-color);
}

.profit-item-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-light);
  margin-bottom: 8px;
}

.profit-item-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
}

.profit-quarterly {
  border-right-color: #3498db;
}

.profit-half-yearly {
  border-right-color: #9b59b6;
}

.profit-three-quarters {
  border-right-color: #e67e22;
}

.profit-yearly {
  border-right-color: #27ae60;
}

.profit-quarterly .profit-item-value {
  color: #3498db;
}

.profit-half-yearly .profit-item-value {
  color: #9b59b6;
}

.profit-three-quarters .profit-item-value {
  color: #e67e22;
}

.profit-yearly .profit-item-value {
  color: #27ae60;
}

/* تنسيق خاص بالأرباح */
.profit-amount {
  color: #27ae60 !important;
  font-weight: 700;
}

/* تأثير متحرك للأرباح */
@keyframes profit-pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.profit-amount {
  animation: profit-pulse 2s infinite;
  display: inline-block;
}

/* أنماط الجداول */
.table-container {
  margin-bottom: 30px;
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 12px 15px;
  text-align: right;
  border-bottom: 1px solid var(--border-color);
}

.table th {
  background-color: var(--primary-color);
  color: white;
  font-weight: 600;
  white-space: nowrap;
}

.table tr:nth-child(even) {
  background-color: rgba(var(--primary-rgb), 0.05);
}

.table tr:hover {
  background-color: rgba(var(--primary-rgb), 0.1);
}

.table .badge {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  padding: 5px 8px;
  border-radius: var(--border-radius-sm);
  font-size: 0.8rem;
  font-weight: 500;
}

.table .badge-danger {
  background-color: rgba(var(--danger-rgb), 0.1);
  color: var(--danger-color);
}

.table .badge-warning {
  background-color: rgba(var(--warning-rgb), 0.1);
  color: var(--warning-color);
}

.table .badge-success {
  background-color: rgba(var(--success-rgb), 0.1);
  color: var(--success-color);
}

.table .badge-info {
  background-color: rgba(var(--info-rgb), 0.1);
  color: var(--info-color);
}

/* أنماط توزيع الأرباح */
.profit-distribution {
  display: flex;
  height: 60px;
  border-radius: var(--border-radius-md);
  overflow: hidden;
  margin-bottom: 20px;
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.profit-distribution-item {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  font-weight: 500;
  font-size: 1rem;
  padding: 10px 0;
  transition: all 0.3s ease;
}

.profit-distribution-item:hover {
  transform: translateY(-2px);
}

.profit-distribution-item:first-child {
  background: linear-gradient(135deg, var(--primary-color), rgba(var(--primary-rgb), 0.8));
}

.profit-distribution-item:last-child {
  background: linear-gradient(135deg, #e67e22, rgba(230, 126, 34, 0.8));
}

.profit-distribution-label {
  white-space: nowrap;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  margin-bottom: 5px;
  font-weight: 600;
}

.profit-distribution-value {
  white-space: nowrap;
  font-weight: 700;
  font-size: 1.1rem;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* أنماط تقرير العملاء الفرعيين */
.customer-select {
  max-width: 400px;
  margin-bottom: 20px;
}

.customer-select-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--text-dark);
}

/* أنماط التقرير المفصل الشامل */
.comprehensive-report-container {
  background-color: white;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  padding: 25px;
  margin-bottom: 30px;
  border: 1px solid rgba(var(--primary-rgb), 0.1);
}

.comprehensive-report-description {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 15px;
  background-color: rgba(var(--info-rgb), 0.1);
  border-radius: var(--border-radius-sm);
  border-right: 4px solid var(--info-color);
}

.comprehensive-report-description p {
  margin: 0;
  font-size: 1rem;
  color: var(--text-dark);
  line-height: 1.6;
}

.comprehensive-report-actions {
  display: flex;
  justify-content: center;
  margin: 30px 0;
}

.comprehensive-report-actions .btn-lg {
  padding: 15px 30px;
  font-size: 1.1rem;
  border-radius: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.comprehensive-report-actions .btn-lg:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.comprehensive-report-sections {
  margin-top: 30px;
}

.comprehensive-report-sections h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(var(--primary-rgb), 0.1);
}

.comprehensive-report-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.comprehensive-report-list li {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  margin-bottom: 10px;
  background-color: white;
  border-radius: var(--border-radius-sm);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(var(--primary-rgb), 0.1);
  transition: all 0.2s ease;
}

.comprehensive-report-list li:hover {
  transform: translateX(-5px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-color: var(--primary-color);
}

.comprehensive-report-list .report-icon {
  font-size: 1.2rem;
  color: var(--primary-color);
  margin-left: 12px;
  flex-shrink: 0;
}

.comprehensive-report-list span {
  font-size: 1rem;
}

.comprehensive-report-list .report-description {
  color: var(--text-light);
  margin-right: 5px;
}

/* أنماط للطباعة */
@media print {
  .tabs-container,
  .filters-container,
  .export-tools {
    display: none;
  }

  .reports-page {
    padding: 0;
  }

  .stat-card {
    box-shadow: none;
    border: 1px solid #ddd;
    break-inside: avoid;
    page-break-inside: avoid;
  }

  .stat-card::before {
    height: 3px;
  }

  .stat-card-icon {
    font-size: 1.8rem;
    margin-bottom: 10px;
  }

  .stat-card-value {
    font-size: 1.5rem;
  }

  /* أنماط طباعة تقرير الأرباح */
  .profits-container {
    box-shadow: none;
    border: 1px solid #ddd;
    break-inside: avoid;
    page-break-inside: avoid;
    margin-bottom: 20px;
  }

  .profits-container::before {
    height: 3px;
  }

  .profits-header {
    border-bottom: 1px solid #ddd;
  }

  .profits-title {
    color: #1a3a5f !important;
  }

  .profits-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
  }

  .profit-item {
    background-color: #f9f9f9 !important;
    border-right: 3px solid #1a3a5f;
    margin-bottom: 10px;
  }

  .profit-quarterly {
    border-right-color: #3498db !important;
  }

  .profit-half-yearly {
    border-right-color: #9b59b6 !important;
  }

  .profit-three-quarters {
    border-right-color: #e67e22 !important;
  }

  .profit-yearly {
    border-right-color: #27ae60 !important;
  }

  .profit-item-value {
    color: #1a3a5f !important;
  }

  .profit-quarterly .profit-item-value {
    color: #3498db !important;
  }

  .profit-half-yearly .profit-item-value {
    color: #9b59b6 !important;
  }

  .profit-three-quarters .profit-item-value {
    color: #e67e22 !important;
  }

  .profit-yearly .profit-item-value {
    color: #27ae60 !important;
  }

  .profit-distribution {
    height: 50px;
    box-shadow: none;
    border: 1px solid #ddd;
    break-inside: avoid;
    page-break-inside: avoid;
  }

  .profit-distribution-item:first-child {
    background: #1a3a5f !important;
  }

  .profit-distribution-item:last-child {
    background: #e67e22 !important;
  }

  .table th {
    background-color: #f2f2f2 !important;
    color: #333 !important;
  }

  .table {
    break-inside: auto;
    page-break-inside: auto;
  }

  .table tr {
    break-inside: avoid;
    page-break-inside: avoid;
  }

  h2, h3, h4 {
    break-after: avoid;
    page-break-after: avoid;
  }

  .print-header {
    display: block !important;
    text-align: center;
    margin-bottom: 20px;
  }
}

/* تنسيقات للشاشات الصغيرة */
@media (max-width: 768px) {
  .tabs {
    flex-direction: column;
    gap: 5px;
  }

  .tab-btn {
    width: 100%;
    text-align: right;
    justify-content: flex-start;
  }

  .filters-container {
    flex-direction: column;
    align-items: flex-start;
  }

  .filter-group {
    width: 100%;
  }

  .filter-select,
  .date-input {
    width: 100%;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .export-tools {
    flex-wrap: wrap;
  }
}
