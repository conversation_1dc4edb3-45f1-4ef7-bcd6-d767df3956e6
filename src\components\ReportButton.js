import React from 'react';
import PropTypes from 'prop-types';

/**
 * مكون زر التقرير المحسن
 * @param {Object} props - خصائص المكون
 * @param {React.ReactNode} props.icon - أيقونة الزر
 * @param {string} props.title - عنوان الزر
 * @param {string} props.description - وصف الزر
 * @param {function} props.onClick - دالة النقر على الزر
 * @param {boolean} props.active - حالة تنشيط الزر
 * @returns {JSX.Element} - مكون زر التقرير
 */
const ReportButton = ({ icon, title, description, onClick, active }) => {
  return (
    <div 
      className={`report-button ${active ? 'active' : ''}`} 
      onClick={onClick}
    >
      <div className="report-button-icon">
        {icon}
      </div>
      <div className="report-button-content">
        <div className="report-button-title">{title}</div>
        {description && <div className="report-button-description">{description}</div>}
      </div>
    </div>
  );
};

ReportButton.propTypes = {
  icon: PropTypes.node.isRequired,
  title: PropTypes.string.isRequired,
  description: PropTypes.string,
  onClick: PropTypes.func.isRequired,
  active: PropTypes.bool
};

ReportButton.defaultProps = {
  description: '',
  active: false
};

export default ReportButton;
