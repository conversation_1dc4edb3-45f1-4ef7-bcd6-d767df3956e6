/**
 * وحدة إدارة الخزينة المحسنة
 * تقوم هذه الوحدة بإدارة جميع عمليات الخزينة بشكل موحد
 */

const { logError, logSystem } = require('./error-handler');
const DatabaseManager = require('./database-singleton');
const eventSystem = require('./event-system');
const cashboxUtils = require('./utils/profitCalculator');

// مرجع لقاعدة البيانات (سيتم الحصول عليه من مدير قاعدة البيانات)
let db = null;

/**
 * تهيئة وحدة إدارة الخزينة
 */
function initialize() {
  try {
    console.log('جاري تهيئة وحدة إدارة الخزينة...');

    // الحصول على اتصال قاعدة البيانات من مدير قاعدة البيانات
    const dbManager = DatabaseManager.getInstance();
    db = dbManager.getConnection();

    if (!db) {
      throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
    }

    console.log('تم تهيئة وحدة إدارة الخزينة بنجاح');
    logSystem('تم تهيئة وحدة إدارة الخزينة بنجاح', 'info');

    return true;
  } catch (error) {
    console.error('خطأ في تهيئة وحدة إدارة الخزينة:', error);
    logError(error, 'initialize - cashbox-manager');
    return false;
  }
}

/**
 * الحصول على معلومات الخزينة
 * @returns {Object} - معلومات الخزينة
 */
function getCashbox() {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // التأكد من بنية الجدول قبل المتابعة
    ensureCashboxTableStructure();

    console.log('[CASHBOX-FIX] استدعاء getCashbox');

    const stmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
    const cashbox = stmt.get();

    if (!cashbox) {
      console.log('[CASHBOX-FIX] لم يتم العثور على خزينة');
      return { exists: false };
    }

    console.log('[CASHBOX-FIX] تم العثور على خزينة:', cashbox);
    console.log('[CASHBOX-FIX] قيم الخزينة:');
    console.log('- الرصيد الافتتاحي:', cashbox.initial_balance);
    console.log('- الرصيد الحالي:', cashbox.current_balance);
    console.log('- إجمالي الأرباح:', cashbox.profit_total);
    console.log('- إجمالي المبيعات:', cashbox.sales_total);
    console.log('- إجمالي المشتريات:', cashbox.purchases_total);
    console.log('- إجمالي المرتجعات:', cashbox.returns_total || 0);

    const result = {
      exists: true,
      id: cashbox.id,
      initial_balance: cashbox.initial_balance,
      current_balance: cashbox.current_balance,
      profit_total: cashbox.profit_total,
      sales_total: cashbox.sales_total,
      purchases_total: cashbox.purchases_total,
      returns_total: cashbox.returns_total || 0,
      transport_total: cashbox.transport_total || 0,
      created_at: cashbox.created_at,
      updated_at: cashbox.updated_at
    };

    console.log('[CASHBOX-FIX] إرجاع نتيجة getCashbox:', result);
    return result;
  } catch (error) {
    console.error('[CASHBOX-FIX] خطأ في getCashbox:', error);
    logError(error, 'getCashbox');
    return { exists: false, error: error.message };
  }
}

/**
 * إنشاء خزينة جديدة
 * @param {number} initialBalance - الرصيد الافتتاحي
 * @returns {Object} - نتيجة العملية
 */
function createCashbox(initialBalance) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // التأكد من بنية الجدول قبل المتابعة
    ensureCashboxTableStructure();

    // التحويل إلى رقم
    const numericInitialBalance = Number(initialBalance);
    if (isNaN(numericInitialBalance) || numericInitialBalance < 0) {
      return { success: false, error: `الرصيد الافتتاحي غير صالح: ${initialBalance}` };
    }

    // بدء معاملة قاعدة البيانات
    return db.transaction(() => {
      // التحقق من عدم وجود خزينة
      const checkStmt = db.prepare('SELECT COUNT(*) as count FROM cashbox');
      const { count } = checkStmt.get();

      if (count > 0) {
        throw new Error('توجد خزينة بالفعل');
      }

      // إنشاء الخزينة
      const now = new Date().toISOString();
      const insertStmt = db.prepare(`
        INSERT INTO cashbox (
          initial_balance, current_balance, profit_total, sales_total, purchases_total, returns_total, transport_total,
          created_at, updated_at
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      insertStmt.run(
        numericInitialBalance,
        numericInitialBalance, // الرصيد الحالي يساوي الرصيد الافتتاحي
        0, // إجمالي الربح يبدأ من صفر
        0, // إجمالي المبيعات
        0, // إجمالي المشتريات
        0, // إجمالي المرتجعات
        0, // إجمالي مصاريف النقل
        now,
        now
      );

      // الحصول على الخزينة المنشأة
      const getCashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
      const cashbox = getCashboxStmt.get();

      // إرسال إشعار بتحديث الخزينة
      const cashboxData = {
        exists: true,
        id: cashbox.id,
        initial_balance: cashbox.initial_balance,
        current_balance: cashbox.current_balance,
        profit_total: cashbox.profit_total,
        sales_total: cashbox.sales_total,
        purchases_total: cashbox.purchases_total,
        returns_total: cashbox.returns_total || 0,
        transport_total: cashbox.transport_total || 0,
        created_at: cashbox.created_at,
        updated_at: cashbox.updated_at
      };

      // إرسال إشعار بتحديث الخزينة
      eventSystem.notifyCashboxUpdated({
        id: cashbox.id,
        current_balance: cashbox.current_balance,
        sales_total: cashbox.sales_total,
        purchases_total: cashbox.purchases_total,
        returns_total: cashbox.returns_total || 0,
        transport_total: cashbox.transport_total || 0,
        profit_total: cashbox.profit_total,
        success: true
      });

      return {
        success: true,
        cashbox: cashboxData
      };
    })();
  } catch (error) {
    console.error('خطأ في إنشاء الخزينة:', error);
    logError(error, 'createCashbox');
    return { success: false, error: error.message };
  }
}

/**
 * التحقق من بنية جدول الخزينة وإضافة الأعمدة المفقودة
 */
function ensureCashboxTableStructure() {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    console.log('[CASHBOX-STRUCTURE] التحقق من بنية جدول الخزينة...');

    // الحصول على معلومات الجدول الحالي
    const tableInfo = db.prepare("PRAGMA table_info(cashbox)").all();
    const existingColumns = tableInfo.map(col => col.name);

    console.log('[CASHBOX-STRUCTURE] الأعمدة الموجودة:', existingColumns);

    // قائمة الأعمدة المطلوبة
    const requiredColumns = [
      { name: 'sales_total', type: 'REAL', default: '0' },
      { name: 'purchases_total', type: 'REAL', default: '0' },
      { name: 'returns_total', type: 'REAL', default: '0' },
      { name: 'transport_total', type: 'REAL', default: '0' }
    ];

    // إضافة الأعمدة المفقودة
    for (const column of requiredColumns) {
      if (!existingColumns.includes(column.name)) {
        console.log(`[CASHBOX-STRUCTURE] إضافة العمود المفقود: ${column.name}`);
        const alterQuery = `ALTER TABLE cashbox ADD COLUMN ${column.name} ${column.type} DEFAULT ${column.default}`;
        db.prepare(alterQuery).run();
        console.log(`[CASHBOX-STRUCTURE] تم إضافة العمود: ${column.name}`);
      }
    }

    console.log('[CASHBOX-STRUCTURE] تم التحقق من بنية الجدول بنجاح');
    return true;
  } catch (error) {
    console.error('[CASHBOX-STRUCTURE] خطأ في التحقق من بنية الجدول:', error);
    return false;
  }
}

/**
 * تحديث الرصيد الافتتاحي للخزينة
 * @param {number} initialBalance - الرصيد الافتتاحي الجديد
 * @returns {Object} - نتيجة العملية
 */
function updateInitialBalance(initialBalance) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // التأكد من بنية الجدول قبل المتابعة
    ensureCashboxTableStructure();

    // التحويل إلى رقم
    const numericInitialBalance = Number(initialBalance);
    if (isNaN(numericInitialBalance) || numericInitialBalance < 0) {
      return { success: false, error: `الرصيد الافتتاحي غير صالح: ${initialBalance}` };
    }

    // بدء معاملة قاعدة البيانات
    return db.transaction(() => {
      // التحقق من وجود الخزينة
      const checkStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
      const cashbox = checkStmt.get();

      if (!cashbox) {
        throw new Error('الخزينة غير موجودة');
      }

      // الرصيد الحالي يساوي دائما الرصيد الافتتاحي الجديد
      const newCurrentBalance = numericInitialBalance;

      // الأرباح = المبيعات - المشتريات - النقل
      const newProfit = (cashbox.sales_total || 0) - (cashbox.purchases_total || 0) - (cashbox.transport_total || 0);


      // تحديث الخزينة
      const now = new Date().toISOString();
      const updateStmt = db.prepare(`
        UPDATE cashbox
        SET initial_balance = ?,
            current_balance = ?,
            profit_total = ?,
            updated_at = ?
        WHERE id = ?
      `);

      updateStmt.run(
        numericInitialBalance,
        newCurrentBalance, // الرصيد الحالي المحدث
        newProfit,  // الربح المحدث
        now,
        cashbox.id
      );

      // ملاحظة: تم تعطيل إعادة حساب الأرباح التلقائي لتجنب التداخل مع unified-transaction-manager
      // الأرباح يتم حسابها الآن في unified-transaction-manager.js بشكل صحيح
      console.log('[CASHBOX-FIX] تم تخطي إعادة حساب الأرباح التلقائي لتجنب التداخل مع unified-transaction-manager');

      // استدعاء دالة fixProfitCalculation داخليًا (بدون استخدام API) - معطل مؤقتاً
      // const fixResult = fixProfitCalculationInternal();

      // الحصول على الخزينة المحدثة
      const getCashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
      const updatedCashbox = getCashboxStmt.get();

      // تسجيل معلومات الخزينة بعد التحديث
      console.log(`[CASHBOX-FIX] قيم الخزينة بعد التحديث: الرصيد الافتتاحي=${updatedCashbox.initial_balance}, الرصيد الحالي=${updatedCashbox.current_balance}, الأرباح=${updatedCashbox.profit_total}`);
      logSystem(`تم تحديث الرصيد الافتتاحي للخزينة من ${cashbox.initial_balance} إلى ${numericInitialBalance} مع تعديل الرصيد الحالي إلى ${updatedCashbox.current_balance}`, 'info');

      // إرسال إشعار بتحديث الخزينة
      const cashboxData = {
        exists: true,
        id: updatedCashbox.id,
        initial_balance: updatedCashbox.initial_balance,
        current_balance: updatedCashbox.current_balance,
        profit_total: updatedCashbox.profit_total,
        sales_total: updatedCashbox.sales_total,
        purchases_total: updatedCashbox.purchases_total,
        returns_total: updatedCashbox.returns_total || 0,
        transport_total: updatedCashbox.transport_total || 0,
        created_at: updatedCashbox.created_at,
        updated_at: updatedCashbox.updated_at
      };

      // إرسال إشعار بتحديث الخزينة
      eventSystem.notifyCashboxUpdated({
        id: updatedCashbox.id,
        current_balance: updatedCashbox.current_balance,
        sales_total: updatedCashbox.sales_total,
        purchases_total: updatedCashbox.purchases_total,
        returns_total: updatedCashbox.returns_total || 0,
        transport_total: updatedCashbox.transport_total || 0,
        profit_total: updatedCashbox.profit_total,
        success: true
      });

      return {
        success: true,
        cashbox: cashboxData
      };
    })();
  } catch (error) {
    console.error('خطأ في تحديث الرصيد الافتتاحي:', error);
    logError(error, 'updateInitialBalance');
    return { success: false, error: error.message };
  }
}

/**
 * إضافة معاملة للخزينة
 * @param {Object} transaction - بيانات المعاملة
 * @returns {Object} - نتيجة العملية
 */
function addTransaction(transaction) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // التأكد من بنية الجدول قبل المتابعة
    ensureCashboxTableStructure();

    // التحقق من صحة البيانات
    if (!transaction.type || (transaction.type !== 'income' && transaction.type !== 'expense' && transaction.type !== 'sale' && transaction.type !== 'purchase' && transaction.type !== 'return' && transaction.type !== 'transport')) {
      return { success: false, error: 'نوع المعاملة غير صالح' };
    }

    // التحويل إلى رقم
    const numericAmount = Number(transaction.amount);
    if (isNaN(numericAmount) || numericAmount <= 0) {
      return { success: false, error: `المبلغ غير صالح: ${transaction.amount}` };
    }

    // بدء معاملة قاعدة البيانات
    return db.transaction(() => {
      // التحقق من وجود الخزينة
      let cashbox = db.prepare('SELECT * FROM cashbox LIMIT 1').get();

      // إذا لم تكن هناك خزينة، قم بإنشاء واحدة
      if (!cashbox) {
        const now = new Date().toISOString();
        const createStmt = db.prepare(`
          INSERT INTO cashbox (
            initial_balance, current_balance, profit_total, sales_total, purchases_total, returns_total, transport_total,
            created_at, updated_at
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        createStmt.run(
          0, // الرصيد الافتتاحي
          0, // الرصيد الحالي
          0, // إجمالي الربح
          0, // إجمالي المبيعات
          0, // إجمالي المشتريات
          0, // إجمالي المرتجعات
          0, // إجمالي مصاريف النقل
          now,
          now
        );
        cashbox = db.prepare('SELECT * FROM cashbox LIMIT 1').get(); // أعد قراءة الخزينة بعد الإنشاء
      }

      // إضافة المعاملة
      const now = new Date().toISOString();
      const insertStmt = db.prepare(`
        INSERT INTO cashbox_transactions (
          type, amount, source, notes, user_id, created_at
        )
        VALUES (?, ?, ?, ?, ?, ?)
      `);

      insertStmt.run(
        transaction.type,
        numericAmount,
        transaction.source || '',
        transaction.notes || '',
        transaction.user_id || null,
        now
      );

      // تحديث الخزينة
      // حساب الرصيد الحالي الجديد بناءً على نوع المعاملة
      let newCurrentBalance = cashbox.current_balance || 0;
      let updatedSalesTotal = cashbox.sales_total || 0;
      let updatedPurchasesTotal = cashbox.purchases_total || 0;
      let updatedReturnsTotal = cashbox.returns_total || 0;
      let updatedTransportTotal = cashbox.transport_total || 0;

      if (transaction.type === 'sale' || transaction.type === 'income') {
        updatedSalesTotal += numericAmount;
        newCurrentBalance += numericAmount; // إضافة المبلغ للرصيد الحالي
        console.log(`[CASHBOX-FIX] عملية بيع/دخل: إضافة ${numericAmount} للرصيد الحالي`);
      } else if (transaction.type === 'purchase' || transaction.type === 'expense') {
        updatedPurchasesTotal += numericAmount;
        newCurrentBalance -= numericAmount; // خصم المبلغ من الرصيد الحالي
        console.log(`[CASHBOX-FIX] عملية شراء/مصروف: خصم ${numericAmount} من الرصيد الحالي`);
      } else if (transaction.type === 'return') {
        updatedReturnsTotal += numericAmount;
        updatedSalesTotal -= numericAmount; // Also reduce sales total for returns
        newCurrentBalance -= numericAmount; // خصم المبلغ من الرصيد الحالي
        console.log(`[CASHBOX-FIX] عملية إرجاع: خصم ${numericAmount} من الرصيد الحالي`);
      } else if (transaction.type === 'transport') {
        updatedTransportTotal += numericAmount;
        newCurrentBalance -= numericAmount; // خصم المبلغ من الرصيد الحالي
        console.log(`[CASHBOX-FIX] عملية نقل: خصم ${numericAmount} من الرصيد الحالي`);
      }

      const newProfit = updatedSalesTotal - updatedPurchasesTotal - updatedTransportTotal;

      console.log(`[CASHBOX-FIX] تحديث الخزينة: الرصيد الحالي من ${cashbox.current_balance} إلى ${newCurrentBalance}`);

      const updateQuery = `
        UPDATE cashbox
        SET current_balance = ?,
            sales_total = ?,
            purchases_total = ?,
            returns_total = ?,
            transport_total = ?,
            profit_total = ?,
            updated_at = ?
        WHERE id = ?
      `;
      db.prepare(updateQuery).run(
        newCurrentBalance,
        updatedSalesTotal,
        updatedPurchasesTotal,
        updatedReturnsTotal,
        updatedTransportTotal,
        newProfit,
        now,
        cashbox.id
      );

      // ملاحظة: تم تعطيل إعادة حساب الأرباح التلقائي لتجنب التداخل مع unified-transaction-manager
      // الأرباح يتم حسابها الآن في unified-transaction-manager.js بشكل صحيح
      console.log('[CASHBOX-FIX] تم تخطي إعادة حساب الأرباح التلقائي لتجنب التداخل مع unified-transaction-manager');

      // استدعاء دالة fixProfitCalculation داخليًا (بدون استخدام API) - معطل مؤقتاً
      // هذا يضمن أن الأرباح سيتم حسابها بشكل صحيح بعد كل عملية
      // const fixResult = fixProfitCalculationInternal();

      // الحصول على الخزينة المحدثة بعد إعادة حساب الأرباح
      const getCashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
      const updatedCashbox = getCashboxStmt.get();

      // إرسال إشعار بتحديث الخزينة
      const cashboxData = {
        exists: true,
        id: updatedCashbox.id,
        initial_balance: updatedCashbox.initial_balance,
        current_balance: updatedCashbox.current_balance,
        profit_total: updatedCashbox.profit_total,
        sales_total: updatedCashbox.sales_total,
        purchases_total: updatedCashbox.purchases_total,
        returns_total: updatedCashbox.returns_total || 0,
        transport_total: updatedCashbox.transport_total || 0,
        created_at: updatedCashbox.created_at,
        updated_at: updatedCashbox.updated_at
      };

      // إرسال إشعار بتحديث الخزينة
      eventSystem.notifyCashboxUpdated({
        id: updatedCashbox.id,
        current_balance: updatedCashbox.current_balance,
        sales_total: updatedCashbox.sales_total,
        purchases_total: updatedCashbox.purchases_total,
        returns_total: updatedCashbox.returns_total || 0,
        transport_total: updatedCashbox.transport_total || 0,
        profit_total: updatedCashbox.profit_total,
        transaction_type: transaction.type,
        amount: numericAmount,
        success: true
      });

      return {
        success: true,
        cashbox: cashboxData
      };
    })();
  } catch (error) {
    console.error('خطأ في إضافة معاملة للخزينة:', error);
    logError(error, 'addTransaction');
    return { success: false, error: error.message };
  }
}

/**
 * الحصول على معاملات الخزينة
 * @param {Object} filters - مرشحات البحث
 * @returns {Array} - قائمة المعاملات
 */
function getTransactions(filters = {}) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    console.log(`[CASHBOX-FIX] بدء الحصول على معاملات الخزينة مع الفلاتر:`, filters);

    // استعلام للحصول على معاملات البيع والشراء والاسترجاع (من جدول transactions)
    // استخدام GROUP BY invoice_number لتجميع المعاملات حسب الفاتورة لتجنب التكرار
    let transactionsQuery = `
      SELECT
        MIN(t.id) as id,
        t.transaction_type as type,
        SUM(t.total_price) as amount,
        t.transaction_type as source,
        COALESCE(t.notes, t.invoice_number) as notes,
        t.user_id,
        MAX(t.transaction_date) as created_at,
        u.username as user_name,
        '' as item_name,
        SUM(t.quantity) as quantity,
        'transaction' as transaction_source,
        t.invoice_number,
        COUNT(t.id) as items_count
      FROM transactions t
      LEFT JOIN users u ON t.user_id = u.id
      WHERE t.transaction_type IN ('sale', 'purchase', 'return')
      GROUP BY t.invoice_number, t.transaction_type
    `;

    const transactionsQueryParams = [];
    const transactionsConditions = [];

    // إضافة الفلاتر لاستعلام المعاملات
    if (filters.type) {
      // تحويل نوع المعاملة للاستعلام
      if (filters.type === 'income') {
        transactionsConditions.push("t.transaction_type = 'sale'");
      } else if (filters.type === 'expense') {
        transactionsConditions.push("t.transaction_type = 'purchase'");
      } else if (filters.type === 'return') {
        transactionsConditions.push("t.transaction_type = 'return'");
        console.log('[CASHBOX-FIX] تم تطبيق فلتر الاسترجاع على استعلام المعاملات');
      } else if (filters.type === 'transport') {
        // إذا كان الفلتر للنقل فقط، لا نحتاج للمعاملات العادية
        transactionsConditions.push("1 = 0"); // شرط لا يحقق أي نتائج
      }
    }

    // إضافة فلتر groupBySource إذا كان موجودًا
    if (filters.groupBySource) {
      console.log('[CASHBOX-FIX] تم تطبيق فلتر groupBySource');
    }

    // إضافة فلتر groupByDate إذا كان موجودًا
    if (filters.groupByDate) {
      console.log('[CASHBOX-FIX] تم تطبيق فلتر groupByDate');
    }

    // تم تعديل هذا الجزء لعرض جميع المعاملات بغض النظر عن المصدر
    if (filters.source && filters.source !== '') {
      // تحويل المصدر للاستعلام
      if (filters.source === 'sale' || filters.source === 'purchase' || filters.source === 'return') {
        transactionsConditions.push('t.transaction_type = ?');
        transactionsQueryParams.push(filters.source);
      }
    }
    // لا نضيف أي شروط إذا كان المصدر فارغًا، مما يعني عرض جميع المعاملات

    if (filters.startDate) {
      transactionsConditions.push('t.transaction_date >= ?');
      transactionsQueryParams.push(filters.startDate);
    }

    if (filters.endDate) {
      transactionsConditions.push('t.transaction_date <= ?');
      transactionsQueryParams.push(filters.endDate);
    }

    // إضافة شروط WHERE لاستعلام المعاملات
    if (transactionsConditions.length > 0) {
      transactionsQuery += ` AND ${transactionsConditions.join(' AND ')}`;
    }

    // إضافة الترتيب
    transactionsQuery += ' ORDER BY t.transaction_date DESC';

    // تنفيذ الاستعلام
    console.log(`[CASHBOX-FIX] تنفيذ استعلام المعاملات: ${transactionsQuery}`);
    console.log(`[CASHBOX-FIX] معلمات استعلام المعاملات:`, transactionsQueryParams);

    const transactionsStmt = db.prepare(transactionsQuery);
    const regularTransactions = transactionsStmt.all(...transactionsQueryParams);

    console.log(`[CASHBOX-FIX] تم الحصول على ${regularTransactions.length} معاملة من جدول المعاملات`);

    // الحصول على معاملات النقل منفصلة
    let transportTransactions = [];
    if (!filters.type || filters.type === 'transport') {
      const transportQuery = `
        SELECT
          MIN(t.id) as id,
          'transport' as type,
          SUM(t.transport_cost) as amount,
          'transport' as source,
          'مصاريف نقل' as notes,
          t.user_id,
          MAX(t.transaction_date) as created_at,
          u.username as user_name,
          '' as item_name,
          0 as quantity,
          'transaction' as transaction_source,
          t.invoice_number,
          COUNT(t.id) as items_count
        FROM transactions t
        LEFT JOIN users u ON t.user_id = u.id
        WHERE t.transport_cost > 0 AND t.transaction_type = 'purchase'
        GROUP BY t.invoice_number
        ORDER BY t.transaction_date DESC
      `;

      transportTransactions = db.prepare(transportQuery).all();
      console.log(`[CASHBOX-FIX] تم الحصول على ${transportTransactions.length} معاملة نقل`);
    }

    // تحويل معاملات البيع والشراء والاسترجاع إلى تنسيق متوافق مع معاملات الخزينة
    const formattedTransactions = regularTransactions.map(t => {
      // تعيين نوع المعاملة بشكل صحيح
      // نحتفظ بنوع المعاملة الأصلي (sale, purchase, return) في حقل transaction_type
      // ونستخدم نوع مناسب (income, expense, return) في حقل type للتوافق مع معاملات الخزينة
      let type;
      if (t.type === 'sale') {
        type = 'income'; // استخدام 'income' للتوافق مع معاملات الخزينة
      } else if (t.type === 'purchase') {
        type = 'expense'; // استخدام 'expense' للتوافق مع معاملات الخزينة
      } else if (t.type === 'return') {
        type = 'return';
      } else {
        type = t.type || t.transaction_type; // الحفاظ على النوع الأصلي إذا كان غير معروف
      }

      // إنشاء ملاحظات تتضمن الكمية فقط بدون اسم الصنف
      const notes = t.notes || `${t.type === 'sale' ? 'بيع' : t.type === 'purchase' ? 'شراء' : 'إرجاع'} ${t.quantity} صنف`;

      return {
        ...t,
        type,
        notes,
        // إضافة حقول إضافية للتمييز
        is_transaction: true,
        transaction_type: t.transaction_type,
        // الحفاظ على نوع المعاملة الأصلي في حقل source
        source: t.transaction_type
      };
    });

    // تنسيق معاملات النقل
    const formattedTransportTransactions = transportTransactions.map(t => ({
      ...t,
      type: 'transport',
      notes: t.notes || 'مصاريف نقل',
      transaction_type: 'transport'
    }));

    // استخدام المعاملات المنسقة مع إضافة معاملات النقل
    const allTransactions = [...formattedTransactions, ...formattedTransportTransactions];

    // ترتيب المعاملات حسب التاريخ (الأحدث أولاً)
    allTransactions.sort((a, b) => {
      return new Date(b.created_at) - new Date(a.created_at);
    });

    // تجميع المعاملات حسب المصدر إذا تم طلب ذلك
    if (filters.groupBySource) {
      console.log('[CASHBOX-FIX] تجميع المعاملات حسب المصدر');

      // إضافة حقل source_group لكل معاملة
      allTransactions.forEach(transaction => {
        transaction.source_group = transaction.source || 'غير محدد';
      });
    }

    // تجميع المعاملات حسب التاريخ إذا تم طلب ذلك
    if (filters.groupByDate) {
      console.log('[CASHBOX-FIX] تجميع المعاملات حسب التاريخ');

      // إضافة حقل date_group لكل معاملة
      allTransactions.forEach(transaction => {
        const date = new Date(transaction.created_at);
        transaction.date_group = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
      });
    }

    console.log(`[CASHBOX-FIX] تم الحصول على إجمالي ${allTransactions.length} معاملة للخزينة`);

    // تسجيل أنواع المعاملات للتشخيص
    const transactionTypes = {};
    allTransactions.forEach(t => {
      const key = `${t.type}${t.transaction_source ? '-' + t.transaction_source : ''}`;
      transactionTypes[key] = (transactionTypes[key] || 0) + 1;
    });
    console.log(`[CASHBOX-FIX] أنواع المعاملات:`, transactionTypes);

    // تسجيل تفاصيل المعاملات للتشخيص
    console.log(`[CASHBOX-FIX] تفاصيل المعاملات:`);
    allTransactions.forEach((t, index) => {
      if (index < 10) { // عرض أول 10 معاملات فقط لتجنب الإفراط في التسجيل
        console.log(`[CASHBOX-FIX] معاملة ${index + 1}: النوع=${t.type}, المصدر=${t.source}, المبلغ=${t.amount}, مصدر المعاملة=${t.transaction_source || 'غير محدد'}`);
      }
    });

    return allTransactions;
  } catch (error) {
    console.error('[CASHBOX-FIX] خطأ في الحصول على معاملات الخزينة:', error);
    logError(error, 'getTransactions');
    return [];
  }
}

/**
 * حذف معاملة من الخزينة
 * @param {number} transactionId - معرف المعاملة
 * @returns {Object} - نتيجة العملية
 */
function deleteTransaction(transactionId) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // التحويل إلى رقم
    const numericTransactionId = Number(transactionId);
    if (isNaN(numericTransactionId) || numericTransactionId <= 0) {
      return { success: false, error: `معرف المعاملة غير صالح: ${transactionId}` };
    }

    // بدء معاملة قاعدة البيانات
    return db.transaction(() => {
      // التحقق من وجود المعاملة
      const checkStmt = db.prepare('SELECT * FROM cashbox_transactions WHERE id = ?');
      const transaction = checkStmt.get(numericTransactionId);

      if (!transaction) {
        throw new Error(`المعاملة غير موجودة: ${numericTransactionId}`);
      }

      // التحقق من وجود الخزينة
      const checkCashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
      const cashbox = checkCashboxStmt.get();

      if (!cashbox) {
        throw new Error('الخزينة غير موجودة');
      }

      // تحديث الخزينة
      const now = new Date().toISOString();

      // التحقق من نوع المعاملة لتحديد الحقل المناسب للتحديث
      if (transaction.source === 'transport') {
        // إذا كانت معاملة مصاريف نقل، نحدث transport_total
        const updateStmt = db.prepare(`
          UPDATE cashbox
          SET current_balance = current_balance + ?,
              transport_total = transport_total - ?,
              updated_at = ?
          WHERE id = ?
        `);

        updateStmt.run(
          transaction.amount,
          transaction.amount,
          now,
          cashbox.id
        );
      } else {
        // للمعاملات العادية
        const updateStmt = db.prepare(`
          UPDATE cashbox
          SET current_balance = current_balance ${transaction.type === 'income' ? '-' : '+'} ?,
              ${transaction.type === 'income' ? 'sales_total = sales_total - ?' : 'purchases_total = purchases_total - ?'},
              updated_at = ?
          WHERE id = ?
        `);

        updateStmt.run(
          transaction.amount,
          transaction.amount,
          now,
          cashbox.id
        );
      }

      // حذف المعاملة
      const deleteStmt = db.prepare('DELETE FROM cashbox_transactions WHERE id = ?');
      deleteStmt.run(numericTransactionId);

      // الحصول على الخزينة المحدثة
      const getCashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
      const updatedCashbox = getCashboxStmt.get();

      // إرسال إشعار بتحديث الخزينة
      const cashboxData = {
        exists: true,
        id: updatedCashbox.id,
        initial_balance: updatedCashbox.initial_balance,
        current_balance: updatedCashbox.current_balance,
        profit_total: updatedCashbox.profit_total,
        sales_total: updatedCashbox.sales_total,
        purchases_total: updatedCashbox.purchases_total,
        returns_total: updatedCashbox.returns_total || 0,
        transport_total: updatedCashbox.transport_total || 0,
        created_at: updatedCashbox.created_at,
        updated_at: updatedCashbox.updated_at
      };

      // إرسال إشعار بتحديث الخزينة
      eventSystem.notifyCashboxUpdated({
        id: updatedCashbox.id,
        current_balance: updatedCashbox.current_balance,
        sales_total: updatedCashbox.sales_total,
        purchases_total: updatedCashbox.purchases_total,
        returns_total: updatedCashbox.returns_total || 0,
        profit_total: updatedCashbox.profit_total,
        transaction_type: transaction.type === 'income' ? 'delete-income' : 'delete-expense',
        amount: transaction.amount,
        success: true
      });

      return {
        success: true,
        cashbox: cashboxData
      };
    })();
  } catch (error) {
    console.error('خطأ في حذف معاملة من الخزينة:', error);
    logError(error, 'deleteTransaction');
    return { success: false, error: error.message };
  }
}

/**
 * تحديث الخزينة بعد عملية إرجاع
 * @param {Object} returnData - بيانات عملية الإرجاع
 * @returns {Object} - نتيجة العملية
 */
function updateCashboxAfterReturn(returnData) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    console.log(`[CASHBOX-RETURN] بدء تحديث الخزينة بعد عملية إرجاع:`, returnData);
    logSystem(`[CASHBOX-RETURN] بدء تحديث الخزينة بعد عملية إرجاع: ${JSON.stringify(returnData)}`, 'info');

    // التحقق من صحة البيانات
    if (!returnData.total_price || isNaN(Number(returnData.total_price))) {
      console.error(`[CASHBOX-RETURN] المبلغ غير صالح:`, returnData.total_price);
      return { success: false, error: `المبلغ غير صالح: ${returnData.total_price}` };
    }

    // التحويل إلى رقم
    const numericAmount = Number(returnData.total_price);
    if (numericAmount <= 0) {
      console.error(`[CASHBOX-RETURN] المبلغ يجب أن يكون أكبر من صفر:`, numericAmount);
      return { success: false, error: `المبلغ يجب أن يكون أكبر من صفر: ${numericAmount}` };
    }

    // بدء معاملة قاعدة البيانات
    return db.transaction(() => {
      // التحقق من وجود الخزينة
      const cashbox = db.prepare('SELECT * FROM cashbox LIMIT 1').get();

      if (!cashbox) {
        console.error(`[CASHBOX-RETURN] الخزينة غير موجودة`);
        throw new Error('الخزينة غير موجودة');
      }

      console.log(`[CASHBOX-RETURN] معلومات الخزينة الحالية:`, cashbox);

      // إضافة معاملة للخزينة
      const now = new Date().toISOString();
      const insertStmt = db.prepare(`
        INSERT INTO cashbox_transactions (
          type, amount, source, notes, user_id, created_at
        )
        VALUES (?, ?, ?, ?, ?, ?)
      `);

      insertStmt.run(
        'expense',
        numericAmount,
        'return',
        returnData.notes || `إرجاع للعميل ${returnData.customer || returnData.customer_id}`,
        returnData.user_id || null,
        now
      );

      // تحديث الخزينة
      // حساب الرصيد الحالي الجديد: خصم مبلغ الإرجاع من الرصيد الحالي
      const newCurrentBalance = (cashbox.current_balance || 0) - numericAmount;
      const updatedSalesTotal = (cashbox.sales_total || 0) - numericAmount; // Reduce sales
      const updatedReturnsTotal = (cashbox.returns_total || 0) + numericAmount; // Increase returns
      const newProfit = updatedSalesTotal - (cashbox.purchases_total || 0) - (cashbox.transport_total || 0);

      console.log(`[CASHBOX-RETURN] تحديث الرصيد الحالي: ${cashbox.current_balance} - ${numericAmount} = ${newCurrentBalance}`);

      const updateStmt = db.prepare(`
        UPDATE cashbox
        SET current_balance = ?,
            sales_total = ?,
            returns_total = ?,
            profit_total = ?,
            updated_at = ?
        WHERE id = ?
      `);

      updateStmt.run(
        newCurrentBalance,
        updatedSalesTotal,
        updatedReturnsTotal,
        newProfit,
        now,
        cashbox.id
      );

      console.log(`[CASHBOX-RETURN] تم تحديث الخزينة بنجاح`);

      // ملاحظة: تم تعطيل إعادة حساب الأرباح التلقائي لتجنب التداخل مع unified-transaction-manager
      // الأرباح يتم حسابها الآن في unified-transaction-manager.js بشكل صحيح
      console.log('[CASHBOX-RETURN] تم تخطي إعادة حساب الأرباح التلقائي لتجنب التداخل مع unified-transaction-manager');

      // استدعاء دالة fixProfitCalculation داخليًا (بدون استخدام API) - معطل مؤقتاً
      // const fixResult = fixProfitCalculationInternal();

      // الحصول على الخزينة المحدثة
      const getCashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
      const updatedCashbox = getCashboxStmt.get();

      // تسجيل معلومات الخزينة المحدثة للتشخيص
      console.log(`[CASHBOX-RETURN] معلومات الخزينة بعد التحديث:`);
      console.log(`[CASHBOX-RETURN] - الرصيد الحالي: ${updatedCashbox.current_balance}`);
      console.log(`[CASHBOX-RETURN] - إجمالي المبيعات: ${updatedCashbox.sales_total}`);
      console.log(`[CASHBOX-RETURN] - إجمالي المشتريات: ${updatedCashbox.purchases_total}`);
      console.log(`[CASHBOX-RETURN] - إجمالي المرتجعات: ${updatedCashbox.returns_total}`);
      console.log(`[CASHBOX-RETURN] - إجمالي الأرباح: ${updatedCashbox.profit_total}`);

      // إرسال إشعار بتحديث الخزينة
      const cashboxData = {
        exists: true,
        id: updatedCashbox.id,
        initial_balance: updatedCashbox.initial_balance,
        current_balance: updatedCashbox.current_balance,
        profit_total: updatedCashbox.profit_total,
        sales_total: updatedCashbox.sales_total,
        purchases_total: updatedCashbox.purchases_total,
        returns_total: updatedCashbox.returns_total || 0,
        created_at: updatedCashbox.created_at,
        updated_at: updatedCashbox.updated_at
      };

      // إرسال إشعار بتحديث الخزينة
      eventSystem.notifyCashboxUpdated({
        id: updatedCashbox.id,
        current_balance: updatedCashbox.current_balance,
        sales_total: updatedCashbox.sales_total,
        purchases_total: updatedCashbox.purchases_total,
        returns_total: updatedCashbox.returns_total || 0,
        profit_total: updatedCashbox.profit_total,
        transaction_type: 'return',
        amount: numericAmount,
        success: true
      });

      return {
        success: true,
        transaction_id: result.lastInsertRowid,
        cashbox: cashboxData
      };
    })();
  } catch (error) {
    console.error('[CASHBOX-RETURN] خطأ في تحديث الخزينة بعد عملية إرجاع:', error);
    logError(error, 'updateCashboxAfterReturn');
    return { success: false, error: error.message };
  }
}

/**
 * إصلاح حساب الأرباح في الخزينة (نسخة داخلية)
 *
 * هذه الدالة تعيد حساب إجمالي الأرباح بناءً على إجمالي المبيعات وإجمالي المشتريات
 * وتنقل أي مبلغ زائد من الرصيد الحالي إلى الأرباح
 *
 * @returns {Object} - نتيجة العملية
 */
function fixProfitCalculationInternal() {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // الحصول على الخزينة الحالية
    const getCashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
    const cashbox = getCashboxStmt.get();

    if (!cashbox) {
      throw new Error('الخزينة غير موجودة');
    }

    // تسجيل قيم الخزينة الحالية
    console.log(`[PROFIT-FIX] قيم الخzينة قبل الإصلاح:`, {
      initial_balance: cashbox.initial_balance,
      current_balance: cashbox.current_balance,
      profit_total: cashbox.profit_total,
      sales_total: cashbox.sales_total,
      purchases_total: cashbox.purchases_total,
      returns_total: cashbox.returns_total || 0,
      transport_total: cashbox.transport_total || 0
    });

    // الرصيد الحالي يساوي دائما الرصيد الافتتاحي
    const newCurrentBalance = cashbox.initial_balance;

    // الأرباح = المبيعات - المشتريات - النقل
    const newProfit = (cashbox.sales_total || 0) - (cashbox.purchases_total || 0) - (cashbox.transport_total || 0);


    console.log(`[PROFIT-FIX] الربح المحسوب: ${newProfit} (الربح القديم: ${cashbox.profit_total})`);

    // تحديث الخزينة بالقيم الجديدة
    const now = new Date().toISOString();
    const updateStmt = db.prepare(`
      UPDATE cashbox
      SET current_balance = ?,
          profit_total = ?,
          updated_at = ?
      WHERE id = ?
    `);

    updateStmt.run(
      newCurrentBalance,
      newProfit,
      now,
      cashbox.id
    );

    // الحصول على الخزينة المحدثة
    const updatedCashbox = getCashboxStmt.get();

    // تسجيل قيم الخزينة بعد الإصلاح
    console.log(`[PROFIT-FIX] قيم الخزينة بعد الإصلاح:`, {
      initial_balance: updatedCashbox.initial_balance,
      current_balance: updatedCashbox.current_balance,
      profit_total: updatedCashbox.profit_total,
      sales_total: updatedCashbox.sales_total,
      purchases_total: updatedCashbox.purchases_total,
      returns_total: updatedCashbox.returns_total || 0
    });

    return {
      success: true,
      cashbox: updatedCashbox
    };
  } catch (error) {
    console.error('خطأ في إصلاح حساب الأرباح (داخلي):', error);
    logError(error, 'fixProfitCalculationInternal');
    return { success: false, error: error.message };
  }
}

/**
 * إصلاح حساب الأرباح في الخزينة
 *
 * هذه الدالة تعيد حساب إجمالي الأرباح بناءً على إجمالي المبيعات وإجمالي المشتريات
 *
 * @returns {Object} - نتيجة العملية
 */
function fixProfitCalculation() {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // بدء معاملة قاعدة البيانات
    return db.transaction(() => {
      // استدعاء الدالة الداخلية لإصلاح حساب الأرباح
      const result = fixProfitCalculationInternal();

      if (!result.success) {
        throw new Error(result.error || 'فشل في إصلاح حساب الأرباح');
      }

      const updatedCashbox = result.cashbox;

      // إرسال إشعار بتحديث الخزينة
      eventSystem.notifyCashboxUpdated({
        id: updatedCashbox.id,
        current_balance: updatedCashbox.current_balance,
        sales_total: updatedCashbox.sales_total,
        purchases_total: updatedCashbox.purchases_total,
        returns_total: updatedCashbox.returns_total || 0,
        transport_total: updatedCashbox.transport_total || 0,
        profit_total: updatedCashbox.profit_total,
        success: true
      });

      return {
        success: true,
        message: 'تم إصلاح حساب الأرباح بنجاح',
        cashbox: updatedCashbox
      };
    })();
  } catch (error) {
    console.error('خطأ في إصلاح حساب الأرباح:', error);
    logError(error, 'fixProfitCalculation');
    return { success: false, error: error.message };
  }
}

// تصدير الوظائف
module.exports = {
  initialize,
  getCashbox,
  createCashbox,
  updateInitialBalance,
  addTransaction,
  getTransactions,
  deleteTransaction,
  updateCashboxAfterReturn,
  fixProfitCalculation
};
