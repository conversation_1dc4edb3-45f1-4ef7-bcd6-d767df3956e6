/**
 * وحدة مساعدة لتصدير ملفات PDF
 * تقوم بتسجيل مكتبة jsPDF في النافذة العالمية
 */

import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';

/**
 * تسجيل مكتبة jsPDF في النافذة العالمية
 * لاستخدامها في ملفات أخرى
 */
export function registerPDFLibrary() {
  if (typeof window !== 'undefined') {
    window.jspdf = { jsPDF };
    window.jsPDF = jsPDF; // تسجيل مباشر للمكتبة

    // تسجيل autoTable كامتداد لـ jsPDF
    if (typeof jsPDF.prototype.autoTable !== 'function') {
      console.log('تسجيل autoTable كامتداد لـ jsPDF...');

      // تسجيل autoTable يدويًا
      jsPDF.prototype.autoTable = autoTable;

      if (typeof jsPDF.prototype.autoTable === 'function') {
        console.log('تم تسجيل autoTable بنجاح');
      } else {
        console.error('فشل في تسجيل autoTable');
      }
    } else {
      console.log('autoTable مسجل بالفعل');
    }

    // تسجيل autoTable في النافذة العالمية للاستخدام المباشر
    window.autoTable = autoTable;

    console.log('تم تسجيل مكتبة jsPDF في النافذة العالمية');
  }
}

/**
 * تصدير عنصر HTML كملف PDF
 * @param {HTMLElement} element - عنصر HTML المراد تصديره
 * @param {string} filename - اسم الملف
 * @param {Object} options - خيارات التصدير
 */
export function exportElementToPDF(element, filename, options = {}) {
  try {
    if (!element) {
      console.error('لم يتم تحديد عنصر HTML للتصدير');
      return;
    }

    const { orientation = 'portrait', format = 'a4' } = options;

    // إنشاء مستند PDF جديد
    // استخدام المكتبة المسجلة في النافذة العالمية إذا كانت متوفرة
    const jsPDFLib = window.jsPDF || jsPDF;
    const doc = new jsPDFLib({
      orientation,
      unit: 'mm',
      format,
      putOnlyUsedFonts: true,
      compress: true
    });

    // تحويل عنصر HTML إلى صورة
    import('html2canvas').then(html2canvas => {
      html2canvas.default(element, { scale: 2 }).then(canvas => {
        const imgData = canvas.toDataURL('image/png');
        const imgWidth = doc.internal.pageSize.getWidth();
        const imgHeight = (canvas.height * imgWidth) / canvas.width;

        doc.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
        doc.save(filename);
      });
    });
  } catch (error) {
    console.error('خطأ في تصدير عنصر HTML كملف PDF:', error);
    alert('حدث خطأ أثناء تصدير الملف. يرجى المحاولة مرة أخرى.');
  }
}

// تسجيل المكتبة عند استيراد الوحدة
registerPDFLibrary();

export default {
  registerPDFLibrary,
  exportElementToPDF
};
