import React from 'react';
import { FaPrint, FaFilePdf } from 'react-icons/fa';
import { formatDate } from '../utils/reportUtils';
import { exportToPDF as exportElementToPDF, printElement } from '../utils/simplePdfExport';

/**
 * مكون لطباعة التقارير
 * @param {Object} props - خصائص المكون
 * @param {string} props.reportType - نوع التقرير ('inventory', 'transactions', 'profits', 'customers', 'financial')
 * @param {string} props.dateRange - النطاق الزمني للتقرير
 * @param {Object} props.data - بيانات التقرير
 */
const ReportExporter = ({
  reportType,
  dateRange,
  data
}) => {
  // استخدام وظيفة formatDate المستوردة من reportUtils

  /**
   * الحصول على عنوان التقرير
   * @returns {string} - عنوان التقرير
   */
  const getReportTitle = () => {
    switch (reportType) {
      case 'inventory':
        return 'تقرير المخزون';
      case 'transactions':
        return 'تقرير المعاملات';
      case 'profits':
        return 'تقرير الأرباح';
      default:
        return 'تقرير';
    }
  };

  /**
   * الحصول على نطاق التاريخ كنص
   * @returns {string} - نطاق التاريخ
   */
  const getDateRangeText = () => {
    switch (dateRange) {
      case 'all':
        return 'جميع الفترات';
      case 'quarter':
        return 'الربع الحالي';
      case 'halfYear':
        return 'النصف سنوي';
      case 'threeQuarters':
        return 'ثلاثة أرباع السنة الحالية';
      case 'year':
        return 'السنة الحالية';
      default:
        return '';
    }
  };

  /**
   * البحث عن عنصر التقرير في الصفحة
   * @returns {HTMLElement|null} - عنصر التقرير أو null إذا لم يتم العثور عليه
   */
  const findReportElement = () => {
    // قائمة بالمحددات المحتملة لعنصر التقرير
    const possibleSelectors = [
      '.reports-page .card',
      '.card',
      '.report-container',
      '.reports-container',
      '.report-content',
      '.report-table',
      '.report-data',
      '.table-container',
      'table',
      '.card-body',
      '.content-area'
    ];

    // البحث عن أول عنصر موجود
    for (const selector of possibleSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        console.log('تم العثور على عنصر التقرير باستخدام المحدد:', selector);
        return element;
      }
    }

    // إذا لم يتم العثور على أي عنصر، ابحث عن أي عنصر يحتوي على جدول
    const tables = document.querySelectorAll('table');
    if (tables.length > 0) {
      console.log('تم العثور على جدول في الصفحة');
      // البحث عن أقرب عنصر أب يحتوي على الجدول
      const table = tables[0];
      let parent = table.parentElement;
      while (parent && parent.tagName !== 'BODY') {
        if (parent.classList.contains('card') ||
            parent.classList.contains('report-container') ||
            parent.classList.contains('content-area')) {
          console.log('تم العثور على عنصر أب مناسب للجدول');
          return parent;
        }
        parent = parent.parentElement;
      }

      // إذا لم يتم العثور على عنصر أب مناسب، استخدم الجدول نفسه
      return table;
    }

    // إذا لم يتم العثور على أي عنصر، ابحث عن أي عنصر يحتوي على بيانات التقرير
    const contentArea = document.querySelector('.content-area') ||
                       document.querySelector('.main-content') ||
                       document.querySelector('main');

    if (contentArea) {
      console.log('تم العثور على منطقة المحتوى');
      return contentArea;
    }

    console.error('لم يتم العثور على أي عنصر تقرير في الصفحة');
    return null;
  };

  /**
   * طباعة التقرير باستخدام وظائف الطباعة البسيطة
   */
  const printReport = () => {
    try {
      if (!data) {
        alert('لا توجد بيانات للطباعة');
        return;
      }

      // الحصول على محتوى التقرير من الصفحة باستخدام وظيفة البحث المحسنة
      const reportContent = findReportElement();

      if (!reportContent) {
        alert('لم يتم العثور على محتوى التقرير');
        return;
      }

      // استخدام وظيفة الطباعة البسيطة
      printElement(reportContent)
        .then(success => {
          if (!success) {
            console.warn('فشلت طباعة التقرير باستخدام الطريقة البسيطة، جاري استخدام الطريقة الاحتياطية...');
            printLegacyReport();
          }
        })
        .catch(error => {
          console.error('خطأ في طباعة التقرير:', error);
          printLegacyReport();
        });
    } catch (error) {
      console.error('خطأ في طباعة التقرير:', error);
      alert('حدث خطأ أثناء محاولة طباعة التقرير. سيتم استخدام طريقة الطباعة الاحتياطية.');
      printLegacyReport();
    }
  };

  /**
   * طباعة التقرير بالطريقة القديمة (احتياطي)
   */
  const printLegacyReport = () => {
    try {
      // الحصول على محتوى التقرير من الصفحة باستخدام وظيفة البحث المحسنة
      const reportContent = findReportElement();

      if (!reportContent) {
        alert('لم يتم العثور على محتوى التقرير');
        return;
      }

      // استخدام طريقة الطباعة البسيطة
      printElement(reportContent)
        .then(success => {
          if (!success) {
            // في حالة فشل الطباعة البسيطة، استخدم الطريقة القديمة
            fallbackPrintMethod(reportContent);
          }
        })
        .catch(error => {
          console.error('خطأ في طباعة التقرير:', error);
          fallbackPrintMethod(reportContent);
        });
    } catch (error) {
      console.error('خطأ في الطريقة البديلة للطباعة:', error);
      alert('فشلت جميع محاولات الطباعة. يرجى التحقق من إعدادات المتصفح.');
    }
  };

  /**
   * طريقة الطباعة الاحتياطية في حالة فشل الطرق الأخرى
   * @param {HTMLElement} reportContent - محتوى التقرير
   */
  const fallbackPrintMethod = (reportContent) => {
    try {
      const printWindow = window.open('', '_blank');

      if (!printWindow) {
        alert('يرجى السماح بالنوافذ المنبثقة لطباعة التقرير');
        return;
      }

      // محاولة الحصول على إعدادات النظام من النافذة العالمية
      const appSettings = window.appSettings || {};
      const companyName = appSettings.systemName || 'Hgrop - اتش قروب';
      const companyAddress = appSettings.address || 'للتصميم و تصنيع الأثاث والديكورات';
      const companyPhone = appSettings.phone || '';
      const companyLogo = appSettings.logoUrl || '';

      // استخدام innerHTML بدلاً من document.write
      printWindow.document.open();
      printWindow.document.documentElement.innerHTML = `
        <html dir="rtl" lang="ar">
          <head>
            <meta charset="UTF-8">
            <title>${getReportTitle()}</title>
            <style>
              @font-face {
                font-family: 'Arial';
                src: local('Arial');
              }

              body {
                font-family: Arial, sans-serif;
                direction: rtl;
                padding: 20px;
                color: #1a3a5f;
              }

              .report-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 30px;
                border-bottom: 1px solid #ddd;
                padding-bottom: 20px;
              }

              .company-info {
                display: flex;
                align-items: center;
              }

              .logo {
                width: 80px;
                height: 80px;
                object-fit: contain;
                margin-left: 15px;
              }

              .company-name {
                font-size: 24px;
                font-weight: bold;
                margin: 0;
              }

              .company-slogan {
                font-size: 16px;
                margin: 5px 0;
              }

              .report-title {
                font-size: 20px;
                font-weight: bold;
                margin-bottom: 5px;
              }

              .footer {
                text-align: center;
                margin-top: 40px;
                padding-top: 20px;
                border-top: 1px solid #eee;
                font-size: 12px;
                color: #777;
              }

              @media print {
                body {
                  padding: 0;
                }

                button {
                  display: none;
                }
              }
            </style>
          </head>
          <body>
            <div class="report-container">
              <div class="report-header">
                <div class="company-info">
                  ${companyLogo ? `<img src="${companyLogo}" class="logo" alt="شعار الشركة" />` : ''}
                  <div>
                    <h1 class="company-name">${companyName}</h1>
                    <p class="company-slogan">${companyAddress}</p>
                    ${companyPhone ? `<p>${companyPhone}</p>` : ''}
                  </div>
                </div>
                <div class="report-info">
                  <div class="report-title">${getReportTitle()}</div>
                  <div class="report-date">${getDateRangeText()} - تاريخ التقرير: ${formatDate(new Date().toISOString())}</div>
                </div>
              </div>

              <div class="report-content">
                ${reportContent.innerHTML}
              </div>

              <div class="footer">
                ${companyName} © ${new Date().getFullYear()} | جميع الحقوق محفوظة
              </div>
            </div>

            <script>
              window.onload = function() {
                setTimeout(function() {
                  window.print();
                  window.close();
                }, 1000);
              };
            </script>
          </body>
        </html>
      `;

      printWindow.document.close();
    } catch (error) {
      console.error('خطأ في الطريقة الاحتياطية للطباعة:', error);
      alert('فشلت جميع محاولات الطباعة. يرجى التحقق من إعدادات المتصفح.');
    }
  };



  /**
   * تصدير التقرير كملف PDF
   */
  const exportToPDF = () => {
    try {
      if (!data) {
        alert('لا توجد بيانات للتصدير');
        return;
      }

      // الحصول على محتوى التقرير من الصفحة باستخدام وظيفة البحث المحسنة
      const reportContent = findReportElement();

      if (!reportContent) {
        alert('لم يتم العثور على محتوى التقرير');
        return;
      }

      // استخدام وظيفة التصدير البسيطة
      const filename = `تقرير_${reportType}_${new Date().toISOString().split('T')[0]}.pdf`;

      exportElementToPDF(reportContent, filename)
        .then(success => {
          if (!success) {
            console.warn('فشل تصدير التقرير باستخدام الطريقة البسيطة، جاري استخدام الطريقة الاحتياطية...');
            fallbackExportMethod();
          }
        })
        .catch(error => {
          console.error('خطأ في تصدير التقرير:', error);
          fallbackExportMethod();
        });
    } catch (error) {
      console.error('خطأ في تصدير التقرير:', error);
      alert('حدث خطأ أثناء محاولة تصدير التقرير. يرجى المحاولة مرة أخرى.');
    }
  };

  /**
   * طريقة التصدير الاحتياطية في حالة فشل الطرق الأخرى
   */
  const fallbackExportMethod = () => {
    try {
      // الحصول على محتوى التقرير من الصفحة باستخدام وظيفة البحث المحسنة
      const reportContent = findReportElement();

      if (!reportContent) {
        alert('لم يتم العثور على محتوى التقرير');
        return;
      }

      // استخدام طريقة بديلة للتصدير - فتح نافذة جديدة وطباعتها كـ PDF
      const printWindow = window.open('', '_blank');

      if (!printWindow) {
        alert('يرجى السماح بالنوافذ المنبثقة لتصدير التقرير');
        return;
      }

      // محاولة الحصول على إعدادات النظام من النافذة العالمية
      const appSettings = window.appSettings || {};
      const companyName = appSettings.systemName || 'Hgrop - اتش قروب';
      const companyAddress = appSettings.address || 'للتصميم و تصنيع الأثاث والديكورات';
      const companyPhone = appSettings.phone || '';
      const companyLogo = appSettings.logoUrl || '';

      // استخدام innerHTML بدلاً من document.write
      printWindow.document.open();
      printWindow.document.documentElement.innerHTML = `
        <html dir="rtl" lang="ar">
          <head>
            <meta charset="UTF-8">
            <title>${getReportTitle()}</title>
            <style>
              @font-face {
                font-family: 'Arial';
                src: local('Arial');
              }

              body {
                font-family: Arial, sans-serif;
                direction: rtl;
                padding: 20px;
                color: #1a3a5f;
              }

              .report-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 30px;
                border-bottom: 1px solid #ddd;
                padding-bottom: 20px;
              }

              .company-info {
                display: flex;
                align-items: center;
              }

              .logo {
                width: 80px;
                height: 80px;
                object-fit: contain;
                margin-left: 15px;
              }

              .company-name {
                font-size: 24px;
                font-weight: bold;
                margin: 0;
              }

              .company-slogan {
                font-size: 16px;
                margin: 5px 0;
              }

              .report-title {
                font-size: 20px;
                font-weight: bold;
                margin-bottom: 5px;
              }

              .footer {
                text-align: center;
                margin-top: 40px;
                padding-top: 20px;
                border-top: 1px solid #eee;
                font-size: 12px;
                color: #777;
              }

              @media print {
                body {
                  padding: 0;
                }

                button {
                  display: none;
                }
              }
            </style>
          </head>
          <body>
            <div class="report-container">
              <div class="report-header">
                <div class="company-info">
                  ${companyLogo ? `<img src="${companyLogo}" class="logo" alt="شعار الشركة" />` : ''}
                  <div>
                    <h1 class="company-name">${companyName}</h1>
                    <p class="company-slogan">${companyAddress}</p>
                    ${companyPhone ? `<p>${companyPhone}</p>` : ''}
                  </div>
                </div>
                <div class="report-info">
                  <div class="report-title">${getReportTitle()}</div>
                  <div class="report-date">${getDateRangeText()} - تاريخ التقرير: ${formatDate(new Date().toISOString())}</div>
                </div>
              </div>

              <div class="report-content">
                ${reportContent.innerHTML}
              </div>

              <div class="footer">
                ${companyName} © ${new Date().getFullYear()} | جميع الحقوق محفوظة
              </div>
            </div>

            <script>
              window.onload = function() {
                setTimeout(function() {
                  window.print();
                }, 1000);
              };
            </script>
          </body>
        </html>
      `;

      printWindow.document.close();

      alert('يرجى استخدام خيار "حفظ كـ PDF" في نافذة الطباعة لتصدير التقرير');
    } catch (error) {
      console.error('خطأ في الطريقة الاحتياطية للتصدير:', error);
      alert('فشلت جميع محاولات التصدير. يرجى التحقق من إعدادات المتصفح.');
    }
  };

  return (
    <div className="report-exporter">
      <button
        className="btn btn-primary ml-2"
        onClick={printReport}
        title="طباعة التقرير"
      >
        <FaPrint className="ml-1" />
        طباعة
      </button>

      <button
        className="btn btn-success"
        onClick={exportToPDF}
        title="تصدير كملف PDF"
      >
        <FaFilePdf className="ml-1" />
        تصدير PDF
      </button>
    </div>
  );
};

export default ReportExporter;
