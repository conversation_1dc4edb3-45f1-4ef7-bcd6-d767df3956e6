/**
 * اختبار التحديث الفوري لبطاقات الأرباح في Dashboard
 * 
 * هذا الملف يحتوي على دوال اختبار للتحقق من عمل آلية التحديث الفوري
 * لبطاقات الأرباح في واجهة المستخدم الرئيسية (Dashboard)
 */

// دالة لمراقبة أحداث التحديث
function monitorDashboardProfitsEvents() {
  console.log('🎯 بدء مراقبة أحداث تحديث أرباح Dashboard...');
  
  const eventsToMonitor = [
    'profits-updated',
    'auto-profits-updated', 
    'dashboard-profits-updated',
    'direct-update',
    'transaction-added',
    'cashbox-updated-ui'
  ];
  
  eventsToMonitor.forEach(eventType => {
    window.addEventListener(eventType, (event) => {
      console.log(`📊 [${eventType}] حدث مستلم:`, {
        type: eventType,
        detail: event.detail,
        timestamp: new Date().toISOString()
      });
      
      // تسجيل خاص لأحداث الأرباح
      if (eventType.includes('profit')) {
        console.log(`💰 تحديث أرباح - النوع: ${eventType}`, event.detail);
      }
    });
  });
  
  console.log('✅ تم تفعيل مراقبة أحداث تحديث أرباح Dashboard');
}

// دالة لاختبار تحديث الأرباح يدوياً
async function testManualProfitsUpdate() {
  console.log('🧪 اختبار التحديث اليدوي للأرباح...');
  
  try {
    // محاكاة حدث تحديث الأرباح
    const testEvent = new CustomEvent('profits-updated', {
      detail: {
        transaction_type: 'sale',
        amount: 1000,
        profit: 200,
        total_profit: 5000,
        auto_update: true,
        timestamp: new Date().toISOString(),
        test: true
      }
    });
    
    window.dispatchEvent(testEvent);
    console.log('✅ تم إرسال حدث اختبار تحديث الأرباح');
    
    // اختبار حدث Dashboard المخصص
    setTimeout(() => {
      const dashboardEvent = new CustomEvent('dashboard-profits-updated', {
        detail: {
          profits: {
            quarterly: 1250,
            halfYearly: 2500,
            threeQuarters: 3750,
            yearly: 5000
          },
          timestamp: new Date().toISOString(),
          test: true
        }
      });
      
      window.dispatchEvent(dashboardEvent);
      console.log('✅ تم إرسال حدث اختبار تحديث أرباح Dashboard');
    }, 1000);
    
  } catch (error) {
    console.error('❌ خطأ في اختبار التحديث اليدوي:', error);
  }
}

// دالة لاختبار محاكاة عملية بيع
async function simulateSaleTransaction() {
  console.log('🛒 محاكاة عملية بيع لاختبار التحديث الفوري...');
  
  try {
    // محاكاة إضافة معاملة بيع
    const saleEvent = new CustomEvent('transaction-added', {
      detail: {
        transaction_type: 'sale',
        item_id: 'test-item-001',
        item_name: 'صنف اختبار',
        quantity: 2,
        price: 500,
        total_price: 1000,
        profit: 200,
        timestamp: new Date().toISOString(),
        test: true
      }
    });
    
    window.dispatchEvent(saleEvent);
    console.log('✅ تم محاكاة إضافة معاملة بيع');
    
    // محاكاة تحديث الخزينة
    setTimeout(() => {
      const cashboxEvent = new CustomEvent('cashbox-updated-ui', {
        detail: {
          current_balance: 15000,
          profit_total: 5200,
          sales_total: 25000,
          transaction_type: 'sale',
          success: true,
          timestamp: new Date().toISOString(),
          test: true
        }
      });
      
      window.dispatchEvent(cashboxEvent);
      console.log('✅ تم محاكاة تحديث الخزينة');
    }, 500);
    
  } catch (error) {
    console.error('❌ خطأ في محاكاة عملية البيع:', error);
  }
}

// دالة لفحص حالة بطاقات الأرباح في Dashboard
function checkDashboardProfitsCards() {
  console.log('🔍 فحص حالة بطاقات الأرباح في Dashboard...');
  
  try {
    // البحث عن بطاقة الربح السنوي
    const profitCards = document.querySelectorAll('.stat-card');
    let yearlyProfitCard = null;
    
    profitCards.forEach(card => {
      const title = card.querySelector('.stat-card-title');
      if (title && title.textContent.includes('الربح السنوي')) {
        yearlyProfitCard = card;
      }
    });
    
    if (yearlyProfitCard) {
      const value = yearlyProfitCard.querySelector('.stat-card-value');
      const subtitle = yearlyProfitCard.querySelector('.stat-card-subtitle');
      
      console.log('📊 بطاقة الربح السنوي موجودة:', {
        value: value ? value.textContent : 'غير موجود',
        subtitle: subtitle ? subtitle.textContent : 'غير موجود',
        hasAutoUpdateText: subtitle ? subtitle.textContent.includes('تلقائياً') : false
      });
      
      return true;
    } else {
      console.warn('⚠️ لم يتم العثور على بطاقة الربح السنوي');
      return false;
    }
    
  } catch (error) {
    console.error('❌ خطأ في فحص بطاقات الأرباح:', error);
    return false;
  }
}

// دالة لاختبار شامل
async function runComprehensiveTest() {
  console.log('🚀 بدء الاختبار الشامل لتحديث أرباح Dashboard...');
  
  // 1. بدء المراقبة
  monitorDashboardProfitsEvents();
  
  // 2. فحص الحالة الحالية
  await new Promise(resolve => setTimeout(resolve, 1000));
  checkDashboardProfitsCards();
  
  // 3. اختبار التحديث اليدوي
  await new Promise(resolve => setTimeout(resolve, 2000));
  await testManualProfitsUpdate();
  
  // 4. محاكاة عملية بيع
  await new Promise(resolve => setTimeout(resolve, 3000));
  await simulateSaleTransaction();
  
  // 5. فحص النتائج النهائية
  await new Promise(resolve => setTimeout(resolve, 5000));
  const finalCheck = checkDashboardProfitsCards();
  
  console.log('🏁 انتهى الاختبار الشامل. النتيجة:', finalCheck ? '✅ نجح' : '❌ فشل');
}

// دالة لعرض تعليمات الاستخدام
function showTestInstructions() {
  console.log(`
🎯 تعليمات اختبار التحديث الفوري لأرباح Dashboard:

📋 الدوال المتاحة:
1. monitorDashboardProfitsEvents() - مراقبة الأحداث
2. testManualProfitsUpdate() - اختبار التحديث اليدوي
3. simulateSaleTransaction() - محاكاة عملية بيع
4. checkDashboardProfitsCards() - فحص بطاقات الأرباح
5. runComprehensiveTest() - اختبار شامل

🚀 للبدء السريع:
runComprehensiveTest()

📊 لمراقبة الأحداث فقط:
monitorDashboardProfitsEvents()

🧪 لاختبار محدد:
testManualProfitsUpdate()
simulateSaleTransaction()
  `);
}

// تصدير الدوال للاستخدام العام
if (typeof window !== 'undefined') {
  window.dashboardProfitsTest = {
    monitor: monitorDashboardProfitsEvents,
    testManual: testManualProfitsUpdate,
    simulateSale: simulateSaleTransaction,
    checkCards: checkDashboardProfitsCards,
    runAll: runComprehensiveTest,
    help: showTestInstructions
  };
  
  console.log('🔧 تم تحميل أدوات اختبار تحديث أرباح Dashboard');
  console.log('📖 استخدم window.dashboardProfitsTest.help() لعرض التعليمات');
}

// عرض التعليمات تلقائياً
showTestInstructions();
