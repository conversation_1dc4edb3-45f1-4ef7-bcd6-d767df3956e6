.cashbox-page {
  padding: 1.5rem;
}

.cashbox-header {
  margin-bottom: 1.5rem;
}

.cashbox-header h1 {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.cashbox-header p {
  color: #666;
}

.cashbox-info {
  margin-bottom: 2rem;
}

.cashbox-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.cashbox-card {
  text-align: center;
}

.cashbox-amount {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 0.5rem;
}

.profit-card {
  background-color: #f0fff4;
  border-color: #c6f6d5;
}

.profit-card .cashbox-amount {
  color: #2d7d32;
  font-weight: 700;
}



.cashbox-profit-details {
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px dashed #c6f6d5;
}

.profit-note {
  font-size: 0.75rem;
  color: #38a169;
  text-align: right;
  line-height: 1.4;
}

.no-cashbox {
  background-color: #f9f9f9;
  border-radius: 0.5rem;
  padding: 2rem;
  text-align: center;
}

.no-cashbox p {
  margin-bottom: 1rem;
}

.filter-form {
  background-color: #f9f9f9;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1rem;
}

.filter-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.filter-group {
  flex: 1;
}

.filter-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.filter-group input,
.filter-group select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 0.25rem;
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.quick-filter-buttons {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.transactions-table-container {
  overflow-x: auto;
}

.transactions-table {
  width: 100%;
  border-collapse: collapse;
}

.transactions-table th,
.transactions-table td {
  padding: 0.75rem;
  text-align: right;
  border-bottom: 1px solid #eee;
}

.transactions-table th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.transactions-table tr:hover {
  background-color: #f9f9f9;
}

.transaction-type {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.text-green-600 {
  color: #10b981;
}

.text-red-600 {
  color: #ef4444;
}

.text-orange-600 {
  color: #ea580c;
}

.no-transactions {
  padding: 2rem;
  text-align: center;
  color: #666;
}

.modal-form {
  padding: 1rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 0.25rem;
}

.form-hint {
  font-size: 0.875rem;
  color: #666;
  margin-top: 0.25rem;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  margin-top: 1.5rem;
}

.transaction-type-buttons {
  display: flex;
  gap: 0.5rem;
}

.type-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 0.25rem;
  background-color: #f9f9f9;
  cursor: pointer;
  transition: all 0.2s;
}

.type-button.active {
  background-color: #e5e7eb;
  border-color: #d1d5db;
  font-weight: 500;
}

.type-button:hover:not(.active) {
  background-color: #f3f4f6;
}

.loading {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.error {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 1rem;
  border-radius: 0.25rem;
  margin-bottom: 1rem;
}

@media (max-width: 768px) {
  .cashbox-cards {
    grid-template-columns: 1fr;
  }

  .filter-row {
    flex-direction: column;
    gap: 0.5rem;
  }
}
