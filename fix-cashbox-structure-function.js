
/**
 * دالة إصلاح بنية جدول الخزينة
 * يمكن استدعاؤها من داخل التطبيق
 */
async function fixCashboxStructure() {
  try {
    console.log('🔧 بدء إصلاح بنية جدول الخزينة...');

    // الحصول على اتصال قاعدة البيانات
    const dbManager = require('./database-singleton').getInstance();
    const db = dbManager.getConnection();

    if (!db) {
      throw new Error('قاعدة البيانات غير متصلة');
    }

    // قائمة الأعمدة المطلوبة
    const requiredColumns = [
      { name: 'profit_total', type: 'REAL', default: '0' },
      { name: 'sales_total', type: 'REAL', default: '0' },
      { name: 'purchases_total', type: 'REAL', default: '0' },
      { name: 'returns_total', type: 'REAL', default: '0' },
      { name: 'transport_total', type: 'REAL', default: '0' }
    ];

    // الحصول على معلومات الجدول الحالي
    const tableInfo = db.prepare("PRAGMA table_info(cashbox)").all();
    const existingColumns = tableInfo.map(col => col.name);

    console.log('📋 الأعمدة الموجودة:', existingColumns);

    let columnsAdded = 0;

    // إضافة الأعمدة المفقودة
    for (const column of requiredColumns) {
      if (!existingColumns.includes(column.name)) {
        try {
          console.log(`➕ إضافة العمود: ${column.name}`);
          const alterQuery = `ALTER TABLE cashbox ADD COLUMN ${column.name} ${column.type} DEFAULT ${column.default}`;
          db.prepare(alterQuery).run();
          console.log(`✅ تم إضافة العمود: ${column.name}`);
          columnsAdded++;
        } catch (error) {
          console.error(`❌ خطأ في إضافة العمود ${column.name}:`, error.message);
        }
      } else {
        console.log(`✅ العمود ${column.name} موجود`);
      }
    }

    // تحديث القيم الافتراضية
    if (columnsAdded > 0) {
      console.log('🔄 تحديث القيم الافتراضية...');
      const updateQuery = `
        UPDATE cashbox
        SET
          profit_total = COALESCE(profit_total, 0),
          sales_total = COALESCE(sales_total, 0),
          purchases_total = COALESCE(purchases_total, 0),
          returns_total = COALESCE(returns_total, 0),
          transport_total = COALESCE(transport_total, 0),
          updated_at = CURRENT_TIMESTAMP
        WHERE id IS NOT NULL
      `;

      const updateResult = db.prepare(updateQuery).run();
      console.log(`✅ تم تحديث ${updateResult.changes} سجل`);
    }

    // عرض بنية الجدول النهائية
    const finalTableInfo = db.prepare("PRAGMA table_info(cashbox)").all();
    console.log('📋 بنية الجدول النهائية:');
    finalTableInfo.forEach(col => {
      console.log(`   - ${col.name}: ${col.type} ${col.notnull ? 'NOT NULL' : ''} ${col.dflt_value ? `DEFAULT ${col.dflt_value}` : ''}`);
    });

    console.log(`✅ تم إكمال إصلاح بنية الجدول بنجاح (${columnsAdded} عمود جديد)`);
    return { success: true, columnsAdded };

  } catch (error) {
    console.error('❌ خطأ في إصلاح بنية جدول الخزينة:', error);
    return { success: false, error: error.message };
  }
}

// تصدير الدالة
module.exports = { fixCashboxStructure };
