/**
 * ملف ترقية لإزالة إعدادات Google Drive من قاعدة البيانات
 * 
 * هذا الملف يقوم بإزالة إعدادات Google Drive من جدول الإعدادات
 * ويتم استدعاؤه عند تهيئة قاعدة البيانات
 */

const removeGoogleDriveMigration = `
-- حذف إعدادات Google Drive من جدول الإعدادات
DELETE FROM settings WHERE key = 'googleDriveBackupEnabled';
DELETE FROM settings WHERE key = 'googleDriveAutoBackup';
DELETE FROM settings WHERE key = 'googleDriveBackupInterval';
DELETE FROM settings WHERE key = 'googleDriveKeepBackupsCount';

-- تحديث إعدادات التحديث التلقائي
INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES ('autoUpdateEnabled', 'false', CURRENT_TIMESTAMP);
INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES ('lastUpdateCheck', 'null', CURRENT_TIMESTAMP);
INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES ('isInternetConnection', 'false', CURRENT_TIMESTAMP);
`;

module.exports = {
  removeGoogleDriveMigration
};
