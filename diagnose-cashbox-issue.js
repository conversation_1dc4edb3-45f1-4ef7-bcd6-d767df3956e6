/**
 * تشخيص شامل لمشكلة توقف تحديث المشتريات في الخزينة
 * يفحص جميع الجوانب المحتملة للمشكلة
 */

const { logError, logSystem } = require('./error-handler');
const DatabaseManager = require('./database-singleton');

/**
 * تشخيص شامل لمشكلة الخزينة
 */
async function diagnoseCashboxIssue() {
  console.log('🔍 بدء التشخيص الشامل لمشكلة الخزينة...');
  
  const diagnosis = {
    timestamp: new Date().toISOString(),
    issues: [],
    warnings: [],
    recommendations: [],
    systemStatus: {}
  };

  try {
    // 1. فحص الاتصال بقاعدة البيانات
    console.log('\n1️⃣ فحص الاتصال بقاعدة البيانات...');
    const db = DatabaseManager.getDatabase();
    if (!db) {
      diagnosis.issues.push('فشل في الاتصال بقاعدة البيانات');
      return diagnosis;
    }
    console.log('✅ الاتصال بقاعدة البيانات سليم');
    diagnosis.systemStatus.database = 'متصل';

    // 2. فحص هيكل جدول الخزينة
    console.log('\n2️⃣ فحص هيكل جدول الخزينة...');
    try {
      const tableInfo = db.prepare("PRAGMA table_info(cashbox)").all();
      const existingColumns = tableInfo.map(col => col.name);
      
      const requiredColumns = [
        'id', 'initial_balance', 'current_balance', 'profit_total',
        'sales_total', 'purchases_total', 'returns_total', 'transport_total'
      ];
      
      const missingColumns = requiredColumns.filter(col => !existingColumns.includes(col));
      
      if (missingColumns.length > 0) {
        diagnosis.issues.push(`أعمدة مفقودة في جدول الخزينة: ${missingColumns.join(', ')}`);
        diagnosis.recommendations.push('تشغيل سكريبت إصلاح هيكل الجدول: node fix-cashbox-transport-column.js');
      } else {
        console.log('✅ جميع الأعمدة المطلوبة موجودة');
      }
      
      diagnosis.systemStatus.tableStructure = {
        existingColumns,
        missingColumns,
        isComplete: missingColumns.length === 0
      };
      
    } catch (tableError) {
      diagnosis.issues.push(`خطأ في فحص هيكل الجدول: ${tableError.message}`);
    }

    // 3. فحص بيانات الخزينة
    console.log('\n3️⃣ فحص بيانات الخزينة...');
    try {
      const cashbox = db.prepare('SELECT * FROM cashbox LIMIT 1').get();
      
      if (!cashbox) {
        diagnosis.issues.push('لا توجد بيانات خزينة في قاعدة البيانات');
        diagnosis.recommendations.push('إنشاء خزينة جديدة من واجهة النظام');
      } else {
        console.log('✅ تم العثور على بيانات الخزينة');
        console.log(`📊 معرف الخزينة: ${cashbox.id}`);
        console.log(`💰 الرصيد الحالي: ${cashbox.current_balance}`);
        console.log(`🛒 إجمالي المشتريات: ${cashbox.purchases_total || 0}`);
        console.log(`🚚 إجمالي مصاريف النقل: ${cashbox.transport_total || 0}`);
        
        // فحص القيم السالبة
        if (cashbox.current_balance < 0) {
          diagnosis.warnings.push('الرصيد الحالي سالب');
        }
        
        if ((cashbox.purchases_total || 0) < 0) {
          diagnosis.warnings.push('إجمالي المشتريات سالب');
        }
        
        diagnosis.systemStatus.cashboxData = {
          exists: true,
          id: cashbox.id,
          current_balance: cashbox.current_balance,
          purchases_total: cashbox.purchases_total || 0,
          transport_total: cashbox.transport_total || 0
        };
      }
    } catch (dataError) {
      diagnosis.issues.push(`خطأ في قراءة بيانات الخزينة: ${dataError.message}`);
    }

    // 4. فحص مدير المعاملات
    console.log('\n4️⃣ فحص مدير المعاملات...');
    try {
      const transactionManager = require('./unified-transaction-manager');
      
      if (typeof transactionManager.updateCashboxAfterTransaction !== 'function') {
        diagnosis.issues.push('دالة updateCashboxAfterTransaction غير موجودة في مدير المعاملات');
      } else {
        console.log('✅ دالة updateCashboxAfterTransaction موجودة');
      }
      
      if (typeof transactionManager.createTransaction !== 'function') {
        diagnosis.warnings.push('دالة createTransaction غير موجودة في مدير المعاملات');
      } else {
        console.log('✅ دالة createTransaction موجودة');
      }
      
      diagnosis.systemStatus.transactionManager = 'متوفر';
      
    } catch (managerError) {
      diagnosis.issues.push(`خطأ في استيراد مدير المعاملات: ${managerError.message}`);
      diagnosis.systemStatus.transactionManager = 'غير متوفر';
    }

    // 5. فحص معاملات الخزينة الأخيرة
    console.log('\n5️⃣ فحص معاملات الخزينة الأخيرة...');
    try {
      const recentTransactions = db.prepare(`
        SELECT * FROM cashbox_transactions 
        WHERE source = 'purchase' 
        ORDER BY created_at DESC 
        LIMIT 5
      `).all();
      
      console.log(`📊 عدد معاملات الشراء الأخيرة: ${recentTransactions.length}`);
      
      if (recentTransactions.length === 0) {
        diagnosis.warnings.push('لا توجد معاملات شراء مسجلة في الخزينة');
      } else {
        console.log('✅ توجد معاملات شراء مسجلة');
        recentTransactions.forEach((transaction, index) => {
          console.log(`   ${index + 1}. المبلغ: ${transaction.amount}, التاريخ: ${transaction.created_at}`);
        });
      }
      
      diagnosis.systemStatus.recentPurchaseTransactions = recentTransactions.length;
      
    } catch (transactionError) {
      diagnosis.warnings.push(`خطأ في فحص معاملات الخزينة: ${transactionError.message}`);
    }

    // 6. فحص المعاملات العامة الأخيرة
    console.log('\n6️⃣ فحص المعاملات العامة الأخيرة...');
    try {
      const recentGeneralTransactions = db.prepare(`
        SELECT * FROM transactions 
        WHERE transaction_type = 'purchase' 
        ORDER BY transaction_date DESC 
        LIMIT 5
      `).all();
      
      console.log(`📊 عدد معاملات الشراء العامة الأخيرة: ${recentGeneralTransactions.length}`);
      
      if (recentGeneralTransactions.length === 0) {
        diagnosis.warnings.push('لا توجد معاملات شراء عامة مسجلة');
      } else {
        console.log('✅ توجد معاملات شراء عامة مسجلة');
        recentGeneralTransactions.forEach((transaction, index) => {
          console.log(`   ${index + 1}. الصنف: ${transaction.item_id}, المبلغ: ${transaction.total_price}, التاريخ: ${transaction.transaction_date}`);
        });
      }
      
      diagnosis.systemStatus.recentGeneralTransactions = recentGeneralTransactions.length;
      
    } catch (generalTransactionError) {
      diagnosis.warnings.push(`خطأ في فحص المعاملات العامة: ${generalTransactionError.message}`);
    }

    // 7. اختبار تحديث الخزينة
    console.log('\n7️⃣ اختبار تحديث الخزينة...');
    try {
      const cashbox = db.prepare('SELECT * FROM cashbox LIMIT 1').get();
      
      if (cashbox) {
        // اختبار تحديث بسيط
        const testQuery = `
          UPDATE cashbox 
          SET updated_at = CURRENT_TIMESTAMP 
          WHERE id = ?
        `;
        
        const testResult = db.prepare(testQuery).run(cashbox.id);
        
        if (testResult.changes > 0) {
          console.log('✅ يمكن تحديث الخزينة بنجاح');
          diagnosis.systemStatus.canUpdateCashbox = true;
        } else {
          diagnosis.issues.push('فشل في تحديث الخزينة');
          diagnosis.systemStatus.canUpdateCashbox = false;
        }
      }
      
    } catch (updateError) {
      diagnosis.issues.push(`خطأ في اختبار تحديث الخزينة: ${updateError.message}`);
      diagnosis.systemStatus.canUpdateCashbox = false;
    }

    // 8. فحص نظام الأحداث
    console.log('\n8️⃣ فحص نظام الأحداث...');
    try {
      const eventSystem = require('./event-system');
      
      if (typeof eventSystem.notifyCashboxUpdated === 'function') {
        console.log('✅ نظام الأحداث متوفر');
        diagnosis.systemStatus.eventSystem = 'متوفر';
      } else {
        diagnosis.warnings.push('دالة notifyCashboxUpdated غير موجودة في نظام الأحداث');
        diagnosis.systemStatus.eventSystem = 'ناقص';
      }
      
    } catch (eventError) {
      diagnosis.warnings.push(`خطأ في استيراد نظام الأحداث: ${eventError.message}`);
      diagnosis.systemStatus.eventSystem = 'غير متوفر';
    }

    // تحليل النتائج
    console.log('\n📋 تحليل النتائج...');
    
    if (diagnosis.issues.length === 0) {
      console.log('✅ لم يتم العثور على مشاكل حرجة');
      
      if (diagnosis.warnings.length > 0) {
        console.log('⚠️ تم العثور على تحذيرات:');
        diagnosis.warnings.forEach((warning, index) => {
          console.log(`   ${index + 1}. ${warning}`);
        });
      }
      
      diagnosis.recommendations.push('تشغيل اختبار شامل للنظام: node test-cashbox-system.js');
      diagnosis.recommendations.push('مراقبة سجلات النظام أثناء إجراء عملية شراء');
      
    } else {
      console.log('❌ تم العثور على مشاكل حرجة:');
      diagnosis.issues.forEach((issue, index) => {
        console.log(`   ${index + 1}. ${issue}`);
      });
    }
    
    if (diagnosis.recommendations.length > 0) {
      console.log('\n💡 التوصيات:');
      diagnosis.recommendations.forEach((recommendation, index) => {
        console.log(`   ${index + 1}. ${recommendation}`);
      });
    }

    return diagnosis;

  } catch (error) {
    console.error('❌ خطأ في التشخيص:', error);
    diagnosis.issues.push(`خطأ عام في التشخيص: ${error.message}`);
    return diagnosis;
  }
}

/**
 * إنشاء تقرير تشخيص مفصل
 */
function generateDiagnosisReport(diagnosis) {
  console.log('\n📄 تقرير التشخيص المفصل:');
  console.log('='.repeat(50));
  console.log(`التاريخ والوقت: ${diagnosis.timestamp}`);
  console.log(`عدد المشاكل الحرجة: ${diagnosis.issues.length}`);
  console.log(`عدد التحذيرات: ${diagnosis.warnings.length}`);
  console.log(`عدد التوصيات: ${diagnosis.recommendations.length}`);
  
  console.log('\n🔧 حالة النظام:');
  Object.entries(diagnosis.systemStatus).forEach(([key, value]) => {
    console.log(`   ${key}: ${JSON.stringify(value)}`);
  });
  
  if (diagnosis.issues.length > 0) {
    console.log('\n❌ المشاكل الحرجة:');
    diagnosis.issues.forEach((issue, index) => {
      console.log(`   ${index + 1}. ${issue}`);
    });
  }
  
  if (diagnosis.warnings.length > 0) {
    console.log('\n⚠️ التحذيرات:');
    diagnosis.warnings.forEach((warning, index) => {
      console.log(`   ${index + 1}. ${warning}`);
    });
  }
  
  if (diagnosis.recommendations.length > 0) {
    console.log('\n💡 التوصيات:');
    diagnosis.recommendations.forEach((recommendation, index) => {
      console.log(`   ${index + 1}. ${recommendation}`);
    });
  }
  
  console.log('='.repeat(50));
}

module.exports = {
  diagnoseCashboxIssue,
  generateDiagnosisReport
};

// تشغيل التشخيص إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  console.log('🚀 بدء التشخيص الشامل لمشكلة الخزينة...');
  
  diagnoseCashboxIssue().then(diagnosis => {
    generateDiagnosisReport(diagnosis);
    
    // حفظ التقرير في ملف
    const fs = require('fs');
    const reportPath = `diagnosis-report-${Date.now()}.json`;
    fs.writeFileSync(reportPath, JSON.stringify(diagnosis, null, 2));
    console.log(`\n💾 تم حفظ تقرير التشخيص في: ${reportPath}`);
    
    console.log('\n🏁 انتهى التشخيص');
  }).catch(error => {
    console.error('❌ خطأ في تشغيل التشخيص:', error);
  });
}
