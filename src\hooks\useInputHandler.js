import { useState, useCallback, useRef } from 'react';

/**
 * هوك مخصص لمعالجة الإدخال بشكل أكثر كفاءة
 * يستخدم تقنية debounce لتحسين أداء حقول الإدخال
 *
 * @param {Object} initialState - الحالة الأولية للنموذج
 * @param {Number} debounceTime - وقت التأخير بالمللي ثانية (الافتراضي: 100)
 * @returns {Object} - كائن يحتوي على حالة النموذج ووظائف التحديث
 */
const useInputHandler = (initialState = {}, debounceTime = 100) => {
  // حالة النموذج
  const [formData, setFormData] = useState(initialState);

  // مرجع للمؤقت
  const timerRef = useRef(null);

  // مرجع للقيم المؤقتة
  const tempValuesRef = useRef({});

  // وظيفة لتحديث حالة النموذج مع تأخير
  const handleInputChange = useCallback((e) => {
    const { name, value, type, checked } = e.target;

    // تخزين القيمة في المرجع المؤقت فورًا لتجنب تأخر واجهة المستخدم
    if (type === 'checkbox') {
      tempValuesRef.current[name] = checked;
    } else if (name === 'invoice_number') {
      // معالجة خاصة لرقم الفاتورة - نحتفظ بالقيمة كما هي
      tempValuesRef.current[name] = value;
    } else {
      tempValuesRef.current[name] = value;
    }

    // إلغاء المؤقت السابق إذا كان موجودًا
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    // إنشاء مؤقت جديد لتحديث الحالة
    timerRef.current = setTimeout(() => {
      setFormData(prevData => {
        const newData = { ...prevData };

        // معالجة أنواع الحقول المختلفة
        if (type === 'checkbox') {
          newData[name] = checked;
        } else if (name === 'invoice_number') {
          // معالجة خاصة لرقم الفاتورة - لا نقوم بتحويله إلى رقم
          // نحتفظ بالقيمة كما هي بدون أي معالجة
          newData[name] = value;
        } else if (type === 'number' || name.includes('price') || name.includes('amount') || name.includes('quantity')) {
          // تحويل القيم العددية
          try {
            // نسخ القيمة بدلاً من تعديلها مباشرة
            let processedValue = String(value).trim();
            const numValue = Number(processedValue);
            newData[name] = isNaN(numValue) ? 0 : numValue;
          } catch (error) {
            console.error(`خطأ في تحويل القيمة ${value} للحقل ${name}:`, error);
            newData[name] = 0;
          }
        } else {
          newData[name] = value;
        }

        return newData;
      });
    }, debounceTime);
  }, [debounceTime]);

  // وظيفة لتحديث حالة النموذج مباشرة (للاستخدام خارج أحداث الإدخال)
  const updateFormData = useCallback((updates) => {
    setFormData(prevData => ({ ...prevData, ...updates }));
  }, []);

  // وظيفة لإعادة تعيين النموذج
  const resetForm = useCallback((newState = initialState) => {
    // تنظيف المؤقت إذا كان موجودًا
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }

    // تنظيف القيم المؤقتة
    tempValuesRef.current = {};

    // تعيين حالة النموذج
    setFormData(newState);
  }, [initialState]);

  // وظيفة للحصول على القيمة الحالية (بما في ذلك القيم المؤقتة)
  const getCurrentValue = useCallback((name) => {
    // إذا كانت القيمة موجودة في المرجع المؤقت، استخدمها
    if (name in tempValuesRef.current) {
      return tempValuesRef.current[name];
    }
    // وإلا استخدم القيمة من حالة النموذج
    return formData[name];
  }, [formData]);

  return {
    formData,
    handleInputChange,
    updateFormData,
    resetForm,
    getCurrentValue
  };
};

export default useInputHandler;
