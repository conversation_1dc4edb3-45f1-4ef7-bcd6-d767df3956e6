# إصلاح مشكلة تكاليف النقل في عمليات الشراء

## المشكلة الأصلية

كانت هناك مشكلة في نظام حساب تكاليف النقل عند إجراء عمليات الشراء، حيث كانت قيمة النقل يتم خصمها من مكانين:

1. **الرصيد الحالي للخزينة** (صحيح)
2. **الأرباح** (خطأ)

## السلوك المطلوب

يجب أن تكون تكاليف النقل في عمليات الشراء:
- تخصم من الرصيد الحالي للخزينة فقط
- لا تؤثر على حساب الأرباح في عمليات الشراء
- تؤثر على حساب الأرباح فقط في عمليات البيع (عند خصمها من الربح)

## الإصلاحات المطبقة

### 1. تحديث unified-transaction-manager.js

#### أ. تحسين دالة recalculateTotalProfits:
```javascript
function recalculateTotalProfits() {
  // حساب إجمالي الأرباح من معاملات البيع فقط
  // الربح في معاملات البيع يتضمن بالفعل خصم تكاليف النقل
  const totalProfitStmt = db.prepare(`
    SELECT COALESCE(SUM(profit), 0) as total_profit
    FROM transactions
    WHERE transaction_type = 'sale'
  `);
  
  const result = totalProfitStmt.get();
  const totalProfit = Number(result ? result.total_profit : 0);
  
  console.log(`ملاحظة: تكاليف النقل في المشتريات لا تؤثر على الأرباح، فقط على الرصيد الحالي`);
  
  return totalProfit;
}
```

#### ب. تحسين معالجة تكاليف النقل في المشتريات:
```javascript
// إضافة معاملة منفصلة لمصاريف النقل إذا كانت موجودة
// في المشتريات: مصاريف النقل تخصم من الرصيد الحالي فقط (لأنها مصروف فعلي)
// ولا تؤثر على حساب الأرباح في عمليات الشراء
if (numericTransportCost > 0) {
  console.log(`مصاريف النقل في المشتريات تخصم من الرصيد الحالي فقط ولا تؤثر على الأرباح`);
  
  // تحديث إجمالي مصاريف النقل في الخزينة
  // مصاريف النقل في المشتريات تخصم من الرصيد الحالي فقط
  const updateTransportStmt = db.prepare(`
    UPDATE cashbox
    SET current_balance = current_balance - ?,
        transport_total = transport_total + ?,
        updated_at = ?
    WHERE id = ?
  `);
  
  // إضافة معاملة مصاريف النقل للخزينة
  addTransportTransactionStmt.run(
    'expense',
    numericTransportCost,
    'transport',
    `مصاريف نقل بقيمة ${numericTransportCost} (مشتريات)`,
    userId,
    now
  );
}
```

#### ج. تحسين تحديث الأرباح بعد المشتريات:
```javascript
// تحديث إجمالي الأرباح تلقائياً بعد معاملة الشراء
// ملاحظة: عمليات الشراء لا تؤثر على الأرباح مباشرة، لكن قد تؤثر على حساب متوسط التكلفة
// مما قد يؤثر على حساب الأرباح في المبيعات المستقبلية
console.log(`ملاحظة: تكاليف النقل في المشتريات تخصم من الرصيد الحالي فقط ولا تؤثر على الأرباح`);

// إعادة حساب إجمالي الأرباح باستخدام الدالة المحسنة
// هذا يحسب الأرباح من معاملات البيع فقط
const calculatedTotalProfit = recalculateTotalProfits();
```

### 2. تحديث src/utils/cashboxUtils.js

```javascript
} else if (type === 'expense' || type === 'purchase') {
  // في حالة المصروفات أو الشراء
  console.log(`ملاحظة: تكاليف النقل في المشتريات تخصم من الرصيد الحالي فقط ولا تؤثر على الأرباح`);

  // تحديث الرصيد الحالي
  updatedCashbox.current_balance -= numAmount;

  // تحديث إجمالي المشتريات
  updatedCashbox.purchases_total += numAmount;

  // ملاحظة: لا نخصم من الأرباح في عمليات الشراء
  // تكاليف النقل في المشتريات تؤثر فقط على الرصيد الحالي
}
```

### 3. تحديث utils/profitCalculator.js

```javascript
/**
 * إعادة حساب إجمالي الأرباح بناءً على المبيعات والمشتريات
 * ملاحظة: تكاليف النقل في المشتريات لا تؤثر على الأرباح، فقط على الرصيد الحالي
 */
function recalculateTotalProfit(salesTotal, purchasesTotal, excessBalance = 0) {
  console.log(`ملاحظة: تكاليف النقل في المشتريات لا تؤثر على الأرباح، فقط على الرصيد الحالي`);
  
  // حساب الربح من المبيعات (الفرق بين إجمالي المبيعات وإجمالي المشتريات)
  // ملاحظة: هذا الحساب تقريبي، الحساب الدقيق يتم من معاملات البيع الفردية
  const profitFromSales = Math.max(0, numSalesTotal - numPurchasesTotal);
  
  // إجمالي الربح = الربح من المبيعات + المبلغ الزائد من الرصيد
  const totalProfit = profitFromSales + numExcessBalance;
  
  return totalProfit;
}
```

## آلية العمل الجديدة

### في عمليات الشراء:
1. **تكاليف النقل تخصم من الرصيد الحالي فقط**
2. **لا تؤثر على حساب الأرباح**
3. **تسجل كمعاملة منفصلة في سجل الخزينة**

### في عمليات البيع:
1. **تكاليف النقل تخصم من الربح المحسوب**
2. **تؤثر على إجمالي الأرباح**
3. **تحسب بناءً على متوسط تكاليف النقل للصنف**

### حساب الأرباح الإجمالية:
1. **يتم من معاملات البيع فقط**
2. **كل معاملة بيع تتضمن خصم تكاليف النقل المناسبة**
3. **عمليات الشراء لا تؤثر على الأرباح مباشرة**

## النتيجة

بعد هذه الإصلاحات:
- ✅ تكاليف النقل في المشتريات تخصم من الرصيد الحالي فقط
- ✅ لا تؤثر على حساب الأرباح في عمليات الشراء
- ✅ تؤثر على حساب الأرباح فقط في عمليات البيع
- ✅ حساب الأرباح الإجمالية دقيق ومن معاملات البيع فقط
- ✅ تسجيل واضح ومفصل لجميع المعاملات

## ملاحظات مهمة

1. **الأرباح تحسب بدقة من معاملات البيع الفردية**
2. **تكاليف النقل موزعة بعدالة على الأصناف المشتراة**
3. **الرصيد الحالي يعكس التدفق النقدي الفعلي**
4. **التقارير المالية تعكس الوضع الصحيح للأرباح والخسائر**
