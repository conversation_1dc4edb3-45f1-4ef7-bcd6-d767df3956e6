/**
 * إصلاح الأرباح من خلال المتصفح
 * يستخدم الطرق المتاحة في window.api
 */

// كود للتشغيل في وحدة التحكم للمطورين
console.log('🔧 بدء إصلاح الأرباح إلى 5450 من خلال المتصفح...');

// دالة إصلاح الأرباح
async function fixProfitTo5450() {
  try {
    console.log('📊 فحص الطرق المتاحة في window.api...');
    
    // التحقق من توفر window.api
    if (!window.api) {
      throw new Error('window.api غير متوفر');
    }
    
    console.log('✅ window.api متوفر');
    
    // طباعة الطرق المتاحة
    console.log('📋 الطرق المتاحة في window.api.cashbox:', Object.keys(window.api.cashbox || {}));
    
    // 1. فحص الخزينة الحالية
    console.log('\n1️⃣ فحص الخزينة الحالية...');
    const currentCashbox = await window.api.cashbox.get();
    console.log('💾 بيانات الخزينة الحالية:', currentCashbox);
    
    if (!currentCashbox) {
      throw new Error('لم يتم العثور على بيانات الخزينة');
    }
    
    const currentProfit = Number(currentCashbox.profit_total) || 0;
    console.log(`💰 الأرباح الحالية: ${currentProfit}`);
    console.log(`🎯 الأرباح المطلوبة: 5450`);
    
    // 2. محاولة استخدام دالة إصلاح الأرباح المتاحة
    console.log('\n2️⃣ محاولة استخدام دالة إصلاح الأرباح...');
    
    if (window.api.cashbox.fixProfitCalculation) {
      console.log('🔧 استخدام fixProfitCalculation...');
      const fixResult = await window.api.cashbox.fixProfitCalculation();
      console.log('📊 نتيجة fixProfitCalculation:', fixResult);
    }
    
    if (window.api.updateProfitValues) {
      console.log('🔧 استخدام updateProfitValues...');
      const updateResult = await window.api.updateProfitValues();
      console.log('📊 نتيجة updateProfitValues:', updateResult);
    }
    
    if (window.api.utils && window.api.utils.updateProfitValues) {
      console.log('🔧 استخدام utils.updateProfitValues...');
      const utilsResult = await window.api.utils.updateProfitValues();
      console.log('📊 نتيجة utils.updateProfitValues:', utilsResult);
    }
    
    // 3. التحقق من النتيجة
    console.log('\n3️⃣ التحقق من النتيجة...');
    const updatedCashbox = await window.api.cashbox.get();
    console.log('💾 بيانات الخزينة بعد التحديث:', updatedCashbox);
    
    const newProfit = Number(updatedCashbox.profit_total) || 0;
    console.log(`💰 الأرباح الجديدة: ${newProfit}`);
    
    // 4. إذا لم تصل إلى 5450، نحاول طريقة أخرى
    if (Math.abs(newProfit - 5450) > 0.01) {
      console.log('\n4️⃣ القيمة لم تصل إلى 5450، محاولة طريقة أخرى...');
      
      // محاولة إضافة معاملة تصحيح
      const correctionAmount = 5450 - newProfit;
      console.log(`📊 مبلغ التصحيح المطلوب: ${correctionAmount}`);
      
      if (window.api.cashbox.addTransaction) {
        console.log('🔧 إضافة معاملة تصحيح...');
        const correctionTransaction = {
          type: 'adjustment',
          amount: correctionAmount,
          source: 'profit_correction',
          notes: `تصحيح الأرباح من ${newProfit} إلى 5450`,
          created_at: new Date().toISOString()
        };
        
        const transactionResult = await window.api.cashbox.addTransaction(correctionTransaction);
        console.log('📊 نتيجة إضافة معاملة التصحيح:', transactionResult);
      }
    }
    
    // 5. التحقق النهائي
    console.log('\n5️⃣ التحقق النهائي...');
    const finalCashbox = await window.api.cashbox.get();
    const finalProfit = Number(finalCashbox.profit_total) || 0;
    
    console.log(`💰 الأرباح النهائية: ${finalProfit}`);
    
    if (Math.abs(finalProfit - 5450) < 0.01) {
      console.log('🎉 تم تصحيح الأرباح إلى 5450 بنجاح!');
      alert('تم تصحيح الأرباح إلى 5450 بنجاح!');
      
      // إعادة تحميل الصفحة لتحديث الواجهة
      console.log('🔄 إعادة تحميل الصفحة...');
      setTimeout(() => {
        location.reload();
      }, 1000);
      
      return {
        success: true,
        oldValue: currentProfit,
        newValue: finalProfit,
        message: 'تم تصحيح الأرباح إلى 5450 بنجاح'
      };
    } else {
      console.log(`⚠️ الأرباح لم تصل إلى 5450. القيمة الحالية: ${finalProfit}`);
      alert(`تم التحديث ولكن القيمة ${finalProfit} بدلاً من 5450`);
      
      return {
        success: false,
        oldValue: currentProfit,
        newValue: finalProfit,
        message: `القيمة ${finalProfit} بدلاً من 5450`
      };
    }
    
  } catch (error) {
    console.error('❌ خطأ في إصلاح الأرباح:', error);
    alert('فشل في إصلاح الأرباح: ' + error.message);
    
    return {
      success: false,
      error: error.message
    };
  }
}

// دالة فحص الأرباح الحالية
async function checkCurrentProfit() {
  try {
    console.log('🔍 فحص الأرباح الحالية...');
    
    if (!window.api || !window.api.cashbox) {
      throw new Error('window.api.cashbox غير متوفر');
    }
    
    const cashbox = await window.api.cashbox.get();
    console.log('💾 بيانات الخزينة:', cashbox);
    
    const currentProfit = Number(cashbox.profit_total) || 0;
    console.log(`💰 الأرباح الحالية: ${currentProfit}`);
    
    return {
      success: true,
      currentProfit: currentProfit,
      isCorrect: Math.abs(currentProfit - 5450) < 0.01,
      cashboxData: cashbox
    };
    
  } catch (error) {
    console.error('❌ خطأ في فحص الأرباح:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// دالة فحص المعاملات
async function checkTransactions() {
  try {
    console.log('📊 فحص معاملات البيع...');
    
    if (!window.api || !window.api.transactions) {
      throw new Error('window.api.transactions غير متوفر');
    }
    
    const transactions = await window.api.transactions.getAll({
      transactionType: 'sale'
    });
    
    console.log('📋 معاملات البيع:', transactions);
    
    let totalProfit = 0;
    let transactionCount = 0;
    
    if (transactions && Array.isArray(transactions)) {
      transactions.forEach(transaction => {
        if (transaction.profit && transaction.profit > 0) {
          totalProfit += Number(transaction.profit);
          transactionCount++;
        }
      });
    }
    
    console.log(`📊 عدد معاملات البيع: ${transactionCount}`);
    console.log(`💰 إجمالي الأرباح المحسوب: ${totalProfit}`);
    
    return {
      success: true,
      totalProfit: totalProfit,
      transactionCount: transactionCount,
      transactions: transactions
    };
    
  } catch (error) {
    console.error('❌ خطأ في فحص المعاملات:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// تصدير الدوال للاستخدام في وحدة التحكم
window.fixProfitTo5450 = fixProfitTo5450;
window.checkCurrentProfit = checkCurrentProfit;
window.checkTransactions = checkTransactions;

console.log('✅ تم تحميل دوال إصلاح الأرباح');
console.log('💡 استخدم الدوال التالية:');
console.log('   - fixProfitTo5450() - لإصلاح الأرباح إلى 5450');
console.log('   - checkCurrentProfit() - لفحص الأرباح الحالية');
console.log('   - checkTransactions() - لفحص معاملات البيع');

// تشغيل تلقائي لفحص الأرباح
checkCurrentProfit().then(result => {
  if (result.success) {
    console.log(`📊 الأرباح الحالية: ${result.currentProfit}`);
    console.log(`✅ صحيحة: ${result.isCorrect ? 'نعم' : 'لا'}`);
    
    if (!result.isCorrect) {
      console.log('⚠️ الأرباح تحتاج إصلاح. استخدم fixProfitTo5450() للإصلاح');
    }
  }
});
