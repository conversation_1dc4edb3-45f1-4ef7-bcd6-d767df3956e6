/**
 * اختبار إصلاح منطق مصاريف النقل في نظام الخزينة
 * 
 * هذا السكريبت يقوم باختبار الإصلاحات المطبقة على منطق مصاريف النقل
 * 
 * السلوك المطلوب:
 * 1. عند الشراء: خصم مصاريف النقل من الرصيد الحالي فقط (لا تؤثر على الأرباح)
 * 2. عند البيع: خصم مصاريف النقل من الأرباح الفردية للصنف المباع
 * 3. في الحسابات العامة: الأرباح = المبيعات - المشتريات (بدون خصم مصاريف النقل)
 */

console.log('🧪 بدء اختبار إصلاح منطق مصاريف النقل...');

// التحقق من وجود window.api
if (typeof window !== 'undefined' && window.api) {
  
  // اختبار شامل لمنطق مصاريف النقل
  async function testTransportCostLogicFix() {
    try {
      console.log('📊 بدء الاختبار الشامل لمنطق مصاريف النقل...');
      
      // 1. تعيين رصيد ابتدائي للاختبار
      console.log('\n1️⃣ تعيين رصيد ابتدائي للاختبار (5000)...');
      const initialBalanceResult = await window.api.cashbox.updateInitialBalance(5000);
      if (initialBalanceResult.success) {
        console.log('✅ تم تعيين الرصيد الابتدائي بنجاح');
        console.log('📊 الخزينة الأولية:', {
          initial_balance: initialBalanceResult.cashbox.initial_balance,
          current_balance: initialBalanceResult.cashbox.current_balance,
          profit_total: initialBalanceResult.cashbox.profit_total
        });
      } else {
        console.error('❌ فشل في تعيين الرصيد الابتدائي:', initialBalanceResult.error);
        return;
      }
      
      // 2. اختبار عملية شراء مع مصاريف نقل
      console.log('\n2️⃣ اختبار عملية شراء مع مصاريف نقل (مبلغ 1000 + نقل 100)...');
      const purchaseWithTransport = {
        type: 'purchase',
        amount: 1000,
        transport_cost: 100,
        source: 'test',
        notes: 'شراء مع مصاريف نقل',
        user_id: 1
      };
      
      const purchaseResult = await window.api.cashbox.addTransaction(purchaseWithTransport);
      if (purchaseResult.success) {
        console.log('✅ تم تسجيل عملية الشراء مع مصاريف نقل');
        console.log('📊 الخزينة بعد الشراء:', {
          current_balance: purchaseResult.cashbox.current_balance,
          purchases_total: purchaseResult.cashbox.purchases_total,
          transport_total: purchaseResult.cashbox.transport_total,
          profit_total: purchaseResult.cashbox.profit_total
        });
        
        // التحقق من صحة الحسابات
        const expectedCurrentBalance = 5000 - 1000 - 100; // 3900
        const expectedPurchasesTotal = 1000;
        const expectedTransportTotal = 100;
        const expectedProfitTotal = 0 - 1000; // المبيعات (0) - المشتريات (1000) = -1000 (بدون خصم مصاريف النقل)
        
        console.log('\n📋 التحقق من النتائج:');
        console.log(`الرصيد الحالي: متوقع ${expectedCurrentBalance}, فعلي ${purchaseResult.cashbox.current_balance} ${purchaseResult.cashbox.current_balance === expectedCurrentBalance ? '✅' : '❌'}`);
        console.log(`إجمالي المشتريات: متوقع ${expectedPurchasesTotal}, فعلي ${purchaseResult.cashbox.purchases_total} ${purchaseResult.cashbox.purchases_total === expectedPurchasesTotal ? '✅' : '❌'}`);
        console.log(`إجمالي مصاريف النقل: متوقع ${expectedTransportTotal}, فعلي ${purchaseResult.cashbox.transport_total} ${purchaseResult.cashbox.transport_total === expectedTransportTotal ? '✅' : '❌'}`);
        console.log(`إجمالي الأرباح: متوقع ${expectedProfitTotal}, فعلي ${purchaseResult.cashbox.profit_total} ${purchaseResult.cashbox.profit_total === expectedProfitTotal ? '✅' : '❌'}`);
        
        // الاختبار الأهم: التأكد من أن مصاريف النقل لا تؤثر على الأرباح في الشراء
        if (purchaseResult.cashbox.profit_total === expectedProfitTotal) {
          console.log('✅ مصاريف النقل لا تؤثر على الأرباح في الشراء (صحيح)');
        } else {
          console.error('❌ مصاريف النقل تؤثر على الأرباح في الشراء (خطأ!)');
        }
      } else {
        console.error('❌ فشل في تسجيل عملية الشراء مع مصاريف نقل:', purchaseResult.error);
        return;
      }
      
      // 3. اختبار عملية بيع
      console.log('\n3️⃣ اختبار عملية بيع (مبلغ 1500)...');
      const saleTransaction = {
        type: 'sale',
        amount: 1500,
        source: 'test',
        notes: 'بيع لاختبار مصاريف النقل',
        user_id: 1
      };
      
      const saleResult = await window.api.cashbox.addTransaction(saleTransaction);
      if (saleResult.success) {
        console.log('✅ تم تسجيل عملية البيع');
        console.log('📊 الخزينة بعد البيع:', {
          current_balance: saleResult.cashbox.current_balance,
          sales_total: saleResult.cashbox.sales_total,
          purchases_total: saleResult.cashbox.purchases_total,
          transport_total: saleResult.cashbox.transport_total,
          profit_total: saleResult.cashbox.profit_total
        });
        
        // التحقق من صحة الحسابات
        const expectedCurrentBalance = 3900 + 1500; // 5400
        const expectedSalesTotal = 1500;
        const expectedPurchasesTotal = 1000;
        const expectedTransportTotal = 100;
        const expectedProfitTotal = 1500 - 1000; // المبيعات (1500) - المشتريات (1000) = 500 (بدون خصم مصاريف النقل في الحسابات العامة)
        
        console.log('\n📋 التحقق من النتائج بعد البيع:');
        console.log(`الرصيد الحالي: متوقع ${expectedCurrentBalance}, فعلي ${saleResult.cashbox.current_balance} ${saleResult.cashbox.current_balance === expectedCurrentBalance ? '✅' : '❌'}`);
        console.log(`إجمالي المبيعات: متوقع ${expectedSalesTotal}, فعلي ${saleResult.cashbox.sales_total} ${saleResult.cashbox.sales_total === expectedSalesTotal ? '✅' : '❌'}`);
        console.log(`إجمالي المشتريات: متوقع ${expectedPurchasesTotal}, فعلي ${saleResult.cashbox.purchases_total} ${saleResult.cashbox.purchases_total === expectedPurchasesTotal ? '✅' : '❌'}`);
        console.log(`إجمالي مصاريف النقل: متوقع ${expectedTransportTotal}, فعلي ${saleResult.cashbox.transport_total} ${saleResult.cashbox.transport_total === expectedTransportTotal ? '✅' : '❌'}`);
        console.log(`إجمالي الأرباح: متوقع ${expectedProfitTotal}, فعلي ${saleResult.cashbox.profit_total} ${saleResult.cashbox.profit_total === expectedProfitTotal ? '✅' : '❌'}`);
        
        // الاختبار الأهم: التأكد من أن الأرباح تحسب بدون خصم مصاريف النقل في الحسابات العامة
        if (saleResult.cashbox.profit_total === expectedProfitTotal) {
          console.log('✅ مصاريف النقل لا تخصم في الحسابات العامة للأرباح (صحيح)');
        } else {
          console.error('❌ مصاريف النقل تخصم في الحسابات العامة للأرباح (خطأ!)');
        }
      } else {
        console.error('❌ فشل في تسجيل عملية البيع:', saleResult.error);
        return;
      }
      
      // 4. اختبار عملية شراء أخرى بدون مصاريف نقل
      console.log('\n4️⃣ اختبار عملية شراء أخرى بدون مصاريف نقل (مبلغ 300)...');
      const purchaseWithoutTransport = {
        type: 'purchase',
        amount: 300,
        source: 'test',
        notes: 'شراء بدون مصاريف نقل',
        user_id: 1
      };
      
      const purchaseResult2 = await window.api.cashbox.addTransaction(purchaseWithoutTransport);
      if (purchaseResult2.success) {
        console.log('✅ تم تسجيل عملية الشراء بدون مصاريف نقل');
        console.log('📊 الخزينة بعد الشراء الثاني:', {
          current_balance: purchaseResult2.cashbox.current_balance,
          purchases_total: purchaseResult2.cashbox.purchases_total,
          transport_total: purchaseResult2.cashbox.transport_total,
          profit_total: purchaseResult2.cashbox.profit_total
        });
        
        // التحقق من صحة الحسابات
        const expectedCurrentBalance = 5400 - 300; // 5100
        const expectedPurchasesTotal = 1000 + 300; // 1300
        const expectedTransportTotal = 100; // لا تتغير
        const expectedProfitTotal = 1500 - 1300; // المبيعات (1500) - المشتريات (1300) = 200
        
        console.log('\n📋 التحقق من النتائج بعد الشراء الثاني:');
        console.log(`الرصيد الحالي: متوقع ${expectedCurrentBalance}, فعلي ${purchaseResult2.cashbox.current_balance} ${purchaseResult2.cashbox.current_balance === expectedCurrentBalance ? '✅' : '❌'}`);
        console.log(`إجمالي المشتريات: متوقع ${expectedPurchasesTotal}, فعلي ${purchaseResult2.cashbox.purchases_total} ${purchaseResult2.cashbox.purchases_total === expectedPurchasesTotal ? '✅' : '❌'}`);
        console.log(`إجمالي مصاريف النقل: متوقع ${expectedTransportTotal}, فعلي ${purchaseResult2.cashbox.transport_total} ${purchaseResult2.cashbox.transport_total === expectedTransportTotal ? '✅' : '❌'}`);
        console.log(`إجمالي الأرباح: متوقع ${expectedProfitTotal}, فعلي ${purchaseResult2.cashbox.profit_total} ${purchaseResult2.cashbox.profit_total === expectedProfitTotal ? '✅' : '❌'}`);
      } else {
        console.error('❌ فشل في تسجيل عملية الشراء بدون مصاريف نقل:', purchaseResult2.error);
        return;
      }
      
      // 5. التحقق من الحالة النهائية
      console.log('\n5️⃣ التحقق من الحالة النهائية للخزينة...');
      const finalCashbox = await window.api.cashbox.getCashbox();
      console.log('📊 الحالة النهائية للخزينة:', finalCashbox);
      
      // التحقق من صحة جميع الحسابات النهائية
      const finalExpectedValues = {
        initial_balance: 5000,
        current_balance: 5100, // 5000 - 1000 - 100 + 1500 - 300
        sales_total: 1500,
        purchases_total: 1300, // 1000 + 300
        transport_total: 100,
        profit_total: 200 // 1500 - 1300 (بدون خصم مصاريف النقل)
      };
      
      console.log('\n📋 مقارنة النتائج النهائية:');
      let allTestsPassed = true;
      
      for (const [key, expectedValue] of Object.entries(finalExpectedValues)) {
        const actualValue = finalCashbox[key];
        const isCorrect = actualValue === expectedValue;
        console.log(`${key}: متوقع ${expectedValue}, فعلي ${actualValue} ${isCorrect ? '✅' : '❌'}`);
        if (!isCorrect) allTestsPassed = false;
      }
      
      // النتيجة النهائية
      if (allTestsPassed) {
        console.log('\n🎉 جميع الاختبارات نجحت! تم إصلاح منطق مصاريف النقل بنجاح');
        console.log('✅ مصاريف النقل تخصم من الرصيد الحالي عند الشراء');
        console.log('✅ مصاريف النقل لا تؤثر على إجمالي الأرباح في الحسابات العامة');
        console.log('✅ الأرباح = المبيعات - المشتريات (بدون خصم مصاريف النقل)');
      } else {
        console.log('\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الإصلاحات');
      }
      
    } catch (error) {
      console.error('❌ خطأ في تشغيل اختبار منطق مصاريف النقل:', error);
    }
  }
  
  // تشغيل الاختبار
  testTransportCostLogicFix();
  
} else {
  console.error('❌ window.api غير متوفر');
  console.log('💡 يجب تشغيل هذا السكريبت من داخل التطبيق');
  console.log('');
  console.log('📋 لتشغيل الاختبار:');
  console.log('1. افتح التطبيق');
  console.log('2. افتح وحدة التحكم (F12)');
  console.log('3. انسخ والصق محتوى هذا الملف');
}
