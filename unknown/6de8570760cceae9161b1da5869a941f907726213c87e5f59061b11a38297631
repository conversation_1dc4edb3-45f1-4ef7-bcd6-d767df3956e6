<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تشخيص الأرباح</title>
    <style>
        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .button {
            background-color: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: background-color 0.3s;
        }
        .button:hover {
            background-color: #2980b9;
        }
        .button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }
        .results {
            margin-top: 20px;
            padding: 15px;
            background-color: #ecf0f1;
            border-radius: 5px;
            border-right: 4px solid #3498db;
        }
        .error {
            background-color: #ffebee;
            border-right-color: #e74c3c;
            color: #c62828;
        }
        .success {
            background-color: #e8f5e8;
            border-right-color: #27ae60;
            color: #2e7d32;
        }
        .data-section {
            margin: 15px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .data-section h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .data-item {
            margin: 5px 0;
            padding: 5px 0;
            border-bottom: 1px solid #dee2e6;
        }
        .data-item:last-child {
            border-bottom: none;
        }
        .loading {
            text-align: center;
            color: #7f8c8d;
            font-style: italic;
        }
        pre {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
            direction: ltr;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 اختبار تشخيص الأرباح</h1>
        
        <div style="text-align: center;">
            <button id="diagnoseBtn" class="button">تشخيص الأرباح</button>
            <button id="clearBtn" class="button">مسح النتائج</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        const diagnoseBtn = document.getElementById('diagnoseBtn');
        const clearBtn = document.getElementById('clearBtn');
        const resultsDiv = document.getElementById('results');

        diagnoseBtn.addEventListener('click', async () => {
            diagnoseBtn.disabled = true;
            diagnoseBtn.textContent = 'جاري التشخيص...';
            
            resultsDiv.innerHTML = '<div class="results loading">🔄 جاري تشخيص مشكلة الأرباح...</div>';

            try {
                // استدعاء معالج التشخيص
                const result = await window.electronAPI.invoke('diagnose-profits');
                
                if (result.success) {
                    displaySuccessResults(result.data);
                } else {
                    displayError(result.error);
                }
            } catch (error) {
                displayError(error.message);
            } finally {
                diagnoseBtn.disabled = false;
                diagnoseBtn.textContent = 'تشخيص الأرباح';
            }
        });

        clearBtn.addEventListener('click', () => {
            resultsDiv.innerHTML = '';
        });

        function displaySuccessResults(data) {
            const { cashbox, salesSummary, salesDetails } = data;
            
            let html = '<div class="results success">';
            html += '<h2>✅ تم تشخيص الأرباح بنجاح</h2>';
            
            // عرض بيانات الخزينة
            html += '<div class="data-section">';
            html += '<h3>💰 بيانات الخزينة:</h3>';
            if (cashbox) {
                html += `<div class="data-item">الرصيد الافتتاحي: ${cashbox.initial_balance || 0}</div>`;
                html += `<div class="data-item">الرصيد الحالي: ${cashbox.current_balance || 0}</div>`;
                html += `<div class="data-item">إجمالي الأرباح: ${cashbox.profit_total || 0}</div>`;
                html += `<div class="data-item">إجمالي المبيعات: ${cashbox.sales_total || 0}</div>`;
                html += `<div class="data-item">إجمالي المشتريات: ${cashbox.purchases_total || 0}</div>`;
            } else {
                html += '<div class="data-item">❌ لا توجد بيانات خزينة</div>';
            }
            html += '</div>';
            
            // عرض ملخص معاملات البيع
            html += '<div class="data-section">';
            html += '<h3>📈 ملخص معاملات البيع:</h3>';
            if (salesSummary) {
                html += `<div class="data-item">عدد معاملات البيع: ${salesSummary.count || 0}</div>`;
                html += `<div class="data-item">إجمالي الأرباح من المعاملات: ${salesSummary.total_profit || 0}</div>`;
                html += `<div class="data-item">إجمالي قيمة المبيعات: ${salesSummary.total_sales || 0}</div>`;
            } else {
                html += '<div class="data-item">❌ لا توجد بيانات معاملات بيع</div>';
            }
            html += '</div>';
            
            // عرض تفاصيل معاملات البيع
            html += '<div class="data-section">';
            html += '<h3>🔍 تفاصيل آخر معاملات البيع:</h3>';
            if (salesDetails && salesDetails.length > 0) {
                salesDetails.forEach((sale, index) => {
                    html += `<div class="data-item">`;
                    html += `<strong>${index + 1}. معاملة ${sale.id}:</strong><br>`;
                    html += `&nbsp;&nbsp;- الصنف: ${sale.item_id}<br>`;
                    html += `&nbsp;&nbsp;- الكمية: ${sale.quantity}<br>`;
                    html += `&nbsp;&nbsp;- سعر البيع: ${sale.selling_price}<br>`;
                    html += `&nbsp;&nbsp;- إجمالي السعر: ${sale.total_price}<br>`;
                    html += `&nbsp;&nbsp;- الربح: ${sale.profit || 0}<br>`;
                    html += `&nbsp;&nbsp;- التاريخ: ${sale.transaction_date}`;
                    html += `</div>`;
                });
            } else {
                html += '<div class="data-item">❌ لا توجد معاملات بيع</div>';
            }
            html += '</div>';
            
            // تحليل المشكلة
            html += '<div class="data-section">';
            html += '<h3>🔍 تحليل المشكلة:</h3>';
            
            if (salesSummary?.count === 0) {
                html += '<div class="data-item">❌ المشكلة: لا توجد معاملات بيع في النظام</div>';
                html += '<div class="data-item">💡 الحل: تأكد من وجود معاملات بيع مسجلة</div>';
            } else if (salesSummary?.total_profit === 0 || salesSummary?.total_profit === null) {
                html += '<div class="data-item">❌ المشكلة: معاملات البيع موجودة لكن الأرباح = 0</div>';
                html += '<div class="data-item">💡 الحل: يجب إعادة حساب الأرباح لمعاملات البيع</div>';
            } else {
                const difference = Math.abs((salesSummary?.total_profit || 0) - (cashbox?.profit_total || 0));
                if (difference > 0.01) {
                    html += '<div class="data-item">❌ المشكلة: عدم تطابق الأرباح المحسوبة مع المحفوظة</div>';
                    html += `<div class="data-item">&nbsp;&nbsp;- الأرباح من المعاملات: ${salesSummary?.total_profit || 0}</div>`;
                    html += `<div class="data-item">&nbsp;&nbsp;- الأرباح في الخزينة: ${cashbox?.profit_total || 0}</div>`;
                    html += `<div class="data-item">&nbsp;&nbsp;- الفرق: ${difference}</div>`;
                    html += '<div class="data-item">💡 الحل: تحديث إجمالي الأرباح في الخزينة</div>';
                } else {
                    html += '<div class="data-item">✅ النظام يبدو سليماً - الأرباح متطابقة</div>';
                }
            }
            html += '</div>';
            
            // عرض البيانات الخام
            html += '<div class="data-section">';
            html += '<h3>📋 البيانات الخام:</h3>';
            html += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            html += '</div>';
            
            html += '</div>';
            resultsDiv.innerHTML = html;
        }

        function displayError(error) {
            resultsDiv.innerHTML = `
                <div class="results error">
                    <h2>❌ خطأ في التشخيص</h2>
                    <p>${error}</p>
                </div>
            `;
        }

        // التحقق من توفر API
        if (!window.electronAPI) {
            resultsDiv.innerHTML = `
                <div class="results error">
                    <h2>❌ خطأ في النظام</h2>
                    <p>واجهة Electron API غير متوفرة. تأكد من تشغيل التطبيق في بيئة Electron.</p>
                </div>
            `;
            diagnoseBtn.disabled = true;
        }
    </script>
</body>
</html>
