/**
 * وحدة حساب الربح
 * تقوم بحساب الربح بناءً على سعر البيع وسعر الشراء والكمية
 */

/**
 * حساب الربح
 * @param {number} sellingPrice - سعر البيع
 * @param {number} costPrice - سعر التكلفة
 * @param {number} quantity - الكمية
 * @returns {number} - الربح
 */
function calculateProfit(sellingPrice, costPrice, quantity) {
  try {
    // التحويل إلى أرقام
    const numericSellingPrice = Number(sellingPrice);
    const numericCostPrice = Number(costPrice);
    const numericQuantity = Number(quantity);

    // التحقق من صحة البيانات
    if (isNaN(numericSellingPrice) || numericSellingPrice < 0) {
      console.warn(`سعر البيع غير صالح: ${sellingPrice}`);
      return 0;
    }

    if (isNaN(numericCostPrice) || numericCostPrice <= 0) {
      console.warn(`سعر التكلفة غير صالح: ${costPrice}`);
      return 0;
    }

    if (isNaN(numericQuantity) || numericQuantity <= 0) {
      console.warn(`الكمية غير صالحة: ${quantity}`);
      return 0;
    }

    // حساب الربح
    const profit = (numericSellingPrice - numericCostPrice) * numericQuantity;

    // تسجيل معلومات للتشخيص
    console.log(`[PROFIT-FIX] حساب الربح: (${numericSellingPrice} - ${numericCostPrice}) × ${numericQuantity} = ${profit}`);

    // حساب الربح الفعلي بدون تحويل الخسائر إلى أرباح
    // إذا كان سعر البيع أقل من سعر الشراء، فالنتيجة ستكون خسارة (قيمة سالبة)
    // الخسائر يجب أن تظهر كقيم سالبة وليس كصفر
    const finalProfit = profit;

    // تقريب الربح إلى رقمين عشريين
    return Math.round(finalProfit * 100) / 100;
  } catch (error) {
    console.error('خطأ في حساب الربح:', error);
    return 0;
  }
}

/**
 * حساب الربح مع أخذ مصاريف النقل في الاعتبار
 * @param {number} sellingPrice - سعر البيع
 * @param {number} costPrice - سعر التكلفة
 * @param {number} quantity - الكمية
 * @param {number} transportCostPerUnit - مصاريف النقل لكل وحدة
 * @returns {number} - الربح المحسوب مع خصم مصاريف النقل
 */
function calculateProfitWithTransport(sellingPrice, costPrice, quantity, transportCostPerUnit = 0) {
  try {
    // التحويل إلى أرقام
    const numericSellingPrice = Number(sellingPrice);
    const numericCostPrice = Number(costPrice);
    const numericQuantity = Number(quantity);
    const numericTransportCostPerUnit = Number(transportCostPerUnit) || 0;

    // التحقق من صحة البيانات
    if (isNaN(numericSellingPrice) || numericSellingPrice < 0) {
      console.warn(`سعر البيع غير صالح: ${sellingPrice}`);
      return 0;
    }

    if (isNaN(numericCostPrice) || numericCostPrice <= 0) {
      console.warn(`سعر التكلفة غير صالح: ${costPrice}`);
      return 0;
    }

    if (isNaN(numericQuantity) || numericQuantity <= 0) {
      console.warn(`الكمية غير صالحة: ${quantity}`);
      return 0;
    }

    // حساب الربح: (سعر البيع - سعر التكلفة - مصاريف النقل لكل وحدة) * الكمية
    const profit = (numericSellingPrice - numericCostPrice - numericTransportCostPerUnit) * numericQuantity;

    // تسجيل معلومات للتشخيص
    console.log(`[PROFIT-FIX-TRANSPORT] حساب الربح مع مصاريف النقل:`);
    console.log(`[PROFIT-FIX-TRANSPORT] - سعر البيع: ${numericSellingPrice}`);
    console.log(`[PROFIT-FIX-TRANSPORT] - سعر التكلفة: ${numericCostPrice}`);
    console.log(`[PROFIT-FIX-TRANSPORT] - مصاريف النقل لكل وحدة: ${numericTransportCostPerUnit}`);
    console.log(`[PROFIT-FIX-TRANSPORT] - الكمية: ${numericQuantity}`);
    console.log(`[PROFIT-FIX-TRANSPORT] - الربح: (${numericSellingPrice} - ${numericCostPrice} - ${numericTransportCostPerUnit}) × ${numericQuantity} = ${profit}`);

    // حساب الربح الفعلي مع مراعاة مصاريف النقل
    // إذا كانت التكلفة الإجمالية (سعر الشراء + مصاريف النقل) أكبر من سعر البيع، فالنتيجة خسارة
    // الخسائر يجب أن تظهر كقيم سالبة وليس كصفر
    const finalProfit = profit;

    // تقريب الربح إلى رقمين عشريين
    return Math.round(finalProfit * 100) / 100;
  } catch (error) {
    console.error('خطأ في حساب الربح مع مصاريف النقل:', error);
    return 0;
  }
}

/**
 * حساب نسبة الربح
 * @param {number} sellingPrice - سعر البيع
 * @param {number} costPrice - سعر التكلفة
 * @returns {number} - نسبة الربح
 */
function calculateProfitMargin(sellingPrice, costPrice) {
  try {
    // التحويل إلى أرقام
    const numericSellingPrice = Number(sellingPrice);
    const numericCostPrice = Number(costPrice);

    // التحقق من صحة البيانات
    if (isNaN(numericSellingPrice) || numericSellingPrice <= 0) {
      console.warn(`سعر البيع غير صالح: ${sellingPrice}`);
      return 0;
    }

    if (isNaN(numericCostPrice) || numericCostPrice <= 0) {
      console.warn(`سعر التكلفة غير صالح: ${costPrice}`);
      return 0;
    }

    // حساب نسبة الربح
    const profitMargin = ((numericSellingPrice - numericCostPrice) / numericSellingPrice) * 100;

    // تقريب نسبة الربح إلى رقمين عشريين
    return Math.round(profitMargin * 100) / 100;
  } catch (error) {
    console.error('خطأ في حساب نسبة الربح:', error);
    return 0;
  }
}

/**
 * حساب سعر البيع بناءً على سعر التكلفة ونسبة الربح المطلوبة
 * @param {number} costPrice - سعر التكلفة
 * @param {number} desiredMargin - نسبة الربح المطلوبة
 * @returns {number} - سعر البيع المقترح
 */
function calculateSellingPrice(costPrice, desiredMargin) {
  try {
    // التحويل إلى أرقام
    const numericCostPrice = Number(costPrice);
    const numericDesiredMargin = Number(desiredMargin);

    // التحقق من صحة البيانات
    if (isNaN(numericCostPrice) || numericCostPrice <= 0) {
      console.warn(`سعر التكلفة غير صالح: ${costPrice}`);
      return 0;
    }

    if (isNaN(numericDesiredMargin) || numericDesiredMargin < 0) {
      console.warn(`نسبة الربح المطلوبة غير صالحة: ${desiredMargin}`);
      return 0;
    }

    // حساب سعر البيع
    const sellingPrice = numericCostPrice / (1 - (numericDesiredMargin / 100));

    // تقريب سعر البيع إلى رقمين عشريين
    return Math.round(sellingPrice * 100) / 100;
  } catch (error) {
    console.error('خطأ في حساب سعر البيع:', error);
    return 0;
  }
}

/**
 * حساب توزيع المبلغ بين الرصيد الحالي والأرباح
 *
 * هذه الدالة تحسب كيفية توزيع المبلغ المضاف بين الرصيد الحالي والأرباح
 * بحيث لا يتجاوز الرصيد الحالي الرصيد الافتتاحي.
 *
 * المبدأ الأساسي هو:
 * 1. إذا كان الرصيد الحالي + المبلغ المضاف <= الرصيد الافتتاحي، يضاف كل المبلغ إلى الرصيد الحالي.
 * 2. إذا كان الرصيد الحالي + المبلغ المضاف > الرصيد الافتتاحي، يضاف فقط ما يكفي للوصول إلى الرصيد الافتتاحي،
 *    والباقي يذهب إلى الأرباح.
 *
 * ملاحظة: تم تعديل هذه الدالة لتنفيذ المتطلبات الجديدة:
 * - الرصيد الحالي لا يتجاوز الرصيد الافتتاحي أبداً
 * - أي مبلغ زائد عن الرصيد الافتتاحي يتم إضافته إلى الأرباح
 * - الأرباح تعكس فقط الفرق بين سعر البيع وسعر الشراء للمنتجات، بالإضافة إلى المبالغ الزائدة
 *
 * @param {number} amount - المبلغ المراد إضافته
 * @param {number} currentBalance - الرصيد الحالي
 * @param {number} initialBalance - الرصيد الافتتاحي
 * @returns {Object} - توزيع المبلغ
 */
function calculateBalanceDistribution(amount, currentBalance, initialBalance) {
  // التأكد من أن جميع المدخلات أرقام صالحة
  const numAmount = Number(amount) || 0;
  const numCurrentBalance = Number(currentBalance) || 0;
  const numInitialBalance = Number(initialBalance) || 0;

  //  الرصيد الحالي يساوي دائما الرصيد الافتتاحي
  const amountToAddToBalance = numInitialBalance - numCurrentBalance;
  const excessAmount = numAmount - amountToAddToBalance;


  return {
    amountToAddToBalance,  // المبلغ الذي سيضاف إلى الرصيد الحالي
    excessAmount,          // المبلغ الزائد الذي سيذهب إلى الأرباح
    hasExcess: excessAmount > 0  // مؤشر يدل على وجود مبلغ زائد
  };
}

/**
 * تحديث الخزينة بعد المعاملة
 * @param {Object} cashbox - الخزينة الحالية
 * @param {Object} transaction - المعاملة
 * @returns {Object} - الخزينة المحدثة
 */
function updateCashboxAfterTransaction(cashbox, transaction) {
  if (!cashbox || !transaction) {
    console.log('[PROFIT-FIX] خطأ: الخزينة أو المعاملة غير موجودة');
    return cashbox;
  }

  // نسخة جديدة من الخزينة
  const updatedCashbox = { ...cashbox };

  const { type, amount, profit = 0 } = transaction;
  const numAmount = Number(amount) || 0;
  const numProfit = Number(profit) || 0;

  console.log(`[PROFIT-FIX] تحديث الخزينة - النوع: ${type}, المبلغ: ${numAmount}, الربح: ${numProfit}`);
  console.log('[PROFIT-FIX] قيم الخزينة قبل التحديث:', {
    current_balance: updatedCashbox.current_balance,
    profit_total: updatedCashbox.profit_total,
    sales_total: updatedCashbox.sales_total,
    purchases_total: updatedCashbox.purchases_total,
    returns_total: updatedCashbox.returns_total
  });

  if (type === 'income' || type === 'sale') {
    // في حالة الدخل أو البيع
    console.log(`[PROFIT-FIX] معاملة دخل/بيع - المبلغ: ${numAmount}, الربح: ${numProfit}`);

    // الرصيد الحالي يساوي دائما الرصيد الافتتاحي
    updatedCashbox.current_balance = updatedCashbox.initial_balance;


    // تحديث إجمالي المبيعات
    updatedCashbox.sales_total += numAmount;
    // الأرباح = المبيعات - المشتريات - النقل
    updatedCashbox.profit_total = (updatedCashbox.sales_total || 0) - (updatedCashbox.purchases_total || 0) - (updatedCashbox.transport_total || 0);


  } else if (type === 'expense' || type === 'purchase') {
    // في حالة المصروفات أو الشراء
    console.log(`[PROFIT-FIX] معاملة مصروفات/شراء - المبلغ: ${numAmount}`);

    // الرصيد الحالي يساوي دائما الرصيد الافتتاحي
    updatedCashbox.current_balance = updatedCashbox.initial_balance;


    // تحديث إجمالي المشتريات
    updatedCashbox.purchases_total += numAmount;
    // الأرباح = المبيعات - المشتريات - النقل
    updatedCashbox.profit_total = (updatedCashbox.sales_total || 0) - (updatedCashbox.purchases_total || 0) - (updatedCashbox.transport_total || 0);

  } else if (type === 'return') {
    // في حالة الإرجاع
    console.log(`[PROFIT-FIX] معاملة إرجاع - المبلغ: ${numAmount}`);

    // الرصيد الحالي يساوي دائما الرصيد الافتتاحي
    updatedCashbox.current_balance = updatedCashbox.initial_balance;

    // تقليل إجمالي المبيعات بالمبلغ المرجع
    updatedCashbox.sales_total -= numAmount;

    // زيادة إجمالي المرتجعات
    updatedCashbox.returns_total = (updatedCashbox.returns_total || 0) + numAmount;

    // الأرباح = المبيعات - المشتريات - النقل
    updatedCashbox.profit_total = (updatedCashbox.sales_total || 0) - (updatedCashbox.purchases_total || 0) - (updatedCashbox.transport_total || 0);
  } else if (type === 'transport') { // Handle transport cost
    console.log(`[PROFIT-FIX] معاملة نقل - المبلغ: ${numAmount}`);

    // الرصيد الحالي يساوي دائما الرصيد الافتتاحي
    updatedCashbox.current_balance = updatedCashbox.initial_balance;

    updatedCashbox.transport_total = (updatedCashbox.transport_total || 0) + numAmount;
    // الأرباح = المبيعات - المشتريات - النقل
    updatedCashbox.profit_total = (updatedCashbox.sales_total || 0) - (updatedCashbox.purchases_total || 0) - (updatedCashbox.transport_total || 0);
  }

  console.log('[PROFIT-FIX] قيم الخزينة بعد التحديث:', {
    current_balance: updatedCashbox.current_balance,
    profit_total: updatedCashbox.profit_total,
    sales_total: updatedCashbox.sales_total,
    purchases_total: updatedCashbox.purchases_total,
    returns_total: updatedCashbox.returns_total
  });

  return updatedCashbox;
}

/**
 * إنشاء خزينة جديدة
 * @param {number} initialBalance - الرصيد الافتتاحي
 * @returns {Object} - الخزينة الجديدة
 */
function createNewCashbox(initialBalance = 0) {
  const numInitialBalance = Number(initialBalance) || 0;

  return {
    id: 1,
    initial_balance: numInitialBalance,
    current_balance: numInitialBalance, // الرصيد الحالي يساوي الرصيد الافتتاحي
    profit_total: 0, // الأرباح تبدأ من صفر
    sales_total: 0,
    purchases_total: 0,
    transport_total: 0, // إضافة حقل مصاريف النقل
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    exists: true
  };
}

/**
 * إصلاح قيم الخزينة السالبة
 * @param {Object} cashbox - الخزينة الحالية
 * @returns {Object} - الخزينة المصححة
 */
function fixNegativeCashboxValues(cashbox) {
  if (!cashbox) {
    return cashbox;
  }

  console.log('[PROFIT-FIX] إصلاح قيم الخزينة السالبة');
  console.log('[PROFIT-FIX] قيم الخزينة قبل الإصلاح:', {
    initial_balance: cashbox.initial_balance,
    current_balance: cashbox.current_balance,
    profit_total: cashbox.profit_total,
    sales_total: cashbox.sales_total,
    purchases_total: cashbox.purchases_total,
    returns_total: cashbox.returns_total,
    transport_total: cashbox.transport_total // إضافة حقل مصاريف النقل
  });

  // نسخة جديدة من الخزينة
  const fixedCashbox = { ...cashbox };

  // تصحيح القيم السالبة
  fixedCashbox.profit_total = Math.max(0, Number(fixedCashbox.profit_total) || 0);
  fixedCashbox.sales_total = Math.max(0, Number(fixedCashbox.sales_total) || 0);
  fixedCashbox.purchases_total = Math.max(0, Number(fixedCashbox.purchases_total) || 0);
  fixedCashbox.returns_total = Math.max(0, Number(fixedCashbox.returns_total) || 0);
  fixedCashbox.transport_total = Math.max(0, Number(fixedCashbox.transport_total) || 0); // إضافة حقل مصاريف النقل

  // التأكد من أن الرصيد الحالي والرصيد الافتتاحي ليسا سالبين
  // الرصيد الحالي يساوي دائما الرصيد الافتتاحي
  fixedCashbox.initial_balance = Math.max(0, Number(fixedCashbox.initial_balance) || 0);
  fixedCashbox.current_balance = fixedCashbox.initial_balance;


  console.log('[PROFIT-FIX] قيم الخزينة بعد الإصلاح:', {
    initial_balance: fixedCashbox.initial_balance,
    current_balance: fixedCashbox.current_balance,
    profit_total: fixedCashbox.profit_total,
    sales_total: fixedCashbox.sales_total,
    purchases_total: fixedCashbox.purchases_total,
    returns_total: fixedCashbox.returns_total,
    transport_total: fixedCashbox.transport_total // إضافة حقل مصاريف النقل
  });

  return fixedCashbox;
}

/**
 * إعادة حساب الأرباح بناءً على الرصيد الافتتاحي والرصيد الحالي
 *
 * @param {number} initialBalance - الرصيد الافتتاحي
 * @param {number} currentBalance - الرصيد الحالي
 * @param {number} salesTotal - إجمالي المبيعات
 * @param {number} purchasesTotal - إجمالي المشتريات
 * @param {number} transportTotal - إجمالي مصاريف النقل
 * @returns {Object} - نتائج إعادة الحساب
 */
function recalculateProfitAfterInitialBalanceChange(initialBalance, currentBalance, salesTotal, purchasesTotal, transportTotal) {
  const numInitialBalance = Number(initialBalance) || 0;
  let numCurrentBalance = Number(currentBalance) || 0;
  const numSalesTotal = Number(salesTotal) || 0;
  const numPurchasesTotal = Number(purchasesTotal) || 0;
  const numTransportTotal = Number(transportTotal) || 0;

  // الرصيد الحالي يساوي دائما الرصيد الافتتاحي
  numCurrentBalance = numInitialBalance;

  // الأرباح = المبيعات - المشتريات - النقل
  const newProfit = numSalesTotal - numPurchasesTotal - numTransportTotal;

  return {
    newCurrentBalance: numCurrentBalance,
    newProfit: newProfit,
  };
}

module.exports = {
  calculateProfit,
  calculateProfitWithTransport,
  calculateProfitMargin,
  calculateSellingPrice,
  calculateBalanceDistribution,
  updateCashboxAfterTransaction,
  createNewCashbox,
  fixNegativeCashboxValues,
  recalculateProfitAfterInitialBalanceChange,
};
