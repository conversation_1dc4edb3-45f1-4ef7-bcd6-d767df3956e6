/**
 * اختبار معالج تشخيص الأرباح
 */

const { ipc<PERSON><PERSON><PERSON> } = require('electron');

async function testProfitDiagnosis() {
  console.log('🔍 بدء اختبار تشخيص الأرباح...');
  
  try {
    // استدعاء معالج التشخيص
    const result = await ipcRenderer.invoke('diagnose-profits');
    
    if (result.success) {
      console.log('✅ تم تشخيص الأرباح بنجاح');
      console.log('📊 نتائج التشخيص:');
      
      const { cashbox, salesSummary, salesDetails } = result.data;
      
      // عرض بيانات الخزينة
      console.log('\n💰 بيانات الخزينة:');
      console.log(`   - الرصيد الافتتاحي: ${cashbox?.initial_balance || 0}`);
      console.log(`   - الرصيد الحالي: ${cashbox?.current_balance || 0}`);
      console.log(`   - إجمالي الأرباح: ${cashbox?.profit_total || 0}`);
      console.log(`   - إجمالي المبيعات: ${cashbox?.sales_total || 0}`);
      console.log(`   - إجمالي المشتريات: ${cashbox?.purchases_total || 0}`);
      
      // عرض ملخص معاملات البيع
      console.log('\n📈 ملخص معاملات البيع:');
      console.log(`   - عدد معاملات البيع: ${salesSummary?.count || 0}`);
      console.log(`   - إجمالي الأرباح من المعاملات: ${salesSummary?.total_profit || 0}`);
      console.log(`   - إجمالي قيمة المبيعات: ${salesSummary?.total_sales || 0}`);
      
      // عرض تفاصيل معاملات البيع
      console.log('\n🔍 تفاصيل آخر معاملات البيع:');
      if (salesDetails && salesDetails.length > 0) {
        salesDetails.forEach((sale, index) => {
          console.log(`   ${index + 1}. معاملة ${sale.id}:`);
          console.log(`      - الصنف: ${sale.item_id}`);
          console.log(`      - الكمية: ${sale.quantity}`);
          console.log(`      - سعر البيع: ${sale.selling_price}`);
          console.log(`      - إجمالي السعر: ${sale.total_price}`);
          console.log(`      - الربح: ${sale.profit || 0}`);
          console.log(`      - التاريخ: ${sale.transaction_date}`);
        });
      } else {
        console.log('   لا توجد معاملات بيع');
      }
      
      // تحليل المشكلة
      console.log('\n🔍 تحليل المشكلة:');
      
      if (salesSummary?.count === 0) {
        console.log('❌ المشكلة: لا توجد معاملات بيع في النظام');
        console.log('💡 الحل: تأكد من وجود معاملات بيع مسجلة');
      } else if (salesSummary?.total_profit === 0 || salesSummary?.total_profit === null) {
        console.log('❌ المشكلة: معاملات البيع موجودة لكن الأرباح = 0');
        console.log('💡 الحل: يجب إعادة حساب الأرباح لمعاملات البيع');
      } else {
        const difference = Math.abs((salesSummary?.total_profit || 0) - (cashbox?.profit_total || 0));
        if (difference > 0.01) {
          console.log('❌ المشكلة: عدم تطابق الأرباح المحسوبة مع المحفوظة');
          console.log(`   - الأرباح من المعاملات: ${salesSummary?.total_profit || 0}`);
          console.log(`   - الأرباح في الخزينة: ${cashbox?.profit_total || 0}`);
          console.log(`   - الفرق: ${difference}`);
          console.log('💡 الحل: تحديث إجمالي الأرباح في الخزينة');
        } else {
          console.log('✅ النظام يبدو سليماً - الأرباح متطابقة');
        }
      }
      
    } else {
      console.error('❌ فشل في تشخيص الأرباح:', result.error);
    }
    
  } catch (error) {
    console.error('❌ خطأ في اختبار التشخيص:', error);
  }
}

// تشغيل الاختبار إذا تم استدعاء الملف مباشرة
if (typeof window !== 'undefined') {
  // في بيئة المتصفح
  window.testProfitDiagnosis = testProfitDiagnosis;
  console.log('✅ تم تحميل دالة testProfitDiagnosis - يمكنك استدعاؤها من وحدة التحكم');
} else {
  // في بيئة Node.js
  testProfitDiagnosis()
    .then(() => {
      console.log('\n✅ انتهى اختبار التشخيص');
    })
    .catch((error) => {
      console.error('❌ خطأ في تشغيل اختبار التشخيص:', error);
    });
}

module.exports = {
  testProfitDiagnosis
};
