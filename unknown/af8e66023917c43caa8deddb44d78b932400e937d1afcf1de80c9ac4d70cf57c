# نظام إدارة المخازن - نسخة المطورين

## نظرة عامة
هذه نسخة مخصصة للمطورين من نظام إدارة المخازن، تحتوي على الملفات الرئيسية الضرورية لتشغيل المنظومة وتطويرها، مع استبعاد الملفات غير الضرورية مثل ملفات الصيانة والنسخ الاحتياطية والملفات المؤقتة.

## هيكل المشروع

### المجلدات الرئيسية
- `assets/`: أصول التطبيق (الصور، الأيقونات، الخطوط)
- `public/`: الملفات العامة التي يتم تقديمها كما هي
- `src/`: كود المصدر للتطبيق
  - `assets/`: أصول التطبيق داخل كود المصدر
  - `components/`: مكونات React القابلة لإعادة الاستخدام
  - `context/`: سياق التطبيق وإدارة الحالة
  - `db/`: وظائف التعامل مع قاعدة البيانات
  - `hooks/`: hooks مخصصة لـ React
  - `pages/`: صفحات التطبيق الرئيسية
  - `services/`: خدمات التطبيق مثل API والمصادقة
  - `styles/`: أنماط CSS العامة
  - `utils/`: وظائف مساعدة متنوعة
  - `workers/`: عمال الخلفية للمهام الثقيلة
- `wms-database/`: ملفات قاعدة البيانات

### الملفات الرئيسية
- `main.js`: نقطة دخول التطبيق الرئيسية
- `preload.js`: يقوم بتعريف واجهة برمجة التطبيق (API) للتواصل بين العمليات
- `ipc-handlers.js`: يقوم بتسجيل جميع معالجات IPC في مكان واحد
- `database-singleton.js`: يوفر نمط singleton للوصول إلى قاعدة البيانات
- `items-manager.js`: يدير عمليات الأصناف (إضافة، تعديل، حذف، استعلام)
- `inventory-manager.js`: يدير عمليات المخزون (تحديث الكميات، حساب المتوسطات)
- `customers-manager.js`: يدير عمليات العملاء (إضافة، تعديل، حذف، استعلام)
- `unified-transaction-manager.js`: يدير جميع المعاملات بطريقة موحدة (مشتريات، مبيعات)
- `return-transactions-manager.js`: يدير عمليات الإرجاع
- `cashbox-manager.js`: يدير عمليات الخزينة (إيداع، سحب، حساب الأرباح)
- `reports-manager.js`: يدير إنشاء وعرض التقارير المختلفة
- `error-handler.js`: يدير معالجة الأخطاء وتسجيلها
- `index.html`: ملف HTML الرئيسي
- `package.json`: يحدد تبعيات المشروع وسكريبتات البناء والتشغيل
- `webpack.config.js`: ملف تكوين Webpack لبناء التطبيق
- `database-schema.sql`: يحدد هيكل قاعدة البيانات (الجداول، العلاقات، الفهارس)

## تثبيت وتشغيل المشروع

### متطلبات النظام
- Node.js (الإصدار 18 أو أحدث)
- npm أو yarn

### خطوات التثبيت
1. قم بتثبيت التبعيات:
   ```
   npm install
   ```
   أو
   ```
   yarn
   ```

2. قم ببناء التطبيق:
   ```
   npm run build
   ```
   أو
   ```
   yarn build
   ```

### تشغيل التطبيق في وضع التطوير
```
npm run dev
```
أو
```
yarn dev
```

### بناء التطبيق للإنتاج
```
npm run dist
```
أو
```
yarn dist
```

## الوظائف الرئيسية

### إدارة المخزون
- إضافة وتعديل وحذف الأصناف
- تتبع كميات المخزون
- حساب متوسط سعر الشراء

### إدارة المعاملات
- تسجيل عمليات الشراء والبيع
- إدارة عمليات الإرجاع
- تتبع المعاملات حسب العميل

### إدارة العملاء
- إضافة وتعديل وحذف العملاء
- تصنيف العملاء (عادي، دائم، فرعي)
- تتبع معاملات العملاء

### إدارة الخزينة
- تسجيل عمليات الإيداع والسحب
- حساب الأرباح
- إنشاء تقارير الخزينة

### التقارير
- تقارير المخزون
- تقارير المبيعات
- تقارير الأرباح
- تقارير العملاء

## المساهمة في المشروع
1. قم بإنشاء فرع جديد للميزة التي تريد إضافتها
2. قم بتطوير الميزة واختبارها
3. قم بإرسال طلب سحب (Pull Request)

## الترخيص
هذا المشروع مرخص بموجب رخصة MIT.
