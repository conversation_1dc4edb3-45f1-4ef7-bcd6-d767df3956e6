/**
 * تعريف نموذج بيانات العملاء
 *
 * هذا النموذج يستخدم لتخزين بيانات العملاء بمختلف أنواعهم:
 * - العميل الدائم (regular): يمكن البيع له مباشرة أو إنشاء عملاء فرعيين تابعين له
 * - العميل الفرعي (sub): يتم إنشاء حسابه من حساب العميل الدائم
 * - العميل العادي (normal): يتم البيع له مباشرة
 */

// نموذج بيانات العميل لقاعدة بيانات NeDB
const customerSchema = {
  name: '',                // اسم العميل
  contact_person: '',      // جهة الاتصال
  phone: '',               // رقم الهاتف
  email: '',               // البريد الإلكتروني
  address: '',             // العنوان
  customer_type: 'normal', // نوع العميل: 'regular' (دائم), 'sub' (فرعي), 'normal' (عادي)
  parent_id: null,         // معرف العميل الدائم (للعملاء الفرعيين فقط)
  credit_limit: 0,         // حد الائتمان
  balance: 0,              // الرصيد الحالي
  sales_history: [],       // سجل المبيعات للعميل
  total_sales: 0,          // إجمالي المبيعات
  total_profit: 0,         // إجمالي الربح
  created_at: new Date(),  // تاريخ الإنشاء
  updated_at: new Date()   // تاريخ التحديث
};

// بيانات العملاء الافتراضية للاختبار
const defaultCustomers = [
  {
    name: 'عميل دائم للاختبار',
    contact_person: 'محمد أحمد',
    phone: '0123456789',
    email: '<EMAIL>',
    address: 'الرياض، المملكة العربية السعودية',
    customer_type: 'regular',
    credit_limit: 5000,
    balance: 0,
    created_at: new Date(),
    updated_at: new Date()
  },
  {
    name: 'عميل عادي للاختبار',
    contact_person: 'أحمد محمد',
    phone: '0123456788',
    email: '<EMAIL>',
    address: 'جدة، المملكة العربية السعودية',
    customer_type: 'normal',
    credit_limit: 1000,
    balance: 0,
    created_at: new Date(),
    updated_at: new Date()
  }
];

module.exports = {
  customerSchema,
  defaultCustomers
};
