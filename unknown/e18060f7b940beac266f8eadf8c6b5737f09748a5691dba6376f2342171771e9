/**
 * اختبار تشخيص الأرباح من وحدة التحكم
 * يمكن تشغيل هذا الكود في وحدة التحكم في المتصفح
 */

async function testProfitDiagnosis() {
  console.log('🔍 بدء اختبار تشخيص الأرباح...');
  
  try {
    // التحقق من توفر API
    if (!window.electronAPI) {
      console.error('❌ واجهة Electron API غير متوفرة');
      return;
    }

    // استدعاء معالج التشخيص
    console.log('📡 استدعاء معالج diagnose-profits...');
    const result = await window.electronAPI.invoke('diagnose-profits');
    
    if (result.success) {
      console.log('✅ تم تشخيص الأرباح بنجاح');
      console.log('📊 نتائج التشخيص:');
      
      const { cashbox, salesSummary, salesDetails } = result.data;
      
      // عرض بيانات الخزينة
      console.log('\n💰 بيانات الخزينة:');
      if (cashbox) {
        console.log(`   - الرصيد الافتتاحي: ${cashbox.initial_balance || 0}`);
        console.log(`   - الرصيد الحالي: ${cashbox.current_balance || 0}`);
        console.log(`   - إجمالي الأرباح: ${cashbox.profit_total || 0}`);
        console.log(`   - إجمالي المبيعات: ${cashbox.sales_total || 0}`);
        console.log(`   - إجمالي المشتريات: ${cashbox.purchases_total || 0}`);
      } else {
        console.log('   ❌ لا توجد بيانات خزينة');
      }
      
      // عرض ملخص معاملات البيع
      console.log('\n📈 ملخص معاملات البيع:');
      if (salesSummary) {
        console.log(`   - عدد معاملات البيع: ${salesSummary.count || 0}`);
        console.log(`   - إجمالي الأرباح من المعاملات: ${salesSummary.total_profit || 0}`);
        console.log(`   - إجمالي قيمة المبيعات: ${salesSummary.total_sales || 0}`);
      } else {
        console.log('   ❌ لا توجد بيانات معاملات بيع');
      }
      
      // عرض تفاصيل معاملات البيع
      console.log('\n🔍 تفاصيل آخر معاملات البيع:');
      if (salesDetails && salesDetails.length > 0) {
        salesDetails.forEach((sale, index) => {
          console.log(`   ${index + 1}. معاملة ${sale.id}:`);
          console.log(`      - الصنف: ${sale.item_id}`);
          console.log(`      - الكمية: ${sale.quantity}`);
          console.log(`      - سعر البيع: ${sale.selling_price}`);
          console.log(`      - إجمالي السعر: ${sale.total_price}`);
          console.log(`      - الربح: ${sale.profit || 0}`);
          console.log(`      - التاريخ: ${sale.transaction_date}`);
        });
      } else {
        console.log('   ❌ لا توجد معاملات بيع');
      }
      
      // تحليل المشكلة
      console.log('\n🔍 تحليل المشكلة:');
      
      if (salesSummary?.count === 0) {
        console.log('❌ المشكلة: لا توجد معاملات بيع في النظام');
        console.log('💡 الحل: تأكد من وجود معاملات بيع مسجلة');
      } else if (salesSummary?.total_profit === 0 || salesSummary?.total_profit === null) {
        console.log('❌ المشكلة: معاملات البيع موجودة لكن الأرباح = 0');
        console.log('💡 الحل: يجب إعادة حساب الأرباح لمعاملات البيع');
      } else {
        const difference = Math.abs((salesSummary?.total_profit || 0) - (cashbox?.profit_total || 0));
        if (difference > 0.01) {
          console.log('❌ المشكلة: عدم تطابق الأرباح المحسوبة مع المحفوظة');
          console.log(`   - الأرباح من المعاملات: ${salesSummary?.total_profit || 0}`);
          console.log(`   - الأرباح في الخزينة: ${cashbox?.profit_total || 0}`);
          console.log(`   - الفرق: ${difference}`);
          console.log('💡 الحل: تحديث إجمالي الأرباح في الخزينة');
        } else {
          console.log('✅ النظام يبدو سليماً - الأرباح متطابقة');
        }
      }
      
      // عرض البيانات الخام
      console.log('\n📋 البيانات الخام:');
      console.log(result.data);
      
    } else {
      console.error('❌ فشل في تشخيص الأرباح:', result.error);
    }
    
  } catch (error) {
    console.error('❌ خطأ في اختبار التشخيص:', error);
  }
}

// تشغيل الاختبار تلقائياً
console.log('🚀 تم تحميل اختبار تشخيص الأرباح');
console.log('📝 لتشغيل الاختبار، اكتب: testProfitDiagnosis()');

// إتاحة الدالة عالمياً
window.testProfitDiagnosis = testProfitDiagnosis;
