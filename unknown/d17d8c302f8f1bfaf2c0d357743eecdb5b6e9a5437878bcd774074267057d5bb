/**
 * اختبار منطق تحديث الأرباح الجديد
 * 
 * هذا الملف يحتوي على دوال اختبار للتحقق من أن الأرباح تتحدث فقط
 * بعد عمليات البيع وليس بعد عمليات الشراء أو الاسترجاع
 */

// دالة لمراقبة أحداث تحديث الأرباح
function monitorProfitsUpdateEvents() {
  console.log('🎯 بدء مراقبة أحداث تحديث الأرباح الجديدة...');
  
  const eventsToMonitor = [
    'profits-updated',
    'auto-profits-updated', 
    'dashboard-profits-updated',
    'purchase-completed',
    'return-completed',
    'direct-update',
    'cashbox-updated-ui'
  ];
  
  eventsToMonitor.forEach(eventType => {
    window.addEventListener(eventType, (event) => {
      console.log(`📊 [${eventType}] حدث مستلم:`, {
        type: eventType,
        detail: event.detail,
        timestamp: new Date().toISOString()
      });
      
      // تسجيل خاص لأحداث الأرباح
      if (eventType.includes('profit')) {
        console.log(`💰 تحديث أرباح - النوع: ${eventType}`, event.detail);
        
        // فحص نوع المعاملة
        if (event.detail && event.detail.transaction_type) {
          console.log(`🔍 نوع المعاملة: ${event.detail.transaction_type}`);
          
          if (event.detail.transaction_type === 'sale') {
            console.log('✅ تحديث أرباح صحيح لعملية بيع');
          } else {
            console.log(`❌ تحديث أرباح غير متوقع لعملية ${event.detail.transaction_type}`);
          }
        }
      }
      
      // تسجيل خاص لأحداث الشراء والاسترجاع الجديدة
      if (eventType === 'purchase-completed') {
        console.log('🛒 تم إكمال عملية شراء - لا يجب تحديث الأرباح');
      }
      
      if (eventType === 'return-completed') {
        console.log('↩️ تم إكمال عملية استرجاع - لا يجب تحديث الأرباح');
      }
    });
  });
  
  console.log('✅ تم إعداد مراقبة أحداث تحديث الأرباح');
}

// دالة لاختبار عملية بيع
async function testSaleTransaction() {
  console.log('🧪 اختبار عملية بيع...');
  
  const testSale = {
    transaction_type: 'sale',
    item_id: 1,
    item_name: 'صنف تجريبي',
    quantity: 1,
    price: 100,
    total_price: 100,
    profit: 20,
    transaction_date: new Date().toISOString(),
    invoice_number: `TEST-SALE-${Date.now()}`
  };
  
  try {
    if (window.api && window.api.invoke) {
      console.log('📤 إرسال عملية بيع تجريبية:', testSale);
      const result = await window.api.invoke('add-transaction', testSale);
      console.log('✅ نتيجة عملية البيع:', result);
      
      // انتظار قصير للسماح بمعالجة الأحداث
      setTimeout(() => {
        console.log('🔍 فحص ما إذا تم تحديث الأرباح بعد عملية البيع...');
        checkProfitsUpdated('sale');
      }, 1000);
    } else {
      console.error('❌ واجهة API غير متوفرة');
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار عملية البيع:', error);
  }
}

// دالة لاختبار عملية شراء
async function testPurchaseTransaction() {
  console.log('🧪 اختبار عملية شراء...');
  
  const testPurchase = {
    transaction_type: 'purchase',
    item_id: 1,
    item_name: 'صنف تجريبي',
    quantity: 1,
    price: 80,
    total_price: 80,
    transaction_date: new Date().toISOString(),
    invoice_number: `TEST-PURCHASE-${Date.now()}`
  };
  
  try {
    if (window.api && window.api.invoke) {
      console.log('📤 إرسال عملية شراء تجريبية:', testPurchase);
      const result = await window.api.invoke('add-transaction', testPurchase);
      console.log('✅ نتيجة عملية الشراء:', result);
      
      // انتظار قصير للسماح بمعالجة الأحداث
      setTimeout(() => {
        console.log('🔍 فحص ما إذا تم تجاهل تحديث الأرباح بعد عملية الشراء...');
        checkProfitsNotUpdated('purchase');
      }, 1000);
    } else {
      console.error('❌ واجهة API غير متوفرة');
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار عملية الشراء:', error);
  }
}

// دالة لاختبار عملية استرجاع
async function testReturnTransaction() {
  console.log('🧪 اختبار عملية استرجاع...');
  
  const testReturn = {
    transaction_type: 'return',
    item_id: 1,
    item_name: 'صنف تجريبي',
    quantity: 1,
    price: 100,
    total_price: 100,
    profit: -20,
    transaction_date: new Date().toISOString(),
    invoice_number: `TEST-RETURN-${Date.now()}`
  };
  
  try {
    if (window.api && window.api.invoke) {
      console.log('📤 إرسال عملية استرجاع تجريبية:', testReturn);
      const result = await window.api.invoke('add-transaction', testReturn);
      console.log('✅ نتيجة عملية الاسترجاع:', result);
      
      // انتظار قصير للسماح بمعالجة الأحداث
      setTimeout(() => {
        console.log('🔍 فحص ما إذا تم تجاهل تحديث الأرباح بعد عملية الاسترجاع...');
        checkProfitsNotUpdated('return');
      }, 1000);
    } else {
      console.error('❌ واجهة API غير متوفرة');
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار عملية الاسترجاع:', error);
  }
}

// دالة للتحقق من تحديث الأرباح
function checkProfitsUpdated(transactionType) {
  console.log(`🔍 فحص تحديث الأرباح لعملية ${transactionType}...`);
  
  // فحص بطاقات الأرباح في الواجهة
  const profitCards = document.querySelectorAll('[class*="profit"], [class*="ربح"]');
  console.log(`📊 عدد بطاقات الأرباح الموجودة: ${profitCards.length}`);
  
  // فحص التقارير المالية
  const financialReports = document.querySelectorAll('[class*="financial"], [class*="مالي"]');
  console.log(`📈 عدد التقارير المالية الموجودة: ${financialReports.length}`);
  
  console.log(`✅ يجب أن تكون الأرباح محدثة بعد عملية ${transactionType}`);
}

// دالة للتحقق من عدم تحديث الأرباح
function checkProfitsNotUpdated(transactionType) {
  console.log(`🔍 فحص عدم تحديث الأرباح لعملية ${transactionType}...`);
  
  console.log(`✅ يجب أن تبقى الأرباح كما هي بعد عملية ${transactionType}`);
}

// دالة لتشغيل جميع الاختبارات
async function runAllTests() {
  console.log('🚀 بدء تشغيل جميع اختبارات منطق تحديث الأرباح...');
  
  // بدء مراقبة الأحداث
  monitorProfitsUpdateEvents();
  
  // انتظار قصير قبل بدء الاختبارات
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // اختبار عملية بيع
  await testSaleTransaction();
  
  // انتظار بين الاختبارات
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // اختبار عملية شراء
  await testPurchaseTransaction();
  
  // انتظار بين الاختبارات
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // اختبار عملية استرجاع
  await testReturnTransaction();
  
  console.log('✅ تم إكمال جميع اختبارات منطق تحديث الأرباح');
}

// دالة لعرض تقرير الاختبار
function showTestReport() {
  console.log('📋 تقرير اختبار منطق تحديث الأرباح:');
  console.log('');
  console.log('✅ المتوقع:');
  console.log('  - عمليات البيع: تحديث الأرباح فوري');
  console.log('  - عمليات الشراء: عدم تحديث الأرباح');
  console.log('  - عمليات الاسترجاع: عدم تحديث الأرباح');
  console.log('');
  console.log('🔍 للتحقق من النتائج:');
  console.log('  1. راقب رسائل وحدة التحكم أثناء الاختبار');
  console.log('  2. تحقق من بطاقات الأرباح في الواجهة');
  console.log('  3. تحقق من التقارير المالية');
  console.log('');
  console.log('📞 لتشغيل الاختبارات: runAllTests()');
}

// تصدير الدوال للاستخدام العام
window.testProfitsUpdateLogic = {
  monitorProfitsUpdateEvents,
  testSaleTransaction,
  testPurchaseTransaction,
  testReturnTransaction,
  runAllTests,
  showTestReport
};

// عرض تقرير الاختبار عند تحميل الملف
showTestReport();

console.log('🧪 تم تحميل ملف اختبار منطق تحديث الأرباح');
console.log('📞 استخدم window.testProfitsUpdateLogic.runAllTests() لتشغيل الاختبارات');
