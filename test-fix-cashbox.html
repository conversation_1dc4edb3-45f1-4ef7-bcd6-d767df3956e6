<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح الخزينة</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>إصلاح الخزينة</h1>
        <p>هذه الأداة تقوم بإعادة حساب جميع قيم الخزينة بناءً على المعاملات الموجودة في قاعدة البيانات.</p>
        
        <button id="fixCashboxBtn" class="button">إصلاح الخزينة</button>
        <button id="checkCashboxBtn" class="button">فحص الخزينة</button>
        <button id="recalculateProfitsBtn" class="button">إعادة حساب الأرباح</button>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        const { ipcRenderer } = require('electron');
        
        const fixCashboxBtn = document.getElementById('fixCashboxBtn');
        const checkCashboxBtn = document.getElementById('checkCashboxBtn');
        const recalculateProfitsBtn = document.getElementById('recalculateProfitsBtn');
        const resultDiv = document.getElementById('result');
        
        function showResult(message, type = 'info') {
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }
        
        function disableButtons() {
            fixCashboxBtn.disabled = true;
            checkCashboxBtn.disabled = true;
            recalculateProfitsBtn.disabled = true;
        }
        
        function enableButtons() {
            fixCashboxBtn.disabled = false;
            checkCashboxBtn.disabled = false;
            recalculateProfitsBtn.disabled = false;
        }
        
        fixCashboxBtn.addEventListener('click', async () => {
            try {
                disableButtons();
                showResult('جاري إصلاح الخزينة...', 'info');
                
                const result = await ipcRenderer.invoke('fix-cashbox-from-transactions');
                
                if (result.success) {
                    const message = `تم إصلاح الخزينة بنجاح!

القيم قبل الإصلاح:
- الرصيد الحالي: ${result.before.current_balance}
- إجمالي المبيعات: ${result.before.sales_total}
- إجمالي المشتريات: ${result.before.purchases_total}
- إجمالي الأرباح: ${result.before.profit_total}

القيم بعد الإصلاح:
- الرصيد الحالي: ${result.after.current_balance}
- إجمالي المبيعات: ${result.after.sales_total}
- إجمالي المشتريات: ${result.after.purchases_total}
- إجمالي الأرباح: ${result.after.profit_total}`;
                    
                    showResult(message, 'success');
                } else {
                    showResult(`فشل في إصلاح الخزينة: ${result.error}`, 'error');
                }
            } catch (error) {
                showResult(`خطأ: ${error.message}`, 'error');
            } finally {
                enableButtons();
            }
        });
        
        checkCashboxBtn.addEventListener('click', async () => {
            try {
                disableButtons();
                showResult('جاري فحص الخزينة...', 'info');
                
                const result = await ipcRenderer.invoke('get-cashbox');
                
                if (result.exists) {
                    const message = `حالة الخزينة الحالية:
- الرصيد الافتتاحي: ${result.initial_balance}
- الرصيد الحالي: ${result.current_balance}
- إجمالي المبيعات: ${result.sales_total}
- إجمالي المشتريات: ${result.purchases_total}
- إجمالي الأرباح: ${result.profit_total}
- إجمالي المرتجعات: ${result.returns_total}
- إجمالي مصاريف النقل: ${result.transport_total}`;
                    
                    showResult(message, 'info');
                } else {
                    showResult('لا توجد خزينة في قاعدة البيانات', 'error');
                }
            } catch (error) {
                showResult(`خطأ: ${error.message}`, 'error');
            } finally {
                enableButtons();
            }
        });
        
        recalculateProfitsBtn.addEventListener('click', async () => {
            try {
                disableButtons();
                showResult('جاري إعادة حساب الأرباح...', 'info');
                
                const result = await ipcRenderer.invoke('recalculate-profits');
                
                if (result.success) {
                    showResult(`تم إعادة حساب الأرباح بنجاح!\nإجمالي الأرباح: ${result.totalProfit}`, 'success');
                } else {
                    showResult(`فشل في إعادة حساب الأرباح: ${result.error}`, 'error');
                }
            } catch (error) {
                showResult(`خطأ: ${error.message}`, 'error');
            } finally {
                enableButtons();
            }
        });
    </script>
</body>
</html>
