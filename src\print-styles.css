/* أنماط الطباعة */
@media print {
  /* إعدادات عامة للطباعة */
  body {
    background-color: white !important;
    color: black !important;
    font-size: 12pt;
    font-family: 'Cairo', Arial, sans-serif;
  }
  
  /* إخفاء العناصر غير الضرورية للطباعة */
  .navbar,
  .sidebar,
  .btn-add,
  .btn-edit,
  .btn-delete,
  .btn-print,
  .btn-export,
  .modal,
  .alert,
  .pagination,
  .form-actions,
  .no-print {
    display: none !important;
  }
  
  /* تنسيق العناصر المطبوعة */
  .main-content-full {
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
  }
  
  .dashboard-page,
  .inventory-page,
  .items-page,
  .reports-page,
  .sales-page,
  .purchases-page {
    padding: 0 !important;
  }
  
  /* تنسيق العناوين */
  h1, h2, h3, h4, h5, h6 {
    page-break-after: avoid;
    page-break-inside: avoid;
  }
  
  h1 {
    font-size: 18pt;
    margin-bottom: 10pt;
  }
  
  h2 {
    font-size: 16pt;
    margin-bottom: 8pt;
  }
  
  h3 {
    font-size: 14pt;
    margin-bottom: 6pt;
  }
  
  /* تنسيق الجداول */
  .table-container {
    page-break-inside: avoid;
    margin-bottom: 20pt;
  }
  
  .table {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 10pt;
  }
  
  .table th,
  .table td {
    border: 1px solid #ddd;
    padding: 5pt;
    text-align: right;
  }
  
  .table th {
    background-color: #f2f2f2 !important;
    font-weight: bold;
  }
  
  /* تنسيق البطاقات */
  .card,
  .chart-container,
  .stat-card {
    page-break-inside: avoid;
    border: 1px solid #ddd;
    margin-bottom: 15pt;
    box-shadow: none !important;
  }
  
  /* تنسيق الرسوم البيانية */
  .chart-content {
    height: 200pt !important;
  }
  
  /* تنسيق الفواتير */
  .invoice-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20pt;
  }
  
  .invoice-logo {
    max-width: 150pt;
    max-height: 60pt;
  }
  
  .invoice-info {
    margin-bottom: 15pt;
  }
  
  .invoice-details {
    margin-bottom: 20pt;
  }
  
  .invoice-table {
    margin-bottom: 20pt;
  }
  
  .invoice-summary {
    margin-top: 20pt;
    page-break-inside: avoid;
  }
  
  .invoice-footer {
    margin-top: 30pt;
    text-align: center;
    font-size: 10pt;
    color: #666;
  }
  
  /* تنسيق التقارير */
  .report-header {
    text-align: center;
    margin-bottom: 20pt;
  }
  
  .report-date {
    font-size: 10pt;
    color: #666;
    margin-bottom: 15pt;
  }
  
  .report-section {
    margin-bottom: 20pt;
    page-break-inside: avoid;
  }
  
  .report-footer {
    margin-top: 30pt;
    text-align: center;
    font-size: 10pt;
    color: #666;
    border-top: 1px solid #ddd;
    padding-top: 10pt;
  }
  
  /* فواصل الصفحات */
  .page-break {
    page-break-after: always;
  }
  
  /* تنسيق الصور */
  img {
    max-width: 100% !important;
  }
}
