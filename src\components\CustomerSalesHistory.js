import { useState, useEffect } from 'react';
import {
  FaHistory,
  FaShoppingCart,
  FaMoneyBillWave,
  FaChartLine,
  FaCalendarAlt,
  FaBoxOpen,
  FaTimes,
  FaPrint,
  FaPhone,
  FaReceipt,
  FaExclamationTriangle,
  FaFileInvoice,
  FaCheck,
  FaCheckSquare,
  FaSquare
} from 'react-icons/fa';
import { useApp } from '../context/AppContext';
import '../assets/css/customer-sales-history.css';

// مكون لعرض سجل مبيعات العميل
const CustomerSalesHistory = ({ customerId, onClose }) => {
  const { getCustomerSalesHistory } = useApp();
  const [salesData, setSalesData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filteredInvoices, setFilteredInvoices] = useState([]);

  // تحديث الفواتير المفلترة عند تغيير البيانات
  useEffect(() => {
    if (salesData && (salesData.groupedSales || salesData.salesHistory)) {
      console.log('تحديث الفواتير المفلترة بناءً على البيانات الجديدة');

      // تجهيز مجموعات الفواتير
      let invoiceGroups = [];

      // استخدام المبيعات المجمعة من الخادم إذا كانت متوفرة
      if (salesData.groupedSales && Array.isArray(salesData.groupedSales) && salesData.groupedSales.length > 0) {
        console.log('استخدام المبيعات المجمعة من الخادم:', salesData.groupedSales.length);

        invoiceGroups = salesData.groupedSales.map(invoice => {
          const items = Array.isArray(invoice.items) ? invoice.items : [];
          let totalAmount = 0;
          let totalProfit = 0;

          items.forEach(item => {
            totalAmount += Number(item.total_price) || 0;
            totalProfit += Number(item.profit) || 0;
          });

          return {
            invoiceNumber: invoice.invoice_number,
            items: items,
            totalAmount: totalAmount,
            totalProfit: totalProfit,
            transaction_date: invoice.transaction_date,
            notes: invoice.notes || '-'
          };
        });
      }
      // تجميع المبيعات محليًا إذا لم تكن المبيعات المجمعة متوفرة
      else if (Array.isArray(salesData.salesHistory) && salesData.salesHistory.length > 0) {
        console.log('تجميع المبيعات محليًا من سجل المبيعات:', salesData.salesHistory.length);

        const localInvoiceGroups = {};

        salesData.salesHistory.forEach(sale => {
          const saleId = sale.id || sale._id || sale.transaction_id || Math.random().toString(36).substring(7);
          const invoiceNumber = sale.invoice_number || `H-${String(saleId).padStart(5, '0')}`;

          if (!localInvoiceGroups[invoiceNumber]) {
            localInvoiceGroups[invoiceNumber] = {
              invoiceNumber: invoiceNumber,
              items: [],
              totalAmount: 0,
              totalProfit: 0,
              transaction_date: sale.transaction_date,
              notes: sale.notes || '-'
            };
          }

          localInvoiceGroups[invoiceNumber].items.push(sale);
          localInvoiceGroups[invoiceNumber].totalAmount += Number(sale.total_price) || 0;
          localInvoiceGroups[invoiceNumber].totalProfit += Number(sale.profit) || 0;
        });

        invoiceGroups = Object.values(localInvoiceGroups);
      } else {
        console.warn('لا توجد بيانات مبيعات متاحة للعرض');
      }

      // تعيين الفواتير المفلترة
      if (invoiceGroups.length > 0) {
        console.log('تم العثور على', invoiceGroups.length, 'فاتورة');
        setFilteredInvoices(invoiceGroups);
      } else {
        console.log('لا توجد فواتير لعرضها');
        setFilteredInvoices([]);
      }
    } else {
      console.warn('لا توجد بيانات متاحة لتحديث الفواتير المفلترة');
      setFilteredInvoices([]);
    }
  }, [salesData]);

  // تنسيق التاريخ (بالتنسيق الميلادي)
  const formatDate = (dateString) => {
    if (!dateString) return '-';
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return '-'; // التحقق من صحة التاريخ

      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();

      // إظهار الوقت فقط إذا كان غير صفر
      if (date.getHours() === 0 && date.getMinutes() === 0) {
        return `${year}/${month}/${day}`;
      } else {
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        return `${year}/${month}/${day} ${hours}:${minutes}`;
      }
    } catch (error) {
      console.error('خطأ في تنسيق التاريخ:', error);
      return '-';
    }
  };

  // طباعة معلومات تشخيصية عن الفواتير
  useEffect(() => {
    if (filteredInvoices.length > 0 && filteredInvoices[0].items && filteredInvoices[0].items.length > 0) {
      console.log('معلومات الفواتير المعروضة:', filteredInvoices);

      // فحص الفواتير الفرعية
      const subInvoices = filteredInvoices[0].items.filter(
        item => item.parent_invoice_number && item.parent_invoice_number !== item.invoice_number
      );

      console.log('عدد الفواتير الفرعية:', subInvoices.length);
      if (subInvoices.length > 0) {
        console.log('نموذج للفاتورة الفرعية:', subInvoices[0]);
      }
    }
  }, [filteredInvoices]);

  // تحميل بيانات المبيعات
  useEffect(() => {
    const loadSalesHistory = async () => {
      try {
        setLoading(true);
        console.log('جاري تحميل سجل المبيعات للعميل:', customerId);
        console.log('نوع معرف العميل:', typeof customerId);
        console.log('قيمة معرف العميل:', customerId);
        console.log('هل وظيفة getCustomerSalesHistory موجودة؟', typeof getCustomerSalesHistory === 'function');

        // التحقق من وجود وظيفة getCustomerSalesHistory
        if (typeof getCustomerSalesHistory !== 'function') {
          console.error('وظيفة getCustomerSalesHistory غير متوفرة');
          setError('وظيفة getCustomerSalesHistory غير متوفرة');
          setLoading(false);
          return;
        }

        // التحقق من صحة معرف العميل
        if (!customerId) {
          console.error('معرف العميل غير صالح');
          setError('معرف العميل غير صالح');
          setLoading(false);
          return;
        }

        // تحديث بيانات العميل أولاً لضمان عرض أحدث البيانات
        try {
          console.log('جاري تحديث بيانات العميل قبل عرض سجل المبيعات...');

          // استدعاء واجهة API للحصول على بيانات العميل المحدثة
          if (window.api && window.api.customers && window.api.customers.getCustomerById) {
            const updatedCustomerData = await window.api.customers.getCustomerById(customerId);

            if (updatedCustomerData) {
              console.log('تم الحصول على بيانات العميل المحدثة:', updatedCustomerData);

              // تحديث بيانات العميل في سياق التطبيق إذا كانت متاحة
              if (window.updateCustomerData) {
                window.updateCustomerData(updatedCustomerData);
                console.log('تم تحديث بيانات العميل في سياق التطبيق');
              }
            }
          }
        } catch (updateError) {
          console.warn('خطأ في تحديث بيانات العميل:', updateError);
          // نستمر في تحميل سجل المبيعات حتى لو فشل تحديث بيانات العميل
        }

        // الحصول على سجل المبيعات
        console.log('استدعاء getCustomerSalesHistory مع المعرف:', customerId);

        // استخدام try/catch داخلي لمعالجة أي أخطاء في استدعاء getCustomerSalesHistory
        let data;
        try {
          data = await getCustomerSalesHistory(customerId);
          console.log('تم استلام بيانات سجل المبيعات:', data);
        } catch (apiError) {
          console.error('خطأ في استدعاء getCustomerSalesHistory:', apiError);
          setError(`خطأ في استدعاء getCustomerSalesHistory: ${apiError.message}`);
          setLoading(false);
          return;
        }

        // التحقق من البيانات
        if (!data) {
          console.warn('لم يتم استلام بيانات سجل المبيعات');
          setError('لم يتم استلام بيانات من الخادم');
          setLoading(false);
          return;
        }

        // التحقق من نوع البيانات
        console.log('نوع البيانات المستلمة:', typeof data);

        // التحقق من وجود خطأ في البيانات
        if (data.error) {
          console.error('خطأ في البيانات المستلمة:', data.error);
          setError(`خطأ في البيانات المستلمة: ${data.error}`);
          setLoading(false);
          return;
        }

        // التحقق من وجود العميل في البيانات
        if (!data.customer) {
          console.error('لا يوجد عميل في البيانات المستلمة');
          setError('لا يوجد عميل في البيانات المستلمة');
          setLoading(false);
          return;
        }

        // التحقق من وجود المبيعات في البيانات
        if (!data.sales && !data.groupedSales) {
          console.error('لا توجد مبيعات في البيانات المستلمة');

          // إنشاء بيانات افتراضية
          const defaultData = {
            customer: data.customer || { name: 'عميل غير معروف', id: customerId },
            sales: [],
            groupedSales: [],
            totalSales: 0,
            totalProfit: 0,
            count: 0,
            invoiceCount: 0
          };

          // تحويل البيانات إلى التنسيق المطلوب
          const processedData = {
            customer: defaultData.customer,
            salesHistory: [],
            groupedSales: [],
            totalSales: 0,
            totalProfit: 0,
            invoiceCount: 0
          };

          // تعيين البيانات
          setSalesData(processedData);
          setError(null);
          setLoading(false);
          return;
        }

        // التحقق من وجود سجل المبيعات
        console.log('التحقق من وجود سجل المبيعات في البيانات المستلمة');
        console.log('data.sales:', data.sales);
        console.log('هل data.sales مصفوفة؟', Array.isArray(data.sales));

        const sales = Array.isArray(data.sales) ? data.sales :
                     (Array.isArray(data) ? data : []);

        console.log('بيانات المبيعات المستلمة:', sales);
        console.log('عدد المبيعات المستلمة:', sales.length);

        if (sales.length > 0) {
          console.log('عينة من المبيعات المستلمة:', sales.slice(0, 2));
        }

        // التحقق من وجود العميل
        console.log('التحقق من وجود العميل في البيانات المستلمة');
        console.log('data.customer:', data.customer);

        // معالجة مشكلة ترميز النص العربي
        let customer = data.customer || { name: 'عميل غير معروف', id: customerId };

        // إصلاح اسم العميل إذا كان هناك مشكلة في الترميز
        if (customer && customer.name && typeof customer.name === 'string') {
          // التحقق مما إذا كان هناك رموز غريبة في الاسم
          if (customer.name.includes('╪') || customer.name.includes('┘') || customer.name.includes('▒')) {
            console.warn('تم اكتشاف مشكلة في ترميز اسم العميل:', customer.name);
            // محاولة إصلاح الاسم أو استخدام اسم افتراضي
            customer.name = 'عميل ' + customer.id;
            console.log('تم تعيين اسم افتراضي للعميل:', customer.name);
          }
        }

        console.log('بيانات العميل المستلمة بعد المعالجة:', customer);

        // التحقق من وجود المبيعات المجمعة
        console.log('التحقق من وجود المبيعات المجمعة في البيانات المستلمة');
        console.log('data.groupedSales:', data.groupedSales);
        console.log('هل data.groupedSales مصفوفة؟', Array.isArray(data.groupedSales));

        // التحقق من وجود إجمالي المبيعات والربح
        console.log('data.totalSales:', data.totalSales);
        console.log('data.totalProfit:', data.totalProfit);
        console.log('data.invoiceCount:', data.invoiceCount);

        // معالجة مشكلة ترميز النص العربي في المبيعات
        if (Array.isArray(sales)) {
          sales.forEach(sale => {
            if (sale.item_name && typeof sale.item_name === 'string') {
              // التحقق مما إذا كان هناك رموز غريبة في اسم الصنف
              if (sale.item_name.includes('╪') || sale.item_name.includes('┘') || sale.item_name.includes('▒')) {
                console.warn('تم اكتشاف مشكلة في ترميز اسم الصنف:', sale.item_name);
                sale.item_name = `صنف ${sale.item_id || 'غير معروف'}`;
                console.log('تم تعيين اسم افتراضي للصنف:', sale.item_name);
              }
            }
          });
        }

        // معالجة مشكلة ترميز النص العربي في المبيعات المجمعة
        let groupedSalesData = [];
        if (Array.isArray(data.groupedSales)) {
          groupedSalesData = data.groupedSales.map(invoice => {
            // معالجة الملاحظات
            if (invoice.notes && typeof invoice.notes === 'string') {
              if (invoice.notes.includes('╪') || invoice.notes.includes('┘') || invoice.notes.includes('▒')) {
                invoice.notes = 'فاتورة مجمعة لمشتريات العميل';
              }
            }

            // معالجة الأصناف
            if (Array.isArray(invoice.items)) {
              invoice.items = invoice.items.map(item => {
                if (item.item_name && typeof item.item_name === 'string') {
                  if (item.item_name.includes('╪') || item.item_name.includes('┘') || item.item_name.includes('▒')) {
                    item.item_name = `صنف ${item.item_id || 'غير معروف'}`;
                  }
                }
                return item;
              });
            }

            return invoice;
          });
        }

        // تحويل البيانات إلى التنسيق المطلوب
        const processedData = {
          customer: customer,
          salesHistory: sales,
          groupedSales: groupedSalesData, // استخدام المبيعات المجمعة المعالجة
          totalSales: Number(data.totalSales) || 0,
          totalProfit: Number(data.totalProfit) || 0,
          invoiceCount: Number(data.invoiceCount) || 0
        };

        console.log('البيانات المعالجة:', processedData);
        console.log('المبيعات المجمعة:', processedData.groupedSales);
        console.log('عدد المبيعات المجمعة:', processedData.groupedSales.length);

        // التحقق من وجود مبيعات مجمعة
        if (!processedData.groupedSales || processedData.groupedSales.length === 0) {
          console.warn('لا توجد مبيعات مجمعة في البيانات المستلمة');

          // إنشاء مبيعات مجمعة افتراضية إذا كان هناك مبيعات ولكن لا توجد مبيعات مجمعة
          if (sales.length > 0) {
            console.log('إنشاء مبيعات مجمعة افتراضية من المبيعات المتاحة');

            try {
              // إنشاء فاتورة واحدة تحتوي على جميع المبيعات
              const defaultInvoice = {
                invoice_number: `INV-CL-${customerId.toString().padStart(3, '0')}`,
                items: [...sales], // استخدام نسخة من المبيعات
                transaction_date: sales[0].transaction_date || new Date().toISOString(),
                notes: 'فاتورة مجمعة لمشتريات العميل'
              };

              console.log('الفاتورة الافتراضية:', defaultInvoice);
              console.log('عدد الأصناف في الفاتورة الافتراضية:', defaultInvoice.items.length);

              // التحقق من صحة الفاتورة
              if (!defaultInvoice.items || !Array.isArray(defaultInvoice.items)) {
                console.error('خطأ: الأصناف في الفاتورة الافتراضية ليست مصفوفة');
                defaultInvoice.items = [];
              }

              processedData.groupedSales = [defaultInvoice];
              processedData.invoiceCount = 1;

              console.log('تم إنشاء مبيعات مجمعة افتراضية:', processedData.groupedSales);
              console.log('عدد المبيعات المجمعة بعد الإنشاء:', processedData.groupedSales.length);
            } catch (groupingError) {
              console.error('خطأ في إنشاء المبيعات المجمعة الافتراضية:', groupingError);

              // إنشاء فاتورة فارغة في حالة الخطأ
              processedData.groupedSales = [{
                invoice_number: `INV-CL-${customerId.toString().padStart(3, '0')}`,
                items: [],
                transaction_date: new Date().toISOString(),
                notes: 'فاتورة فارغة (حدث خطأ في إنشاء المبيعات المجمعة)'
              }];
              processedData.invoiceCount = 1;
            }
          }
        }

        console.log('البيانات المعالجة:', processedData);
        console.log('المبيعات المجمعة:', processedData.groupedSales);

        // التحقق النهائي من البيانات المعالجة
        console.log('التحقق النهائي من البيانات المعالجة قبل تعيينها');

        // التحقق من وجود العميل
        if (!processedData.customer) {
          console.error('خطأ: العميل غير موجود في البيانات المعالجة');
          processedData.customer = { name: 'عميل غير معروف', id: customerId };
        }

        // التحقق من وجود المبيعات
        if (!processedData.salesHistory || !Array.isArray(processedData.salesHistory)) {
          console.error('خطأ: المبيعات غير موجودة أو ليست مصفوفة في البيانات المعالجة');
          processedData.salesHistory = [];
        }

        // التحقق من وجود المبيعات المجمعة
        if (!processedData.groupedSales || !Array.isArray(processedData.groupedSales)) {
          console.error('خطأ: المبيعات المجمعة غير موجودة أو ليست مصفوفة في البيانات المعالجة');
          processedData.groupedSales = [];
        }

        // تعيين البيانات
        console.log('تعيين البيانات المعالجة في حالة المكون');
        setSalesData(processedData);
        setError(null);

        // تحديث الفواتير المفلترة مباشرة بعد تحميل البيانات
        if (processedData.groupedSales && processedData.groupedSales.length > 0) {
          console.log('تحديث الفواتير المفلترة من المبيعات المجمعة');
          const invoiceGroups = processedData.groupedSales.map(invoice => {
            const items = Array.isArray(invoice.items) ? invoice.items : [];
            let totalAmount = 0;
            let totalProfit = 0;

            items.forEach(item => {
              totalAmount += Number(item.total_price) || 0;
              totalProfit += Number(item.profit) || 0;
            });

            return {
              invoiceNumber: invoice.invoice_number,
              items: items,
              totalAmount: totalAmount,
              totalProfit: totalProfit,
              transaction_date: invoice.transaction_date,
              notes: invoice.notes || '-'
            };
          });

          setFilteredInvoices(invoiceGroups);

          // تحديث قائمة الفواتير المتاحة للتصفية
          if (invoiceGroups.length > 0) {
            const uniqueInvoices = [...new Set(invoiceGroups.map(invoice => invoice.invoiceNumber))];
            console.log('الفواتير الفريدة المتاحة:', uniqueInvoices);
          }
        }
      } catch (err) {
        console.error('Error loading sales history:', err);
        console.error('رسالة الخطأ:', err.message);
        console.error('مكدس الخطأ:', err.stack);

        setError(err.message || 'حدث خطأ أثناء تحميل سجل المبيعات');

        // تعيين بيانات فارغة في حالة الخطأ
        const defaultData = {
          customer: { name: 'عميل غير معروف', id: customerId },
          salesHistory: [],
          groupedSales: [],
          totalSales: 0,
          totalProfit: 0,
          invoiceCount: 0
        };

        console.log('تعيين بيانات افتراضية في حالة الخطأ:', defaultData);
        setSalesData(defaultData);

        // تحديث الفواتير المفلترة
        setFilteredInvoices([]);
      } finally {
        console.log('انتهاء تحميل سجل المبيعات');
        setLoading(false);
      }
    };

    loadSalesHistory();
  }, [customerId, getCustomerSalesHistory]);

  if (loading) {
    return (
      <div className="modal-backdrop">
        <div className="modal-container sales-history-container">
          <div className="modal-header">
            <h3>
              <span className="header-icon"><FaHistory /></span>
              <span className="header-text">سجل مبيعات العميل</span>
            </h3>
            <button className="close-btn" onClick={onClose} title="إغلاق">
              <FaTimes />
            </button>
          </div>
          <div className="modal-body">
            <div className="loading-state">
              <div className="loading-spinner"></div>
              <h3 className="loading-title">جاري تحميل سجل المبيعات</h3>
              <p className="loading-description">يرجى الانتظار قليلاً...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="modal-backdrop">
        <div className="modal-container sales-history-container">
          <div className="modal-header">
            <h3>
              <span className="header-icon"><FaHistory /></span>
              <span className="header-text">سجل مبيعات العميل</span>
            </h3>
            <button className="close-btn" onClick={onClose} title="إغلاق">
              <FaTimes />
            </button>
          </div>
          <div className="modal-body">
            <div className="error-state">
              <div className="error-icon">
                <FaExclamationTriangle />
              </div>
              <h3 className="error-title">حدث خطأ أثناء تحميل البيانات</h3>
              <p className="error-description">{error}</p>
              <button className="retry-button" onClick={() => window.location.reload()}>
                إعادة المحاولة
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!salesData || !salesData.customer) {
    return (
      <div className="modal-backdrop">
        <div className="modal-container sales-history-container">
          <div className="modal-header">
            <h3>سجل مبيعات العميل</h3>
            <button className="close-btn" onClick={onClose}>
              <FaTimes />
            </button>
          </div>
          <div className="modal-body">
            <div className="empty-state">
              <div className="empty-icon">
                <FaShoppingCart />
              </div>
              <h3 className="empty-title">لا توجد بيانات متاحة</h3>
              <p className="empty-description">لا توجد بيانات متاحة لهذا العميل في الوقت الحالي</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const { customer, totalSales, totalProfit } = salesData;

  // تم نقل كود تجميع الفواتير إلى useEffect

  // لا نحتاج إلى هذا الكود بعد الآن لأننا نستخدم useEffect لتطبيق الفلترة



  // طباعة فاتورة تحتوي على جميع الأصناف
  const printInvoiceWithItems = (invoiceGroup) => {
    try {
      console.log('طباعة الفاتورة مع الأصناف:', invoiceGroup);

      // التحقق من وجود الأصناف
      if (!invoiceGroup.items || !Array.isArray(invoiceGroup.items) || invoiceGroup.items.length === 0) {
        console.error('لا توجد أصناف في الفاتورة:', invoiceGroup);
        alert('لا توجد أصناف في هذه الفاتورة');
        return;
      }

      try {
        // استخدام وظائف الطباعة المباشرة من ملف printInvoice
        const printInvoice = require('../utils/printInvoice').default;

        if (typeof printInvoice !== 'function') {
          console.error('وظيفة طباعة الفاتورة غير متوفرة');
          alert('وظيفة طباعة الفاتورة غير متوفرة. يرجى التحقق من تثبيت المكتبات اللازمة.');
          return;
        }
        // حساب إجمالي المبلغ
        const totalAmount = invoiceGroup.items.reduce((sum, item) => sum + (Number(item.total_price) || 0), 0);

        // حساب إجمالي الربح
        const totalProfit = invoiceGroup.items.reduce((sum, item) => sum + (Number(item.profit) || 0), 0);

        // تحضير بيانات الفاتورة
        const invoiceData = {
          invoice_number: invoiceGroup.invoiceNumber || invoiceGroup.invoice_number || `C-${customer.id}`,
          transaction_date: new Date().toISOString(),
          customer_name: customer.name,
          customer_phone: customer.phone || '',
          customer_address: customer.address || '',
          items: invoiceGroup.items.map(item => ({
            item_name: item.item_name || 'صنف غير معروف',
            quantity: Number(item.quantity) || 0,
            price: Number(item.price) || 0,
            total_price: Number(item.total_price) || 0
          })),
          total_price: totalAmount,
          total_profit: totalProfit,
          notes: 'فاتورة مجمعة لجميع مشتريات العميل'
        };

        // إعدادات الشركة
        const companySettings = {
          systemName: window.appSettings?.companyName || 'شركة اتش قروب',
          address: window.appSettings?.companyAddress || 'للتصميم و تصنيع الأثاث والديكورات',
          phone: window.appSettings?.companyPhone || '',
          logoUrl: window.appSettings?.logoUrl || ''
        };

        // طباعة الفاتورة
        printInvoice(invoiceData, customer, companySettings);

        // تسجيل البيانات للتشخيص
        console.log('بيانات الفاتورة للطباعة:', invoiceData);
        console.log('بيانات العميل للطباعة:', customer);
        console.log('إعدادات الشركة:', companySettings);
      } catch (printError) {
        console.error('خطأ في استدعاء وظيفة طباعة الفاتورة:', printError);
        alert('حدث خطأ في وظيفة طباعة الفاتورة. يرجى التحقق من وجود الملفات اللازمة.');
      }
    } catch (error) {
      console.error('خطأ في طباعة الفاتورة:', error);
      alert('حدث خطأ أثناء محاولة طباعة الفاتورة. يرجى المحاولة مرة أخرى.');
    }
  };

  return (
    <div className="modal-backdrop">
      <div className="modal-container sales-history-container">
        <div className="modal-header">
          <h3>
            <span className="header-icon"><FaHistory /></span>
            <span className="header-text">سجل مبيعات العميل: {customer.name}</span>
          </h3>
          <button className="close-btn" onClick={onClose} title="إغلاق">
            <FaTimes />
          </button>
        </div>
        <div className="modal-body">
          {/* ملخص المبيعات */}
          <div className="sales-summary">
            <div className="row">
              <div className="col-md-4">
                <div className="summary-card items-card">
                  <div className="card-body">
                    <div className="card-icon">
                      <FaShoppingCart />
                    </div>
                    <h5 className="card-title">عدد الأصناف</h5>
                    <p className="card-text">
                      {filteredInvoices.length > 0 && filteredInvoices[0].items ? filteredInvoices[0].items.length : 0}
                    </p>
                  </div>
                </div>
              </div>
              <div className="col-md-4">
                <div className="summary-card sales-card">
                  <div className="card-body">
                    <div className="card-icon">
                      <FaMoneyBillWave />
                    </div>
                    <h5 className="card-title">إجمالي المبيعات</h5>
                    <p className="card-text">
                      {totalSales.toFixed(2)} د ل
                    </p>
                  </div>
                </div>
              </div>
              <div className="col-md-4">
                <div className="summary-card profit-card">
                  <div className="card-body">
                    <div className="card-icon">
                      <FaChartLine />
                    </div>
                    <h5 className="card-title">إجمالي الربح</h5>
                    <p className="card-text">
                      {totalProfit.toFixed(2)} د ل
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* أزرار التحكم العلوية */}
          <div className="control-bar">
            <div className="customer-info">
              <h4 className="section-title">
                <span className="section-icon"><FaHistory /></span>
                <span>سجل مشتريات العميل</span>
              </h4>
              <div className="customer-details">
                <span className="customer-name">{customer.name}</span>
                {customer.phone && <span className="customer-phone"><FaPhone /> {customer.phone}</span>}
              </div>
            </div>
            <div className="action-buttons">
              {filteredInvoices.length > 0 && filteredInvoices[0].items && filteredInvoices[0].items.length > 0 && (
                <button className="btn-print" onClick={() => printInvoiceWithItems(filteredInvoices[0])}>
                  <span className="button-icon"><FaPrint /></span>
                  <span>طباعة الفاتورة</span>
                </button>
              )}
              <button className="btn-close" onClick={onClose}>
                <span className="button-icon"><FaTimes /></span>
                <span>إغلاق</span>
              </button>
            </div>
          </div>

          {/* جدول الفواتير */}
          {filteredInvoices.length > 0 && filteredInvoices[0].items && filteredInvoices[0].items.length > 0 ? (
            <div className="table-container">
              {/* تقسيم الفواتير إلى فواتير رئيسية وفرعية */}
              {(() => {
                // تجميع الأصناف حسب نوع الفاتورة (رئيسية/فرعية)
                const mainInvoiceItems = [];
                const subInvoiceItems = [];

                // فحص كل صنف وتصنيفه
                filteredInvoices[0].items.forEach(item => {
                  if (item.parent_invoice_number && item.parent_invoice_number !== item.invoice_number) {
                    subInvoiceItems.push(item);
                  } else {
                    mainInvoiceItems.push(item);
                  }
                });

                console.log('عدد أصناف الفواتير الرئيسية:', mainInvoiceItems.length);
                console.log('عدد أصناف الفواتير الفرعية:', subInvoiceItems.length);

                return (
                  <>
                    {/* عرض الفاتورة الرئيسية */}
                    {mainInvoiceItems.length > 0 && (
                      <div>
                        <div style={{
                          marginBottom: '25px',
                          backgroundColor: 'white',
                          padding: '20px 25px',
                          borderRadius: '16px',
                          boxShadow: '0 5px 15px rgba(0, 0, 0, 0.05)',
                          border: '1px solid rgba(0, 0, 0, 0.05)',
                          position: 'relative',
                          overflow: 'hidden'
                        }}>
                          <div className="invoice-info">
                            <h4 className="invoice-title">
                              <span style={{
                                display: 'inline-flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: '36px',
                                height: '36px',
                                borderRadius: '50%',
                                backgroundColor: 'rgba(243, 156, 18, 0.1)',
                                color: '#f39c12',
                                marginLeft: '10px',
                                fontSize: '18px'
                              }}>
                                <FaReceipt />
                              </span>
                              <span>
                                فاتورة رقم: {filteredInvoices[0].invoice_number || filteredInvoices[0].invoiceNumber}
                              </span>
                            </h4>
                            <div className="invoice-date">
                              <span className="date-label">التاريخ: </span>
                              <span className="date-value">{formatDate(filteredInvoices[0].transaction_date)}</span>
                            </div>
                          </div>
                        </div>

                        <table className="data-table">
                          <thead>
                            <tr>
                              <th className="text-center">#</th>
                              <th><span className="th-icon"><FaBoxOpen /></span> الصنف</th>
                              <th className="text-center">الكمية</th>
                              <th className="text-center">السعر (د ل)</th>
                              <th className="text-center">الإجمالي (د ل)</th>
                              <th className="text-center profit-column">الربح (د ل)</th>
                              <th><span className="th-icon"><FaCalendarAlt /></span> التاريخ</th>
                            </tr>
                          </thead>
                          <tbody>
                            {mainInvoiceItems.map((item, index) => {
                              // التأكد من أن قيم الصنف صحيحة
                              const quantity = Number(item.quantity) || 0;
                              const price = Number(item.price) || 0;
                              const totalPrice = Number(item.total_price) || (quantity * price);
                              const profit = Number(item.profit) || 0;
                              const isProfitNegative = profit < 0;

                              return (
                                <tr
                                  key={`main-item-${index}`}
                                  className="fade-in"
                                  style={{ animationDelay: `${index * 0.05}s` }}
                                >
                                  <td className="text-center">{index + 1}</td>
                                  <td>
                                    <strong>{item.item_name || 'صنف غير معروف'}</strong>
                                  </td>
                                  <td className="text-center">{quantity}</td>
                                  <td className="text-center">{price.toFixed(2)}</td>
                                  <td className="text-center"><strong>{totalPrice.toFixed(2)}</strong></td>
                                  <td className={`text-center profit-value profit-column ${isProfitNegative ? 'negative' : ''}`}>
                                    <strong>{profit.toFixed(2)}</strong>
                                  </td>
                                  <td className="text-center">{formatDate(item.transaction_date)}</td>
                                </tr>
                              );
                            })}
                          </tbody>
                          <tfoot>
                            <tr className="total-row">
                              <td colSpan="4" style={{ textAlign: 'right' }}>
                                <strong>إجمالي الفاتورة الرئيسية:</strong>
                              </td>
                              <td className="text-center">
                                <strong>
                                  {mainInvoiceItems.reduce((sum, item) => sum + (Number(item.total_price) || 0), 0).toFixed(2)} د ل
                                </strong>
                              </td>
                              <td className="text-center profit-column">
                                <strong>
                                  {mainInvoiceItems.reduce((sum, item) => sum + (Number(item.profit) || 0), 0).toFixed(2)} د ل
                                </strong>
                              </td>
                              <td></td>
                            </tr>
                          </tfoot>
                        </table>
                      </div>
                    )}

                    {/* عرض الفواتير الفرعية */}
                    {subInvoiceItems.length > 0 && (
                      <div style={{ marginTop: '30px' }}>
                        <div style={{
                          marginBottom: '25px',
                          backgroundColor: 'rgba(52, 152, 219, 0.05)',
                          padding: '20px 25px',
                          borderRadius: '16px',
                          boxShadow: '0 5px 15px rgba(0, 0, 0, 0.05)',
                          border: '1px solid rgba(0, 0, 0, 0.05)',
                          borderRight: '4px solid #3498db',
                          position: 'relative',
                          overflow: 'hidden'
                        }}>
                          <div className="invoice-info">
                            <h4 className="invoice-title">
                              <span style={{
                                display: 'inline-flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: '36px',
                                height: '36px',
                                borderRadius: '50%',
                                backgroundColor: 'rgba(52, 152, 219, 0.1)',
                                color: '#3498db',
                                marginLeft: '10px',
                                fontSize: '18px'
                              }}>
                                <FaFileInvoice />
                              </span>
                              <span>
                                الفواتير الفرعية
                              </span>
                              <span style={{
                                display: 'inline-block',
                                marginRight: '10px',
                                fontSize: '14px',
                                color: '#3498db'
                              }}>
                                (تابعة للفاتورة الرئيسية: {subInvoiceItems[0].parent_invoice_number})
                              </span>
                            </h4>
                          </div>
                        </div>

                        <table className="data-table">
                          <thead>
                            <tr>
                              <th className="text-center">#</th>
                              <th><span className="th-icon"><FaBoxOpen /></span> الصنف</th>
                              <th className="text-center">رقم الفاتورة الفرعية</th>
                              <th className="text-center">الكمية</th>
                              <th className="text-center">السعر (د ل)</th>
                              <th className="text-center">الإجمالي (د ل)</th>
                              <th><span className="th-icon"><FaCalendarAlt /></span> التاريخ</th>
                            </tr>
                          </thead>
                          <tbody>
                            {subInvoiceItems.map((item, index) => {
                              // التأكد من أن قيم الصنف صحيحة
                              const quantity = Number(item.quantity) || 0;
                              const price = Number(item.price) || 0;
                              const totalPrice = Number(item.total_price) || (quantity * price);

                              return (
                                <tr
                                  key={`sub-item-${index}`}
                                  className="fade-in"
                                  style={{
                                    animationDelay: `${index * 0.05}s`,
                                    backgroundColor: 'rgba(52, 152, 219, 0.05)',
                                    borderRight: '3px solid #3498db'
                                  }}
                                >
                                  <td className="text-center">{index + 1}</td>
                                  <td>
                                    <div
                                      style={{
                                        display: 'inline-block',
                                        marginLeft: '5px',
                                        padding: '2px 5px',
                                        fontSize: '10px',
                                        backgroundColor: '#3498db',
                                        color: 'white',
                                        borderRadius: '3px',
                                        verticalAlign: 'middle'
                                      }}
                                    >
                                      <FaFileInvoice style={{ marginLeft: '3px', fontSize: '10px' }} />
                                      فرعية
                                    </div>
                                    <strong>{item.item_name || 'صنف غير معروف'}</strong>
                                  </td>
                                  <td className="text-center">{item.invoice_number}</td>
                                  <td className="text-center">{quantity}</td>
                                  <td className="text-center">{price.toFixed(2)}</td>
                                  <td className="text-center"><strong>{totalPrice.toFixed(2)}</strong></td>
                                  <td className="text-center">{formatDate(item.transaction_date)}</td>
                                </tr>
                              );
                            })}
                          </tbody>
                          <tfoot>
                            <tr className="total-row">
                              <td colSpan="5" style={{ textAlign: 'right' }}>
                                <strong>إجمالي الفواتير الفرعية:</strong>
                              </td>
                              <td className="text-center">
                                <strong>
                                  {subInvoiceItems.reduce((sum, item) => sum + (Number(item.total_price) || 0), 0).toFixed(2)} د ل
                                </strong>
                              </td>
                              <td></td>
                            </tr>
                          </tfoot>
                        </table>
                      </div>
                    )}

                    {/* إجمالي جميع الفواتير */}
                    <div style={{ marginTop: '30px' }}>
                      <table className="data-table">
                        <tfoot>
                          <tr className="total-row" style={{ backgroundColor: '#f8f9fa' }}>
                            <td colSpan="4" style={{ textAlign: 'right', padding: '20px' }}>
                              <strong style={{ fontSize: '18px' }}>الإجمالي الكلي:</strong>
                            </td>
                            <td className="text-center" style={{ padding: '20px' }}>
                              <strong style={{ fontSize: '18px' }}>{totalSales.toFixed(2)} د ل</strong>
                            </td>
                            <td className="text-center profit-column" style={{ padding: '20px' }}>
                              <strong style={{ fontSize: '18px' }}>{totalProfit.toFixed(2)} د ل</strong>
                            </td>
                            <td></td>
                          </tr>
                        </tfoot>
                      </table>
                    </div>
                  </>
                );
              })()}
            </div>
          ) : (
            <div className="empty-state">
              <div className="empty-icon">
                <FaShoppingCart />
              </div>
              <h3 className="empty-title">لا توجد مبيعات مسجلة</h3>
              <p className="empty-description">عندما تقوم بإجراء عمليات بيع لهذا العميل، ستظهر هنا</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CustomerSalesHistory;


