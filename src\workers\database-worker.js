/**
 * Worker Thread لتنفيذ استعلامات قاعدة البيانات
 * يتم استخدامه لتنفيذ استعلامات قاعدة البيانات في خلفية التطبيق
 * دون التأثير على واجهة المستخدم
 */

const { parentPort, workerData } = require('worker_threads');
const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// متغير لتخزين اتصال قاعدة البيانات
let db = null;

// استقبال الرسائل من العملية الرئيسية
parentPort.on('message', (message) => {
  const { id, type, data } = message;

  try {
    let result;

    // تنفيذ العملية المطلوبة بناءً على النوع
    switch (type) {
      case 'initialize':
        result = initializeDatabase(data);
        break;
      case 'getItems':
        result = getItems(data);
        break;
      case 'getItemsPaginated':
        result = getItemsPaginated(data);
        break;
      case 'addItem':
        result = addItem(data);
        break;
      case 'updateItem':
        result = updateItem(data);
        break;
      case 'deleteItem':
        result = deleteItem(data);
        break;
      case 'getInventory':
        result = getInventory(data);
        break;
      case 'updateInventory':
        result = updateInventory(data);
        break;
      case 'getTransactions':
        result = getTransactions(data);
        break;
      case 'getTransactionsPaginated':
        result = getTransactionsPaginated(data);
        break;
      case 'addTransaction':
        result = addTransaction(data);
        break;
      case 'getCustomers':
        result = getCustomers(data);
        break;
      case 'getCustomersPaginated':
        result = getCustomersPaginated(data);
        break;
      case 'addCustomer':
        result = addCustomer(data);
        break;
      case 'updateCustomer':
        result = updateCustomer(data);
        break;
      case 'deleteCustomer':
        result = deleteCustomer(data);
        break;
      case 'executeQuery':
        result = executeQuery(data);
        break;
      default:
        throw new Error(`نوع العملية غير معروف: ${type}`);
    }

    // إرسال النتيجة إلى العملية الرئيسية
    parentPort.postMessage({
      id,
      type,
      result,
      error: null
    });
  } catch (error) {
    console.error(`خطأ في Worker Thread أثناء تنفيذ ${type}:`, error);

    // إرسال الخطأ إلى العملية الرئيسية
    parentPort.postMessage({
      id,
      type,
      result: null,
      error: error.message
    });
  }
});

/**
 * تهيئة قاعدة البيانات
 * @param {Object} data - بيانات التهيئة
 * @returns {Object} - نتيجة التهيئة
 */
function initializeDatabase(data) {
  try {
    const { dbPath } = data;

    // التحقق من وجود المسار
    if (!dbPath) {
      throw new Error('لم يتم تحديد مسار قاعدة البيانات');
    }

    // التحقق من وجود المجلد
    const dbDir = path.dirname(dbPath);
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
    }

    // إنشاء اتصال بقاعدة البيانات
    db = new Database(dbPath, { verbose: console.log });

    // تمكين الدعم للمفاتيح الأجنبية
    db.pragma('foreign_keys = ON');

    return { success: true, message: 'تم تهيئة قاعدة البيانات بنجاح' };
  } catch (error) {
    console.error('خطأ في تهيئة قاعدة البيانات:', error);
    throw error;
  }
}

/**
 * تنفيذ استعلام SQL
 * @param {Object} data - بيانات الاستعلام
 * @returns {Object} - نتيجة الاستعلام
 */
function executeQuery(data) {
  try {
    const { sql, params, type } = data;

    // التحقق من وجود اتصال بقاعدة البيانات
    if (!db) {
      throw new Error('لم يتم تهيئة قاعدة البيانات');
    }

    // تنفيذ الاستعلام بناءً على النوع
    switch (type) {
      case 'run':
        return db.prepare(sql).run(...(params || []));
      case 'get':
        return db.prepare(sql).get(...(params || []));
      case 'all':
        return db.prepare(sql).all(...(params || []));
      case 'exec':
        return db.exec(sql);
      default:
        throw new Error(`نوع الاستعلام غير معروف: ${type}`);
    }
  } catch (error) {
    console.error('خطأ في تنفيذ الاستعلام:', error);
    throw error;
  }
}

// تنفيذ استعلامات الأصناف
function getItems() {
  try {
    // التحقق من وجود اتصال بقاعدة البيانات
    if (!db) {
      throw new Error('لم يتم تهيئة قاعدة البيانات');
    }

    // استعلام للحصول على الأصناف مع معلومات المخزون
    const stmt = db.prepare(`
      SELECT
        i.*,
        COALESCE(inv.current_quantity, 0) as current_quantity,
        i.minimum_quantity as item_minimum_quantity,
        inv.minimum_quantity as inv_minimum_quantity,
        COALESCE(inv.minimum_quantity, i.minimum_quantity, 0) as minimum_quantity,
        i.avg_price as item_avg_price,
        inv.avg_price as inv_avg_price,
        COALESCE(inv.avg_price, i.avg_price, 0) as avg_price,
        i.selling_price as item_selling_price,
        inv.selling_price as inv_selling_price,
        COALESCE(inv.selling_price, i.selling_price, 0) as selling_price
      FROM items i
      LEFT JOIN inventory inv ON i.id = inv.item_id
      ORDER BY i.name ASC
    `);

    // الحصول على البيانات
    const items = stmt.all();

    // تسجيل معلومات تشخيصية للأصناف
    for (const item of items) {
      console.log(`معلومات الصنف ${item.id} (${item.name}) من getItems:`, {
        minimum_quantity: item.minimum_quantity,
        avg_price: item.avg_price,
        selling_price: item.selling_price,
        item_minimum_quantity: item.item_minimum_quantity,
        inv_minimum_quantity: item.inv_minimum_quantity,
        minimum_quantity_type: typeof item.minimum_quantity
      });
    }

    return items;
  } catch (error) {
    console.error('خطأ في الحصول على الأصناف:', error);
    throw error;
  }
}

// تنفيذ استعلامات المخزون
function getInventory() {
  try {
    // التحقق من وجود اتصال بقاعدة البيانات
    if (!db) {
      throw new Error('لم يتم تهيئة قاعدة البيانات');
    }

    // التحقق من وجود الأعمدة المطلوبة في جدول items
    try {
      // التحقق من وجود عمود avg_price
      const checkAvgPriceColumnStmt = db.prepare(`
        SELECT COUNT(*) as count FROM pragma_table_info('items') WHERE name = 'avg_price'
      `);
      const { count: avgPriceCount } = checkAvgPriceColumnStmt.get();

      if (avgPriceCount === 0) {
        console.log('جاري إضافة عمود avg_price إلى جدول items...');
        db.exec(`ALTER TABLE items ADD COLUMN avg_price REAL DEFAULT 0`);
        console.log('تم إضافة عمود avg_price إلى جدول items بنجاح');
      }

      // التحقق من وجود عمود selling_price
      const checkSellingPriceColumnStmt = db.prepare(`
        SELECT COUNT(*) as count FROM pragma_table_info('items') WHERE name = 'selling_price'
      `);
      const { count: sellingPriceCount } = checkSellingPriceColumnStmt.get();

      if (sellingPriceCount === 0) {
        console.log('جاري إضافة عمود selling_price إلى جدول items...');
        db.exec(`ALTER TABLE items ADD COLUMN selling_price REAL DEFAULT 0`);
        console.log('تم إضافة عمود selling_price إلى جدول items بنجاح');
      }

      // التحقق من وجود عمود minimum_quantity
      const checkMinimumQuantityColumnStmt = db.prepare(`
        SELECT COUNT(*) as count FROM pragma_table_info('items') WHERE name = 'minimum_quantity'
      `);
      const { count: minimumQuantityCount } = checkMinimumQuantityColumnStmt.get();

      if (minimumQuantityCount === 0) {
        console.log('جاري إضافة عمود minimum_quantity إلى جدول items...');
        db.exec(`ALTER TABLE items ADD COLUMN minimum_quantity INTEGER DEFAULT 0`);
        console.log('تم إضافة عمود minimum_quantity إلى جدول items بنجاح');
      }
    } catch (error) {
      console.error('خطأ في التحقق من وجود الأعمدة المطلوبة في جدول items:', error);
    }

    // استعلام للحصول على المخزون مع معلومات الأصناف
    // تحسين الاستعلام للتأكد من استرجاع البيانات من كلا الجدولين
    const stmt = db.prepare(`
      SELECT
        i.id,
        i.name,
        i.unit,
        i.minimum_quantity as item_minimum_quantity,
        i.avg_price as item_avg_price,
        i.selling_price as item_selling_price,
        inv.minimum_quantity as inv_minimum_quantity,
        inv.avg_price as inv_avg_price,
        inv.selling_price as inv_selling_price,
        COALESCE(inv.minimum_quantity, i.minimum_quantity, 0) as minimum_quantity,
        COALESCE(inv.avg_price, i.avg_price, 0) as avg_price,
        COALESCE(inv.selling_price, i.selling_price, 0) as selling_price,
        COALESCE(inv.current_quantity, i.current_quantity, 0) as current_quantity,
        COALESCE(inv.last_updated, i.updated_at, i.created_at) as last_updated,
        inv.item_id as inventory_item_id
      FROM items i
      LEFT JOIN inventory inv ON i.id = inv.item_id
      ORDER BY i.name ASC
    `);

    // الحصول على البيانات
    const inventory = stmt.all();

    // مزامنة البيانات بين جدولي items و inventory
    syncItemsWithInventory(inventory);

    // تحديث البيانات في الذاكرة
    for (const item of inventory) {
      // تسجيل معلومات تشخيصية للصنف قبل التحديث
      console.log(`معلومات الصنف ${item.id} (${item.name}) قبل التحديث:`, {
        avg_price: item.avg_price,
        selling_price: item.selling_price,
        minimum_quantity: item.minimum_quantity,
        current_quantity: item.current_quantity,
        avg_price_type: typeof item.avg_price,
        selling_price_type: typeof item.selling_price,
        minimum_quantity_type: typeof item.minimum_quantity,
        current_quantity_type: typeof item.current_quantity
      });

      // التأكد من أن جميع الحقول لها قيم صالحة
      item.avg_price = typeof item.avg_price === 'number' ? item.avg_price : 0;
      item.selling_price = typeof item.selling_price === 'number' ? item.selling_price : 0;
      item.minimum_quantity = typeof item.minimum_quantity === 'number' ? item.minimum_quantity : 0;
      item.current_quantity = typeof item.current_quantity === 'number' ? item.current_quantity : 0;

      // تسجيل معلومات تشخيصية للصنف بعد التحديث
      console.log(`معلومات الصنف ${item.id} (${item.name}) بعد التحديث:`, {
        avg_price: item.avg_price,
        selling_price: item.selling_price,
        minimum_quantity: item.minimum_quantity,
        current_quantity: item.current_quantity
      });
    }

    console.log(`تم الحصول على ${inventory.length} صنف في المخزون بنجاح`);
    return inventory;
  } catch (error) {
    console.error('خطأ في الحصول على المخزون:', error);
    throw error;
  }
}

// وظيفة مساعدة لمزامنة البيانات بين جدولي items و inventory
function syncItemsWithInventory(inventoryItems) {
  try {
    // التحقق من وجود اتصال بقاعدة البيانات
    if (!db) {
      throw new Error('لم يتم تهيئة قاعدة البيانات');
    }

    // بدء معاملة قاعدة البيانات
    db.exec('BEGIN TRANSACTION');

    try {
      // مزامنة كل صنف
      for (const item of inventoryItems) {
        const itemId = parseInt(item.id);

        // التحقق من وجود سجل في المخزون
        const getInventoryStmt = db.prepare('SELECT * FROM inventory WHERE item_id = ?');
        const inventory = getInventoryStmt.get(itemId);

        // الحصول على الوقت الحالي
        const now = new Date().toISOString();

        if (inventory) {
          // الاحتفاظ بمتوسط السعر وسعر البيع والحد الأدنى الحاليين
          // استخدام القيم من المخزون أولاً، ثم من الصنف إذا كانت غير موجودة
          const avgPrice = (inventory.avg_price !== null && inventory.avg_price !== undefined) ? inventory.avg_price :
                          ((item.avg_price !== null && item.avg_price !== undefined) ? item.avg_price : 0);

          const sellingPrice = (inventory.selling_price !== null && inventory.selling_price !== undefined) ? inventory.selling_price :
                              ((item.selling_price !== null && item.selling_price !== undefined) ? item.selling_price : 0);

          const minimumQuantity = (inventory.minimum_quantity !== null && inventory.minimum_quantity !== undefined) ? inventory.minimum_quantity :
                                 ((item.minimum_quantity !== null && item.minimum_quantity !== undefined) ? item.minimum_quantity : 0);

          try {
            // تحديث سجل المخزون الموجود مع الاحتفاظ بمتوسط السعر وسعر البيع والحد الأدنى
            const updateStmt = db.prepare(`
              UPDATE inventory
              SET avg_price = ?, selling_price = ?, minimum_quantity = ?, last_updated = ?
              WHERE item_id = ?
            `);

            updateStmt.run(
              avgPrice,
              sellingPrice,
              minimumQuantity,
              now,
              itemId
            );

            console.log(`تم تحديث سجل المخزون للصنف ${itemId} بنجاح: متوسط السعر=${avgPrice}, سعر البيع=${sellingPrice}, الحد الأدنى=${minimumQuantity}`);
          } catch (updateError) {
            console.error(`خطأ في تحديث سجل المخزون للصنف ${itemId}:`, updateError);
            // استمر في المزامنة حتى مع وجود خطأ في تحديث سجل المخزون
          }

          try {
            // تحديث الصنف أيضًا للتأكد من تطابق البيانات مع متوسط السعر وسعر البيع والحد الأدنى
            const updateItemStmt = db.prepare(`
              UPDATE items
              SET avg_price = ?, selling_price = ?, minimum_quantity = ?, updated_at = ?
              WHERE id = ?
            `);

            updateItemStmt.run(
              avgPrice,
              sellingPrice,
              minimumQuantity,
              now,
              itemId
            );

            console.log(`تم تحديث الصنف ${itemId} بنجاح: متوسط السعر=${avgPrice}, سعر البيع=${sellingPrice}, الحد الأدنى=${minimumQuantity}`);
          } catch (updateItemError) {
            console.error(`خطأ في تحديث الصنف ${itemId}:`, updateItemError);
            // استمر في المزامنة حتى مع وجود خطأ في تحديث الصنف
          }

          // تحديث البيانات في الذاكرة أيضًا
          item.avg_price = avgPrice;
          item.selling_price = sellingPrice;
          item.minimum_quantity = minimumQuantity;
        } else {
          // إنشاء سجل مخزون جديد
          // استخدام متوسط السعر وسعر البيع والحد الأدنى من الصنف إذا كانت متوفرة
          const avgPrice = (item.avg_price !== null && item.avg_price !== undefined) ? item.avg_price : 0;
          const sellingPrice = (item.selling_price !== null && item.selling_price !== undefined) ? item.selling_price : 0;
          const minimumQuantity = (item.minimum_quantity !== null && item.minimum_quantity !== undefined) ? item.minimum_quantity : 0;

          try {
            const insertStmt = db.prepare(`
              INSERT INTO inventory (
                item_id,
                current_quantity,
                minimum_quantity,
                avg_price,
                selling_price,
                last_updated
              )
              VALUES (?, ?, ?, ?, ?, ?)
            `);

            insertStmt.run(
              itemId,
              item.current_quantity || 0,
              minimumQuantity,
              avgPrice,
              sellingPrice,
              now
            );

            console.log(`تم إنشاء سجل مخزون جديد للصنف ${itemId} بنجاح: متوسط السعر=${avgPrice}, سعر البيع=${sellingPrice}, الحد الأدنى=${minimumQuantity}`);
          } catch (insertError) {
            console.error(`خطأ في إنشاء سجل مخزون جديد للصنف ${itemId}:`, insertError);
            continue; // تخطي هذا الصنف والانتقال إلى الصنف التالي
          }

          try {
            // تحديث الصنف أيضًا للتأكد من تطابق البيانات
            const updateItemStmt = db.prepare(`
              UPDATE items
              SET avg_price = ?, selling_price = ?, minimum_quantity = ?, updated_at = ?
              WHERE id = ?
            `);

            updateItemStmt.run(
              avgPrice,
              sellingPrice,
              minimumQuantity,
              now,
              itemId
            );

            console.log(`تم تحديث الصنف ${itemId} بنجاح: متوسط السعر=${avgPrice}, سعر البيع=${sellingPrice}, الحد الأدنى=${minimumQuantity}`);
          } catch (updateItemError) {
            console.error(`خطأ في تحديث الصنف ${itemId}:`, updateItemError);
            // استمر في المزامنة حتى مع وجود خطأ في تحديث الصنف
          }

          // تحديث البيانات في الذاكرة أيضًا
          item.avg_price = avgPrice;
          item.selling_price = sellingPrice;
          item.minimum_quantity = minimumQuantity;
        }
      }

      // إنهاء المعاملة
      db.exec('COMMIT');
      console.log('تمت مزامنة البيانات بين جدولي items و inventory بنجاح');
      return true;
    } catch (error) {
      // التراجع عن المعاملة في حالة حدوث خطأ
      db.exec('ROLLBACK');
      console.error('خطأ في مزامنة البيانات بين جدولي items و inventory:', error);
      return false;
    }
  } catch (error) {
    console.error('خطأ في مزامنة البيانات بين جدولي items و inventory:', error);
    return false;
  }
}

// تنفيذ استعلامات المعاملات
function getTransactions(filters) {
  try {
    // التحقق من وجود اتصال بقاعدة البيانات
    if (!db) {
      throw new Error('لم يتم تهيئة قاعدة البيانات');
    }

    // بناء استعلام SQL مع معايير التصفية
    let sql = `
      SELECT
        t.*,
        i.name as item_name,
        i.unit as item_unit,
        inv.avg_price as item_avg_price,
        inv.selling_price as item_selling_price,
        inv.minimum_quantity as minimum_quantity,
        u.username as user_name
      FROM transactions t
      LEFT JOIN items i ON t.item_id = i.id
      LEFT JOIN inventory inv ON t.item_id = inv.item_id
      LEFT JOIN users u ON t.user_id = u.id
    `;

    // إضافة شروط التصفية إذا وجدت
    const params = [];
    const conditions = [];

    if (filters) {
      // تصفية حسب نوع المعاملة
      if (filters.transaction_type) {
        conditions.push('t.transaction_type = ?');
        params.push(filters.transaction_type);
      }

      // تصفية حسب التاريخ
      if (filters.start_date) {
        conditions.push('t.transaction_date >= ?');
        params.push(filters.start_date);
      }

      if (filters.end_date) {
        conditions.push('t.transaction_date <= ?');
        params.push(filters.end_date);
      }

      // تصفية حسب الصنف
      if (filters.item_id) {
        conditions.push('t.item_id = ?');
        params.push(filters.item_id);
      }

      // تصفية حسب العميل
      if (filters.customer) {
        conditions.push('t.customer LIKE ?');
        params.push(`%${filters.customer}%`);
      }
    }

    // إضافة شروط التصفية إلى الاستعلام
    if (conditions.length > 0) {
      sql += ' WHERE ' + conditions.join(' AND ');
    }

    // إضافة ترتيب
    sql += ' ORDER BY t.transaction_date DESC';

    // تنفيذ الاستعلام
    const stmt = db.prepare(sql);
    return stmt.all(...params);
  } catch (error) {
    console.error('خطأ في الحصول على المعاملات:', error);
    throw error;
  }
}

// تحديث المخزون
function updateInventory(data) {
  try {
    // التحقق من وجود اتصال بقاعدة البيانات
    if (!db) {
      throw new Error('لم يتم تهيئة قاعدة البيانات');
    }

    const { itemId, updates } = data;

    // تسجيل معلومات تشخيصية
    console.log(`تحديث المخزون للصنف ${itemId} بالتحديثات:`, updates);

    // التحقق من وجود الصنف
    const checkItemStmt = db.prepare('SELECT * FROM items WHERE id = ?');
    const item = checkItemStmt.get(itemId);

    if (!item) {
      throw new Error(`الصنف غير موجود: ${itemId}`);
    }

    // التحقق من وجود سجل في المخزون
    const checkInventoryStmt = db.prepare('SELECT * FROM inventory WHERE item_id = ?');
    const inventory = checkInventoryStmt.get(itemId);

    // الحصول على الوقت الحالي
    const now = new Date().toISOString();

    // تنظيف البيانات
    const cleanedUpdates = {
      current_quantity: updates.current_quantity !== undefined ? Number(updates.current_quantity) : (inventory ? inventory.current_quantity : 0),
      minimum_quantity: updates.minimum_quantity !== undefined ? Number(updates.minimum_quantity) : (inventory ? inventory.minimum_quantity : 0),
      avg_price: updates.avg_price !== undefined ? Number(updates.avg_price) : (inventory ? inventory.avg_price : 0),
      selling_price: updates.selling_price !== undefined ? Number(updates.selling_price) : (inventory ? inventory.selling_price : 0),
      last_updated: now
    };

    // تسجيل معلومات تشخيصية
    console.log(`البيانات المنظفة للتحديث:`, cleanedUpdates);

    let result;

    // بدء معاملة قاعدة البيانات
    db.exec('BEGIN TRANSACTION');

    try {
      if (inventory) {
        // تحديث سجل المخزون الموجود
        const updateStmt = db.prepare(`
          UPDATE inventory
          SET current_quantity = ?, minimum_quantity = ?, avg_price = ?, selling_price = ?, last_updated = ?
          WHERE item_id = ?
        `);

        result = updateStmt.run(
          cleanedUpdates.current_quantity,
          cleanedUpdates.minimum_quantity,
          cleanedUpdates.avg_price,
          cleanedUpdates.selling_price,
          cleanedUpdates.last_updated,
          itemId
        );

        console.log(`تم تحديث سجل المخزون الموجود للصنف ${itemId}`);
      } else {
        // إنشاء سجل مخزون جديد
        const insertStmt = db.prepare(`
          INSERT INTO inventory (
            item_id, current_quantity, minimum_quantity, avg_price, selling_price, last_updated
          )
          VALUES (?, ?, ?, ?, ?, ?)
        `);

        result = insertStmt.run(
          itemId,
          cleanedUpdates.current_quantity,
          cleanedUpdates.minimum_quantity,
          cleanedUpdates.avg_price,
          cleanedUpdates.selling_price,
          cleanedUpdates.last_updated
        );

        console.log(`تم إنشاء سجل مخزون جديد للصنف ${itemId}`);
      }

      // تحديث الصنف أيضًا للتأكد من تطابق البيانات
      const updateItemStmt = db.prepare(`
        UPDATE items
        SET minimum_quantity = ?, avg_price = ?, selling_price = ?, updated_at = ?
        WHERE id = ?
      `);

      updateItemStmt.run(
        cleanedUpdates.minimum_quantity,
        cleanedUpdates.avg_price,
        cleanedUpdates.selling_price,
        cleanedUpdates.last_updated,
        itemId
      );

      console.log(`تم تحديث الصنف ${itemId} بنجاح`);

      // إنهاء المعاملة
      db.exec('COMMIT');

      return {
        success: true,
        itemId,
        itemName: item.name,
        updates: cleanedUpdates,
        changes: result.changes
      };
    } catch (error) {
      // التراجع عن المعاملة في حالة حدوث خطأ
      db.exec('ROLLBACK');
      console.error(`خطأ في تحديث المخزون للصنف ${itemId}:`, error);
      throw error;
    }
  } catch (error) {
    console.error('خطأ في تحديث المخزون:', error);
    throw error;
  }
}

// تصدير الوظائف الأخرى حسب الحاجة
// ...

// إشعار العملية الرئيسية بأن Worker Thread جاهز
parentPort.postMessage({ type: 'ready', id: 'init', result: true, error: null });
