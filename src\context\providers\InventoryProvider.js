import React, { createContext, useState, useEffect, useRef, useContext } from 'react';
import * as database from '../../utils/database';
import { NotificationsContext } from './NotificationsProvider';
import { SettingsContext } from './SettingsProvider';

// إنشاء سياق المخزون
export const InventoryContext = createContext();

/**
 * مزود سياق المخزون
 *
 * يوفر هذا المكون وظائف إدارة المخزون والأصناف
 *
 * @param {Object} props - خصائص المكون
 * @param {React.ReactNode} props.children - المكونات الفرعية
 * @returns {React.ReactElement} مزود سياق المخزون
 */
export const InventoryProvider = ({ children }) => {
  // استخدام سياق الإعدادات والإشعارات
  const { settings } = useContext(SettingsContext);
  const { showNotification } = useContext(NotificationsContext);

  // حالة الأصناف
  const [items, setItems] = useState([]);

  // حالة المخزون
  const [inventory, setInventory] = useState([]);

  // حالة التحميل
  const [loading, setLoading] = useState(true);

  // مرجع للمزامنة الدورية للمخزون
  const inventorySyncIntervalRef = useRef(null);

  // مرجع للتحكم في تكرار مزامنة المخزون
  const syncInventoryDebounceRef = useRef({
    isRunning: false,
    lastSyncTime: 0,
    pendingPromise: null
  });

  // تحميل البيانات عند بدء التطبيق
  useEffect(() => {
    // محاولة تحميل البيانات من قاعدة البيانات
    loadData()
      .then(() => {
        // مزامنة المخزون تلقائياً بعد تحميل البيانات (مع إظهار مؤشر التحميل والإشعارات)
        console.log('تحميل البيانات تم بنجاح، جاري مزامنة المخزون تلقائياً...');
        syncAllInventory({
          showLoadingIndicator: true,
          showNotifications: true
        })
          .then(() => {
            console.log('تمت مزامنة المخزون تلقائياً عند بدء التطبيق');
          })
          .catch(syncError => {
            console.error('خطأ في المزامنة التلقائية للمخزون عند بدء التطبيق:', syncError);
          });
      })
      .catch(error => {
        console.error('Error loading data from database:', error);
        // في حالة فشل تحميل البيانات من قاعدة البيانات، نستخدم البيانات الافتراضية
        loadDefaultData();
      });
  }, []);

  // إعداد المزامنة الدورية للمخزون
  useEffect(() => {
    // إلغاء المزامنة الدورية الحالية للمخزون إن وجدت
    if (inventorySyncIntervalRef.current) {
      clearInterval(inventorySyncIntervalRef.current);
      inventorySyncIntervalRef.current = null;
    }

    // إعداد المزامنة الدورية للمخزون (كل 30 دقيقة)
    const INVENTORY_SYNC_INTERVAL = 30 * 60 * 1000; // 30 دقيقة بالمللي ثانية

    console.log('إعداد المزامنة الدورية للمخزون كل 30 دقيقة');

    inventorySyncIntervalRef.current = setInterval(() => {
      // تنفيذ المزامنة فقط إذا لم تكن هناك تحميل جاري
      if (!loading) {
        console.log('تنفيذ المزامنة الدورية للمخزون...');
        syncAllInventory()
          .then(() => {
            console.log('تمت المزامنة الدورية للمخزون بنجاح');
          })
          .catch(error => {
            console.error('خطأ في المزامنة الدورية للمخزون:', error);
          });
      }
    }, INVENTORY_SYNC_INTERVAL);

    // تنظيف المزامنة عند إلغاء تحميل المكون
    return () => {
      if (inventorySyncIntervalRef.current) {
        clearInterval(inventorySyncIntervalRef.current);
        inventorySyncIntervalRef.current = null;
      }
    };
  }, [loading]);

  // تحميل البيانات من قاعدة البيانات
  const loadData = async () => {
    try {
      console.log('بدء تحميل البيانات...');
      setLoading(true);

      // متغير للتحقق من نجاح تحميل البيانات
      let loadedSuccessfully = true;

      // تحميل الأصناف مع فرض تحديث التخزين المؤقت
      try {
        console.log('تحميل الأصناف مع فرض تحديث التخزين المؤقت...');
        const itemsData = await database.getItems(true); // فرض تحديث التخزين المؤقت
        setItems(itemsData);
        console.log('تم تحميل الأصناف بنجاح:', itemsData.length);
      } catch (itemsError) {
        console.error('خطأ في تحميل الأصناف:', itemsError);
        loadedSuccessfully = false;
      }

      // تحميل المخزون مع فرض تحديث التخزين المؤقت
      try {
        console.log('تحميل المخزون مع فرض تحديث التخزين المؤقت...');
        const inventoryData = await database.getInventory(true); // فرض تحديث التخزين المؤقت
        setInventory(inventoryData);
        console.log('تم تحميل المخزون بنجاح:', inventoryData.length);
      } catch (inventoryError) {
        console.error('خطأ في تحميل المخزون:', inventoryError);
        loadedSuccessfully = false;
      }

      setLoading(false);
      return loadedSuccessfully;
    } catch (error) {
      console.error('خطأ عام في تحميل البيانات:', error);
      setLoading(false);
      return false;
    }
  };

  // تحميل الأصناف فقط
  const loadItems = async () => {
    try {
      console.log('بدء تحميل الأصناف...');

      // تحميل الأصناف باستخدام window.api.invoke مباشرة
      const itemsData = await window.api.invoke('get-all-items');
      setItems(itemsData);
      console.log('تم تحميل الأصناف بنجاح:', itemsData.length);
      return itemsData;
    } catch (itemsError) {
      console.error('خطأ في تحميل الأصناف:', itemsError);
      return [];
    }
  };

  // تحميل البيانات الافتراضية
  const loadDefaultData = () => {
    setLoading(true);

    // بيانات افتراضية للأصناف (فارغة)
    const defaultItems = [];

    // بيانات افتراضية للمخزون (فارغة)
    const defaultInventory = [];

    setItems(defaultItems);
    setInventory(defaultInventory);
    setLoading(false);
  };

  // مزامنة المخزون تلقائياً (بدون خيارات للمزامنة اليدوية)
  const syncAllInventory = async (options = {}) => {
    const {
      showLoadingIndicator = false,  // عدم إظهار مؤشر التحميل افتراضياً
      showNotifications = false,     // عدم إظهار الإشعارات افتراضياً
      debounceTime = 2000           // وقت التأخير بين المزامنات المتتالية (2 ثانية)
    } = options;

    const now = Date.now();
    const syncRef = syncInventoryDebounceRef.current;

    // إذا كانت هناك مزامنة جارية، أعد الوعد الحالي
    if (syncRef.isRunning) {
      console.log('هناك مزامنة جارية بالفعل، سيتم إعادة الوعد الحالي');
      return syncRef.pendingPromise;
    }

    // إذا كان الوقت منذ آخر مزامنة أقل من وقت التأخير، أعد الوعد الحالي
    if ((now - syncRef.lastSyncTime < debounceTime) && syncRef.pendingPromise) {
      console.log(`تم طلب المزامنة قبل ${now - syncRef.lastSyncTime}ms، سيتم إعادة الوعد الحالي`);
      return syncRef.pendingPromise;
    }

    // إنشاء وعد جديد للمزامنة
    syncRef.isRunning = true;
    syncRef.lastSyncTime = now;

    // إنشاء وظيفة المزامنة
    const performSync = async () => {
      try {
        console.log('بدء مزامنة المخزون تلقائياً...');

        // تعيين حالة التحميل إذا تم طلب ذلك
        if (showLoadingIndicator) {
          setLoading(true);
        }

        // التحقق من وجود window.api قبل استدعاء وظيفة المزامنة
        if (typeof window === 'undefined' || !window.api) {
          console.error('window.api غير متوفر. محاولة إعادة تحميل الصفحة...');

          // محاولة إعادة تحميل الصفحة بعد فترة قصيرة
          setTimeout(() => {
            try {
              window.location.reload();
            } catch (reloadError) {
              console.error('فشل في إعادة تحميل الصفحة:', reloadError);
            }
          }, 2000);

          throw new Error('فشل في مزامنة المخزون بسبب عدم توفر window.api');
        }

        // محاولة استدعاء وظيفة مزامنة المخزون في قاعدة البيانات مع محاولات متعددة
        let result;
        let attempts = 0;
        const maxAttempts = 3;

        while (attempts < maxAttempts) {
          attempts++;
          try {
            console.log(`محاولة مزامنة المخزون (${attempts}/${maxAttempts})...`);
            result = await database.syncAllInventory();
            console.log('نتيجة مزامنة المخزون:', result);
            break; // الخروج من الحلقة إذا نجحت المزامنة
          } catch (syncError) {
            console.error(`خطأ في محاولة المزامنة ${attempts}/${maxAttempts}:`, syncError);

            if (attempts >= maxAttempts) {
              throw syncError; // إعادة رمي الخطأ إذا فشلت جميع المحاولات
            }

            // انتظار قبل المحاولة التالية
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }

        // إعادة تحميل المخزون من قاعدة البيانات
        let updatedInventory;
        try {
          updatedInventory = await database.getInventory();
          setInventory(updatedInventory);
          console.log('تم تحديث المخزون بنجاح:', updatedInventory.length, 'عنصر');
        } catch (inventoryError) {
          console.error('خطأ في تحميل المخزون المحدث:', inventoryError);
        }

        // إعادة تحميل الأصناف من قاعدة البيانات
        let updatedItems;
        try {
          updatedItems = await database.getItems();
          setItems(updatedItems);
          console.log('تم تحديث الأصناف بنجاح:', updatedItems.length, 'صنف');
        } catch (itemsError) {
          console.error('خطأ في تحميل الأصناف المحدثة:', itemsError);
        }

        // إنهاء حالة التحميل إذا تم طلب ذلك
        if (showLoadingIndicator) {
          setLoading(false);
        }

        // إظهار إشعار بنجاح المزامنة إذا تم طلب ذلك
        if (showNotifications) {
          showNotification('تمت مزامنة المخزون بنجاح', 'success');
        }

        // تحديث حالة المزامنة
        syncRef.isRunning = false;

        // تحديث حالة المزامنة في التخزين المحلي
        try {
          localStorage.setItem('lastSyncTime', new Date().toISOString());
        } catch (storageError) {
          console.error('خطأ في تحديث حالة المزامنة في التخزين المحلي:', storageError);
        }

        return result;
      } catch (error) {
        console.error('خطأ في مزامنة المخزون:', error);

        // إنهاء حالة التحميل إذا تم طلب ذلك
        if (showLoadingIndicator) {
          setLoading(false);
        }

        // إظهار إشعار بفشل المزامنة إذا تم طلب ذلك
        if (showNotifications) {
          showNotification(`فشل في مزامنة المخزون: ${error.message}`, 'error');
        }

        // تحديث حالة المزامنة
        syncRef.isRunning = false;

        throw error;
      }
    };

    // تخزين الوعد وإعادته
    syncRef.pendingPromise = performSync();
    return syncRef.pendingPromise;
  };

  // إضافة مستمع لحدث تحديث المخزون
  useEffect(() => {
    // دالة لتحديث المخزون عند استلام حدث تحديث
    const handleInventoryUpdate = async (event, data) => {
      try {
        console.log('تم استلام حدث تحديث المخزون في InventoryProvider:', data);

        // مسح التخزين المؤقت للمخزون
        await window.api.invoke('clear-inventory-cache');

        // تحميل المخزون المحدث
        const updatedInventory = await window.api.invoke('get-all-inventory', true);

        if (Array.isArray(updatedInventory)) {
          console.log(`تم تحديث المخزون في InventoryProvider (${updatedInventory.length} صنف)`);
          setInventory(updatedInventory);
        }
      } catch (err) {
        console.error('خطأ في تحديث المخزون في InventoryProvider:', err);
      }
    };

    // إضافة مستمع للحدث
    if (window.api && window.api.on) {
      window.api.on('inventory-updated', handleInventoryUpdate);
      window.api.on('refresh-needed', (data) => {
        if (data && data.target === 'inventory') {
          handleInventoryUpdate(null, data);
        }
      });

      console.log('تم إضافة مستمع لحدث تحديث المخزون في InventoryProvider');
    }

    // تنظيف المستمعين عند إلغاء تحميل المكون
    return () => {
      if (window.api && window.api.removeListener) {
        window.api.removeListener('inventory-updated', handleInventoryUpdate);
        console.log('تم إزالة مستمع حدث تحديث المخزون في InventoryProvider');
      }
    };
  }, []);

  // القيمة التي سيتم توفيرها للمكونات
  const value = {
    items,
    inventory,
    loading,
    setInventory,
    loadItems,
    syncAllInventory,
    // إضافة دالة لتحديث المخزون يدويًا
    refreshInventory: async () => {
      try {
        console.log('تحديث المخزون يدويًا من InventoryProvider');

        // مسح التخزين المؤقت للمخزون
        await window.api.invoke('clear-inventory-cache');

        // تحميل المخزون المحدث
        const updatedInventory = await window.api.invoke('get-all-inventory', true);

        if (Array.isArray(updatedInventory)) {
          console.log(`تم تحديث المخزون في InventoryProvider (${updatedInventory.length} صنف)`);
          setInventory(updatedInventory);
          return true;
        }

        return false;
      } catch (err) {
        console.error('خطأ في تحديث المخزون في InventoryProvider:', err);
        return false;
      }
    }
  };

  return (
    <InventoryContext.Provider value={value}>
      {children}
    </InventoryContext.Provider>
  );
};

export default InventoryProvider;
