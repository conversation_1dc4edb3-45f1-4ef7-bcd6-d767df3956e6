/**
 * اختبار مبسط للتحديث الفوري لبطاقات الأرباح
 */

const path = require('path');

// محاكاة بيانات الاختبار
const currentYear = new Date().getFullYear();
const testTransactions = [
  {
    id: 1,
    transaction_type: 'sale',
    item_name: 'منتج اختبار 1',
    quantity: 2,
    price: 150,
    selling_price: 150,
    total_price: 300,
    profit: 100,
    transaction_date: new Date(currentYear, 0, 15).toISOString() // 15 يناير (الربع الأول)
  },
  {
    id: 2,
    transaction_type: 'purchase',
    item_name: 'منتج اختبار 1',
    quantity: 5,
    price: 50,
    total_price: 250,
    profit: 0,
    transaction_date: new Date(currentYear, 1, 15).toISOString() // 15 فبراير (الربع الأول)
  },
  {
    id: 3,
    transaction_type: 'return',
    item_name: 'منتج اختبار 1',
    quantity: 1,
    price: 150,
    total_price: 150,
    profit: -50,
    transaction_date: new Date(currentYear, 2, 15).toISOString() // 15 مارس (الربع الأول)
  }
];

// اختبار دالة حساب الأرباح
function testProfitCalculation() {
  console.log('🧪 اختبار حساب الأرباح...');

  try {
    // استيراد دالة حساب الأرباح
    const { calculateQuarterlyProfits } = require('./src/utils/profitCalculator.js');

    // اختبار حساب الأرباح
    const profitResults = calculateQuarterlyProfits(testTransactions);

    console.log('📊 نتائج حساب الأرباح:');
    console.log('   - الربع الأول:', profitResults.quarterly);
    console.log('   - النصف سنوي:', profitResults.halfYearly);
    console.log('   - ثلاثة أرباع:', profitResults.threeQuarters);
    console.log('   - السنوي:', profitResults.yearly);

    // التحقق من صحة النتائج
    const expectedProfit = 100 - 50; // بيع 100 - إرجاع 50 = 50
    if (profitResults.quarterly === expectedProfit) {
      console.log('✅ اختبار حساب الأرباح نجح');
      return true;
    } else {
      console.log('❌ اختبار حساب الأرباح فشل');
      console.log(`   المتوقع: ${expectedProfit}, الفعلي: ${profitResults.quarterly}`);
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار حساب الأرباح:', error.message);
    return false;
  }
}

// اختبار نظام الأحداث
function testEventSystem() {
  console.log('🔄 اختبار نظام الأحداث...');

  try {
    // محاكاة إرسال حدث
    const EventEmitter = require('events');
    const testEmitter = new EventEmitter();

    let eventReceived = false;

    // إضافة مستمع للحدث
    testEmitter.on('profits-updated', (data) => {
      console.log('📨 تم استلام حدث تحديث الأرباح:', data);
      eventReceived = true;
    });

    // إرسال حدث تجريبي
    testEmitter.emit('profits-updated', {
      transaction_type: 'sale',
      profit: 100,
      timestamp: new Date().toISOString()
    });

    if (eventReceived) {
      console.log('✅ اختبار نظام الأحداث نجح');
      return true;
    } else {
      console.log('❌ اختبار نظام الأحداث فشل');
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار نظام الأحداث:', error.message);
    return false;
  }
}

// اختبار تحديث البيانات
function testDataUpdate() {
  console.log('📊 اختبار تحديث البيانات...');

  try {
    // محاكاة تحديث البيانات
    let cachedProfitValues = { quarterly: 0, halfYearly: 0, threeQuarters: 0, yearly: 0 };

    // محاكاة تحديث القيم
    const newProfitValues = { quarterly: 50, halfYearly: 50, threeQuarters: 50, yearly: 50 };

    // التحقق من التحديث
    const isUpdated = JSON.stringify(newProfitValues) !== JSON.stringify(cachedProfitValues);

    if (isUpdated) {
      cachedProfitValues = newProfitValues;
      console.log('✅ اختبار تحديث البيانات نجح');
      console.log('📈 القيم الجديدة:', cachedProfitValues);
      return true;
    } else {
      console.log('❌ اختبار تحديث البيانات فشل');
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار تحديث البيانات:', error.message);
    return false;
  }
}

// تشغيل جميع الاختبارات
function runAllTests() {
  console.log('🚀 بدء اختبار التحديث الفوري لبطاقات الأرباح...');
  console.log('=' .repeat(50));

  const tests = [
    { name: 'حساب الأرباح', func: testProfitCalculation },
    { name: 'نظام الأحداث', func: testEventSystem },
    { name: 'تحديث البيانات', func: testDataUpdate }
  ];

  let passedTests = 0;
  let totalTests = tests.length;

  tests.forEach((test, index) => {
    console.log(`\n${index + 1}. اختبار ${test.name}:`);
    const result = test.func();
    if (result) {
      passedTests++;
    }
  });

  console.log('\n' + '=' .repeat(50));
  console.log('📋 ملخص النتائج:');
  console.log(`   ✅ نجح: ${passedTests}/${totalTests}`);
  console.log(`   ❌ فشل: ${totalTests - passedTests}/${totalTests}`);

  if (passedTests === totalTests) {
    console.log('🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.');
  } else {
    console.log('⚠️  بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.');
  }

  return passedTests === totalTests;
}

// تشغيل الاختبارات
if (require.main === module) {
  runAllTests();
}

module.exports = {
  testProfitCalculation,
  testEventSystem,
  testDataUpdate,
  runAllTests
};
