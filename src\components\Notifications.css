.notifications-container {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  width: 100%;
  max-width: 500px;
  display: none; /* تم تغييره من flex إلى none لإخفاء الإشعارات */
  flex-direction: column;
  gap: 10px;
  pointer-events: none;
}

.notification {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  animation: slide-in 0.3s ease-out forwards;
  pointer-events: auto;
  direction: rtl;
  text-align: right;
}

.notification-success {
  border-right: 4px solid #10b981;
}

.notification-error {
  border-right: 4px solid #ef4444;
}

.notification-warning {
  border-right: 4px solid #f59e0b;
}

.notification-info {
  border-right: 4px solid #3b82f6;
}

.notification-icon {
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-success .notification-icon {
  color: #10b981;
}

.notification-error .notification-icon {
  color: #ef4444;
}

.notification-warning .notification-icon {
  color: #f59e0b;
}

.notification-info .notification-icon {
  color: #3b82f6;
}

.notification-content {
  flex: 1;
  font-size: 14px;
  line-height: 1.5;
}

.notification-close {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #9ca3af;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.notification-close:hover {
  background-color: #f3f4f6;
}

@keyframes slide-in {
  0% {
    transform: translateY(-20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slide-out {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateY(-20px);
    opacity: 0;
  }
}

.notification.removing {
  animation: slide-out 0.3s ease-in forwards;
}
