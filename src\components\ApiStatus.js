import React, { useState, useEffect } from 'react';
import { checkApiAvailability, testApiConnection, troubleshootApi } from '../utils/api-checker';

/**
 * مكون لعرض حالة اتصال window.api وتشخيص المشاكل
 */
const ApiStatus = () => {
  // Variable no utilizada - considere eliminarla o usarla
  const [status, setStatus] = useState({ checking: true });
  // Variable no utilizada - considere eliminarla o usarla
  const [testResult, setTestResult] = useState(null);
  // Variable no utilizada - considere eliminarla o usarla
  const [troubleshootResult, setTroubleshootResult] = useState(null);

  // التحقق من حالة window.api عند تحميل المكون
  useEffect(() => {
    const checkApi = async () => {
      try {
        const availability = checkApiAvailability();
        setStatus({ checking: false, ...availability });
      } catch (error) {
        setStatus({ checking: false, available: false, error: error.message });
      }
    };

    checkApi();
  }, []);

  // اختبار اتصال window.api
  const handleTestConnection = async () => {
    setTestResult({ testing: true });
    try {
      const result = await testApiConnection();
      setTestResult({ testing: false, ...result });
    } catch (error) {
      setTestResult({ testing: false, success: false, error: error.message });
    }
  };

  // تشخيص وإصلاح مشاكل window.api
  const handleTroubleshoot = async () => {
    setTroubleshootResult({ troubleshooting: true });
    try {
      const result = await troubleshootApi();
      setTroubleshootResult({ troubleshooting: false, ...result });
    } catch (error) {
      setTroubleshootResult({ troubleshooting: false, needsFix: true, error: error.message });
    }
  };

  // إعادة تحميل الصفحة
  const handleReload = () => {
    window.location.reload();
  };

  return (
    <div className="api-status-container" style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h2 style={{ textAlign: 'center', marginBottom: '20px' }}>حالة اتصال واجهة برمجة التطبيقات (API)</h2>
      
      {/* حالة window.api */}
      <div className="status-section" style={{ marginBottom: '20px', padding: '15px', border: '1px solid #ddd', borderRadius: '5px' }}>
        <h3>حالة window.api</h3>
        {status.checking ? (
          <p>جاري التحقق من حالة window.api...</p>
        ) : status.available ? (
          <div>
            <p style={{ color: 'green' }}>✓ window.api متوفر</p>
            {status.warning && (
              <div>
                <p style={{ color: 'orange' }}>⚠️ {status.warning}</p>
                <p>الوظائف المفقودة: {status.missingMethods.join(', ')}</p>
              </div>
            )}
            <p>{status.details}</p>
          </div>
        ) : (
          <div>
            <p style={{ color: 'red' }}>✗ {status.error}</p>
            <p>{status.details}</p>
          </div>
        )}
        <div style={{ marginTop: '10px' }}>
          <button onClick={handleTestConnection} style={{ marginLeft: '10px' }}>اختبار الاتصال</button>
          <button onClick={handleTroubleshoot} style={{ marginLeft: '10px' }}>تشخيص المشاكل</button>
          <button onClick={handleReload}>إعادة تحميل</button>
        </div>
      </div>
      
      {/* نتيجة اختبار الاتصال */}
      {testResult && (
        <div className="test-section" style={{ marginBottom: '20px', padding: '15px', border: '1px solid #ddd', borderRadius: '5px' }}>
          <h3>نتيجة اختبار الاتصال</h3>
          {testResult.testing ? (
            <p>جاري اختبار الاتصال...</p>
          ) : testResult.success ? (
            <div>
              <p style={{ color: 'green' }}>✓ تم اختبار الاتصال بنجاح</p>
              <p>{testResult.details}</p>
              <pre style={{ background: '#f5f5f5', padding: '10px', borderRadius: '5px', direction: 'ltr', textAlign: 'left' }}>
                {JSON.stringify(testResult.result, null, 2)}
              </pre>
            </div>
          ) : (
            <div>
              <p style={{ color: 'red' }}>✗ فشل اختبار الاتصال</p>
              <p>{testResult.error}</p>
              <p>{testResult.details}</p>
            </div>
          )}
        </div>
      )}
      
      {/* نتيجة تشخيص المشاكل */}
      {troubleshootResult && (
        <div className="troubleshoot-section" style={{ marginBottom: '20px', padding: '15px', border: '1px solid #ddd', borderRadius: '5px' }}>
          <h3>نتيجة تشخيص المشاكل</h3>
          {troubleshootResult.troubleshooting ? (
            <p>جاري تشخيص المشاكل...</p>
          ) : troubleshootResult.needsFix ? (
            <div>
              <p style={{ color: 'red' }}>✗ توجد مشاكل تحتاج إلى إصلاح</p>
              <p>{troubleshootResult.message}</p>
              {troubleshootResult.recommendations && (
                <div>
                  <h4>توصيات الإصلاح:</h4>
                  <ul>
                    {troubleshootResult.recommendations.map((rec, index) => (
                      <li key={index}>{rec}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          ) : (
            <div>
              <p style={{ color: 'green' }}>✓ {troubleshootResult.message}</p>
              {troubleshootResult.fixed && <p>تم إصلاح المشكلة تلقائيًا!</p>}
              <p>{troubleshootResult.details?.details}</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ApiStatus;
