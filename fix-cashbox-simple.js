/**
 * سكريبت بسيط لإصلاح الخزينة
 */

const Database = require('better-sqlite3');
const path = require('path');

// مسار قاعدة البيانات
const dbPath = path.join(process.env.APPDATA || process.env.HOME, 'warehouse-management-system', 'wms-database');

console.log('مسار قاعدة البيانات:', dbPath);

try {
  // الاتصال بقاعدة البيانات
  const db = new Database(dbPath);
  console.log('تم الاتصال بقاعدة البيانات بنجاح');

  // الحصول على الخزينة الحالية
  const getCashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
  const currentCashbox = getCashboxStmt.get();

  if (!currentCashbox) {
    console.error('لا توجد خزينة في قاعدة البيانات');
    process.exit(1);
  }

  console.log('الخزينة الحالية:', {
    current_balance: currentCashbox.current_balance,
    sales_total: currentCashbox.sales_total,
    purchases_total: currentCashbox.purchases_total,
    profit_total: currentCashbox.profit_total,
    transport_total: currentCashbox.transport_total || 0
  });

  // الحصول على جميع المعاملات
  const getTransactionsStmt = db.prepare(`
    SELECT t.*, inv.avg_price, inv.selling_price as inventory_selling_price
    FROM transactions t
    LEFT JOIN inventory inv ON t.item_id = inv.item_id
    ORDER BY t.transaction_date ASC
  `);
  const transactions = getTransactionsStmt.all();

  console.log(`تم العثور على ${transactions.length} معاملة`);

  // حساب الإجماليات
  let totalSales = 0;
  let totalPurchases = 0;
  let totalReturns = 0;
  let totalProfit = 0;
  let totalTransportCost = 0;

  for (const transaction of transactions) {
    const amount = parseFloat(transaction.total_price) || 0;
    const transportCost = parseFloat(transaction.transport_cost) || 0;

    if (transaction.transaction_type === 'sale') {
      totalSales += amount;

      // حساب الربح
      let profit = 0;
      if (transaction.profit && transaction.profit !== 0) {
        profit = parseFloat(transaction.profit);
      } else {
        // الحصول على سعر البيع
        let sellingPrice = transaction.selling_price;
        if (!sellingPrice || sellingPrice === 0) {
          sellingPrice = transaction.inventory_selling_price || 0;
        }

        if (sellingPrice > 0 && transaction.avg_price > 0) {
          // حساب الربح الأساسي
          profit = (sellingPrice - transaction.avg_price) * transaction.quantity;
          
          // خصم مصاريف النقل إذا كانت موجودة
          if (transportCost > 0) {
            profit -= transportCost;
          }
        } else {
          // تقدير 20% من سعر البيع
          profit = amount * 0.2;
        }
      }

      totalProfit += profit;

    } else if (transaction.transaction_type === 'purchase') {
      totalPurchases += amount;
      totalTransportCost += transportCost;

    } else if (transaction.transaction_type === 'return') {
      totalReturns += amount;

      // خصم الربح المرتبط بالإرجاع
      let returnProfit = transaction.profit || (amount * 0.2);
      totalProfit -= returnProfit;
    }
  }

  // التأكد من أن الأرباح لا تكون سالبة
  totalProfit = Math.max(0, totalProfit);

  console.log('الإجماليات المحسوبة:', {
    totalSales,
    totalPurchases,
    totalReturns,
    totalProfit,
    totalTransportCost
  });

  // حساب الرصيد الحالي الجديد
  const newCurrentBalance = currentCashbox.initial_balance + totalSales - totalPurchases - totalReturns - totalTransportCost;

  console.log('الرصيد الحالي الجديد:', newCurrentBalance);

  // تحديث الخزينة
  const updateCashboxStmt = db.prepare(`
    UPDATE cashbox
    SET current_balance = ?,
        sales_total = ?,
        purchases_total = ?,
        returns_total = ?,
        profit_total = ?,
        transport_total = ?,
        updated_at = ?
    WHERE id = ?
  `);

  const updateResult = updateCashboxStmt.run(
    newCurrentBalance,
    totalSales,
    totalPurchases,
    totalReturns,
    totalProfit,
    totalTransportCost,
    new Date().toISOString(),
    currentCashbox.id
  );

  console.log(`تم تحديث الخزينة بنجاح. عدد الصفوف المتأثرة: ${updateResult.changes}`);

  // عرض النتائج النهائية
  const getFinalCashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
  const finalCashbox = getFinalCashboxStmt.get();

  console.log('الخزينة بعد الإصلاح:', {
    current_balance: finalCashbox.current_balance,
    sales_total: finalCashbox.sales_total,
    purchases_total: finalCashbox.purchases_total,
    profit_total: finalCashbox.profit_total,
    transport_total: finalCashbox.transport_total || 0,
    returns_total: finalCashbox.returns_total || 0
  });

  // إغلاق قاعدة البيانات
  db.close();
  console.log('تم إصلاح الخزينة بنجاح!');

} catch (error) {
  console.error('خطأ في إصلاح الخزينة:', error);
  process.exit(1);
}
