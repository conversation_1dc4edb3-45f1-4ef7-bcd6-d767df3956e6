/**
 * وحدة إصلاح جدول المعاملات
 * تقوم بإضافة عمود selling_price إذا لم يكن موجودًا
 */

const { logError, logSystem } = require('./error-handler');
const DatabaseManager = require('./database-singleton');

/**
 * إصلاح جدول المعاملات بإضافة عمود selling_price
 * @returns {Object} نتيجة عملية الإصلاح
 */
function fixTransactionsTable() {
  try {
    console.log('بدء إصلاح جدول المعاملات...');
    
    // الحصول على اتصال قاعدة البيانات
    const dbManager = DatabaseManager.getInstance();
    const db = dbManager.getConnection();
    
    if (!db) {
      throw new Error('قاعدة البيانات غير متصلة');
    }
    
    // التحقق من وجود عمود selling_price
    const tableInfoQuery = db.prepare("PRAGMA table_info(transactions)");
    const columns = tableInfoQuery.all();
    
    // البحث عن عمود selling_price
    const sellingPriceColumn = columns.find(col => col.name === 'selling_price');
    
    if (!sellingPriceColumn) {
      console.log('عمود selling_price غير موجود، جاري إضافته...');
      
      // إضافة العمود
      db.prepare("ALTER TABLE transactions ADD COLUMN selling_price REAL DEFAULT 0").run();
      
      console.log('تم إضافة عمود selling_price بنجاح');
      logSystem('تم إضافة عمود selling_price إلى جدول المعاملات', 'info');
      
      return {
        success: true,
        message: 'تم إضافة عمود selling_price بنجاح'
      };
    } else {
      console.log('عمود selling_price موجود بالفعل');
      
      return {
        success: true,
        message: 'عمود selling_price موجود بالفعل'
      };
    }
  } catch (error) {
    console.error('خطأ في إصلاح جدول المعاملات:', error);
    logError(error, 'fixTransactionsTable');
    
    return {
      success: false,
      message: `خطأ في إصلاح جدول المعاملات: ${error.message}`
    };
  }
}

module.exports = {
  fixTransactionsTable
};