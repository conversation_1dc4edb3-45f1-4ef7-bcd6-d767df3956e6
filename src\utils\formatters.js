/**
 * وحدة تنسيق البيانات
 * توفر دوال لتنسيق التواريخ والأرقام والعملات
 */

/**
 * تنسيق التاريخ إلى صيغة مقروءة
 * @param {string|Date} date - التاريخ المراد تنسيقه
 * @param {boolean} includeTime - ما إذا كان يجب تضمين الوقت في التنسيق
 * @returns {string} - التاريخ المنسق
 */
export const formatDate = (date, includeTime = false) => {
  if (!date) return 'غير متوفر';
  
  try {
    const dateObj = new Date(date);
    
    // التحقق من صحة التاريخ
    if (isNaN(dateObj.getTime())) {
      return 'تاريخ غير صالح';
    }
    
    // تنسيق التاريخ بالأرقام الإنجليزية
    const day = dateObj.getDate().toString().padStart(2, '0');
    const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
    const year = dateObj.getFullYear();
    
    let formattedDate = `${year}/${month}/${day}`;
    
    // إضافة الوقت إذا كان مطلوبًا
    if (includeTime) {
      const hours = dateObj.getHours().toString().padStart(2, '0');
      const minutes = dateObj.getMinutes().toString().padStart(2, '0');
      formattedDate += ` ${hours}:${minutes}`;
    }
    
    return formattedDate;
  } catch (error) {
    console.error('خطأ في تنسيق التاريخ:', error);
    return 'خطأ في التاريخ';
  }
};

/**
 * تنسيق رقم بإضافة فواصل الآلاف
 * @param {number} number - الرقم المراد تنسيقه
 * @param {number} decimalPlaces - عدد المنازل العشرية
 * @returns {string} - الرقم المنسق
 */
export const formatNumber = (number, decimalPlaces = 0) => {
  if (number === undefined || number === null) return '-';
  
  try {
    // تحويل إلى رقم
    const num = Number(number);
    
    // التحقق من صحة الرقم
    if (isNaN(num)) {
      return '-';
    }
    
    // تنسيق الرقم بفواصل الآلاف
    return num.toLocaleString('en-US', {
      minimumFractionDigits: decimalPlaces,
      maximumFractionDigits: decimalPlaces
    });
  } catch (error) {
    console.error('خطأ في تنسيق الرقم:', error);
    return String(number);
  }
};

/**
 * تنسيق رقم كعملة
 * @param {number} amount - المبلغ المراد تنسيقه
 * @param {Object} options - خيارات التنسيق
 * @param {boolean} options.showSymbol - ما إذا كان سيتم عرض رمز العملة
 * @param {number} options.decimalPlaces - عدد المنازل العشرية
 * @param {string} options.currencySymbol - رمز العملة
 * @param {boolean} options.useThousandSeparator - استخدام فاصل الآلاف
 * @returns {string} - المبلغ المنسق
 */
export const formatCurrency = (amount, options = {}) => {
  const {
    showSymbol = true,
    decimalPlaces = 0,
    currencySymbol = 'د ل',
    useThousandSeparator = true
  } = options;

  // التعامل مع القيم الفارغة أو غير الصالحة
  if (amount === null || amount === undefined || isNaN(amount)) {
    return showSymbol ? `0 ${currencySymbol}` : '0';
  }

  // التأكد من أن المبلغ رقم
  let numAmount;
  try {
    numAmount = typeof amount === 'string' ? parseFloat(amount) : Number(amount);

    // التحقق مرة أخرى من أن القيمة رقم صالح
    if (isNaN(numAmount)) {
      return showSymbol ? `0 ${currencySymbol}` : '0';
    }
  } catch (error) {
    console.error('خطأ في تحويل القيمة إلى رقم:', error, amount);
    return showSymbol ? `0 ${currencySymbol}` : '0';
  }

  // تنسيق الرقم
  try {
    let formattedAmount;
    // تخزين إشارة الرقم
    const isNegative = numAmount < 0;
    // استخدام القيمة المطلقة للرقم
    const absAmount = Math.abs(numAmount);

    if (useThousandSeparator) {
      // استخدام تنسيق مع فواصل للآلاف - استخدام تنسيق إنجليزي بدلاً من عربي
      formattedAmount = new Intl.NumberFormat('en-US', {
        minimumFractionDigits: decimalPlaces,
        maximumFractionDigits: decimalPlaces
      }).format(absAmount);
    } else {
      // استخدام التنسيق البسيط بدون فواصل
      formattedAmount = absAmount.toFixed(decimalPlaces);
    }

    // إضافة رمز العملة وإشارة السالب إذا كان الرقم سالبًا
    if (showSymbol) {
      return isNegative ? `${formattedAmount} ${currencySymbol}-` : `${formattedAmount} ${currencySymbol}`;
    } else {
      return isNegative ? `-${formattedAmount}` : formattedAmount;
    }
  } catch (error) {
    console.error('خطأ في تنسيق الرقم:', error, numAmount);
    return showSymbol ? `${numAmount} ${currencySymbol}` : String(numAmount);
  }
};

/**
 * تحويل الأرقام العربية إلى أرقام إنجليزية
 * @param {string} str - النص الذي يحتوي على أرقام عربية
 * @returns {string} - النص بعد تحويل الأرقام إلى إنجليزية
 */
export const convertToEnglishDigits = (str) => {
  if (!str || typeof str !== 'string') return str;
  
  const arabicDigits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
  const englishDigits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

  return str.replace(/[٠-٩]/g, match => {
    return englishDigits[arabicDigits.indexOf(match)];
  });
};

/**
 * تحويل الأرقام الإنجليزية إلى أرقام عربية
 * @param {string} str - النص الذي يحتوي على أرقام إنجليزية
 * @returns {string} - النص بعد تحويل الأرقام إلى عربية
 */
export const convertToArabicDigits = (str) => {
  if (!str || typeof str !== 'string') return str;
  
  const arabicDigits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
  const englishDigits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

  return str.replace(/[0-9]/g, match => {
    return arabicDigits[englishDigits.indexOf(match)];
  });
};

export default {
  formatDate,
  formatNumber,
  formatCurrency,
  convertToEnglishDigits,
  convertToArabicDigits
};
