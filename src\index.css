/* تم استبدال استيراد Google Fonts بالخطوط المحلية */
/* @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap'); */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Cairo', 'Segoe UI', 'Roboto', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  direction: rtl;
  background-color: var(--bg-color, #f8fafc);
  color: var(--text-color, #263845);
  line-height: 1.6;
  overflow-x: hidden;
}

/* تحسين مظهر شريط التمرير */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-color, #f8fafc);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-lighter, #4e6373);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-color, #263845);
}

/* تحسين مظهر التحديد */
::selection {
  background-color: var(--primary-lightest, #eef2f5);
  color: var(--primary-color, #263845);
}
