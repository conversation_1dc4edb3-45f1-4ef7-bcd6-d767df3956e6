/* أنماط صفحة المخزون */
.inventory-page {
  padding: 20px;
}

/* أنماط شاشة التحميل */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100%;
}

.loading-text {
  margin-top: 20px;
  font-size: 1.2rem;
  color: var(--primary-color);
  font-weight: 500;
}

.inventory-header {
  margin-bottom: 30px;
}

.inventory-header h1 {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 10px;
}

.inventory-header p {
  font-size: 1rem;
  color: var(--text-light);
}

/* شبكة الإحصائيات */
.inventory-stats {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card-wrapper {
  height: 100%;
}

.stat-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  height: 100%;
  transition: transform 0.2s;
  text-align: center;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-bottom: 15px;
  transition: transform 0.3s;
}

.stat-card:hover .stat-card-icon {
  transform: scale(1.1);
}

.stat-card-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 5px;
}

.stat-card-title {
  font-size: 1rem;
  color: var(--text-light);
  margin-bottom: 5px;
}

.stat-card-subtitle {
  font-size: 0.85rem;
  color: var(--text-muted);
  margin-bottom: 10px;
}

/* أدوات البحث والفلترة */
.search-filters-container {
  margin-bottom: 30px;
}

.search-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
}

.search-container {
  position: relative;
  flex: 1;
  min-width: 250px;
}

.search-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-light);
}

.search-input {
  width: 100%;
  padding: 10px 15px 10px 40px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  font-size: 0.95rem;
}

.search-input:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.filters {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.filter-item {
  min-width: 200px;
}

.filter-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: var(--text-dark);
}

.filter-select {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: 0.95rem;
  background-color: white;
}

.filter-select:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

/* جدول المخزون */
.inventory-table-container {
  margin-bottom: 30px;
}

.inventory-count {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  background-color: var(--primary-color);
  color: white;
}

.item-name {
  font-weight: 600;
  color: var(--text-dark);
}

.item-unit {
  color: var(--text-light);
  font-size: 0.9rem;
}

.item-quantity {
  font-weight: 500;
}

.quantity-badge {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
}

.quantity-badge.out {
  background-color: rgba(var(--danger-rgb), 0.1);
  color: var(--danger-color);
}

.quantity-badge.low {
  background-color: rgba(var(--warning-rgb), 0.1);
  color: var(--warning-color);
}

.quantity-badge.available {
  background-color: rgba(var(--success-rgb), 0.1);
  color: var(--success-color);
}

.item-price {
  font-weight: 500;
}

.item-date {
  font-size: 0.9rem;
  color: var(--text-light);
}

/* تنسيقات نافذة تعديل سعر البيع */
.edit-price-modal {
  padding: 15px;
}

.edit-price-modal .form-group {
  margin-bottom: 20px;
}

.edit-price-modal .form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.edit-price-modal .current-price {
  font-size: 1.2rem;
  font-weight: 600;
  color: #28a745;
  padding: 8px;
  background-color: rgba(40, 167, 69, 0.1);
  border-radius: 4px;
}

.edit-price-modal .form-control {
  width: 100%;
  padding: 10px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 1rem;
}

.edit-price-modal .modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.item-actions {
  display: flex;
  gap: 8px;
}

/* تنسيقات للشاشات الصغيرة */
@media (max-width: 992px) {
  .search-filters {
    flex-direction: column;
    align-items: stretch;
  }

  .search-container {
    width: 100%;
  }

  .filters {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .inventory-header h1 {
    font-size: 1.5rem;
  }

  .inventory-header p {
    font-size: 0.9rem;
  }

  .stat-card-value {
    font-size: 1.5rem;
  }

  .inventory-stats {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }
}
