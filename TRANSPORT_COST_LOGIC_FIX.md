# إصلاح منطق احتساب مصاريف النقل

## المشكلة الأصلية

كان هناك خطأ في منطق احتساب مصاريف النقل للقطعة الواحدة عند تنفيذ عملية شراء، حيث:

1. **في عمليات الشراء**: مصاريف النقل تخصم من الرصيد الحالي (صحيح)
2. **في عمليات البيع**: مصاريف النقل تخصم مرة أخرى من الأرباح (خطأ)

هذا أدى إلى **خصم مزدوج** لمصاريف النقل، مما قلل من الأرباح المحسوبة بشكل غير صحيح.

## مثال على المشكلة

### السيناريو:
- شراء قطعة واحدة بسعر 1000 + مصاريف نقل 50 = إجمالي 1050
- بيع نفس القطعة بسعر 1200

### الطريقة القديمة (خاطئة):
```
عملية الشراء:
- خصم من الرصيد الحالي: 1050 ✅
- تأثير على الأرباح: 0 ✅

عملية البيع:
- إضافة للرصيد الحالي: 1200 ✅
- حساب الربح: (1200 - 1000 - 50) × 1 = 150 ❌ (خصم مزدوج)
```

### الطريقة الجديدة (صحيحة):
```
عملية الشراء:
- خصم من الرصيد الحالي: 1050 ✅
- تأثير على الأرباح: 0 ✅

عملية البيع:
- إضافة للرصيد الحالي: 1200 ✅
- حساب الربح: (1200 - 1000) × 1 = 200 ✅ (بدون خصم مزدوج)
```

## الإصلاحات المطبقة

### 1. تحديث `unified-transaction-manager.js`

```javascript
// قبل الإصلاح
if (transaction_type === 'sale' && selling_price > 0) {
  // حساب مصاريف النقل المخصصة لهذا الصنف
  let transportCostPerUnit = 0;
  // ... حساب معقد لمصاريف النقل
  profit = calculateProfitWithTransport(selling_price, avgPrice, quantity, transportCostPerUnit);
}

// بعد الإصلاح
if (transaction_type === 'sale' && selling_price > 0) {
  // حساب الربح الأساسي بدون مصاريف النقل
  // مصاريف النقل تم خصمها بالفعل من الرصيد الحالي في عمليات الشراء
  profit = calculateProfit(selling_price, avgPrice, quantity);
}
```

### 2. تحديث `cashbox-manager.js`

```javascript
// قبل الإصلاح
for (const sale of sales) {
  if (sale.selling_price > 0 && sale.avg_price > 0) {
    // حساب مصاريف النقل المخصصة لهذا الصنف
    let transportCostPerUnit = 0;
    // ... حساب معقد
    const saleProfit = calculateProfitWithTransport(/* ... */);
  }
}

// بعد الإصلاح
for (const sale of sales) {
  if (sale.selling_price > 0 && sale.avg_price > 0) {
    // حساب الربح الأساسي بدون مصاريف النقل
    const basicProfit = (sale.selling_price - sale.avg_price) * sale.quantity;
    const saleProfit = Math.max(0, basicProfit);
  }
}
```

### 3. تحديث `src/pages/Reports.js`

تم تحديث جميع دوال حساب الأرباح في التقارير لاستخدام المنطق الجديد:

```javascript
// قبل الإصلاح
const calculateProfitWithTransport = (transaction) => {
  const basicProfit = (transaction.selling_price - transaction.avg_price) * transaction.quantity;
  const transportCost = transaction.transport_cost || 0;
  const finalProfit = basicProfit - transportCost;
  return Math.max(0, finalProfit);
};

// بعد الإصلاح
const calculateProfitWithoutTransport = (transaction) => {
  const basicProfit = (transaction.selling_price - transaction.avg_price) * transaction.quantity;
  return Math.max(0, basicProfit);
};
```

## النتائج المتوقعة

1. **✅ إزالة الخصم المزدوج**: مصاريف النقل لا تخصم مرة أخرى من الأرباح
2. **✅ حساب أرباح أكثر دقة**: الأرباح تعكس الفرق الفعلي بين سعر البيع والشراء
3. **✅ شفافية أكبر**: منطق واضح ومفهوم لحساب التكاليف والأرباح
4. **✅ اتساق في النظام**: جميع أجزاء النظام تستخدم نفس المنطق

## اختبار الإصلاح

يمكن تشغيل الاختبار للتحقق من صحة الإصلاح:

```bash
node test-transport-fix-verification.js
```

## ملاحظات مهمة

- **مصاريف النقل في المشتريات**: تؤثر فقط على الرصيد الحالي
- **مصاريف النقل في المبيعات**: لا تؤثر على حساب الأرباح
- **الأرباح**: تحسب كالفرق بين سعر البيع وسعر الشراء فقط
- **الشفافية**: كل تكلفة تخصم مرة واحدة فقط من المكان المناسب
