import React from 'react';
import { FaPrint, FaFilePdf } from 'react-icons/fa';
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import html2canvas from 'html2canvas';

/**
 * مكون لطباعة فاتورة المبيعات
 * @param {Object} props - خصائص المكون
 * @param {Object} props.sale - بيانات عملية البيع
 * @param {Object} props.companyInfo - معلومات الشركة (اختياري)
 */
const InvoicePrinter = ({ sale, companyInfo = {} }) => {
  /**
   * تنسيق التاريخ (بالتنسيق الميلادي)
   * @param {string} dateString - سلسلة التاريخ
   * @returns {string} - التاريخ المنسق
   */
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();

    return `${year}/${month}/${day}`;
  };

  /**
   * طباعة الفاتورة
   */
  const printInvoice = () => {
    try {
      console.log('بدء طباعة الفاتورة:', { sale, companyInfo });

      // استخدام وظيفة الطباعة المحسنة من ملف printInvoice
      const printInvoiceUtil = require('../utils/printInvoice').default;

      if (typeof printInvoiceUtil !== 'function') {
        console.error('وظيفة طباعة الفاتورة غير متوفرة');
        alert('وظيفة طباعة الفاتورة غير متوفرة. يرجى التحقق من تثبيت المكتبات اللازمة.');
        return;
      }

      // إعدادات الشركة
      const settings = {
        systemName: companyInfo?.name || 'شركة اتش قروب',
        address: companyInfo?.address || 'للتصميم و تصنيع الأثاث والديكورات',
        logoUrl: companyInfo?.logo || ''
      };

      // بيانات العميل
      const customer = {
        name: sale?.customer_name || 'عميل',
        phone: ''
      };

      // طباعة الفاتورة
      printInvoiceUtil(sale, customer, settings);

      console.log('تم إرسال الفاتورة للطباعة بنجاح');
      return;
    } catch (error) {
      console.error('خطأ في طباعة الفاتورة:', error);
      alert('حدث خطأ أثناء محاولة طباعة الفاتورة. سيتم استخدام الطريقة البديلة.');
    }

    // الطريقة البديلة للطباعة
    const printWindow = window.open('', '_blank');

    if (!printWindow) {
      alert('يرجى السماح بالنوافذ المنبثقة لطباعة الفاتورة');
      return;
    }

    // الحصول على معلومات الشركة
    const companyName = companyInfo.name || 'شركة اتش قروب';
    const companyAddress = companyInfo.address || 'للتصميم و تصنيع الأثاث والديكورات';
    const companyPhone = companyInfo.phone || '';
    const companyEmail = companyInfo.email || '';
    // استخدام شعار الشركة من إعدادات النظام
    const companyLogo = companyInfo.logo || (window.appSettings && window.appSettings.logoUrl) || '';

    // تنسيق التاريخ والوقت
    const invoiceDate = formatDate(sale.transaction_date);

    // إنشاء رقم الفاتورة
    const invoiceNumber = sale.transaction_id || `H${sale.id.toString().padStart(5, '0')}`;

    // استخدام document.open() قبل الكتابة
    printWindow.document.open();

    // استخدام innerHTML بدلاً من document.write
    const htmlContent = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>فاتورة رقم ${invoiceNumber}</title>
          <!-- استخدام ملف الخطوط المحلي -->
          <style>
            @font-face {
              font-family: 'Cairo';
              font-style: normal;
              font-weight: 400;
              src: url('${window.location.origin}/assets/fonts/Cairo-Regular.ttf') format('truetype');
            }

            @font-face {
              font-family: 'Cairo';
              font-style: normal;
              font-weight: 600;
              src: url('${window.location.origin}/assets/fonts/Cairo-SemiBold.ttf') format('truetype');
            }

            @font-face {
              font-family: 'Cairo';
              font-style: normal;
              font-weight: 700;
              src: url('${window.location.origin}/assets/fonts/Cairo-Bold.ttf') format('truetype');
            }
          </style>
          <style>
            @page {
              size: A4;
              margin: 1cm;
            }

            body {
              font-family: 'Cairo', Arial, sans-serif;
              direction: rtl;
              padding: 0;
              margin: 0;
              font-size: 14px;
              color: #1a3a5f;
            }
            .invoice-container {
              max-width: 800px;
              margin: 0 auto;
              padding: 20px;
              border: 1px solid #ddd;
              box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            }
            .invoice-header {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              margin-bottom: 30px;
            }
            .company-info {
              text-align: right;
            }
            .logo-container {
              display: flex;
              align-items: center;
              margin-bottom: 10px;
            }
            .logo {
              width: 80px;
              height: 80px;
              object-fit: contain;
              margin-left: 15px;
              border-radius: 5px;
              border: 1px solid #eee;
            }
            .company-name {
              font-size: 24px;
              font-weight: bold;
              color: #1a3a5f;
              margin: 0;
            }
            .company-slogan {
              font-size: 16px;
              color: #e67e22;
              margin: 5px 0;
            }
            .invoice-info {
              text-align: left;
            }
            .invoice-number {
              font-size: 16px;
              font-weight: bold;
              color: #1a3a5f;
              margin-bottom: 5px;
            }
            .invoice-date {
              font-size: 14px;
              color: #1a3a5f;
            }
            .separator {
              border-bottom: 1px dotted #e67e22;
              margin: 15px 0;
            }
            .customer-info {
              display: flex;
              justify-content: space-between;
              margin-bottom: 20px;
            }
            .customer-name {
              font-weight: bold;
            }
            .phone-number {
              direction: ltr;
              text-align: right;
            }
            .invoice-table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 20px;
              border: 1px solid #eee;
            }
            .invoice-table th {
              background-color: #f7f9fc;
              color: #1a3a5f;
              font-weight: bold;
              padding: 10px;
              text-align: right;
              border-bottom: 2px solid #e67e22;
            }
            .invoice-table td {
              padding: 10px;
              text-align: right;
              border-bottom: 1px solid #eee;
            }
            .invoice-table tr:nth-child(even) {
              background-color: #f9f9f9;
            }
            .invoice-table .amount {
              font-weight: bold;
            }
            .invoice-summary {
              margin-top: 20px;
              border: 1px solid #eee;
              padding: 15px;
              border-radius: 5px;
              background-color: #f9f9f9;
            }
            .summary-row {
              display: flex;
              justify-content: space-between;
              margin-bottom: 8px;
              padding-bottom: 8px;
              border-bottom: 1px dotted #ddd;
            }
            .summary-label {
              font-weight: bold;
              color: #1a3a5f;
            }
            .summary-value {
              font-weight: bold;
            }
            .total-row {
              font-size: 16px;
              font-weight: bold;
              color: #1a3a5f;
              margin-top: 10px;
              border-bottom: none !important;
              padding-top: 5px;
            }
            .terms {
              margin-top: 30px;
              border: 1px solid #eee;
              padding: 20px;
              border-radius: 5px;
              background-color: #fff;
            }
            .terms-title {
              font-weight: bold;
              color: #e67e22;
              margin-bottom: 15px;
              font-size: 16px;
              border-bottom: 1px solid #eee;
              padding-bottom: 8px;
            }
            .terms-item {
              margin-bottom: 8px;
              color: #1a3a5f;
              padding-right: 15px;
              position: relative;
            }
            .terms-item:before {
              content: "•";
              position: absolute;
              right: 0;
              color: #e67e22;
            }
            .signature {
              margin-top: 40px;
              text-align: left;
              display: flex;
              flex-direction: column;
              align-items: flex-start;
            }
            .signature-line {
              width: 200px;
              border-bottom: 2px solid #1a3a5f;
              margin-bottom: 8px;
            }
            .signature img {
              max-width: 150px;
              margin-bottom: 10px;
            }
            .footer {
              margin-top: 30px;
              text-align: center;
              font-size: 12px;
              color: #777;
              padding-top: 15px;
              border-top: 1px dashed #ddd;
            }
            .highlight {
              color: #e67e22;
              font-weight: bold;
            }
            @media print {
              body {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
              }
              .no-print {
                display: none;
              }
            }
          </style>
        </head>
        <body>
          <div class="invoice-container">
            <div class="invoice-header">
              <div class="company-info">
                <div class="logo-container">
                  ${companyLogo ? `<img src="${companyLogo}" class="logo" alt="شعار الشركة" />` : ''}
                  <div>
                    <h1 class="company-name">${companyName}</h1>
                    <p class="company-slogan">${companyAddress}</p>
                  </div>
                </div>
              </div>
              <div class="invoice-info">
                <div class="invoice-number">فاتورة رقم: ${invoiceNumber}</div>
                <div class="invoice-date">التاريخ: ${invoiceDate}</div>
              </div>
            </div>

            <div class="separator"></div>

            <div class="customer-info">
              <div>
                <span class="customer-name">صاحب الطلب: </span>
                <span>${sale.customer || 'عميل نقدي'}</span>
              </div>
              <div>
                <span class="customer-name">رقم الهاتف: </span>
                <span class="phone-number">${companyPhone || '-'}</span>
              </div>
            </div>

            <table class="invoice-table">
              <thead>
                <tr>
                  <th>#</th>
                  <th>وصف المنتج</th>
                  <th>الكمية</th>
                  <th>السعر</th>
                  <th>المجموع</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>1</td>
                  <td>${sale.item_name}</td>
                  <td>${sale.quantity}</td>
                  <td>${sale.price ? sale.price.toFixed(3) : '------'}</td>
                  <td class="amount">${sale.total_price ? sale.total_price.toFixed(3) : '------'}</td>
                </tr>
              </tbody>
            </table>

            <div class="invoice-summary">
              <div class="summary-row">
                <span class="summary-label">المبلغ المستلم:</span>
                <span class="summary-value">${sale.paid_amount ? sale.paid_amount.toFixed(3) : '0.000'}</span>
              </div>
              <div class="summary-row">
                <span class="summary-label">المبلغ الباقي:</span>
                <span class="summary-value">${sale.remaining_amount ? sale.remaining_amount.toFixed(3) : (sale.total_price ? sale.total_price.toFixed(3) : '0.000')}</span>
              </div>
              <div class="summary-row total-row">
                <span class="summary-label">المبلغ الإجمالي:</span>
                <span class="summary-value">${sale.total_price ? sale.total_price.toFixed(3) : '------'}</span>
              </div>
            </div>

            <div class="terms">
              <div class="terms-title">بنود الاتفاق:</div>
              <div class="terms-item">مدة العرض 3 أيام من تاريخ اصدار الفاتورة.</div>
              <div class="terms-item">دفعة متقدمة بنسبة 70% عند الموافقة على العرض.</div>
              <div class="terms-item">دفعـة ثانية بنسبة 30% قبل 3 أيام من موعد التركيب.</div>
              <div class="terms-item">مدة التسليم 45 يوم من تاريخ استلام الدفعة الأولى.</div>
              <div class="terms-item highlight">هذا العرض شامل النقل والتنزيل الطابق الأرضي فقط.</div>
            </div>

            <div class="signature">
              <div class="signature-line"></div>
              <div>التوقيع</div>
            </div>

            <div class="footer">
              اختياركم لنا سبب كافي لأن نقدم لكم أفضل ما لدينا من خدماتنا، شكراً لكم.
            </div>
          </div>

          <script>
            window.onload = function() {
              setTimeout(function() {
                window.print();
                window.close();
              }, 500);
            };
          </script>
        </body>
      </html>
    `;

    // تعيين محتوى HTML للنافذة - استخدام طريقة آمنة
    printWindow.document.documentElement.innerHTML = htmlContent;

    printWindow.document.close();
  };

  /**
   * تصدير الفاتورة كملف PDF
   */
  const exportToPDF = () => {
    try {
      // إنشاء مستند PDF جديد
      const doc = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4',
        putOnlyUsedFonts: true,
        compress: true
      });

      // الحصول على معلومات الشركة
      const companyName = companyInfo.name || 'شركة اتش قروب';
      const companyAddress = companyInfo.address || 'للتصميم و تصنيع الأثاث والديكورات';
      const companyPhone = companyInfo.phone || '';
      // استخدام شعار الشركة من إعدادات النظام
      const companyLogo = companyInfo.logo || (window.appSettings && window.appSettings.logoUrl) || '';

      // تنسيق التاريخ والوقت
      const invoiceDate = formatDate(sale.transaction_date);

      // إنشاء رقم الفاتورة
      const invoiceNumber = sale.transaction_id || `H${sale.id.toString().padStart(5, '0')}`;

      // الحصول على أبعاد الصفحة
      const pageWidth = doc.internal.pageSize.getWidth();
      const pageHeight = doc.internal.pageSize.getHeight();

      // تعيين الألوان
      const primaryColor = [26, 58, 95]; // #1a3a5f - اللون الأزرق الداكن
      const accentColor = [230, 126, 34]; // #e67e22 - اللون البرتقالي

      // إضافة معلومات الرأس - الشعار والاسم
      doc.setFillColor(255, 255, 255);
      doc.roundedRect(10, 10, 80, 25, 0, 0, 'F');

      // إضافة الشعار إذا كان متوفراً
      if (companyLogo) {
        try {
          doc.addImage(companyLogo, 'PNG', 15, 10, 25, 25);
        } catch (logoError) {
          console.error('خطأ في إضافة الشعار:', logoError);
        }
      }

      // إضافة اسم الشركة
      doc.setFont('helvetica', 'bold');
      doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2]);
      doc.setFontSize(18);
      doc.text(companyName, 100, 20, { align: 'right' });

      // إضافة شعار الشركة
      doc.setTextColor(accentColor[0], accentColor[1], accentColor[2]);
      doc.setFontSize(12);
      doc.text(companyAddress, 100, 28, { align: 'right' });

      // إضافة معلومات الفاتورة
      doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2]);
      doc.setFontSize(12);
      doc.text(`فاتورة رقم: ${invoiceNumber}`, pageWidth - 15, 20, { align: 'right' });
      doc.text(`التاريخ: ${invoiceDate}`, pageWidth - 15, 28, { align: 'right' });

      // إضافة خط فاصل
      doc.setDrawColor(accentColor[0], accentColor[1], accentColor[2]);
      doc.setLineWidth(0.5);
      doc.line(15, 40, pageWidth - 15, 40);

      // إضافة معلومات العميل
      doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2]);
      doc.setFontSize(11);
      doc.text(`صاحب الطلب: ${sale.customer || 'عميل نقدي'}`, 15, 50);
      doc.text(`رقم الهاتف: ${companyPhone || '-'}`, pageWidth - 15, 50, { align: 'right' });

      // إضافة جدول المنتجات
      const tableColumn = ['المجموع', 'السعر', 'الكمية', 'وصف المنتج', '#'];
      const tableRows = [[
        `${sale.total_price ? sale.total_price.toFixed(3) : '------'}`,
        `${sale.price ? sale.price.toFixed(3) : '------'}`,
        sale.quantity || 1,
        sale.item_name || 'منتج',
        '1'
      ]];

      doc.autoTable({
        head: [tableColumn],
        body: tableRows,
        startY: 60,
        styles: {
          halign: 'right',
          font: 'helvetica',
          fontSize: 10,
          textColor: [50, 50, 50]
        },
        headStyles: {
          fillColor: [247, 249, 252],
          textColor: primaryColor,
          fontStyle: 'bold',
          lineWidth: 0.1,
          lineColor: accentColor
        },
        columnStyles: {
          0: { fontStyle: 'bold' }
        },
        theme: 'grid',
        tableLineColor: [220, 220, 220]
      });

      // إضافة ملخص الفاتورة
      const finalY = doc.lastAutoTable ? doc.lastAutoTable.finalY + 10 : 100;

      doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2]);
      doc.setFontSize(11);
      doc.text('المبلغ المستلم:', 15, finalY);
      doc.text(`${sale.paid_amount ? sale.paid_amount.toFixed(3) : '0.000'}`, pageWidth - 15, finalY, { align: 'right' });

      doc.text('المبلغ الباقي:', 15, finalY + 8);
      doc.text(`${sale.remaining_amount ? sale.remaining_amount.toFixed(3) : (sale.total_price ? sale.total_price.toFixed(3) : '0.000')}`, pageWidth - 15, finalY + 8, { align: 'right' });

      doc.setFontSize(13);
      doc.setFont('helvetica', 'bold');
      doc.text('المبلغ الإجمالي:', 15, finalY + 18);
      doc.text(`${sale.total_price ? sale.total_price.toFixed(3) : '------'}`, pageWidth - 15, finalY + 18, { align: 'right' });

      // إضافة بنود الاتفاق
      doc.setFontSize(12);
      doc.setTextColor(accentColor[0], accentColor[1], accentColor[2]);
      doc.text('بنود الاتفاق:', 15, finalY + 35);

      doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2]);
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');
      doc.text('مدة العرض 3 أيام من تاريخ اصدار الفاتورة.', 15, finalY + 45);
      doc.text('دفعة متقدمة بنسبة 70% عند الموافقة على العرض.', 15, finalY + 52);
      doc.text('دفعـة ثانية بنسبة 30% قبل 3 أيام من موعد التركيب.', 15, finalY + 59);
      doc.text('مدة التسليم 45 يوم من تاريخ استلام الدفعة الأولى.', 15, finalY + 66);

      doc.setTextColor(accentColor[0], accentColor[1], accentColor[2]);
      doc.setFont('helvetica', 'bold');
      doc.text('هذا العرض شامل النقل والتنزيل الطابق الأرضي فقط.', 15, finalY + 76);

      // إضافة مكان للتوقيع
      doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2]);
      doc.setFont('helvetica', 'normal');
      doc.text('التوقيع:', pageWidth - 50, finalY + 90);
      doc.line(pageWidth - 50, finalY + 95, pageWidth - 15, finalY + 95);

      // إضافة تذييل الصفحة
      doc.setFontSize(9);
      doc.setTextColor(100, 100, 100);
      doc.text('اختياركم لنا سبب كافي لأن نقدم لكم أفضل ما لدينا من خدماتنا، شكراً لكم.', pageWidth / 2, pageHeight - 20, { align: 'center' });

      // حفظ الملف
      doc.save(`فاتورة_${invoiceNumber}.pdf`);
    } catch (error) {
      console.error('خطأ في تصدير الفاتورة كملف PDF:', error);
      alert('حدث خطأ أثناء تصدير الفاتورة كملف PDF. يرجى المحاولة مرة أخرى.');
    }
  };

  return (
    <div className="invoice-actions">
      <button
        className="btn btn-sm btn-info ml-2"
        onClick={printInvoice}
        title="طباعة الفاتورة"
      >
        <FaPrint className="ml-1" />
        طباعة الفاتورة
      </button>

      <button
        className="btn btn-sm btn-primary"
        onClick={exportToPDF}
        title="تصدير كملف PDF"
      >
        <FaFilePdf className="ml-1" />
        PDF
      </button>
    </div>
  );
};

export default InvoicePrinter;
