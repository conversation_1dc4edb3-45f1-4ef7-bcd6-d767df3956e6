/* أنماط مكون البطاقة */
.app-card {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-lg);
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
  border: none;
}

.app-card:not(.no-hover):hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.app-card.no-shadow {
  box-shadow: none;
  border: 1px solid var(--border-color);
}

.app-card-header {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background-color: rgba(0, 0, 0, 0.01);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.app-card-header-left {
  display: flex;
  align-items: center;
}

.app-card-icon {
  margin-left: var(--spacing-md);
  font-size: 1.5rem;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(var(--primary-rgb), 0.1);
  border-radius: 50%;
  transition: transform 0.2s;
}

.app-card:hover .app-card-icon {
  transform: scale(1.1);
}

.app-card-titles {
  display: flex;
  flex-direction: column;
}

.app-card-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-dark);
}

.app-card-subtitle {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-top: 3px;
}

.app-card-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.app-card-body {
  padding: var(--spacing-lg);
}

.app-card-body.no-padding {
  padding: 0;
}

.app-card-footer {
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  background-color: rgba(0, 0, 0, 0.01);
}

/* تنسيقات للشاشات الصغيرة */
@media (max-width: 768px) {
  .app-card-header {
    padding: var(--spacing-sm) var(--spacing-md);
    flex-direction: column;
    align-items: flex-start;
  }
  
  .app-card-actions {
    margin-top: var(--spacing-sm);
    width: 100%;
    justify-content: flex-end;
  }
  
  .app-card-body {
    padding: var(--spacing-md);
  }
  
  .app-card-footer {
    padding: var(--spacing-sm) var(--spacing-md);
  }
}
