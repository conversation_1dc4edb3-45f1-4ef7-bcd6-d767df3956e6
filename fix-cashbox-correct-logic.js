/**
 * إصلاح منطق الخزينة وفقاً للمعادلات الصحيحة
 * الرصيد الحالي = الرصيد الافتتاحي (ثابت)
 * إجمالي الأرباح = إجمالي المبيعات - إجمالي المشتريات - مصاريف النقل
 */

const { logError, logSystem } = require('./error-handler');
const DatabaseManager = require('./database-singleton');

/**
 * إصلاح منطق الخزينة وفقاً للمعادلات الصحيحة
 */
async function fixCashboxLogic() {
  console.log('🔧 بدء إصلاح منطق الخزينة وفقاً للمعادلات الصحيحة...');
  
  try {
    // 1. تهيئة قاعدة البيانات
    console.log('\n1️⃣ تهيئة قاعدة البيانات...');
    const dbManager = DatabaseManager.getInstance();
    const dbPath = 'C:\\Users\\<USER>\\AppData\\Roaming\\warehouse-management-system\\wms-database\\warehouse.db';
    await dbManager.initialize(dbPath);
    const db = dbManager.getConnection();
    
    if (!db) {
      throw new Error('فشل في الاتصال بقاعدة البيانات');
    }
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // 2. قراءة البيانات الحالية
    console.log('\n2️⃣ قراءة البيانات الحالية...');
    const currentCashbox = db.prepare('SELECT * FROM cashbox LIMIT 1').get();
    
    if (!currentCashbox) {
      throw new Error('لا توجد خزينة في قاعدة البيانات');
    }
    
    console.log('📊 البيانات الحالية:');
    console.log(`   - الرصيد الافتتاحي: ${currentCashbox.initial_balance}`);
    console.log(`   - الرصيد الحالي: ${currentCashbox.current_balance}`);
    console.log(`   - إجمالي المبيعات: ${currentCashbox.sales_total}`);
    console.log(`   - إجمالي المشتريات: ${currentCashbox.purchases_total}`);
    console.log(`   - إجمالي مصاريف النقل: ${currentCashbox.transport_total || 0}`);
    console.log(`   - إجمالي الأرباح الحالي: ${currentCashbox.profit_total}`);

    // 3. حساب إجمالي المبيعات من المعاملات
    console.log('\n3️⃣ حساب إجمالي المبيعات من المعاملات...');
    const salesResult = db.prepare(`
      SELECT COALESCE(SUM(total_price), 0) as total_sales
      FROM transactions 
      WHERE transaction_type = 'sale'
    `).get();
    const totalSales = salesResult.total_sales;
    console.log(`   - إجمالي المبيعات من المعاملات: ${totalSales}`);

    // 4. حساب إجمالي المشتريات من المعاملات
    console.log('\n4️⃣ حساب إجمالي المشتريات من المعاملات...');
    const purchasesResult = db.prepare(`
      SELECT COALESCE(SUM(total_price), 0) as total_purchases
      FROM transactions 
      WHERE transaction_type = 'purchase'
    `).get();
    const totalPurchases = purchasesResult.total_purchases;
    console.log(`   - إجمالي المشتريات من المعاملات: ${totalPurchases}`);

    // 5. حساب إجمالي مصاريف النقل من المعاملات
    console.log('\n5️⃣ حساب إجمالي مصاريف النقل من المعاملات...');
    const transportResult = db.prepare(`
      SELECT COALESCE(SUM(transport_cost), 0) as total_transport
      FROM transactions 
      WHERE transport_cost > 0
    `).get();
    const totalTransport = transportResult.total_transport;
    console.log(`   - إجمالي مصاريف النقل من المعاملات: ${totalTransport}`);

    // 6. تطبيق المعادلات الصحيحة
    console.log('\n6️⃣ تطبيق المعادلات الصحيحة...');
    
    // المعادلة 1: الرصيد الحالي = الرصيد الافتتاحي
    const correctCurrentBalance = currentCashbox.initial_balance;
    console.log(`   - الرصيد الحالي الصحيح: ${correctCurrentBalance} (= الرصيد الافتتاحي)`);
    
    // المعادلة 2: إجمالي الأرباح = إجمالي المبيعات - إجمالي المشتريات - مصاريف النقل
    const correctProfitTotal = totalSales - totalPurchases - totalTransport;
    console.log(`   - إجمالي الأرباح الصحيح: ${correctProfitTotal} (= ${totalSales} - ${totalPurchases} - ${totalTransport})`);

    // 7. مقارنة القيم
    console.log('\n7️⃣ مقارنة القيم...');
    const currentBalanceDiff = Math.abs(currentCashbox.current_balance - correctCurrentBalance);
    const profitDiff = Math.abs(currentCashbox.profit_total - correctProfitTotal);
    const salesDiff = Math.abs(currentCashbox.sales_total - totalSales);
    const purchasesDiff = Math.abs(currentCashbox.purchases_total - totalPurchases);
    const transportDiff = Math.abs((currentCashbox.transport_total || 0) - totalTransport);
    
    console.log('📊 الفروقات:');
    console.log(`   - الرصيد الحالي: ${currentBalanceDiff > 0.01 ? '❌' : '✅'} فرق ${currentBalanceDiff}`);
    console.log(`   - إجمالي الأرباح: ${profitDiff > 0.01 ? '❌' : '✅'} فرق ${profitDiff}`);
    console.log(`   - إجمالي المبيعات: ${salesDiff > 0.01 ? '❌' : '✅'} فرق ${salesDiff}`);
    console.log(`   - إجمالي المشتريات: ${purchasesDiff > 0.01 ? '❌' : '✅'} فرق ${purchasesDiff}`);
    console.log(`   - إجمالي مصاريف النقل: ${transportDiff > 0.01 ? '❌' : '✅'} فرق ${transportDiff}`);

    // 8. تطبيق الإصلاحات
    console.log('\n8️⃣ تطبيق الإصلاحات...');
    
    const needsUpdate = currentBalanceDiff > 0.01 || profitDiff > 0.01 || salesDiff > 0.01 || purchasesDiff > 0.01 || transportDiff > 0.01;
    
    if (needsUpdate) {
      console.log('🔧 تطبيق الإصلاحات...');
      
      const updateStmt = db.prepare(`
        UPDATE cashbox
        SET current_balance = ?,
            sales_total = ?,
            purchases_total = ?,
            transport_total = ?,
            profit_total = ?,
            updated_at = ?
        WHERE id = ?
      `);
      
      const updateResult = updateStmt.run(
        correctCurrentBalance,
        totalSales,
        totalPurchases,
        totalTransport,
        correctProfitTotal,
        new Date().toISOString(),
        currentCashbox.id
      );
      
      if (updateResult.changes > 0) {
        console.log('✅ تم تطبيق الإصلاحات بنجاح');
        
        // التحقق من النتائج
        const updatedCashbox = db.prepare('SELECT * FROM cashbox WHERE id = ?').get(currentCashbox.id);
        console.log('\n📊 البيانات بعد الإصلاح:');
        console.log(`   - الرصيد الافتتاحي: ${updatedCashbox.initial_balance}`);
        console.log(`   - الرصيد الحالي: ${updatedCashbox.current_balance} (= الرصيد الافتتاحي)`);
        console.log(`   - إجمالي المبيعات: ${updatedCashbox.sales_total}`);
        console.log(`   - إجمالي المشتريات: ${updatedCashbox.purchases_total}`);
        console.log(`   - إجمالي مصاريف النقل: ${updatedCashbox.transport_total}`);
        console.log(`   - إجمالي الأرباح: ${updatedCashbox.profit_total}`);
        
        // التحقق من صحة المعادلات
        console.log('\n🧮 التحقق من صحة المعادلات:');
        const calculatedProfit = updatedCashbox.sales_total - updatedCashbox.purchases_total - updatedCashbox.transport_total;
        console.log(`   - المعادلة 1: الرصيد الحالي (${updatedCashbox.current_balance}) = الرصيد الافتتاحي (${updatedCashbox.initial_balance}) ✅`);
        console.log(`   - المعادلة 2: إجمالي الأرباح (${updatedCashbox.profit_total}) = ${updatedCashbox.sales_total} - ${updatedCashbox.purchases_total} - ${updatedCashbox.transport_total} = ${calculatedProfit} ${Math.abs(updatedCashbox.profit_total - calculatedProfit) < 0.01 ? '✅' : '❌'}`);
        
      } else {
        console.log('❌ فشل في تطبيق الإصلاحات');
      }
    } else {
      console.log('✅ لا توجد حاجة لإصلاحات - جميع القيم صحيحة وفقاً للمعادلات الجديدة');
    }

    console.log('\n🎉 تم إكمال إصلاح منطق الخزينة بنجاح!');
    console.log('\n📝 ملاحظات مهمة:');
    console.log('   - الرصيد الحالي يبقى دائماً مساوياً للرصيد الافتتاحي');
    console.log('   - إجمالي الأرباح يحسب من: المبيعات - المشتريات - مصاريف النقل');
    console.log('   - هذا المنطق يعني أن الخزينة تعرض الأرباح الصافية وليس حركة النقد');
    
    return {
      success: true,
      message: 'تم إصلاح منطق الخزينة بنجاح',
      corrections: {
        current_balance: correctCurrentBalance,
        profit_total: correctProfitTotal,
        sales_total: totalSales,
        purchases_total: totalPurchases,
        transport_total: totalTransport
      },
      applied_fixes: needsUpdate
    };

  } catch (error) {
    console.error('❌ خطأ في إصلاح منطق الخزينة:', error);
    logError(error, 'fixCashboxLogic');
    
    return {
      success: false,
      message: `خطأ في إصلاح منطق الخزينة: ${error.message}`
    };
  }
}

// تشغيل الإصلاح إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  console.log('🚀 بدء إصلاح منطق الخزينة...');
  
  fixCashboxLogic().then(result => {
    console.log('\n📋 نتيجة الإصلاح:', result);
    console.log('\n🏁 انتهى الإصلاح');
  }).catch(error => {
    console.error('❌ خطأ في تشغيل الإصلاح:', error);
  });
}

module.exports = {
  fixCashboxLogic
};
