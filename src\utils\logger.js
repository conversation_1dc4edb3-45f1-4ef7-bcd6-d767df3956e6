/**
 * نظام تسجيل الأخطاء المركزي
 * يوفر واجهة موحدة لتسجيل الأخطاء والتحذيرات والمعلومات
 */

const fs = require('fs');
const path = require('path');
const { app } = require('electron');
const { format } = require('date-fns');

// تكوين نظام التسجيل
const config = {
  logToFile: true,
  logToConsole: true,
  logLevel: 'info', // debug, info, warn, error
  maxLogSize: 5 * 1024 * 1024, // 5 ميجابايت
  maxLogFiles: 5, // عدد ملفات السجل القديمة للاحتفاظ بها
};

// مستويات التسجيل
const LOG_LEVELS = {
  debug: 0,
  info: 1,
  warn: 2,
  error: 3,
};

// الحصول على مسار ملف السجل
function getLogFilePath() {
  const userData = app ? app.getPath('userData') : path.join(process.cwd(), 'logs');
  const logsDir = path.join(userData, 'logs');
  
  // إنشاء مجلد السجلات إذا لم يكن موجودًا
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
  }
  
  const today = format(new Date(), 'yyyy-MM-dd');
  return path.join(logsDir, `app-${today}.log`);
}

// تدوير ملفات السجل
function rotateLogFiles() {
  if (!config.logToFile) return;
  
  const logPath = getLogFilePath();
  
  // التحقق من حجم ملف السجل
  if (fs.existsSync(logPath)) {
    const stats = fs.statSync(logPath);
    
    if (stats.size >= config.maxLogSize) {
      // تدوير ملفات السجل
      const logsDir = path.dirname(logPath);
      const baseName = path.basename(logPath);
      const timestamp = format(new Date(), 'HHmmss');
      const rotatedName = `${baseName}.${timestamp}`;
      
      fs.renameSync(logPath, path.join(logsDir, rotatedName));
      
      // حذف ملفات السجل القديمة إذا تجاوز العدد الحد الأقصى
      const logFiles = fs.readdirSync(logsDir)
        .filter(file => file.startsWith(baseName) && file !== baseName)
        .sort()
        .reverse();
      
      if (logFiles.length >= config.maxLogFiles) {
        for (let i = config.maxLogFiles; i < logFiles.length; i++) {
          fs.unlinkSync(path.join(logsDir, logFiles[i]));
        }
      }
    }
  }
}

// كتابة رسالة إلى ملف السجل
function writeToLogFile(level, message) {
  if (!config.logToFile) return;
  
  try {
    rotateLogFiles();
    
    const logPath = getLogFilePath();
    const timestamp = format(new Date(), 'yyyy-MM-dd HH:mm:ss.SSS');
    const logEntry = `[${timestamp}] [${level.toUpperCase()}] ${message}\n`;
    
    fs.appendFileSync(logPath, logEntry, 'utf8');
  } catch (error) {
    console.error('خطأ في كتابة ملف السجل:', error);
  }
}

// تسجيل رسالة
function log(level, message, ...args) {
  if (LOG_LEVELS[level] < LOG_LEVELS[config.logLevel]) {
    return;
  }
  
  let formattedMessage = message;
  
  // تنسيق الرسالة مع المعلمات الإضافية
  if (args.length > 0) {
    try {
      formattedMessage += ' ' + args.map(arg => {
        if (typeof arg === 'object') {
          return JSON.stringify(arg);
        }
        return String(arg);
      }).join(' ');
    } catch (error) {
      formattedMessage += ` [Error formatting arguments: ${error.message}]`;
    }
  }
  
  // تسجيل إلى وحدة التحكم
  if (config.logToConsole) {
    const consoleMethod = level === 'error' ? 'error' : 
                         level === 'warn' ? 'warn' : 
                         level === 'debug' ? 'debug' : 'log';
    
    console[consoleMethod](`[${level.toUpperCase()}] ${formattedMessage}`);
  }
  
  // تسجيل إلى ملف
  writeToLogFile(level, formattedMessage);
}

// تصدير واجهة التسجيل
const logger = {
  debug: (message, ...args) => log('debug', message, ...args),
  info: (message, ...args) => log('info', message, ...args),
  warn: (message, ...args) => log('warn', message, ...args),
  error: (message, ...args) => log('error', message, ...args),
  
  // تسجيل استثناء مع تتبع المكدس
  exception: (error, context = '') => {
    const message = `Exception${context ? ` [${context}]` : ''}: ${error.message}`;
    log('error', message, '\nStack:', error.stack);
  },
  
  // تكوين نظام التسجيل
  configure: (options) => {
    Object.assign(config, options);
  },
  
  // الحصول على مسار ملف السجل الحالي
  getLogFilePath,
};

module.exports = logger;
