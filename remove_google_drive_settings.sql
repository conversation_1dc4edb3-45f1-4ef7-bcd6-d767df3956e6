-- سكريب<PERSON> لإزالة إعدادات Google Drive من قاعدة البيانات

-- حذف إعدادات Google Drive
DELETE FROM settings WHERE key = 'googleDriveBackupEnabled';
DELETE FROM settings WHERE key = 'googleDriveAutoBackup';
DELETE FROM settings WHERE key = 'googleDriveBackupInterval';
DELETE FROM settings WHERE key = 'googleDriveKeepBackupsCount';

-- تحديث إعدادات التحديث التلقائي
INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES ('autoUpdateEnabled', 'false', CURRENT_TIMESTAMP);
INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES ('lastUpdateCheck', 'null', CURRENT_TIMESTAMP);
INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES ('isInternetConnection', 'false', CURRENT_TIMESTAMP);

-- ت<PERSON><PERSON>ي<PERSON> الحذف
SELECT 'تم حذف إعدادات Google Drive بنجاح' AS message;

-- عرض الإعدادات المتبقية
SELECT * FROM settings;
