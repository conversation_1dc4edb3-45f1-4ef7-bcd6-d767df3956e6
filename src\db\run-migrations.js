/**
 * ملف لتنفيذ ترقيات قاعدة البيانات
 */

const { addInitialQuantityMigration } = require('./migrations/add_initial_quantity');
const { removeGoogleDriveMigration } = require('./migrations/remove_google_drive');

// قائمة بالترقيات التي سيتم تنفيذها
const migrations = [
  addInitialQuantityMigration,
  removeGoogleDriveMigration
];

// وظيفة لتنفيذ الترقيات
const runMigrations = async (db) => {
  console.log('بدء تنفيذ ترقيات قاعدة البيانات...');

  try {
    // إنشاء جدول لتتبع الترقيات المنفذة إذا لم يكن موجودًا
    db.exec(`
      CREATE TABLE IF NOT EXISTS migrations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        executed_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // تنفيذ كل ترقية
    for (const migration of migrations) {
      try {
        // تنفيذ الترقية
        db.exec(migration);
        console.log('تم تنفيذ الترقية بنجاح');
      } catch (err) {
        console.error('خطأ في تنفيذ الترقية:', err);
        // لا نتوقف عن التنفيذ في حالة فشل ترقية واحدة
      }
    }

    console.log('تم الانتهاء من تنفيذ جميع الترقيات');
    return true;
  } catch (error) {
    console.error('خطأ في تنفيذ الترقيات:', error);
    return false;
  }
};

module.exports = {
  runMigrations
};
