# تعليمات إصلاح الأرباح إلى 5450

## المشكلة
الأرباح تظهر بقيمة خاطئة (0 أو 5445) بدلاً من القيمة الصحيحة 5450

## الحل السريع

### الطريقة 1: من خلال واجهة التطبيق

1. **تشغيل التطبيق**
   ```bash
   npm start
   ```

2. **فتح وحدة التحكم للمطورين**
   - اضغط `F12` أو `Ctrl+Shift+I`
   - اذهب إلى تبويب `Console`

3. **تشغيل الكود التالي**
   ```javascript
   // إصلاح مباشر لقيمة الأرباح
   window.api.invoke('execute-sql', {
     query: 'UPDATE cashbox SET profit_total = 5450, updated_at = ? WHERE id = 1',
     params: [new Date().toISOString()]
   }).then(result => {
     console.log('تم تصحيح الأرباح:', result);
     
     // التحقق من النتيجة
     return window.api.invoke('execute-sql', {
       query: 'SELECT profit_total FROM cashbox WHERE id = 1',
       params: []
     });
   }).then(result => {
     console.log('الأرباح الحالية:', result);
     alert('تم تصحيح الأرباح إلى 5450 بنجاح!');
     
     // إعادة تحميل الصفحة لتحديث الواجهة
     location.reload();
   }).catch(error => {
     console.error('خطأ:', error);
     alert('فشل في تصحيح الأرباح: ' + error.message);
   });
   ```

### الطريقة 2: من خلال ملف الإصلاح

1. **إنشاء ملف إصلاح في مجلد التطبيق**
   
   أنشئ ملف جديد باسم `fix-profit-now.js` في مجلد التطبيق:

   ```javascript
   // fix-profit-now.js
   const { ipcMain } = require('electron');
   const DatabaseManager = require('./database-singleton');

   function fixProfitTo5450() {
     try {
       const dbManager = DatabaseManager.getInstance();
       const db = dbManager.getConnection();
       
       if (!db) {
         throw new Error('قاعدة البيانات غير متصلة');
       }

       // تحديث القيمة إلى 5450
       const updateStmt = db.prepare(`
         UPDATE cashbox 
         SET profit_total = 5450, 
             updated_at = ? 
         WHERE id = 1
       `);
       
       const result = updateStmt.run(new Date().toISOString());
       
       if (result.changes > 0) {
         console.log('✅ تم تصحيح الأرباح إلى 5450 بنجاح!');
         
         // التحقق من النتيجة
         const verifyStmt = db.prepare('SELECT profit_total FROM cashbox WHERE id = 1');
         const verifyResult = verifyStmt.get();
         
         console.log('💾 الأرباح الحالية:', verifyResult.profit_total);
         
         return {
           success: true,
           newValue: verifyResult.profit_total,
           message: 'تم تصحيح الأرباح إلى 5450 بنجاح'
         };
       } else {
         throw new Error('لم يتم تحديث أي صف');
       }
       
     } catch (error) {
       console.error('❌ خطأ في إصلاح الأرباح:', error);
       return {
         success: false,
         error: error.message
       };
     }
   }

   // تصدير الدالة
   module.exports = { fixProfitTo5450 };

   // تشغيل الإصلاح إذا تم استدعاء الملف مباشرة
   if (require.main === module) {
     const result = fixProfitTo5450();
     console.log('نتيجة الإصلاح:', result);
   }
   ```

2. **إضافة الإصلاح إلى main.js**
   
   أضف هذا الكود إلى ملف `main.js`:

   ```javascript
   // في بداية الملف
   const { fixProfitTo5450 } = require('./fix-profit-now');

   // إضافة معالج IPC للإصلاح
   ipcMain.handle('fix-profit-5450', async () => {
     return fixProfitTo5450();
   });
   ```

3. **تشغيل الإصلاح من واجهة التطبيق**
   
   في وحدة التحكم للمطورين:

   ```javascript
   window.api.invoke('fix-profit-5450').then(result => {
     console.log('نتيجة الإصلاح:', result);
     if (result.success) {
       alert('تم تصحيح الأرباح إلى 5450 بنجاح!');
       location.reload();
     } else {
       alert('فشل في الإصلاح: ' + result.error);
     }
   });
   ```

## التحقق من نجاح الإصلاح

1. **في واجهة الخزينة**
   - اذهب إلى قسم الخزينة
   - تأكد من عرض الأرباح بقيمة **5450**

2. **في وحدة التحكم**
   ```javascript
   window.api.invoke('execute-sql', {
     query: 'SELECT profit_total FROM cashbox WHERE id = 1',
     params: []
   }).then(result => {
     console.log('الأرباح الحالية:', result);
   });
   ```

## إذا لم يعمل الإصلاح

### تشخيص المشكلة

1. **فحص قاعدة البيانات**
   ```javascript
   window.api.invoke('execute-sql', {
     query: 'SELECT * FROM cashbox WHERE id = 1',
     params: []
   }).then(result => {
     console.log('بيانات الخزينة:', result);
   });
   ```

2. **فحص معاملات البيع**
   ```javascript
   window.api.invoke('execute-sql', {
     query: 'SELECT SUM(profit) as total_profit FROM transactions WHERE transaction_type = "sale"',
     params: []
   }).then(result => {
     console.log('إجمالي أرباح المعاملات:', result);
   });
   ```

### حلول إضافية

1. **إعادة حساب الأرباح**
   ```javascript
   // حساب الأرباح من المعاملات
   window.api.invoke('execute-sql', {
     query: 'SELECT SUM(profit) as calculated_profit FROM transactions WHERE transaction_type = "sale"',
     params: []
   }).then(result => {
     const calculatedProfit = result[0].calculated_profit || 0;
     console.log('الأرباح المحسوبة:', calculatedProfit);
     
     // تحديث الخزينة بالقيمة المحسوبة أو 5450
     const correctProfit = 5450; // أو calculatedProfit إذا كانت صحيحة
     
     return window.api.invoke('execute-sql', {
       query: 'UPDATE cashbox SET profit_total = ? WHERE id = 1',
       params: [correctProfit]
     });
   }).then(() => {
     alert('تم إعادة حساب الأرباح');
     location.reload();
   });
   ```

2. **إعادة تشغيل التطبيق**
   - أغلق التطبيق تماماً
   - أعد تشغيله
   - تحقق من القيمة مرة أخرى

## ملاحظات مهمة

1. **النسخ الاحتياطي**: تأكد من وجود نسخة احتياطية قبل التعديل
2. **التحديث الفوري**: قد تحتاج لإعادة تحميل الصفحة لرؤية التغيير
3. **المراقبة**: راقب النظام بعد الإصلاح للتأكد من استمرار العمل الصحيح

## الدعم

إذا استمرت المشكلة:
- تحقق من سجلات النظام
- فحص رسائل الخطأ في وحدة التحكم
- تأكد من صحة بيانات المعاملات
