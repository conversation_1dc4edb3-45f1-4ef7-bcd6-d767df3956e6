// آلية احتياطية لـ window.api في حالة عدم توفره
console.log('[API-FALLBACK] تحميل آلية احتياطية لـ window.api');

// التحقق من توفر window.api
const checkAPI = () => {
  if (typeof window !== 'undefined' && window.api) {
    console.log('[API-FALLBACK] ✅ window.api متوفر');
    return true;
  }
  console.log('[API-FALLBACK] ❌ window.api غير متوفر');
  return false;
};

// إنشاء API احتياطي
const createFallbackAPI = () => {
  console.log('[API-FALLBACK] إنشاء API احتياطي...');
  
  const fallbackAPI = {
    invoke: async (channel, ...args) => {
      console.log(`[API-FALLBACK] استدعاء: ${channel}`, args);
      
      // محاولة استخدام window.api الأصلي إذا كان متوفراً
      if (window.api && typeof window.api.invoke === 'function') {
        return await window.api.invoke(channel, ...args);
      }
      
      // إرجاع استجابة احتياطية
      console.warn(`[API-FALLBACK] لا يمكن تنفيذ ${channel} - API غير متوفر`);
      return { success: false, error: 'API غير متوفر' };
    },
    
    on: (channel, callback) => {
      console.log(`[API-FALLBACK] تسجيل مستمع: ${channel}`);
      
      if (window.api && typeof window.api.on === 'function') {
        return window.api.on(channel, callback);
      }
      
      console.warn(`[API-FALLBACK] لا يمكن تسجيل مستمع ${channel} - API غير متوفر`);
    },
    
    removeAllListeners: (channel) => {
      console.log(`[API-FALLBACK] إزالة مستمعين: ${channel}`);
      
      if (window.api && typeof window.api.removeAllListeners === 'function') {
        return window.api.removeAllListeners(channel);
      }
      
      console.warn(`[API-FALLBACK] لا يمكن إزالة مستمعين ${channel} - API غير متوفر`);
    },
    
    test: () => {
      console.log('[API-FALLBACK] اختبار API احتياطي');
      return 'API احتياطي يعمل';
    }
  };
  
  return fallbackAPI;
};

// تهيئة API
const initializeAPI = () => {
  console.log('[API-FALLBACK] تهيئة API...');
  
  // انتظار قصير للتأكد من تحميل window.api
  setTimeout(() => {
    if (!checkAPI()) {
      console.log('[API-FALLBACK] إنشاء API احتياطي...');
      window.api = createFallbackAPI();
      console.log('[API-FALLBACK] ✅ تم إنشاء API احتياطي');
    }
  }, 100);
  
  // فحص دوري للتأكد من توفر API
  const checkInterval = setInterval(() => {
    if (checkAPI()) {
      console.log('[API-FALLBACK] ✅ window.api متوفر الآن');
      clearInterval(checkInterval);
    }
  }, 1000);
  
  // إيقاف الفحص الدوري بعد 10 ثوان
  setTimeout(() => {
    clearInterval(checkInterval);
    console.log('[API-FALLBACK] انتهى الفحص الدوري');
  }, 10000);
};

// تصدير الوظائف
export { checkAPI, createFallbackAPI, initializeAPI };

// تهيئة تلقائية عند تحميل الملف
if (typeof window !== 'undefined') {
  initializeAPI();
}
