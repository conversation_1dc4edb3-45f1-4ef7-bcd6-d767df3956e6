/* تحسينات صفحة البيع للعملاء - متوافقة مع modern-theme.css */
/* تم تحديثه في المرحلة السادسة من خطة التحسين */

/* تنسيق العنوان الرئيسي */
.page-header {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  box-shadow: var(--shadow-sm);
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: transform 0.2s, box-shadow 0.2s;
  border: none;
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to left, var(--primary-color), var(--accent-color));
  opacity: 0.8;
}

.page-header:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.page-header h2 {
  font-size: 1.8rem;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  font-weight: 700;
}

.page-header p {
  color: var(--text-light);
  margin: 0;
}

.header-icon {
  margin-left: 0.75rem;
  color: var(--accent-color);
  font-size: 1.5rem;
  background-color: rgba(230, 126, 34, 0.1);
  padding: 8px;
  border-radius: 50%;
  transition: transform 0.3s;
}

.page-header:hover .header-icon {
  transform: scale(1.1);
}

/* تحسين البطاقات */
.card {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-lg);
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
  border: none;
  position: relative;
}

.card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to left, var(--secondary-color), var(--primary-color));
  opacity: 0.8;
}

.card-header {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background-color: rgba(0, 0, 0, 0.01);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-dark);
  display: flex;
  align-items: center;
}

.card-header h3 .header-icon {
  margin-left: var(--spacing-xs);
  font-size: 1.5em;
  background-color: rgba(44, 67, 86, 0.1);
  padding: 8px;
  border-radius: 50%;
  margin-left: 10px;
  transition: transform 0.3s;
  color: var(--secondary-color);
}

.card:hover .card-header h3 .header-icon {
  transform: scale(1.1);
}

.card-actions {
  display: flex;
  align-items: center;
}

.customer-count {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--text-light);
  padding: 0.4rem 0.8rem;
  border-radius: 50px;
  font-size: 0.85rem;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.card-body {
  padding: var(--spacing-lg);
}

/* تحسين أزرار التصفية */
.filter-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  align-items: center;
}

/* تنسيق قائمة اختيار العميل الدائم */
.filter-dropdown {
  margin: var(--spacing-sm) 0 var(--spacing-lg);
  padding: var(--spacing-md);
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color);
  transition: all 0.3s;
}

.filter-dropdown:hover {
  box-shadow: var(--shadow-sm);
  border-color: var(--primary-color);
}

.filter-dropdown label {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-xs);
  color: var(--text-dark);
  font-weight: 600;
}

.form-select {
  width: 100%;
  padding: 0.6rem 0.8rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
  background-color: white;
  color: var(--text-dark);
}

.form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
  outline: none;
}

.search-container {
  flex: 1;
  min-width: 250px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  right: 1rem;
  color: var(--text-light);
  transition: color 0.3s;
}

.search-input {
  width: 100%;
  padding: 0.6rem 2.5rem 0.6rem 0.8rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
  background-color: white;
  color: var(--text-dark);
}

.search-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
  outline: none;
}

.search-input:focus + .search-icon {
  color: var(--primary-color);
}

.filter-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.filter-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid var(--border-color);
  background-color: var(--card-bg);
  color: var(--text-dark);
  font-size: 0.9rem;
}

.filter-btn:hover {
  background-color: rgba(0, 0, 0, 0.02);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.filter-btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.filter-btn.active:hover {
  background-color: var(--primary-dark);
}

.icon-left {
  margin-left: var(--spacing-xs);
}

/* تحسين الجدول */
.table-container {
  overflow-x: auto;
  border-radius: var(--border-radius-md);
  background-color: var(--card-bg);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-lg);
  transition: transform 0.3s, box-shadow 0.3s;
}

.table-container:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th {
  background-color: rgba(0, 0, 0, 0.02);
  color: var(--text-dark);
  font-weight: 600;
  text-align: right;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  transition: all 0.3s;
}

.table th:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.th-icon {
  margin-left: var(--spacing-xs);
  color: var(--primary-color);
  font-size: 0.9rem;
}

.th-small {
  width: 50px;
}

.th-actions {
  width: 120px;
}

.table td {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  vertical-align: middle;
}

.table tbody tr {
  transition: background-color 0.2s, transform 0.2s;
}

.table tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.01);
  transform: translateY(-2px);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* تنسيق الشارات */
.badge {
  display: inline-block;
  padding: 0.25em 0.6em;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: 50px;
  text-align: center;
  transition: all 0.3s;
  display: inline-flex;
  align-items: center;
}

.badge.primary {
  background-color: rgba(var(--primary-rgb), 0.1);
  color: var(--primary-color);
  border: 1px solid rgba(var(--primary-rgb), 0.2);
}

.badge.primary:hover {
  background-color: rgba(var(--primary-rgb), 0.2);
  transform: translateY(-2px);
}

.badge.info {
  background-color: rgba(var(--info-rgb), 0.1);
  color: var(--info-color);
  border: 1px solid rgba(var(--info-rgb), 0.2);
}

.badge.info:hover {
  background-color: rgba(var(--info-rgb), 0.2);
  transform: translateY(-2px);
}

.badge.secondary {
  background-color: rgba(var(--secondary-rgb), 0.1);
  color: var(--secondary-color);
  border: 1px solid rgba(var(--secondary-rgb), 0.2);
}

.badge.secondary:hover {
  background-color: rgba(var(--secondary-rgb), 0.2);
  transform: translateY(-2px);
}

.icon-right {
  margin-right: var(--spacing-xs);
}

/* تنسيق الرصيد */
.balance {
  display: inline-flex;
  align-items: center;
  font-weight: 600;
  padding: 0.25em 0.6em;
  border-radius: 50px;
  transition: all 0.3s;
}

.balance.positive {
  color: var(--success-color);
  background-color: rgba(var(--success-rgb), 0.1);
}

.balance.negative {
  color: var(--danger-color);
  background-color: rgba(var(--danger-rgb), 0.1);
}

.balance:hover {
  transform: translateY(-2px);
}

/* تنسيق رقم الهاتف */
.phone-number {
  display: inline-flex;
  align-items: center;
  color: var(--text-dark);
  transition: all 0.3s;
}

.phone-number:hover {
  color: var(--primary-color);
  transform: translateY(-2px);
}

/* تنسيق اسم العميل الدائم */
.parent-name {
  display: inline-flex;
  align-items: center;
  color: var(--info-color);
  font-weight: 600;
  transition: all 0.3s;
}

.parent-name:hover {
  transform: translateY(-2px);
}

/* تنسيق الإجراءات */
.actions {
  display: flex;
  gap: var(--spacing-xs);
}

.btn-success {
  background-color: var(--success-color);
  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  padding: 0.5rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-success:hover {
  background-color: #219653;
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.btn-info {
  background-color: var(--info-color);
  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  padding: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-info:hover {
  background-color: #8e44ad;
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.action-text {
  display: inline-block;
  margin-right: 0.25rem;
}

/* تنسيق نافذة البيع */
.modal-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* تنسيق مكون الإكمال التلقائي في نافذة البيع للعميل */
.sales-autocomplete .item-autocomplete-input {
  padding: 0.6rem 2.5rem 0.6rem 0.8rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
  background-color: white;
  color: var(--text-dark);
}

.sales-autocomplete .item-autocomplete-icon {
  right: 1rem;
  color: var(--text-light);
  transition: color 0.3s;
}

.sales-autocomplete .item-autocomplete-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
  outline: none;
}

.sales-autocomplete .item-autocomplete-input:focus + .item-autocomplete-icon {
  color: var(--primary-color);
}

.sales-autocomplete .item-autocomplete-suggestions-container-open {
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
  z-index: 1100;
}

.sales-autocomplete .item-autocomplete-suggestion-highlighted {
  background-color: rgba(var(--primary-rgb), 0.05);
}

.sales-autocomplete .item-suggestion-name {
  font-weight: 600;
  color: var(--text-dark);
}

.sales-autocomplete .item-quantity.out {
  color: var(--danger-color);
}

.sales-autocomplete .item-quantity.low {
  color: var(--warning-color);
}

.btn-view-history {
  background-color: var(--info-color);
  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-view-history:hover {
  background-color: #8e44ad;
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-light);
  cursor: pointer;
  transition: color 0.2s;
}

.close-btn:hover {
  color: var(--danger-color);
}

/* تنسيق رسالة لا يوجد عملاء */
.empty-table {
  text-align: center;
  padding: var(--spacing-xl) !important;
  color: var(--text-light);
  background-color: rgba(0, 0, 0, 0.01);
  border-radius: var(--border-radius-md);
}

/* تحسين الأزرار */
.btn-add {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  padding: 0.5rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-add:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

/* تأثيرات الحركة */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes modal-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* تحسين تجربة المستخدم */
.table tr {
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.table tr:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
  z-index: 1;
  position: relative;
}

/* تحسين الأزرار عند التفاعل */
button {
  position: relative;
  overflow: hidden;
}

button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%, -50%);
  transform-origin: 50% 50%;
}

button:focus:not(:active)::after {
  animation: ripple 1s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  100% {
    transform: scale(100, 100);
    opacity: 0;
  }
}

/* تأثيرات إضافية */
.hover-lift {
  transition: transform 0.2s, box-shadow 0.2s;
}

.hover-lift:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

/* تحسين التوافقية مع الشاشات الصغيرة */
@media (max-width: 768px) {
  .filter-container {
    flex-direction: column;
    align-items: stretch;
  }

  .search-container {
    margin-bottom: 1rem;
  }

  .filter-buttons {
    justify-content: center;
  }
}
