# إصلاح قيد الرصيد الحالي في عمليات البيع

## وصف المشكلة

كان هناك خلل في تطبيق قيد الرصيد الحالي في نظام الخزينة:

### المشكلة الحالية:
- **عند إجراء عمليات البيع**: كان الرصيد الحالي يتجاوز الرصيد الافتتاحي أثناء العمليات
- **عند إعادة تشغيل التطبيق**: كان يتم تطبيق القيد وتصحيح الرصيد الحالي

### السبب:
القيد الذي يمنع الرصيد الحالي من تجاوز الرصيد الافتتاحي كان موجوداً فقط في `main.js` عند بدء التطبيق، ولكن لم يكن يتم تطبيقه أثناء عمليات البيع في الوقت الفعلي.

## السلوك المطلوب (الصحيح)

### القاعدة الأساسية:
**الرصيد الحالي ≤ الرصيد الافتتاحي** (دائماً)

### آلية التوزيع:
1. **إذا كان الرصيد المحسوب ≤ الرصيد الافتتاحي**:
   - الرصيد الحالي = الرصيد المحسوب
   
2. **إذا كان الرصيد المحسوب > الرصيد الافتتاحي**:
   - الرصيد الحالي = الرصيد الافتتاحي
   - المبلغ الزائد يذهب تلقائياً إلى الأرباح

### مثال توضيحي:
```
الرصيد الافتتاحي: 5000
عملية بيع: 7000
الرصيد المحسوب: 5000 + 7000 = 12000

النتيجة:
- الرصيد الحالي: 5000 (لا يتجاوز الرصيد الافتتاحي)
- المبلغ الزائد: 12000 - 5000 = 7000 (يذهب للأرباح)
- إجمالي المبيعات: 7000
- إجمالي الأرباح: 7000
```

## الإصلاحات المطبقة

### 1. إصلاح `unified-transaction-manager.js` (السطر 1248-1268)

#### قبل الإصلاح:
```javascript
// حساب الرصيد الحالي الجديد: الرصيد الحالي + مبلغ البيع
const newCurrentBalance = currentBalance + numericTotalPrice;
```

#### بعد الإصلاح:
```javascript
// حساب الرصيد الحالي الجديد مع تطبيق قيد الرصيد الافتتاحي
const calculatedBalance = currentBalance + numericTotalPrice;
const initialBalance = currentCashbox.initial_balance || 0;

// تطبيق قيد الرصيد الحالي: لا يتجاوز الرصيد الافتتاحي
let newCurrentBalance;
let excessAmount = 0;

if (calculatedBalance > initialBalance) {
  // المبلغ الزائد عن الرصيد الافتتاحي يذهب إلى الأرباح
  newCurrentBalance = initialBalance;
  excessAmount = calculatedBalance - initialBalance;
  console.log(`[CASHBOX-CONSTRAINT] الرصيد المحسوب ${calculatedBalance} يتجاوز الرصيد الافتتاحي ${initialBalance}`);
  console.log(`[CASHBOX-CONSTRAINT] الرصيد النهائي: ${newCurrentBalance}, المبلغ الزائد: ${excessAmount} (يذهب للأرباح)`);
} else {
  // الرصيد المحسوب لا يتجاوز الرصيد الافتتاحي
  newCurrentBalance = calculatedBalance;
  console.log(`[CASHBOX-CONSTRAINT] الرصيد المحسوب (${calculatedBalance}) لا يتجاوز الرصيد الافتتاحي (${initialBalance})`);
}
```

### 2. إصلاح `cashbox-manager.js` (السطر 414-434)

#### قبل الإصلاح:
```javascript
if (transaction.type === 'sale' || transaction.type === 'income') {
  updatedSalesTotal += numericAmount;
  newCurrentBalance += numericAmount; // إضافة المبلغ للرصيد الحالي
  console.log(`[CASHBOX-FIX] عملية بيع/دخل: إضافة ${numericAmount} للرصيد الحالي`);
}
```

#### بعد الإصلاح:
```javascript
if (transaction.type === 'sale' || transaction.type === 'income') {
  updatedSalesTotal += numericAmount;
  
  // حساب الرصيد الحالي الجديد مع تطبيق قيد الرصيد الافتتاحي
  const calculatedBalance = newCurrentBalance + numericAmount;
  const initialBalance = cashbox.initial_balance || 0;
  
  // تطبيق قيد الرصيد الحالي: لا يتجاوز الرصيد الافتتاحي
  if (calculatedBalance > initialBalance) {
    // المبلغ الزائد عن الرصيد الافتتاحي يذهب إلى الأرباح
    newCurrentBalance = initialBalance;
    const excessAmount = calculatedBalance - initialBalance;
    console.log(`[CASHBOX-CONSTRAINT] الرصيد المحسوب ${calculatedBalance} يتجاوز الرصيد الافتتاحي ${initialBalance}`);
    console.log(`[CASHBOX-CONSTRAINT] الرصيد النهائي: ${newCurrentBalance}, المبلغ الزائد: ${excessAmount} (يذهب للأرباح)`);
  } else {
    // الرصيد المحسوب لا يتجاوز الرصيد الافتتاحي
    newCurrentBalance = calculatedBalance;
    console.log(`[CASHBOX-CONSTRAINT] الرصيد المحسوب (${calculatedBalance}) لا يتجاوز الرصيد الافتتاحي (${initialBalance})`);
  }
  
  console.log(`[CASHBOX-FIX] عملية بيع/دخل: ${cashbox.current_balance} + ${numericAmount} = ${calculatedBalance} → ${newCurrentBalance} (مع تطبيق القيد)`);
}
```

## كيفية اختبار الإصلاح

### الطريقة الأولى: الاختبار التلقائي

1. **افتح التطبيق**
2. **افتح وحدة التحكم** (F12)
3. **انسخ والصق محتوى ملف** `test-balance-constraint-fix.js`
4. **راقب النتائج** في وحدة التحكم

### الطريقة الثانية: الاختبار السريع

1. **افتح التطبيق**
2. **افتح وحدة التحكم** (F12)
3. **انسخ والصق الكود التالي**:

```javascript
// اختبار سريع لقيد الرصيد الحالي
(async () => {
  const initial = await window.api.cashbox.updateInitialBalance(3000);
  console.log("رصيد ابتدائي:", initial.cashbox.current_balance);
  
  const sale = await window.api.cashbox.addTransaction({type: "sale", amount: 5000, source: "test"});
  console.log("بعد البيع:", "رصيد حالي:", sale.cashbox.current_balance, "رصيد ابتدائي:", sale.cashbox.initial_balance);
  console.log("القيد يعمل:", sale.cashbox.current_balance <= sale.cashbox.initial_balance ? "✅" : "❌");
})();
```

### الطريقة الثالثة: الاختبار اليدوي

1. **سجل رصيد ابتدائي** (مثلاً 3000)
2. **قم بعملية بيع كبيرة** (مثلاً 5000)
3. **تحقق من النتائج**:
   - الرصيد الحالي يجب أن يبقى 3000 (لا يتجاوز الرصيد الافتتاحي)
   - إجمالي المبيعات يجب أن يكون 5000
   - إجمالي الأرباح يجب أن يكون 5000

## النتائج المتوقعة

بعد تطبيق الإصلاحات:

✅ **القيد يعمل في الوقت الفعلي**: الرصيد الحالي لا يتجاوز الرصيد الافتتاحي أثناء عمليات البيع  
✅ **التوزيع التلقائي**: المبلغ الزائد يذهب تلقائياً إلى الأرباح  
✅ **الاتساق**: لا حاجة لإعادة تشغيل التطبيق لتطبيق القيد  
✅ **دقة الحسابات**: جميع الحسابات تتم بشكل صحيح ومتسق  
✅ **الشفافية**: رسائل واضحة في وحدة التحكم توضح كيفية تطبيق القيد  

## مثال شامل

### السيناريو:
1. رصيد ابتدائي: 3000
2. بيع أول: 1000 (لا يتجاوز الرصيد الافتتاحي)
3. بيع ثاني: 5000 (يتجاوز الرصيد الافتتاحي)
4. شراء: 2000

### النتائج المتوقعة:

| العملية | الرصيد الحالي | إجمالي المبيعات | إجمالي المشتريات | إجمالي الأرباح |
|---------|---------------|-----------------|------------------|----------------|
| البداية | 3000 | 0 | 0 | 0 |
| بيع 1000 | 3000 | 1000 | 0 | 1000 |
| بيع 5000 | 3000 | 6000 | 0 | 6000 |
| شراء 2000 | 1000 | 6000 | 2000 | 4000 |

### الملاحظات:
- الرصيد الحالي لا يتجاوز 3000 أبداً
- المبلغ الزائد من البيع الثاني (5000) ذهب بالكامل للأرباح
- الشراء خصم من الرصيد الحالي بشكل طبيعي

## الملفات المعدلة

- `unified-transaction-manager.js` - إضافة قيد الرصيد الحالي في عمليات البيع
- `cashbox-manager.js` - إضافة قيد الرصيد الحالي في دالة `addTransaction`

## الملفات الجديدة

- `test-balance-constraint-fix.js` - اختبار شامل لقيد الرصيد الحالي
- `quick-balance-constraint-test.js` - اختبار سريع للقيد
- `BALANCE_CONSTRAINT_FIX.md` - هذا الملف (دليل الإصلاح)

## ملاحظات مهمة

1. **التوافق**: الإصلاحات متوافقة مع النظام الحالي ولا تؤثر على البيانات الموجودة
2. **الأداء**: لا تؤثر الإصلاحات على أداء النظام
3. **المنطق**: الإصلاحات تطبق المنطق المحاسبي المطلوب
4. **الشفافية**: رسائل واضحة في وحدة التحكم لتتبع تطبيق القيد

## في حالة استمرار المشكلة

إذا استمرت المشكلة بعد تطبيق الإصلاحات:

1. تحقق من سجلات وحدة التحكم للحصول على تفاصيل إضافية
2. تأكد من تطبيق جميع الإصلاحات بشكل صحيح
3. جرب تشغيل الاختبار السريع للتشخيص
4. تواصل مع فريق التطوير مع تفاصيل الخطأ الجديد
