/**
 * وحدة إدارة المخزون الجديدة - تقوم بإدارة عمليات المخزون وحساب متوسط السعر وسعر البيع
 * هذه الوحدة تحل محل الوظائف المتعلقة بالمخزون في الملفات الأخرى
 */

const { logError, logSystem } = require('./error-handler');
const eventSystem = require('./event-system');

// مرجع لقاعدة البيانات (سيتم تعيينه عند تهيئة الوحدة)
let db = null;

/**
 * تهيئة وحدة إدارة المخزون
 */
function initialize() {
  try {
    console.log('جاري تهيئة وحدة إدارة المخزون...');

    // الحصول على اتصال قاعدة البيانات من مدير قاعدة البيانات
    const dbManager = require('./database-singleton').getInstance();
    db = dbManager.getConnection();

    if (!db) {
      throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
    }

    console.log('تم تهيئة وحدة إدارة المخزون بنجاح');
    logSystem('تم تهيئة وحدة إدارة المخزون بنجاح', 'info');

    // التحقق من وجود الأعمدة المطلوبة في جدول المخزون
    ensureInventoryTableStructure();

    return true;
  } catch (error) {
    console.error('خطأ في تهيئة وحدة إدارة المخزون:', error);
    logError(error, 'initialize - inventory-manager');
    return false;
  }
}

/**
 * التأكد من وجود الأعمدة المطلوبة في جدول المخزون
 */
function ensureInventoryTableStructure() {
  try {
    // التحقق من وجود جدول المخزون
    const tableExistsStmt = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='inventory'");
    const tableExists = tableExistsStmt.get();

    if (!tableExists) {
      logSystem('جدول المخزون غير موجود، جاري إنشاؤه...', 'warning');

      // إنشاء جدول المخزون
      db.exec(`
        CREATE TABLE inventory (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          item_id INTEGER NOT NULL,
          current_quantity INTEGER DEFAULT 0,
          minimum_quantity INTEGER DEFAULT 0,
          avg_price REAL DEFAULT 0,
          selling_price REAL DEFAULT 0,
          last_updated TEXT DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (item_id) REFERENCES items (id) ON DELETE CASCADE
        )
      `);

      logSystem('تم إنشاء جدول المخزون بنجاح', 'info');
    } else {
      // التحقق من وجود الأعمدة المطلوبة
      const columns = [
        { name: 'current_quantity', type: 'INTEGER', default: '0' },
        { name: 'minimum_quantity', type: 'INTEGER', default: '0' },
        { name: 'avg_price', type: 'REAL', default: '0' },
        { name: 'selling_price', type: 'REAL', default: '0' },
        { name: 'last_updated', type: 'TEXT', default: 'CURRENT_TIMESTAMP' }
      ];

      for (const column of columns) {
        const columnExistsStmt = db.prepare(`PRAGMA table_info(inventory)`);
        const tableInfo = columnExistsStmt.all();
        const columnExists = tableInfo.some(col => col.name === column.name);

        if (!columnExists) {
          logSystem(`جاري إضافة عمود ${column.name} إلى جدول المخزون...`, 'warning');
          db.exec(`ALTER TABLE inventory ADD COLUMN ${column.name} ${column.type} DEFAULT ${column.default}`);
          logSystem(`تم إضافة عمود ${column.name} إلى جدول المخزون بنجاح`, 'info');
        }
      }
    }

    // إنشاء فهرس للبحث السريع
    db.exec('CREATE INDEX IF NOT EXISTS idx_inventory_item_id ON inventory(item_id)');

    return true;
  } catch (error) {
    logError(error, 'ensureInventoryTableStructure');
    return false;
  }
}

// تخزين مؤقت للأصناف المستخدمة بشكل متكرر
const itemCache = new Map();
const CACHE_EXPIRY = 60 * 1000; // مدة صلاحية التخزين المؤقت (60 ثانية)

/**
 * الحصول على معلومات المخزون لصنف معين
 * @param {number} itemId - معرف الصنف
 * @param {boolean} bypassCache - تجاوز التخزين المؤقت (اختياري)
 * @returns {Object|null} - معلومات المخزون أو null إذا لم يتم العثور على الصنف
 */
function getInventoryForItem(itemId, bypassCache = false) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // تحويل المعرف إلى رقم
    itemId = parseInt(itemId);
    if (isNaN(itemId) || itemId <= 0) {
      logSystem(`معرف الصنف غير صالح: ${itemId}`, 'warning');
      return null;
    }

    // التحقق من وجود الصنف في التخزين المؤقت
    const cacheKey = `item_${itemId}`;
    const cachedItem = itemCache.get(cacheKey);

    if (!bypassCache && cachedItem && (Date.now() - cachedItem.timestamp < CACHE_EXPIRY)) {
      logSystem(`استخدام بيانات الصنف ${itemId} من التخزين المؤقت`, 'info');
      return cachedItem.data;
    }

    // استعلام محسن يجلب فقط الحقول المطلوبة
    const stmt = db.prepare(`
      SELECT
        i.id, i.name, i.unit,
        inv.current_quantity, inv.minimum_quantity, inv.avg_price, inv.selling_price, inv.last_updated,
        inv.item_id
      FROM items i
      LEFT JOIN inventory inv ON i.id = inv.item_id
      WHERE i.id = ?
    `);

    const item = stmt.get(itemId);

    if (!item) {
      logSystem(`لم يتم العثور على الصنف بالمعرف: ${itemId}`, 'warning');
      return null;
    }

    // معالجة البيانات للتأكد من أن جميع الحقول موجودة وبالنوع الصحيح
    const processedItem = {
      id: item.id,
      item_id: item.item_id || item.id, // استخدام معرف الصنف من جدول المخزون أو من جدول الأصناف
      name: item.name || '',
      unit: item.unit || 'قطعة',
      current_quantity: typeof item.current_quantity === 'number' ? item.current_quantity : 0,
      minimum_quantity: typeof item.minimum_quantity === 'number' ? item.minimum_quantity : 0,
      avg_price: typeof item.avg_price === 'number' ? item.avg_price : 0,
      selling_price: typeof item.selling_price === 'number' ? item.selling_price : 0,
      last_updated: item.last_updated || new Date().toISOString()
    };

    // تسجيل معلومات الصنف للتشخيص بشكل أكثر تفصيلاً
    logSystem(`معلومات الصنف ${itemId} (${processedItem.name}) من قاعدة البيانات:`, 'info');
    logSystem(`- الكمية المتوفرة: ${item.current_quantity} (${typeof item.current_quantity})`, 'info');
    logSystem(`- الحد الأدنى: ${item.minimum_quantity} (${typeof item.minimum_quantity})`, 'info');
    logSystem(`- متوسط السعر: ${item.avg_price} (${typeof item.avg_price})`, 'info');
    logSystem(`- سعر البيع: ${item.selling_price} (${typeof item.selling_price})`, 'info');

    // تسجيل معلومات الصنف للتشخيص
    logSystem(`تم الحصول على معلومات الصنف ${itemId} (${processedItem.name})`, 'info');
    logSystem(`- الكمية المتوفرة: ${processedItem.current_quantity}`, 'info');
    logSystem(`- سعر البيع: ${processedItem.selling_price}`, 'info');
    logSystem(`- الحد الأدنى: ${processedItem.minimum_quantity}`, 'info');

    // إذا كان سعر البيع أو الكمية المتوفرة غير محددة، حاول الحصول عليها من جدول المعاملات
    if (processedItem.selling_price <= 0 || processedItem.current_quantity <= 0) {
      logSystem(`محاولة الحصول على معلومات إضافية للصنف ${itemId} من جدول المعاملات`, 'info');

      try {
        // الحصول على آخر معاملة شراء للصنف
        const lastPurchaseStmt = db.prepare(`
          SELECT price, quantity, selling_price, minimum_quantity
          FROM transactions
          WHERE item_id = ? AND transaction_type = 'purchase'
          ORDER BY transaction_date DESC
          LIMIT 1
        `);

        const lastPurchase = lastPurchaseStmt.get(itemId);

        if (lastPurchase) {
          logSystem(`تم العثور على آخر معاملة شراء للصنف ${itemId}`, 'info');
          logSystem(`- سعر الشراء: ${lastPurchase.price}`, 'info');
          logSystem(`- الكمية: ${lastPurchase.quantity}`, 'info');
          logSystem(`- سعر البيع المخزن: ${lastPurchase.selling_price}`, 'info');
          logSystem(`- الحد الأدنى المخزن: ${lastPurchase.minimum_quantity}`, 'info');

          // إذا كان سعر البيع غير محدد، استخدم سعر البيع المخزن في المعاملة
          if (processedItem.selling_price <= 0 && lastPurchase.selling_price > 0) {
            processedItem.selling_price = lastPurchase.selling_price;
            logSystem(`تم تعيين سعر البيع من المعاملة: ${processedItem.selling_price}`, 'info');
          }
          // إذا لم يكن هناك سعر بيع مخزن، استخدم سعر الشراء مع هامش ربح
          else if (processedItem.selling_price <= 0 && lastPurchase.price > 0) {
            // هامش ربح افتراضي 20%
            processedItem.selling_price = lastPurchase.price * 1.2;
            logSystem(`تم تعيين سعر البيع بناءً على سعر الشراء: ${processedItem.selling_price}`, 'info');
          }

          // إذا كان الحد الأدنى غير محدد، استخدم الحد الأدنى المخزن في المعاملة
          if (processedItem.minimum_quantity <= 0 && lastPurchase.minimum_quantity > 0) {
            processedItem.minimum_quantity = lastPurchase.minimum_quantity;
            logSystem(`تم تعيين الحد الأدنى من المعاملة: ${processedItem.minimum_quantity}`, 'info');
          }
        }

        // حساب الكمية المتوفرة من خلال جمع معاملات الشراء وطرح معاملات البيع
        const calculateQuantityStmt = db.prepare(`
          SELECT
            SUM(CASE WHEN transaction_type = 'purchase' THEN quantity ELSE 0 END) as total_purchased,
            SUM(CASE WHEN transaction_type = 'sale' THEN quantity ELSE 0 END) as total_sold
          FROM transactions
          WHERE item_id = ?
        `);

        const quantityData = calculateQuantityStmt.get(itemId);

        if (quantityData) {
          const totalPurchased = quantityData.total_purchased || 0;
          const totalSold = quantityData.total_sold || 0;
          const calculatedQuantity = totalPurchased - totalSold;

          logSystem(`حساب الكمية المتوفرة للصنف ${itemId}:`, 'info');
          logSystem(`- إجمالي المشتريات: ${totalPurchased}`, 'info');
          logSystem(`- إجمالي المبيعات: ${totalSold}`, 'info');
          logSystem(`- الكمية المحسوبة: ${calculatedQuantity}`, 'info');

          // استخدام الكمية المحسوبة إذا كانت الكمية الحالية غير محددة أو صفر
          if (processedItem.current_quantity <= 0 && calculatedQuantity > 0) {
            processedItem.current_quantity = calculatedQuantity;
            logSystem(`تم تحديث الكمية المتوفرة بناءً على المعاملات: ${processedItem.current_quantity}`, 'info');
          }

          // تحديث المخزون في قاعدة البيانات إذا كانت الكمية المحسوبة مختلفة عن الكمية المخزنة
          if (calculatedQuantity !== processedItem.current_quantity) {
            try {
              const updateStmt = db.prepare(`
                UPDATE inventory
                SET current_quantity = ?
                WHERE item_id = ?
              `);

              updateStmt.run(calculatedQuantity, itemId);

              processedItem.current_quantity = calculatedQuantity;
              logSystem(`تم تحديث الكمية المتوفرة في قاعدة البيانات: ${calculatedQuantity}`, 'info');
            } catch (updateError) {
              logError(updateError, 'getInventoryForItem - تحديث الكمية في قاعدة البيانات');
            }
          }
        }

        // إذا لم يكن هناك سجل في جدول المخزون، قم بإنشائه
        if (processedItem.current_quantity > 0 || processedItem.selling_price > 0) {
          try {
            const checkStmt = db.prepare('SELECT id FROM inventory WHERE item_id = ?');
            const inventoryRecord = checkStmt.get(itemId);

            if (!inventoryRecord) {
              const insertStmt = db.prepare(`
                INSERT INTO inventory (
                  item_id, current_quantity, minimum_quantity, avg_price, selling_price, last_updated
                )
                VALUES (?, ?, ?, ?, ?, ?)
              `);

              insertStmt.run(
                itemId,
                processedItem.current_quantity,
                processedItem.minimum_quantity,
                processedItem.avg_price,
                processedItem.selling_price,
                new Date().toISOString()
              );

              logSystem(`تم إنشاء سجل مخزون جديد للصنف ${itemId}`, 'info');
            }
          } catch (insertError) {
            logError(insertError, 'getInventoryForItem - إنشاء سجل مخزون جديد');
          }
        }
      } catch (innerError) {
        logError(innerError, 'getInventoryForItem - محاولة الحصول على معلومات إضافية');
      }
    }

    // تخزين النتيجة في التخزين المؤقت
    itemCache.set(cacheKey, {
      data: processedItem,
      timestamp: Date.now()
    });

    // تنظيف التخزين المؤقت إذا تجاوز حجمه 1000 عنصر
    if (itemCache.size > 1000) {
      // حذف أقدم العناصر
      const keysToDelete = [...itemCache.entries()]
        .sort((a, b) => a[1].timestamp - b[1].timestamp)
        .slice(0, 100)
        .map(entry => entry[0]);

      keysToDelete.forEach(key => itemCache.delete(key));
      logSystem(`تم تنظيف التخزين المؤقت، تم حذف ${keysToDelete.length} عنصر`, 'info');
    }

    return processedItem;
  } catch (error) {
    logError(error, 'getInventoryForItem');
    return null;
  }
}

// تخزين مؤقت لقائمة المخزون
let inventoryCache = null;
let inventoryCacheTimestamp = 0;
const INVENTORY_CACHE_EXPIRY = 5 * 60 * 1000; // مدة صلاحية التخزين المؤقت للمخزون (5 دقائق)

/**
 * الحصول على قائمة المخزون كاملة
 * هذه الوظيفة تجلب بيانات المخزون من قاعدة البيانات أو من التخزين المؤقت
 * تم تحسينها للتعامل بشكل صحيح مع عمليات الاسترجاع من خلال:
 * 1. استخدام COALESCE لضمان عدم وجود قيم NULL في النتائج
 * 2. التحقق من الكميات المسترجعة وتسجيلها للتشخيص
 * 3. تحسين التسجيل لتتبع مصدر المشكلات
 *
 * @param {boolean} forceRefresh - إجبار تحديث البيانات من قاعدة البيانات
 * @returns {Array} - قائمة المخزون
 */
function getAllInventory(forceRefresh = false) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // تسجيل معلومات الاستدعاء للتشخيص (فقط إذا كان هناك تحديث إجباري)
    if (forceRefresh) {
      console.log(`[INVENTORY-GET-ALL] بدء الحصول على جميع المخزون (forceRefresh=${forceRefresh})`);
      logSystem(`[INVENTORY-GET-ALL] بدء الحصول على جميع المخزون (forceRefresh=${forceRefresh})`, 'info');
    }

    // التحقق من وجود البيانات في التخزين المؤقت
    if (!forceRefresh &&
        inventoryCache &&
        (Date.now() - inventoryCacheTimestamp < INVENTORY_CACHE_EXPIRY)) {
      // تسجيل فقط إذا كان هناك تحديث إجباري
      if (forceRefresh) {
        console.log(`[INVENTORY-GET-ALL] استخدام بيانات المخزون من التخزين المؤقت (${inventoryCache.length} صنف)`);
        logSystem(`[INVENTORY-GET-ALL] استخدام بيانات المخزون من التخزين المؤقت (${inventoryCache.length} صنف)`, 'info');
      }
      return inventoryCache;
    }

    // تسجيل فقط إذا كان هناك تحديث إجباري
    if (forceRefresh) {
      console.log(`[INVENTORY-GET-ALL] جلب بيانات المخزون من قاعدة البيانات`);
      logSystem(`[INVENTORY-GET-ALL] جلب بيانات المخزون من قاعدة البيانات`, 'info');
    }

    // استعلام محسن يجلب فقط الحقول المطلوبة
    // استخدام COALESCE لضمان عدم وجود قيم NULL
    const stmt = db.prepare(`
      SELECT
        i.id, i.name, i.unit,
        COALESCE(inv.current_quantity, 0) as current_quantity,
        COALESCE(inv.minimum_quantity, 0) as minimum_quantity,
        COALESCE(inv.avg_price, 0) as avg_price,
        COALESCE(inv.selling_price, 0) as selling_price,
        COALESCE(inv.last_updated, i.updated_at, i.created_at) as last_updated,
        inv.item_id
      FROM items i
      LEFT JOIN inventory inv ON i.id = inv.item_id
      ORDER BY i.name
    `);

    const rawInventory = stmt.all();
    console.log(`[INVENTORY-GET-ALL] تم جلب ${rawInventory.length} صنف من قاعدة البيانات`);

    // معالجة البيانات للتأكد من أن جميع الحقول موجودة وبالنوع الصحيح
    const inventory = rawInventory.map(item => {
      // تحويل القيم إلى الأنواع المناسبة
      const processedItem = {
        id: item.id,
        item_id: item.item_id || item.id, // استخدام معرف الصنف من جدول المخزون أو من جدول الأصناف
        name: item.name || '',
        unit: item.unit || 'قطعة',
        current_quantity: typeof item.current_quantity === 'number' ? item.current_quantity : 0,
        minimum_quantity: typeof item.minimum_quantity === 'number' ? item.minimum_quantity : 0,
        avg_price: typeof item.avg_price === 'number' ? item.avg_price : 0,
        selling_price: typeof item.selling_price === 'number' ? item.selling_price : 0,
        last_updated: item.last_updated || new Date().toISOString()
      };

      return processedItem;
    });

    // التحقق من الكميات المسترجعة
    // هذا الجزء مهم للتأكد من أن الكميات المسترجعة تظهر بشكل صحيح
    try {
      // تسجيل فقط إذا كان هناك تحديث إجباري
      if (forceRefresh) {
        console.log(`[INVENTORY-GET-ALL] التحقق من الكميات المسترجعة...`);
      }

      // الحصول على إجمالي الكميات المسترجعة لكل صنف
      const getReturnsStmt = db.prepare(`
        SELECT item_id, SUM(quantity) as total_returned
        FROM return_transactions
        GROUP BY item_id
      `);

      const returnsData = getReturnsStmt.all();

      // تسجيل فقط إذا كان هناك تحديث إجباري
      if (forceRefresh) {
        console.log(`[INVENTORY-GET-ALL] تم العثور على ${returnsData.length} صنف له عمليات إرجاع`);
      }

      // إنشاء خريطة للكميات المسترجعة
      const returnsMap = new Map();
      returnsData.forEach(item => {
        returnsMap.set(item.item_id, item.total_returned || 0);
      });

      // تسجيل معلومات عن الكميات المسترجعة للتشخيص (فقط إذا كان هناك تحديث إجباري)
      if (forceRefresh && returnsData.length > 0) {
        console.log(`[INVENTORY-GET-ALL] عينة من الكميات المسترجعة:`);
        returnsData.slice(0, 3).forEach(item => {
          console.log(`- الصنف ${item.item_id}: ${item.total_returned} وحدة`);
        });
      }
    } catch (returnsError) {
      // تسجيل الخطأ فقط إذا كان هناك تحديث إجباري
      if (forceRefresh) {
        console.error(`[INVENTORY-GET-ALL] خطأ في التحقق من الكميات المسترجعة:`, returnsError);
        logSystem(`[INVENTORY-GET-ALL] خطأ في التحقق من الكميات المسترجعة: ${returnsError.message}`, 'error');
      }
      // لا نريد إيقاف العملية إذا فشل التحقق من الكميات المسترجعة
    }

    // تخزين النتيجة في التخزين المؤقت
    inventoryCache = inventory;
    inventoryCacheTimestamp = Date.now();

    // تسجيل فقط إذا كان هناك تحديث إجباري
    if (forceRefresh) {
      console.log(`[INVENTORY-GET-ALL] تم تحميل ${inventory.length} صنف من المخزون من قاعدة البيانات`);
      logSystem(`[INVENTORY-GET-ALL] تم تحميل ${inventory.length} صنف من المخزون من قاعدة البيانات`, 'info');

      // تسجيل معلومات عن بعض الأصناف للتشخيص (فقط إذا كان هناك تحديث إجباري)
      if (inventory.length > 0) {
        const sampleItems = inventory.slice(0, Math.min(3, inventory.length));
        sampleItems.forEach(item => {
          console.log(`[INVENTORY-GET-ALL] عينة من المخزون - الصنف ${item.id} (${item.name}):`);
          console.log(`- الكمية المتوفرة: ${item.current_quantity}`);
          console.log(`- الحد الأدنى: ${item.minimum_quantity}`);
          console.log(`- متوسط السعر: ${item.avg_price}`);
          console.log(`- سعر البيع: ${item.selling_price}`);
        });
      }
    }

    return inventory;
  } catch (error) {
    console.error(`[INVENTORY-GET-ALL] خطأ في الحصول على جميع المخزون:`, error);
    logError(error, 'getAllInventory');
    return [];
  }
}

/**
 * تحديث المخزون بعد عملية شراء
 * @param {number} itemId - معرف الصنف
 * @param {number} quantity - الكمية المشتراة
 * @param {number} price - سعر الشراء للوحدة
 * @param {number} sellingPrice - سعر البيع المقترح (اختياري)
 * @param {number} minimumQuantity - الحد الأدنى للكمية (اختياري)
 * @returns {Object} - نتيجة العملية
 */
function updateInventoryAfterPurchase(itemId, quantity, price, sellingPrice = 0, minimumQuantity = null) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // التحقق من صحة المدخلات
    itemId = parseInt(itemId);
    quantity = parseInt(quantity);
    price = parseFloat(price);
    sellingPrice = parseFloat(sellingPrice);

    if (isNaN(itemId) || itemId <= 0) throw new Error('معرف الصنف غير صالح');
    if (isNaN(quantity) || quantity <= 0) throw new Error('الكمية غير صالحة');
    if (isNaN(price) || price < 0) throw new Error('السعر غير صالح');

    // الحصول على معلومات المخزون الحالية
    const inventory = getInventoryForItem(itemId);

    if (!inventory) {
      throw new Error(`الصنف غير موجود: ${itemId}`);
    }

    // لا نستخدم معاملة قاعدة البيانات هنا لأن الدالة تُستدعى من داخل معاملة أخرى
    try {
      const now = new Date().toISOString();

      // الكمية الحالية في المخزون
      const currentQuantity = inventory.current_quantity || 0;

      // متوسط السعر الحالي
      const currentAvgPrice = inventory.avg_price || 0;

      // سعر البيع الحالي
      const currentSellingPrice = inventory.selling_price || 0;

      // حساب الكمية الجديدة
      const newQuantity = currentQuantity + quantity;

      // حساب متوسط السعر المرجح بالكمية
      let newAvgPrice;

      if (currentQuantity === 0) {
        // إذا كانت الكمية الحالية صفر، فإن متوسط السعر هو السعر الجديد
        newAvgPrice = price;
      } else {
        // حساب متوسط السعر المرجح بالكمية
        const totalValueBefore = currentQuantity * currentAvgPrice;
        const newValue = quantity * price;
        newAvgPrice = (totalValueBefore + newValue) / newQuantity;
      }

      // تحديد سعر البيع الجديد
      let newSellingPrice = currentSellingPrice;

      // تحديث سعر البيع فقط إذا تم تمريره وكان أكبر من صفر
      if (sellingPrice > 0) {
        newSellingPrice = sellingPrice;
      }

      // تسجيل المعلومات للتشخيص
      logSystem(`=== تحديث المخزون بعد عملية شراء ===`, 'info');
      logSystem(`- الصنف: ${inventory.name} (${itemId})`, 'info');
      logSystem(`- الكمية المشتراة: ${quantity}`, 'info');
      logSystem(`- سعر الشراء: ${price}`, 'info');
      logSystem(`- الكمية قبل التحديث: ${currentQuantity}`, 'info');
      logSystem(`- الكمية بعد التحديث: ${newQuantity}`, 'info');
      logSystem(`- متوسط السعر قبل التحديث: ${currentAvgPrice}`, 'info');
      logSystem(`- متوسط السعر بعد التحديث: ${newAvgPrice}`, 'info');
      logSystem(`- سعر البيع قبل التحديث: ${currentSellingPrice}`, 'info');
      logSystem(`- سعر البيع بعد التحديث: ${newSellingPrice}`, 'info');

      // التحقق من وجود سجل في المخزون
      const checkInventoryStmt = db.prepare('SELECT id FROM inventory WHERE item_id = ?');
      const inventoryRecord = checkInventoryStmt.get(itemId);

      // تحديد الحد الأدنى للكمية
      let minQuantity = inventory.minimum_quantity || 0;

      try {
        // إذا تم تمرير الحد الأدنى، استخدمه بغض النظر عن القيمة الحالية
        if (minimumQuantity !== null && minimumQuantity !== undefined) {
          // تحويل القيمة إلى رقم صحيح بشكل صريح
          let processedMinQuantity = minimumQuantity;
          if (typeof processedMinQuantity === 'string') {
            processedMinQuantity = processedMinQuantity.trim();
          }

          // استخدام Number بدلاً من parseInt لتجنب مشاكل التحويل
          const numericMinQuantity = Number(processedMinQuantity);

          // التحقق من أن القيمة رقم صحيح
          if (!isNaN(numericMinQuantity)) {
            minQuantity = numericMinQuantity;
            logSystem(`تم تعيين الحد الأدنى إلى: ${minQuantity}`, 'info');
          } else {
            logSystem(`تم استلام قيمة غير صالحة للحد الأدنى: ${minimumQuantity}، سيتم استخدام القيمة الحالية: ${minQuantity}`, 'warning');
          }
        } else {
          logSystem(`لم يتم تمرير قيمة للحد الأدنى، سيتم استخدام القيمة الحالية: ${minQuantity}`, 'info');
        }
      } catch (error) {
        logSystem(`خطأ في معالجة الحد الأدنى: ${error.message}، سيتم استخدام القيمة الحالية: ${minQuantity}`, 'error');
      }

      // تسجيل معلومات الحد الأدنى
      logSystem(`الحد الأدنى للصنف ${itemId} (${inventory.name}):`, 'info');
      logSystem(`- الحد الأدنى الحالي: ${inventory.minimum_quantity}`, 'info');
      logSystem(`- الحد الأدنى المستلم: ${minimumQuantity}`, 'info');
      logSystem(`- الحد الأدنى النهائي: ${minQuantity}`, 'info');

      // تسجيل معلومات إضافية للتشخيص
      logSystem(`نوع الحد الأدنى المستلم: ${typeof minimumQuantity}`, 'info');
      logSystem(`قيمة الحد الأدنى المستلم: ${minimumQuantity}`, 'info');
      logSystem(`هل الحد الأدنى المستلم null؟ ${minimumQuantity === null}`, 'info');
      logSystem(`هل الحد الأدنى المستلم undefined؟ ${minimumQuantity === undefined}`, 'info');

      // تسجيل معلومات إضافية للتشخيص
      logSystem(`قيمة minQuantity قبل التحديث: ${minQuantity}`, 'info');

      if (inventoryRecord) {
        // تحديث سجل المخزون الموجود
        const updateStmt = db.prepare(`
          UPDATE inventory
          SET current_quantity = ?, avg_price = ?, selling_price = ?, minimum_quantity = ?, last_updated = ?
          WHERE item_id = ?
        `);

        updateStmt.run(
          newQuantity,
          newAvgPrice,
          newSellingPrice,
          minQuantity,
          now,
          itemId
        );

        logSystem(`تم تحديث المخزون للصنف ${itemId} مع الحد الأدنى: ${minQuantity}`, 'info');
      } else {
        // إنشاء سجل مخزون جديد
        const insertStmt = db.prepare(`
          INSERT INTO inventory (
            item_id, current_quantity, minimum_quantity, avg_price, selling_price, last_updated
          )
          VALUES (?, ?, ?, ?, ?, ?)
        `);

        insertStmt.run(
          itemId,
          newQuantity,
          minQuantity,
          newAvgPrice,
          newSellingPrice,
          now
        );

        logSystem(`تم إنشاء سجل مخزون جديد للصنف ${itemId} مع الحد الأدنى: ${minQuantity}`, 'info');
      }

      // مسح التخزين المؤقت بعد تحديث المخزون
      clearInventoryCache('purchase', itemId);

      // إرسال إشعار بتحديث المخزون
      eventSystem.notifyInventoryUpdated({
        itemId,
        name: inventory.name,
        current_quantity: newQuantity,
        avg_price: newAvgPrice,
        selling_price: newSellingPrice,
        operation: 'purchase',
        quantity: quantity
      });

      logSystem(`تم تحديث المخزون بنجاح بعد عملية الشراء للصنف ${itemId}`, 'info');

      return {
        success: true,
        itemId,
        newQuantity,
        newAvgPrice,
        newSellingPrice
      };
    } catch (error) {
      // لا نحتاج إلى التراجع عن المعاملة هنا لأن الدالة تُستدعى من داخل معاملة أخرى
      logError(error, 'updateInventoryAfterPurchase');
      throw error;
    }
  } catch (error) {
    logError(error, 'updateInventoryAfterPurchase');
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * تحديث المخزون بعد عملية بيع
 * @param {number} itemId - معرف الصنف
 * @param {number} quantity - الكمية المباعة
 * @param {number} sellingPrice - سعر البيع للوحدة (اختياري، إذا كان مختلفًا عن سعر البيع المخزن)
 * @returns {Object} - نتيجة العملية
 */
function updateInventoryAfterSale(itemId, quantity, sellingPrice = 0) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // التحقق من صحة المدخلات
    itemId = parseInt(itemId);
    quantity = parseInt(quantity);
    sellingPrice = parseFloat(sellingPrice);

    if (isNaN(itemId) || itemId <= 0) throw new Error('معرف الصنف غير صالح');
    if (isNaN(quantity) || quantity <= 0) throw new Error('الكمية غير صالحة');

    // الحصول على معلومات المخزون الحالية
    const inventory = getInventoryForItem(itemId);

    if (!inventory) {
      throw new Error(`الصنف غير موجود: ${itemId}`);
    }

    // لا نستخدم معاملة قاعدة البيانات هنا لأن الدالة تُستدعى من داخل معاملة أخرى
    try {
      const now = new Date().toISOString();

      // الكمية الحالية في المخزون
      const currentQuantity = inventory.current_quantity || 0;

      // التحقق من توفر الكمية المطلوبة
      if (currentQuantity < quantity) {
        throw new Error(`الكمية المتوفرة (${currentQuantity}) أقل من الكمية المطلوبة (${quantity})`);
      }

      // حساب الكمية الجديدة
      const newQuantity = currentQuantity - quantity;

      // الحصول على سعر البيع المخزن
      const storedSellingPrice = inventory.selling_price || 0;

      // استخدام سعر البيع المخزن إذا لم يتم تمرير سعر بيع
      const actualSellingPrice = (sellingPrice > 0) ? sellingPrice : storedSellingPrice;

      // تسجيل المعلومات للتشخيص
      logSystem(`=== تحديث المخزون بعد عملية بيع ===`, 'info');
      logSystem(`- الصنف: ${inventory.name} (${itemId})`, 'info');
      logSystem(`- الكمية المباعة: ${quantity}`, 'info');
      logSystem(`- سعر البيع: ${actualSellingPrice}`, 'info');
      logSystem(`- الكمية قبل التحديث: ${currentQuantity}`, 'info');
      logSystem(`- الكمية بعد التحديث: ${newQuantity}`, 'info');

      // التحقق من وجود سجل في المخزون
      const checkInventoryStmt = db.prepare('SELECT id FROM inventory WHERE item_id = ?');
      const inventoryRecord = checkInventoryStmt.get(itemId);

      if (inventoryRecord) {
        // تحديث سجل المخزون الموجود
        const updateStmt = db.prepare(`
          UPDATE inventory
          SET current_quantity = ?, last_updated = ?
          WHERE item_id = ?
        `);

        updateStmt.run(
          newQuantity,
          now,
          itemId
        );
      } else {
        // هذا لا ينبغي أن يحدث، ولكن للتأكد من سلامة البيانات
        throw new Error(`الصنف غير موجود في المخزون: ${itemId}`);
      }

      // مسح التخزين المؤقت بعد تحديث المخزون
      clearInventoryCache('sale', itemId);

      // إرسال إشعار بتحديث المخزون
      eventSystem.notifyInventoryUpdated({
        itemId,
        name: inventory.name,
        current_quantity: newQuantity,
        avg_price: inventory.avg_price,
        selling_price: actualSellingPrice,
        operation: 'sale',
        quantity: quantity
      });

      logSystem(`تم تحديث المخزون بنجاح بعد عملية البيع للصنف ${itemId}`, 'info');

      return {
        success: true,
        itemId,
        newQuantity,
        sellingPrice: actualSellingPrice
      };
    } catch (error) {
      // لا نحتاج إلى التراجع عن المعاملة هنا لأن الدالة تُستدعى من داخل معاملة أخرى
      logError(error, 'updateInventoryAfterSale');
      throw error;
    }
  } catch (error) {
    logError(error, 'updateInventoryAfterSale');
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * مزامنة المخزون مع الأصناف
 * هذه الوظيفة تقوم بمزامنة بيانات المخزون مع بيانات الأصناف
 * تم تحسينها للتعامل بشكل صحيح مع عمليات الاسترجاع من خلال:
 * 1. تحديث كل شيء ما عدا الكمية الحالية لتجنب إعادة تعيين الكمية بعد عمليات البيع والإرجاع
 * 2. التحقق من وجود عمليات إرجاع للصنف قبل تحديث المخزون
 * 3. استدعاء وظيفة التحقق من المخزون بعد الإرجاع للتأكد من صحة البيانات
 * 4. تحسين التسجيل لتتبع مصدر المشكلات
 *
 * @param {number} itemId - معرف الصنف (اختياري، إذا تم تمريره سيتم مزامنة صنف واحد فقط)
 * @returns {Object} - نتيجة العملية
 */
function syncInventoryWithItems(itemId = null) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // التحقق من وجود مزامنة قيد التنفيذ
    if (global.isSyncInProgress) {
      logSystem('هناك عملية مزامنة قيد التنفيذ بالفعل، سيتم تجاهل هذا الطلب', 'warning');
      return {
        success: false,
        error: 'هناك عملية مزامنة قيد التنفيذ بالفعل، يرجى الانتظار'
      };
    }

    // تعيين علامة المزامنة قيد التنفيذ
    global.isSyncInProgress = true;

    // بدء معاملة قاعدة البيانات
    db.exec('BEGIN TRANSACTION');

    try {
      const now = new Date().toISOString();
      logSystem(`[SYNC-INVENTORY] بدء عملية مزامنة المخزون في ${now}`, 'info');

      let itemsQuery = 'SELECT * FROM items';
      let params = [];

      if (itemId) {
        itemId = parseInt(itemId);
        if (isNaN(itemId) || itemId <= 0) throw new Error('معرف الصنف غير صالح');

        itemsQuery += ' WHERE id = ?';
        params.push(itemId);

        logSystem(`[SYNC-INVENTORY] مزامنة المخزون للصنف: ${itemId}`, 'info');
      } else {
        logSystem(`[SYNC-INVENTORY] مزامنة المخزون لجميع الأصناف`, 'info');
      }

      const getItemsStmt = db.prepare(itemsQuery);
      const items = getItemsStmt.all(...params);

      if (items.length === 0) {
        logSystem('لا توجد أصناف للمزامنة', 'warning');
        db.exec('COMMIT');
        global.isSyncInProgress = false; // إعادة تعيين علامة المزامنة
        return { success: true, message: 'لا توجد أصناف للمزامنة', syncedItems: 0 };
      }

      let syncedItems = 0;
      let errors = [];

      for (const item of items) {
        try {
          // التحقق من وجود سجل في المخزون
          const getInventoryStmt = db.prepare('SELECT * FROM inventory WHERE item_id = ?');
          const inventory = getInventoryStmt.get(item.id);

          // تسجيل معلومات المخزون الحالية للتشخيص
          if (inventory) {
            logSystem(`[SYNC-INVENTORY] معلومات المخزون الحالية للصنف ${item.id} (${item.name}):`, 'info');
            logSystem(`[SYNC-INVENTORY] - الكمية الحالية: ${inventory.current_quantity}`, 'info');
            logSystem(`[SYNC-INVENTORY] - الحد الأدنى: ${inventory.minimum_quantity}`, 'info');
            logSystem(`[SYNC-INVENTORY] - متوسط السعر: ${inventory.avg_price}`, 'info');
            logSystem(`[SYNC-INVENTORY] - سعر البيع: ${inventory.selling_price}`, 'info');
            logSystem(`[SYNC-INVENTORY] - آخر تحديث: ${inventory.last_updated}`, 'info');
          } else {
            logSystem(`[SYNC-INVENTORY] لا يوجد سجل مخزون للصنف ${item.id} (${item.name})`, 'info');
          }

          // تحويل القيم إلى أرقام للتأكد من صحة البيانات
          // نحتفظ بالكمية الحالية في المخزون إذا كانت موجودة
          const currentQuantity = inventory ? (inventory.current_quantity || 0) : 0;
          // نستخدم الحد الأدنى من المخزون إذا كان موجودًا، وإلا نستخدم القيمة من الصنف
          const minimumQuantity = inventory ? (inventory.minimum_quantity || 0) : 0;
          const avgPrice = inventory ? (inventory.avg_price || 0) : (parseFloat(item.avg_price) || 0);
          const sellingPrice = inventory ? (inventory.selling_price || 0) : (parseFloat(item.selling_price) || 0);

          // تسجيل معلومات المزامنة للتشخيص
          logSystem(`[SYNC-INVENTORY] قيم المزامنة للصنف ${item.id} (${item.name}):`, 'info');
          logSystem(`[SYNC-INVENTORY] - الكمية الحالية: ${currentQuantity}`, 'info');
          logSystem(`[SYNC-INVENTORY] - الحد الأدنى: ${minimumQuantity}`, 'info');
          logSystem(`[SYNC-INVENTORY] - متوسط السعر: ${avgPrice}`, 'info');
          logSystem(`[SYNC-INVENTORY] - سعر البيع: ${sellingPrice}`, 'info');

          if (inventory) {
            // تحديث سجل المخزون الموجود مع الحفاظ على الكمية الحالية
            // نقوم بتحديث كل شيء ما عدا الكمية الحالية لتجنب إعادة تعيين الكمية بعد عمليات البيع والإرجاع

            // تسجيل معلومات إضافية للتشخيص
            console.log(`[SYNC-INVENTORY] تحديث سجل المخزون للصنف ${item.id} (${item.name}):`);
            console.log(`[SYNC-INVENTORY] - الكمية الحالية (قبل التحديث): ${currentQuantity}`);
            console.log(`[SYNC-INVENTORY] - الحد الأدنى: ${minimumQuantity}`);
            console.log(`[SYNC-INVENTORY] - متوسط السعر: ${avgPrice}`);
            console.log(`[SYNC-INVENTORY] - سعر البيع: ${sellingPrice}`);

            // التحقق من وجود عمليات إرجاع للصنف
            try {
              const getReturnsStmt = db.prepare(`
                SELECT SUM(quantity) as total_returned
                FROM return_transactions
                WHERE item_id = ?
              `);

              const returnsResult = getReturnsStmt.get(item.id);
              const totalReturned = returnsResult ? (returnsResult.total_returned || 0) : 0;

              if (totalReturned > 0) {
                console.log(`[SYNC-INVENTORY] تم العثور على ${totalReturned} وحدة مسترجعة للصنف ${item.id} (${item.name})`);
                logSystem(`[SYNC-INVENTORY] تم العثور على ${totalReturned} وحدة مسترجعة للصنف ${item.id} (${item.name})`, 'info');
              }
            } catch (returnsError) {
              console.error(`[SYNC-INVENTORY] خطأ في التحقق من عمليات الإرجاع:`, returnsError);
              logSystem(`[SYNC-INVENTORY] خطأ في التحقق من عمليات الإرجاع: ${returnsError.message}`, 'error');
            }

            const updateStmt = db.prepare(`
              UPDATE inventory
              SET minimum_quantity = ?, avg_price = ?, selling_price = ?, last_updated = ?
              WHERE item_id = ?
            `);

            const updateResult = updateStmt.run(
              minimumQuantity,
              avgPrice,
              sellingPrice,
              now,
              item.id
            );

            console.log(`[SYNC-INVENTORY] تم تحديث سجل المخزون للصنف ${item.id} (${item.name}) مع الحفاظ على الكمية الحالية: ${currentQuantity}`);
            logSystem(`[SYNC-INVENTORY] تم تحديث سجل المخزون للصنف ${item.id} (${item.name}) مع الحفاظ على الكمية الحالية: ${currentQuantity}`, 'info');

            console.log(`[SYNC-INVENTORY] تم تحديث سجل المخزون للصنف ${item.id} (${item.name}):`);
            console.log(`[SYNC-INVENTORY] - عدد الصفوف المتأثرة: ${updateResult.changes}`);
            logSystem(`[SYNC-INVENTORY] تم تحديث سجل المخزون للصنف ${item.id} (${item.name}):`, 'info');
            logSystem(`[SYNC-INVENTORY] - عدد الصفوف المتأثرة: ${updateResult.changes}`, 'info');

            // التحقق من نجاح التحديث
            if (updateResult.changes === 0) {
              console.warn(`[SYNC-INVENTORY] تحذير: لم يتم تحديث أي صفوف للصنف ${item.id} (${item.name})`);
              logSystem(`[SYNC-INVENTORY] تحذير: لم يتم تحديث أي صفوف للصنف ${item.id} (${item.name})`, 'warning');
            }

            // التحقق من القيم بعد التحديث
            try {
              const verifyStmt = db.prepare('SELECT * FROM inventory WHERE item_id = ?');
              const verifyResult = verifyStmt.get(item.id);

              if (verifyResult) {
                logSystem(`[SYNC-INVENTORY] التحقق من القيم بعد التحديث للصنف ${item.id} (${item.name}):`, 'info');
                logSystem(`[SYNC-INVENTORY] - الكمية الحالية: ${verifyResult.current_quantity}`, 'info');

                // التحقق من تطابق القيم
                if (verifyResult.current_quantity !== currentQuantity) {
                  logSystem(`[SYNC-INVENTORY] تحذير: الكمية الحالية بعد التحديث (${verifyResult.current_quantity}) لا تتطابق مع القيمة المتوقعة (${currentQuantity})`, 'warning');
                }
              } else {
                logSystem(`[SYNC-INVENTORY] تحذير: لم يتم العثور على سجل المخزون بعد التحديث للصنف ${item.id} (${item.name})`, 'warning');
              }
            } catch (verifyError) {
              logSystem(`[SYNC-INVENTORY] خطأ في التحقق من القيم بعد التحديث: ${verifyError.message}`, 'error');
            }
          } else {
            // إنشاء سجل مخزون جديد
            const insertStmt = db.prepare(`
              INSERT INTO inventory (
                item_id, current_quantity, minimum_quantity, avg_price, selling_price, last_updated
              )
              VALUES (?, ?, ?, ?, ?, ?)
            `);

            // نستخدم الكمية الابتدائية من الصنف إذا كانت موجودة، وإلا نستخدم 0
            const initialQuantity = item.initial_quantity ? parseInt(item.initial_quantity) : 0;

            logSystem(`[SYNC-INVENTORY] إنشاء سجل مخزون جديد للصنف ${item.id} (${item.name}):`, 'info');
            logSystem(`[SYNC-INVENTORY] - الكمية الابتدائية: ${initialQuantity}`, 'info');

            const insertResult = insertStmt.run(
              item.id,
              initialQuantity, // نستخدم الكمية الابتدائية للصنف الجديد
              minimumQuantity,
              avgPrice,
              sellingPrice,
              now
            );

            logSystem(`[SYNC-INVENTORY] تم إنشاء سجل مخزون جديد للصنف ${item.id} (${item.name}):`, 'info');
            logSystem(`[SYNC-INVENTORY] - معرف السجل الجديد: ${insertResult.lastInsertRowid}`, 'info');

            // التحقق من القيم بعد الإنشاء
            try {
              const verifyStmt = db.prepare('SELECT * FROM inventory WHERE item_id = ?');
              const verifyResult = verifyStmt.get(item.id);

              if (verifyResult) {
                logSystem(`[SYNC-INVENTORY] التحقق من القيم بعد الإنشاء للصنف ${item.id} (${item.name}):`, 'info');
                logSystem(`[SYNC-INVENTORY] - الكمية الحالية: ${verifyResult.current_quantity}`, 'info');

                // التحقق من تطابق القيم
                if (verifyResult.current_quantity !== initialQuantity) {
                  logSystem(`[SYNC-INVENTORY] تحذير: الكمية الحالية بعد الإنشاء (${verifyResult.current_quantity}) لا تتطابق مع القيمة المتوقعة (${initialQuantity})`, 'warning');
                }
              } else {
                logSystem(`[SYNC-INVENTORY] تحذير: لم يتم العثور على سجل المخزون بعد الإنشاء للصنف ${item.id} (${item.name})`, 'warning');
              }
            } catch (verifyError) {
              logSystem(`[SYNC-INVENTORY] خطأ في التحقق من القيم بعد الإنشاء: ${verifyError.message}`, 'error');
            }
          }

          syncedItems++;
          logSystem(`تمت مزامنة الصنف ${item.id} (${item.name}) بنجاح`, 'info');
        } catch (itemError) {
          logError(itemError, `syncInventoryWithItems - الصنف ${item.id}`);
          errors.push({ itemId: item.id, error: itemError.message });
        }
      }

      // إنهاء المعاملة
      db.exec('COMMIT');
      logSystem(`[SYNC-INVENTORY] تم إنهاء معاملة قاعدة البيانات بنجاح`, 'info');

      // مسح التخزين المؤقت بعد المزامنة
      try {
        // تمرير معلومات العملية والصنف لتحسين التشخيص
        clearInventoryCache('sync', itemId);
        console.log(`[SYNC-INVENTORY] تم مسح التخزين المؤقت بنجاح`);
        logSystem(`[SYNC-INVENTORY] تم مسح التخزين المؤقت بنجاح`, 'info');
      } catch (cacheError) {
        console.error(`[SYNC-INVENTORY] خطأ في مسح التخزين المؤقت:`, cacheError);
        logSystem(`[SYNC-INVENTORY] تحذير: فشل في مسح التخزين المؤقت: ${cacheError.message}`, 'warning');
      }

      // التحقق من حالة المخزون بعد المزامنة
      if (itemId) {
        try {
          // استخدام استعلام مباشر بدلاً من getInventoryForItem لتجنب أي مشاكل في التخزين المؤقت
          const verifyStmt = db.prepare(`
            SELECT
              i.id, i.name, i.unit,
              COALESCE(inv.current_quantity, 0) as current_quantity,
              COALESCE(inv.minimum_quantity, 0) as minimum_quantity,
              COALESCE(inv.avg_price, 0) as avg_price,
              COALESCE(inv.selling_price, 0) as selling_price,
              COALESCE(inv.last_updated, i.updated_at, i.created_at) as last_updated,
              inv.item_id
            FROM items i
            LEFT JOIN inventory inv ON i.id = inv.item_id
            WHERE i.id = ?
          `);

          const finalInventory = verifyStmt.get(itemId);

          if (finalInventory) {
            console.log(`[SYNC-INVENTORY] حالة المخزون النهائية للصنف ${itemId} (${finalInventory.name}):`);
            console.log(`[SYNC-INVENTORY] - الكمية الحالية: ${finalInventory.current_quantity}`);
            console.log(`[SYNC-INVENTORY] - الحد الأدنى: ${finalInventory.minimum_quantity}`);
            console.log(`[SYNC-INVENTORY] - متوسط السعر: ${finalInventory.avg_price}`);
            console.log(`[SYNC-INVENTORY] - سعر البيع: ${finalInventory.selling_price}`);
            console.log(`[SYNC-INVENTORY] - آخر تحديث: ${finalInventory.last_updated}`);

            logSystem(`[SYNC-INVENTORY] حالة المخزون النهائية للصنف ${itemId} (${finalInventory.name}):`, 'info');
            logSystem(`[SYNC-INVENTORY] - الكمية الحالية: ${finalInventory.current_quantity}`, 'info');
            logSystem(`[SYNC-INVENTORY] - الحد الأدنى: ${finalInventory.minimum_quantity}`, 'info');
            logSystem(`[SYNC-INVENTORY] - متوسط السعر: ${finalInventory.avg_price}`, 'info');
            logSystem(`[SYNC-INVENTORY] - سعر البيع: ${finalInventory.selling_price}`, 'info');
            logSystem(`[SYNC-INVENTORY] - آخر تحديث: ${finalInventory.last_updated}`, 'info');

            // التحقق من عمليات الإرجاع للصنف
            try {
              const getReturnsStmt = db.prepare(`
                SELECT SUM(quantity) as total_returned
                FROM return_transactions
                WHERE item_id = ?
              `);

              const returnsResult = getReturnsStmt.get(itemId);
              const totalReturned = returnsResult ? (returnsResult.total_returned || 0) : 0;

              if (totalReturned > 0) {
                console.log(`[SYNC-INVENTORY] تم العثور على ${totalReturned} وحدة مسترجعة للصنف ${itemId} (${finalInventory.name})`);
                logSystem(`[SYNC-INVENTORY] تم العثور على ${totalReturned} وحدة مسترجعة للصنف ${itemId} (${finalInventory.name})`, 'info');

                // التحقق من أن الكمية الحالية تعكس عمليات الإرجاع
                console.log(`[SYNC-INVENTORY] التحقق من أن الكمية الحالية تعكس عمليات الإرجاع للصنف ${itemId}`);

                // استدعاء وظيفة التحقق من المخزون بعد الإرجاع
                verifyInventoryAfterReturn(itemId);
              }
            } catch (returnsError) {
              console.error(`[SYNC-INVENTORY] خطأ في التحقق من عمليات الإرجاع:`, returnsError);
              logSystem(`[SYNC-INVENTORY] خطأ في التحقق من عمليات الإرجاع: ${returnsError.message}`, 'error');
            }
          } else {
            console.warn(`[SYNC-INVENTORY] تحذير: لم يتم العثور على سجل المخزون النهائي للصنف ${itemId}`);
            logSystem(`[SYNC-INVENTORY] تحذير: لم يتم العثور على سجل المخزون النهائي للصنف ${itemId}`, 'warning');
          }
        } catch (finalVerifyError) {
          console.error(`[SYNC-INVENTORY] خطأ في التحقق من حالة المخزون النهائية:`, finalVerifyError);
          logSystem(`[SYNC-INVENTORY] خطأ في التحقق من حالة المخزون النهائية: ${finalVerifyError.message}`, 'error');
        }
      }

      logSystem(`[SYNC-INVENTORY] تمت مزامنة ${syncedItems} من ${items.length} صنف بنجاح`, 'info');

      // إعادة تعيين علامة المزامنة
      global.isSyncInProgress = false;
      logSystem(`[SYNC-INVENTORY] تم إعادة تعيين علامة المزامنة`, 'info');

      return {
        success: true,
        syncedItems,
        totalItems: items.length,
        errors: errors.length > 0 ? errors : null
      };
    } catch (error) {
      // التراجع عن المعاملة في حالة حدوث خطأ
      db.exec('ROLLBACK');
      logError(error, 'syncInventoryWithItems');

      // إعادة تعيين علامة المزامنة في حالة الخطأ
      global.isSyncInProgress = false;

      throw error;
    }
  } catch (error) {
    logError(error, 'syncInventoryWithItems');

    // إعادة تعيين علامة المزامنة في حالة الخطأ
    if (global.isSyncInProgress) {
      global.isSyncInProgress = false;
    }

    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * الحصول على سعر البيع لصنف معين
 * @param {number} itemId - معرف الصنف
 * @returns {number} - سعر البيع أو 0 إذا لم يتم العثور على الصنف
 */
function getSellingPriceForItem(itemId) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    itemId = parseInt(itemId);
    if (isNaN(itemId) || itemId <= 0) throw new Error('معرف الصنف غير صالح');

    const stmt = db.prepare(`
      SELECT selling_price
      FROM inventory
      WHERE item_id = ?
    `);

    const result = stmt.get(itemId);

    if (!result) {
      logSystem(`لم يتم العثور على سعر بيع للصنف ${itemId}`, 'warning');
      return 0;
    }

    return parseFloat(result.selling_price) || 0;
  } catch (error) {
    logError(error, 'getSellingPriceForItem');
    return 0;
  }
}

/**
 * تحديث المخزون بعد عملية الإرجاع
 * @param {number} itemId - معرف الصنف
 * @param {number} quantity - الكمية المسترجعة
 * @returns {Object} - نتيجة العملية
 */
function updateInventoryAfterReturn(itemId, quantity) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // التحقق من صحة المدخلات
    itemId = parseInt(itemId);
    quantity = parseInt(quantity);

    if (isNaN(itemId) || itemId <= 0) throw new Error('معرف الصنف غير صالح');
    if (isNaN(quantity) || quantity <= 0) throw new Error('الكمية غير صالحة');

    // الحصول على معلومات المخزون الحالية
    const inventory = getInventoryForItem(itemId, true); // تجاوز التخزين المؤقت للحصول على أحدث البيانات

    if (!inventory) {
      throw new Error(`الصنف غير موجود: ${itemId}`);
    }

    // لا نستخدم معاملة قاعدة البيانات هنا لأن الدالة تُستدعى من داخل معاملة أخرى
    try {
      const now = new Date().toISOString();

      // الكمية الحالية في المخزون
      const currentQuantity = inventory.current_quantity || 0;

      // حساب الكمية الجديدة (إضافة الكمية المسترجعة إلى المخزون)
      const newQuantity = currentQuantity + quantity;

      // تسجيل المعلومات للتشخيص
      logSystem(`=== تحديث المخزون بعد عملية إرجاع ===`, 'info');
      logSystem(`- الصنف: ${inventory.name} (${itemId})`, 'info');
      logSystem(`- الكمية المسترجعة: ${quantity}`, 'info');
      logSystem(`- الكمية قبل التحديث: ${currentQuantity}`, 'info');
      logSystem(`- الكمية بعد التحديث: ${newQuantity}`, 'info');

      // التحقق من وجود سجل في المخزون
      const checkInventoryStmt = db.prepare('SELECT id FROM inventory WHERE item_id = ?');
      const inventoryRecord = checkInventoryStmt.get(itemId);

      if (inventoryRecord) {
        // تحديث سجل المخزون الموجود
        const updateStmt = db.prepare(`
          UPDATE inventory
          SET current_quantity = ?, last_updated = ?
          WHERE item_id = ?
        `);

        updateStmt.run(
          newQuantity,
          now,
          itemId
        );
      } else {
        // هذا لا ينبغي أن يحدث، ولكن للتأكد من سلامة البيانات
        throw new Error(`الصنف غير موجود في المخزون: ${itemId}`);
      }

      // مسح التخزين المؤقت بعد تحديث المخزون
      // تمرير معلومات العملية والصنف لتحسين التشخيص
      clearInventoryCache('return', itemId);

      // إرسال إشعار بتحديث المخزون
      eventSystem.notifyInventoryUpdated({
        itemId,
        name: inventory.name,
        current_quantity: newQuantity,
        avg_price: inventory.avg_price,
        selling_price: inventory.selling_price,
        operation: 'return',
        quantity: quantity
      });

      logSystem(`تم تحديث المخزون بنجاح بعد عملية الإرجاع للصنف ${itemId}`, 'info');

      return {
        success: true,
        itemId,
        newQuantity
      };
    } catch (error) {
      // لا نحتاج إلى التراجع عن المعاملة هنا لأن الدالة تُستدعى من داخل معاملة أخرى
      logError(error, 'updateInventoryAfterReturn');
      throw error;
    }
  } catch (error) {
    logError(error, 'updateInventoryAfterReturn');
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * تحديث سعر البيع لصنف معين
 * @param {number} itemId - معرف الصنف
 * @param {number} sellingPrice - سعر البيع الجديد
 * @returns {Object} - نتيجة العملية
 */
function updateSellingPrice(itemId, sellingPrice) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // التحقق من صحة المدخلات
    itemId = parseInt(itemId);
    sellingPrice = parseFloat(sellingPrice);

    if (isNaN(itemId) || itemId <= 0) throw new Error('معرف الصنف غير صالح');
    if (isNaN(sellingPrice) || sellingPrice < 0) throw new Error('سعر البيع غير صالح');

    // بدء معاملة قاعدة البيانات
    db.exec('BEGIN TRANSACTION');

    try {
      const now = new Date().toISOString();

      // التحقق من وجود الصنف
      const checkItemStmt = db.prepare('SELECT id, name FROM items WHERE id = ?');
      const item = checkItemStmt.get(itemId);

      if (!item) {
        throw new Error(`الصنف غير موجود: ${itemId}`);
      }

      // التحقق من وجود سجل في المخزون
      const checkInventoryStmt = db.prepare('SELECT id FROM inventory WHERE item_id = ?');
      const inventoryRecord = checkInventoryStmt.get(itemId);

      if (inventoryRecord) {
        // تحديث سجل المخزون الموجود
        const updateStmt = db.prepare(`
          UPDATE inventory
          SET selling_price = ?, last_updated = ?
          WHERE item_id = ?
        `);

        updateStmt.run(
          sellingPrice,
          now,
          itemId
        );
      } else {
        // إنشاء سجل مخزون جديد
        const insertStmt = db.prepare(`
          INSERT INTO inventory (
            item_id, selling_price, last_updated
          )
          VALUES (?, ?, ?)
        `);

        insertStmt.run(
          itemId,
          sellingPrice,
          now
        );
      }

      // إنهاء المعاملة
      db.exec('COMMIT');

      // مسح التخزين المؤقت بعد تحديث سعر البيع
      clearInventoryCache('update-selling-price', itemId);

      logSystem(`تم تحديث سعر البيع للصنف ${itemId} (${item.name}) إلى ${sellingPrice} بنجاح`, 'info');

      return {
        success: true,
        itemId,
        itemName: item.name,
        newSellingPrice: sellingPrice
      };
    } catch (error) {
      // التراجع عن المعاملة في حالة حدوث خطأ
      db.exec('ROLLBACK');
      logError(error, 'updateSellingPrice');
      throw error;
    }
  } catch (error) {
    logError(error, 'updateSellingPrice');
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * إضافة صنف جديد
 * @param {Object} item - بيانات الصنف
 * @returns {Object} - نتيجة العملية
 */
function addItem(item) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // التحقق من صحة المدخلات
    if (!item.name || item.name.trim() === '') {
      throw new Error('اسم الصنف مطلوب');
    }

    // بدء معاملة قاعدة البيانات
    db.exec('BEGIN TRANSACTION');

    try {
      // التحقق من عدم وجود صنف بنفس الاسم
      const checkStmt = db.prepare('SELECT COUNT(*) as count FROM items WHERE name = ?');
      const { count } = checkStmt.get(item.name);

      if (count > 0) {
        throw new Error('يوجد صنف بنفس الاسم بالفعل');
      }

      // إضافة الصنف
      const now = new Date().toISOString();
      const insertStmt = db.prepare(`
        INSERT INTO items (name, unit, initial_quantity, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?)
      `);

      const result = insertStmt.run(
        item.name,
        item.unit || 'قطعة',
        item.initial_quantity || 0,
        now,
        now
      );

      const itemId = result.lastInsertRowid;

      // إنشاء سجل مخزون للصنف الجديد
      const insertInventoryStmt = db.prepare(`
        INSERT INTO inventory (
          item_id,
          current_quantity,
          minimum_quantity,
          avg_price,
          selling_price,
          last_updated
        ) VALUES (?, ?, ?, ?, ?, ?)
      `);

      insertInventoryStmt.run(
        itemId,
        item.initial_quantity || 0,
        item.minimum_quantity || 0,
        item.avg_price || 0,
        item.selling_price || 0,
        now
      );

      // إنهاء المعاملة
      db.exec('COMMIT');

      // مسح التخزين المؤقت بعد إضافة الصنف
      clearInventoryCache('add-item', itemId);

      // إرسال إشعار بإضافة صنف جديد باستخدام نظام الأحداث المركزي
      eventSystem.notifyItemAdded({
        id: itemId,
        name: item.name,
        unit: item.unit || 'قطعة',
        initial_quantity: item.initial_quantity || 0,
        avg_price: item.avg_price || 0,
        selling_price: item.selling_price || 0,
        success: true
      });

      // إرسال إشعار بتحديث المخزون أيضاً
      eventSystem.notifyInventoryUpdated({
        itemId: itemId,
        name: item.name,
        current_quantity: item.initial_quantity || 0,
        avg_price: item.avg_price || 0,
        selling_price: item.selling_price || 0,
        operation: 'add-item',
        quantity: item.initial_quantity || 0
      });

      logSystem(`تم إضافة الصنف ${item.name} بنجاح`, 'info');

      return {
        success: true,
        itemId,
        item: {
          id: itemId,
          name: item.name,
          unit: item.unit || 'قطعة',
          initial_quantity: item.initial_quantity || 0,
          created_at: now,
          updated_at: now
        }
      };
    } catch (error) {
      // التراجع عن المعاملة في حالة حدوث خطأ
      db.exec('ROLLBACK');
      logError(error, 'addItem');
      throw error;
    }
  } catch (error) {
    logError(error, 'addItem');
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * تحديث صنف موجود
 * @param {number} itemId - معرف الصنف
 * @param {Object} updates - التحديثات المطلوبة
 * @returns {Object} - نتيجة العملية
 */
function updateItem(itemId, updates) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // التحقق من صحة المدخلات
    itemId = parseInt(itemId);
    if (isNaN(itemId) || itemId <= 0) throw new Error('معرف الصنف غير صالح');

    if (!updates || Object.keys(updates).length === 0) {
      throw new Error('لم يتم تحديد أي تحديثات');
    }

    // بدء معاملة قاعدة البيانات
    db.exec('BEGIN TRANSACTION');

    try {
      // التحقق من وجود الصنف
      const checkItemStmt = db.prepare('SELECT * FROM items WHERE id = ?');
      const item = checkItemStmt.get(itemId);

      if (!item) {
        throw new Error(`الصنف غير موجود: ${itemId}`);
      }

      // التحقق من عدم وجود صنف آخر بنفس الاسم
      if (updates.name && updates.name !== item.name) {
        const checkNameStmt = db.prepare('SELECT COUNT(*) as count FROM items WHERE name = ? AND id != ?');
        const { count } = checkNameStmt.get(updates.name, itemId);

        if (count > 0) {
          throw new Error('يوجد صنف آخر بنفس الاسم بالفعل');
        }
      }

      // تحديث الصنف
      const now = new Date().toISOString();
      let updateFields = [];
      let updateParams = [];

      if (updates.name !== undefined) {
        updateFields.push('name = ?');
        updateParams.push(updates.name);
      }

      if (updates.unit !== undefined) {
        updateFields.push('unit = ?');
        updateParams.push(updates.unit);
      }

      if (updates.initial_quantity !== undefined) {
        updateFields.push('initial_quantity = ?');
        updateParams.push(updates.initial_quantity);
      }

      updateFields.push('updated_at = ?');
      updateParams.push(now);
      updateParams.push(itemId);

      const updateStmt = db.prepare(`
        UPDATE items
        SET ${updateFields.join(', ')}
        WHERE id = ?
      `);

      updateStmt.run(...updateParams);

      // تحديث المخزون إذا لزم الأمر
      if (updates.minimum_quantity !== undefined || updates.selling_price !== undefined) {
        const getInventoryStmt = db.prepare('SELECT * FROM inventory WHERE item_id = ?');
        const inventory = getInventoryStmt.get(itemId);

        if (inventory) {
          let inventoryUpdateFields = [];
          let inventoryUpdateParams = [];

          if (updates.minimum_quantity !== undefined) {
            inventoryUpdateFields.push('minimum_quantity = ?');
            inventoryUpdateParams.push(updates.minimum_quantity);
          }

          if (updates.selling_price !== undefined) {
            inventoryUpdateFields.push('selling_price = ?');
            inventoryUpdateParams.push(updates.selling_price);
          }

          inventoryUpdateFields.push('last_updated = ?');
          inventoryUpdateParams.push(now);
          inventoryUpdateParams.push(itemId);

          const updateInventoryStmt = db.prepare(`
            UPDATE inventory
            SET ${inventoryUpdateFields.join(', ')}
            WHERE item_id = ?
          `);

          updateInventoryStmt.run(...inventoryUpdateParams);
        }
      }

      // إنهاء المعاملة
      db.exec('COMMIT');

      // مسح التخزين المؤقت بعد تحديث الصنف
      clearInventoryCache('update-item', itemId);

      // إرسال إشعار بتحديث الصنف
      try {
        // الحصول على بيانات الصنف المحدثة من قاعدة البيانات
        const getUpdatedItemStmt = db.prepare('SELECT id, name, unit FROM items WHERE id = ?');
        const updatedItem = getUpdatedItemStmt.get(itemId);

        if (!updatedItem) {
          logSystem(`تحذير: لم يتم العثور على الصنف المحدث بالمعرف ${itemId}`, 'warning');
        } else {
          // إرسال الإشعار بالبيانات المحدثة
          eventSystem.notifyItemUpdated({
            id: updatedItem.id,
            _id: updatedItem.id.toString(), // إضافة _id للتوافق مع الواجهة الأمامية
            name: updatedItem.name,
            unit: updatedItem.unit,
            minimum_quantity: updates.minimum_quantity,
            selling_price: updates.selling_price,
            success: true
          });

          logSystem(`تم إرسال إشعار بتحديث الصنف ${itemId} (${updatedItem.name}) بنجاح`, 'info');
        }
      } catch (notifyError) {
        logError(notifyError, 'updateItem - notifyItemUpdated');
        logSystem(`فشل في إرسال إشعار تحديث الصنف: ${notifyError.message}`, 'error');
        // لا نريد إيقاف العملية إذا فشل إرسال الإشعار
      }

      logSystem(`تم تحديث الصنف ${itemId} (${item.name}) بنجاح`, 'info');

      return {
        success: true,
        itemId,
        itemName: updates.name || item.name
      };
    } catch (error) {
      // التراجع عن المعاملة في حالة حدوث خطأ
      db.exec('ROLLBACK');
      logError(error, 'updateItem');
      throw error;
    }
  } catch (error) {
    logError(error, 'updateItem');
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * حذف صنف
 * @param {number} itemId - معرف الصنف
 * @returns {Object} - نتيجة العملية
 */
function deleteItem(itemId) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // التحقق من صحة المدخلات
    itemId = parseInt(itemId);
    if (isNaN(itemId) || itemId <= 0) throw new Error('معرف الصنف غير صالح');

    // بدء معاملة قاعدة البيانات
    db.exec('BEGIN TRANSACTION');

    try {
      // التحقق من وجود الصنف
      const checkItemStmt = db.prepare('SELECT name FROM items WHERE id = ?');
      const item = checkItemStmt.get(itemId);

      if (!item) {
        throw new Error(`الصنف غير موجود: ${itemId}`);
      }

      // التحقق من عدم وجود معاملات مرتبطة بالصنف
      const checkTransactionsStmt = db.prepare('SELECT COUNT(*) as count FROM transactions WHERE item_id = ?');
      const { count } = checkTransactionsStmt.get(itemId);

      if (count > 0) {
        throw new Error(`لا يمكن حذف الصنف لأنه مرتبط بـ ${count} معاملة`);
      }

      // حذف سجل المخزون أولاً
      const deleteInventoryStmt = db.prepare('DELETE FROM inventory WHERE item_id = ?');
      deleteInventoryStmt.run(itemId);

      // حذف الصنف
      const deleteItemStmt = db.prepare('DELETE FROM items WHERE id = ?');
      deleteItemStmt.run(itemId);

      // إنهاء المعاملة
      db.exec('COMMIT');

      // مسح التخزين المؤقت بعد حذف الصنف
      clearInventoryCache('delete-item', itemId);

      logSystem(`تم حذف الصنف ${itemId} (${item.name}) بنجاح`, 'info');

      return {
        success: true,
        itemId,
        itemName: item.name
      };
    } catch (error) {
      // التراجع عن المعاملة في حالة حدوث خطأ
      db.exec('ROLLBACK');
      logError(error, 'deleteItem');
      throw error;
    }
  } catch (error) {
    logError(error, 'deleteItem');
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * تحديث عنصر في المخزون
 * @param {number} itemId - معرف الصنف
 * @param {Object} updates - التحديثات المطلوبة
 * @returns {Object} - نتيجة العملية
 */
function updateInventoryItem(itemId, updates) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // التحقق من صحة المدخلات
    itemId = parseInt(itemId);
    if (isNaN(itemId) || itemId <= 0) throw new Error('معرف الصنف غير صالح');

    // بدء معاملة قاعدة البيانات
    db.exec('BEGIN TRANSACTION');

    try {
      // التحقق من وجود الصنف
      const checkItemStmt = db.prepare('SELECT id, name FROM items WHERE id = ?');
      const item = checkItemStmt.get(itemId);

      if (!item) {
        throw new Error(`الصنف غير موجود: ${itemId}`);
      }

      // الحصول على معلومات المخزون الحالية
      const getInventoryStmt = db.prepare('SELECT * FROM inventory WHERE item_id = ?');
      const inventory = getInventoryStmt.get(itemId);

      const now = new Date().toISOString();

      // تحضير التحديثات
      const cleanedUpdates = {
        current_quantity: updates.current_quantity !== undefined ? Number(updates.current_quantity) : (inventory ? inventory.current_quantity : 0),
        minimum_quantity: updates.minimum_quantity !== undefined ? Number(updates.minimum_quantity) : (inventory ? inventory.minimum_quantity : 0),
        avg_price: updates.avg_price !== undefined ? Number(updates.avg_price) : (inventory ? inventory.avg_price : 0),
        selling_price: updates.selling_price !== undefined ? Number(updates.selling_price) : (inventory ? inventory.selling_price : 0),
        last_updated: now
      };

      logSystem(`تحديث المخزون للصنف ${itemId} (${item.name}):`, 'info');
      logSystem(`- الكمية الحالية: ${cleanedUpdates.current_quantity}`, 'info');
      logSystem(`- الحد الأدنى قبل التحديث: ${inventory ? inventory.minimum_quantity : 0}`, 'info');
      logSystem(`- الحد الأدنى بعد التحديث: ${cleanedUpdates.minimum_quantity}`, 'info');
      logSystem(`- متوسط السعر: ${cleanedUpdates.avg_price}`, 'info');
      logSystem(`- سعر البيع: ${cleanedUpdates.selling_price}`, 'info');

      let result;

      if (inventory) {
        // تحديث سجل المخزون الموجود
        const updateStmt = db.prepare(`
          UPDATE inventory
          SET current_quantity = ?, minimum_quantity = ?, avg_price = ?, selling_price = ?, last_updated = ?
          WHERE item_id = ?
        `);

        result = updateStmt.run(
          cleanedUpdates.current_quantity,
          cleanedUpdates.minimum_quantity,
          cleanedUpdates.avg_price,
          cleanedUpdates.selling_price,
          cleanedUpdates.last_updated,
          itemId
        );

        logSystem(`تم تحديث سجل المخزون الموجود للصنف ${itemId}`, 'info');
      } else {
        // إنشاء سجل مخزون جديد
        const insertStmt = db.prepare(`
          INSERT INTO inventory (
            item_id, current_quantity, minimum_quantity, avg_price, selling_price, last_updated
          )
          VALUES (?, ?, ?, ?, ?, ?)
        `);

        result = insertStmt.run(
          itemId,
          cleanedUpdates.current_quantity,
          cleanedUpdates.minimum_quantity,
          cleanedUpdates.avg_price,
          cleanedUpdates.selling_price,
          cleanedUpdates.last_updated
        );

        logSystem(`تم إنشاء سجل مخزون جديد للصنف ${itemId}`, 'info');
      }

      // إنهاء المعاملة
      db.exec('COMMIT');

      // مسح التخزين المؤقت بعد تحديث المخزون
      clearInventoryCache('update-inventory', itemId);

      return {
        success: true,
        itemId,
        itemName: item.name,
        updates: cleanedUpdates,
        changes: result.changes
      };
    } catch (error) {
      // التراجع عن المعاملة في حالة حدوث خطأ
      db.exec('ROLLBACK');
      logError(error, 'updateInventoryItem');
      throw error;
    }
  } catch (error) {
    logError(error, 'updateInventoryItem');
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * مسح التخزين المؤقت للمخزون
 * يجب استدعاء هذه الدالة بعد أي عملية تحديث للمخزون
 * تم تحسينها للتعامل بشكل أفضل مع عمليات الاسترجاع من خلال:
 * 1. إضافة معلومات عن نوع العملية (مثل 'return' أو 'purchase' أو 'sync') للتشخيص
 * 2. إضافة معرف الصنف المتأثر للتشخيص
 * 3. تحسين آلية مسح التخزين المؤقت للأصناف الفردية
 * 4. تحسين التسجيل لتتبع مصدر المشكلات
 *
 * @param {string} operation - نوع العملية التي تسببت في مسح التخزين المؤقت (اختياري)
 * @param {number} itemId - معرف الصنف المتأثر (اختياري)
 * @returns {boolean} - نجاح العملية
 */
function clearInventoryCache(operation = '', itemId = null) {
  try {
    // مسح التخزين المؤقت لقائمة المخزون
    inventoryCache = null;
    inventoryCacheTimestamp = 0;

    // إذا تم تحديد صنف معين، نقوم بمسح التخزين المؤقت لهذا الصنف فقط
    if (itemId) {
      const cacheKey = `item_${itemId}`;
      itemCache.delete(cacheKey);
    } else {
      // مسح التخزين المؤقت لجميع الأصناف
      itemCache.clear();
    }

    return true;
  } catch (error) {
    logError(error, 'clearInventoryCache');

    // محاولة المسح بطريقة أخرى في حالة الخطأ
    try {
      // مسح التخزين المؤقت للمخزون بغض النظر عن الخطأ
      inventoryCache = null;
      inventoryCacheTimestamp = 0;

      // مسح التخزين المؤقت للصنف المحدد أو لجميع الأصناف
      if (itemId) {
        itemCache.delete(`item_${itemId}`);
      } else {
        itemCache.clear();
      }

      return true;
    } catch (fallbackError) {
      logError(fallbackError, 'clearInventoryCache - fallback');
      return false;
    }
  }
}

/**
 * تحديث المخزون بعد عملية إرجاع
 * @param {number} itemId - معرف الصنف
 * @param {number} quantity - الكمية المسترجعة
 * @returns {Object} - نتيجة العملية
 */
function updateInventoryAfterReturn(itemId, quantity) {
  try {
    console.log(`[INVENTORY-RETURN] بدء تحديث المخزون بعد عملية إرجاع للصنف ${itemId} بكمية ${quantity}`);
    logSystem(`[INVENTORY-RETURN] بدء تحديث المخزون بعد عملية إرجاع للصنف ${itemId} بكمية ${quantity}`, 'info');

    if (!db) {
      throw new Error('قاعدة البيانات غير مهيأة');
    }

    // التحقق من صحة المدخلات
    itemId = parseInt(itemId);
    quantity = parseInt(quantity);

    if (isNaN(itemId) || itemId <= 0) {
      throw new Error(`معرف الصنف غير صالح: ${itemId}`);
    }

    if (isNaN(quantity) || quantity <= 0) {
      throw new Error(`الكمية غير صالحة: ${quantity}`);
    }

    // بدء معاملة قاعدة البيانات
    console.log(`[INVENTORY-RETURN] بدء معاملة قاعدة البيانات`);
    db.exec('BEGIN TRANSACTION');

    try {
      // الحصول على معلومات المخزون الحالية
      console.log(`[INVENTORY-RETURN] الحصول على معلومات المخزون للصنف ${itemId}`);
      const getInventoryStmt = db.prepare(`
        SELECT i.id, i.name, inv.current_quantity, inv.item_id
        FROM items i
        LEFT JOIN inventory inv ON i.id = inv.item_id
        WHERE i.id = ?
      `);

      const inventory = getInventoryStmt.get(itemId);

      if (!inventory) {
        throw new Error(`الصنف غير موجود: ${itemId}`);
      }

      console.log(`[INVENTORY-RETURN] تم العثور على الصنف: ${inventory.name} (${itemId})`);

      // الكمية الحالية في المخزون
      const currentQuantity = inventory.current_quantity || 0;

      // حساب الكمية الجديدة (إضافة الكمية المسترجعة إلى المخزون)
      const newQuantity = currentQuantity + quantity;

      console.log(`[INVENTORY-RETURN] تحديث المخزون للصنف ${itemId} (${inventory.name}):`);
      console.log(`[INVENTORY-RETURN] - الكمية الحالية: ${currentQuantity}`);
      console.log(`[INVENTORY-RETURN] - الكمية المسترجعة: ${quantity}`);
      console.log(`[INVENTORY-RETURN] - الكمية الجديدة: ${newQuantity}`);

      logSystem(`[INVENTORY-RETURN] تحديث المخزون للصنف ${itemId} (${inventory.name}):`, 'info');
      logSystem(`[INVENTORY-RETURN] - الكمية الحالية: ${currentQuantity}`, 'info');
      logSystem(`[INVENTORY-RETURN] - الكمية المسترجعة: ${quantity}`, 'info');
      logSystem(`[INVENTORY-RETURN] - الكمية الجديدة: ${newQuantity}`, 'info');

      // التحقق من وجود سجل في المخزون
      if (inventory.item_id) {
        // تحديث سجل المخزون الموجود
        console.log(`[INVENTORY-RETURN] تحديث سجل المخزون الموجود للصنف ${itemId}`);
        const updateStmt = db.prepare(`
          UPDATE inventory
          SET current_quantity = ?, last_updated = ?
          WHERE item_id = ?
        `);

        const updateResult = updateStmt.run(
          newQuantity,
          new Date().toISOString(),
          itemId
        );

        console.log(`[INVENTORY-RETURN] نتيجة تحديث المخزون:`, updateResult);
      } else {
        // إنشاء سجل مخزون جديد
        console.log(`[INVENTORY-RETURN] إنشاء سجل مخزون جديد للصنف ${itemId}`);
        const insertStmt = db.prepare(`
          INSERT INTO inventory (item_id, current_quantity, last_updated)
          VALUES (?, ?, ?)
        `);

        const insertResult = insertStmt.run(
          itemId,
          newQuantity,
          new Date().toISOString()
        );

        console.log(`[INVENTORY-RETURN] نتيجة إنشاء سجل مخزون جديد:`, insertResult);
      }

      // التحقق من نجاح التحديث
      const verifyStmt = db.prepare(`
        SELECT current_quantity FROM inventory WHERE item_id = ?
      `);
      const verifyResult = verifyStmt.get(itemId);

      if (!verifyResult) {
        throw new Error(`فشل في التحقق من تحديث المخزون للصنف ${itemId}`);
      }

      console.log(`[INVENTORY-RETURN] التحقق من تحديث المخزون: الكمية الحالية = ${verifyResult.current_quantity}`);

      if (verifyResult.current_quantity !== newQuantity) {
        console.warn(`[INVENTORY-RETURN] تحذير: الكمية المتوقعة (${newQuantity}) لا تتطابق مع الكمية المخزنة (${verifyResult.current_quantity})`);
      }

      // إنهاء المعاملة
      console.log(`[INVENTORY-RETURN] إنهاء معاملة قاعدة البيانات`);
      db.exec('COMMIT');

      // مسح التخزين المؤقت بعد تحديث المخزون
      console.log(`[INVENTORY-RETURN] مسح التخزين المؤقت للمخزون`);
      clearInventoryCache('return', itemId);

      // إرسال إشعار بتحديث المخزون
      try {
        console.log(`[INVENTORY-RETURN] إرسال إشعار بتحديث المخزون`);
        eventSystem.notifyInventoryUpdated({
          itemId,
          name: inventory.name,
          current_quantity: newQuantity,
          operation: 'return',
          quantity: quantity
        });
        console.log(`[INVENTORY-RETURN] تم إرسال إشعار بتحديث المخزون بنجاح`);
      } catch (notifyError) {
        console.error(`[INVENTORY-RETURN] خطأ في إرسال إشعار بتحديث المخزون:`, notifyError);
        logError(notifyError, 'updateInventoryAfterReturn - notifyInventoryUpdated');
        // لا نريد إيقاف العملية إذا فشل إرسال الإشعار
      }

      console.log(`[INVENTORY-RETURN] تم تحديث المخزون بنجاح بعد عملية الإرجاع للصنف ${itemId}. الكمية الجديدة: ${newQuantity}`);
      logSystem(`[INVENTORY-RETURN] تم تحديث المخزون بنجاح بعد عملية الإرجاع للصنف ${itemId}. الكمية الجديدة: ${newQuantity}`, 'info');

      return {
        success: true,
        itemId,
        newQuantity,
        message: `تم تحديث المخزون بنجاح. الكمية الجديدة: ${newQuantity}`
      };
    } catch (error) {
      // التراجع عن المعاملة في حالة حدوث خطأ
      console.error(`[INVENTORY-RETURN] خطأ في تحديث المخزون:`, error);
      try {
        console.log(`[INVENTORY-RETURN] التراجع عن معاملة قاعدة البيانات`);
        db.exec('ROLLBACK');
        console.log(`[INVENTORY-RETURN] تم التراجع عن معاملة قاعدة البيانات بنجاح`);
      } catch (rollbackError) {
        console.error(`[INVENTORY-RETURN] خطأ في التراجع عن المعاملة:`, rollbackError);
        logError(rollbackError, 'updateInventoryAfterReturn - rollback');
      }
      logError(error, 'updateInventoryAfterReturn - inner');
      throw error;
    }
  } catch (error) {
    console.error(`[INVENTORY-RETURN] خطأ في تحديث المخزون بعد عملية الإرجاع:`, error);
    logError(error, 'updateInventoryAfterReturn');
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * التحقق من حالة المخزون بعد عملية المزامنة
 * هذه الوظيفة تتحقق من حالة المخزون بعد عملية المزامنة وتستخدم استعلامات مباشرة لقاعدة البيانات
 * بدلاً من التخزين المؤقت لضمان الحصول على أحدث البيانات
 * تستخدم هذه الوظيفة للتأكد من أن عمليات المزامنة لا تؤثر على الكميات المحدثة بعد عمليات الاسترجاع
 *
 * @param {number} itemId - معرف الصنف
 * @returns {Object} - معلومات حالة المخزون
 */
function verifyInventoryAfterSync(itemId) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // التحقق من صحة المدخلات
    itemId = parseInt(itemId);
    if (isNaN(itemId) || itemId <= 0) throw new Error('معرف الصنف غير صالح');

    console.log(`[INVENTORY-SYNC-VERIFY] التحقق من حالة المخزون بعد عملية المزامنة للصنف ${itemId}`);
    logSystem(`[INVENTORY-SYNC-VERIFY] التحقق من حالة المخزون بعد عملية المزامنة للصنف ${itemId}`, 'info');

    // الحصول على معلومات المخزون الحالية مباشرة من قاعدة البيانات
    // استخدام استعلام مباشر بدلاً من getInventoryForItem لتجنب أي مشاكل في التخزين المؤقت
    const getInventoryStmt = db.prepare(`
      SELECT
        i.id, i.name, i.unit,
        COALESCE(inv.current_quantity, 0) as current_quantity,
        COALESCE(inv.minimum_quantity, 0) as minimum_quantity,
        COALESCE(inv.avg_price, 0) as avg_price,
        COALESCE(inv.selling_price, 0) as selling_price,
        COALESCE(inv.last_updated, i.updated_at, i.created_at) as last_updated,
        inv.item_id
      FROM items i
      LEFT JOIN inventory inv ON i.id = inv.item_id
      WHERE i.id = ?
    `);

    const inventory = getInventoryStmt.get(itemId);

    console.log(`[INVENTORY-SYNC-VERIFY] تم الحصول على معلومات المخزون مباشرة من قاعدة البيانات للصنف ${itemId}`);
    logSystem(`[INVENTORY-SYNC-VERIFY] تم الحصول على معلومات المخزون مباشرة من قاعدة البيانات للصنف ${itemId}`, 'info');

    if (!inventory) {
      console.error(`[INVENTORY-SYNC-VERIFY] الصنف غير موجود في المخزون: ${itemId}`);
      logSystem(`[INVENTORY-SYNC-VERIFY] الصنف غير موجود في المخزون: ${itemId}`, 'error');
      return {
        success: false,
        error: `الصنف غير موجود في المخزون: ${itemId}`
      };
    }

    console.log(`[INVENTORY-SYNC-VERIFY] معلومات المخزون للصنف ${itemId} (${inventory.name}):`);
    console.log(`[INVENTORY-SYNC-VERIFY] - الكمية الحالية: ${inventory.current_quantity}`);
    console.log(`[INVENTORY-SYNC-VERIFY] - الحد الأدنى: ${inventory.minimum_quantity}`);
    console.log(`[INVENTORY-SYNC-VERIFY] - متوسط السعر: ${inventory.avg_price}`);
    console.log(`[INVENTORY-SYNC-VERIFY] - سعر البيع: ${inventory.selling_price}`);

    logSystem(`[INVENTORY-SYNC-VERIFY] معلومات المخزون للصنف ${itemId} (${inventory.name}):`, 'info');
    logSystem(`[INVENTORY-SYNC-VERIFY] - الكمية الحالية: ${inventory.current_quantity}`, 'info');
    logSystem(`[INVENTORY-SYNC-VERIFY] - الحد الأدنى: ${inventory.minimum_quantity}`, 'info');
    logSystem(`[INVENTORY-SYNC-VERIFY] - متوسط السعر: ${inventory.avg_price}`, 'info');
    logSystem(`[INVENTORY-SYNC-VERIFY] - سعر البيع: ${inventory.selling_price}`, 'info');

    return {
      success: true,
      itemId,
      name: inventory.name,
      currentQuantity: inventory.current_quantity,
      minimumQuantity: inventory.minimum_quantity,
      avgPrice: inventory.avg_price,
      sellingPrice: inventory.selling_price,
      lastUpdated: inventory.last_updated
    };
  } catch (error) {
    console.error(`[INVENTORY-SYNC-VERIFY] خطأ في التحقق من حالة المخزون:`, error);
    logSystem(`[INVENTORY-SYNC-VERIFY] خطأ في التحقق من حالة المخزون: ${error.message}`, 'error');
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * التحقق من حالة المخزون بعد عملية الإرجاع
 * هذه الوظيفة تتحقق من حالة المخزون بعد عملية الإرجاع وتقارن بين الكمية المتوقعة والكمية الفعلية
 * تستخدم استعلامات مباشرة لقاعدة البيانات بدلاً من التخزين المؤقت لضمان الحصول على أحدث البيانات
 * وتتحقق من تطابق الكمية المتوقعة (المشتريات - المبيعات + المرتجعات) مع الكمية الفعلية في المخزون
 *
 * @param {number} itemId - معرف الصنف
 * @returns {Object} - معلومات حالة المخزون
 */
function verifyInventoryAfterReturn(itemId) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // التحقق من صحة المدخلات
    itemId = parseInt(itemId);
    if (isNaN(itemId) || itemId <= 0) throw new Error('معرف الصنف غير صالح');

    console.log(`[INVENTORY-VERIFY] التحقق من حالة المخزون بعد عملية الإرجاع للصنف ${itemId}`);
    logSystem(`[INVENTORY-VERIFY] التحقق من حالة المخزون بعد عملية الإرجاع للصنف ${itemId}`, 'info');

    // الحصول على معلومات المخزون الحالية مباشرة من قاعدة البيانات
    // استخدام استعلام مباشر بدلاً من getInventoryForItem لتجنب أي مشاكل في التخزين المؤقت
    const getInventoryStmt = db.prepare(`
      SELECT
        i.id, i.name, i.unit,
        COALESCE(inv.current_quantity, 0) as current_quantity,
        COALESCE(inv.minimum_quantity, 0) as minimum_quantity,
        COALESCE(inv.avg_price, 0) as avg_price,
        COALESCE(inv.selling_price, 0) as selling_price,
        COALESCE(inv.last_updated, i.updated_at, i.created_at) as last_updated,
        inv.item_id
      FROM items i
      LEFT JOIN inventory inv ON i.id = inv.item_id
      WHERE i.id = ?
    `);

    const inventory = getInventoryStmt.get(itemId);

    console.log(`[INVENTORY-VERIFY] تم الحصول على معلومات المخزون مباشرة من قاعدة البيانات للصنف ${itemId}`);
    logSystem(`[INVENTORY-VERIFY] تم الحصول على معلومات المخزون مباشرة من قاعدة البيانات للصنف ${itemId}`, 'info');

    if (!inventory) {
      console.error(`[INVENTORY-VERIFY] الصنف غير موجود في المخزون: ${itemId}`);
      logSystem(`[INVENTORY-VERIFY] الصنف غير موجود في المخزون: ${itemId}`, 'error');
      return {
        success: false,
        error: `الصنف غير موجود في المخزون: ${itemId}`
      };
    }

    console.log(`[INVENTORY-VERIFY] معلومات المخزون للصنف ${itemId} (${inventory.name}):`);
    console.log(`[INVENTORY-VERIFY] - الكمية الحالية: ${inventory.current_quantity}`);
    console.log(`[INVENTORY-VERIFY] - الحد الأدنى: ${inventory.minimum_quantity}`);
    console.log(`[INVENTORY-VERIFY] - متوسط السعر: ${inventory.avg_price}`);
    console.log(`[INVENTORY-VERIFY] - سعر البيع: ${inventory.selling_price}`);

    logSystem(`[INVENTORY-VERIFY] معلومات المخزون للصنف ${itemId} (${inventory.name}):`, 'info');
    logSystem(`[INVENTORY-VERIFY] - الكمية الحالية: ${inventory.current_quantity}`, 'info');
    logSystem(`[INVENTORY-VERIFY] - الحد الأدنى: ${inventory.minimum_quantity}`, 'info');
    logSystem(`[INVENTORY-VERIFY] - متوسط السعر: ${inventory.avg_price}`, 'info');
    logSystem(`[INVENTORY-VERIFY] - سعر البيع: ${inventory.selling_price}`, 'info');

    // التحقق من وجود تعارض بين المخزون وجدول المعاملات
    try {
      // الحصول على إجمالي الكمية المباعة
      const getSalesStmt = db.prepare(`
        SELECT SUM(quantity) as total_sold
        FROM transactions
        WHERE item_id = ? AND type = 'sale'
      `);
      const salesResult = getSalesStmt.get(itemId);
      const totalSold = salesResult ? (salesResult.total_sold || 0) : 0;

      // الحصول على إجمالي الكمية المشتراة
      const getPurchasesStmt = db.prepare(`
        SELECT SUM(quantity) as total_purchased
        FROM transactions
        WHERE item_id = ? AND type = 'purchase'
      `);
      const purchasesResult = getPurchasesStmt.get(itemId);
      const totalPurchased = purchasesResult ? (purchasesResult.total_purchased || 0) : 0;

      // الحصول على إجمالي الكمية المسترجعة
      const getReturnsStmt = db.prepare(`
        SELECT SUM(quantity) as total_returned
        FROM return_transactions
        WHERE item_id = ?
      `);
      const returnsResult = getReturnsStmt.get(itemId);
      const totalReturned = returnsResult ? (returnsResult.total_returned || 0) : 0;

      // حساب الكمية المتوقعة في المخزون
      const expectedQuantity = totalPurchased - totalSold + totalReturned;

      console.log(`[INVENTORY-VERIFY] إحصائيات المعاملات للصنف ${itemId} (${inventory.name}):`);
      console.log(`[INVENTORY-VERIFY] - إجمالي الكمية المشتراة: ${totalPurchased}`);
      console.log(`[INVENTORY-VERIFY] - إجمالي الكمية المباعة: ${totalSold}`);
      console.log(`[INVENTORY-VERIFY] - إجمالي الكمية المسترجعة: ${totalReturned}`);
      console.log(`[INVENTORY-VERIFY] - الكمية المتوقعة: ${expectedQuantity}`);
      console.log(`[INVENTORY-VERIFY] - الكمية الفعلية: ${inventory.current_quantity}`);

      logSystem(`[INVENTORY-VERIFY] إحصائيات المعاملات للصنف ${itemId} (${inventory.name}):`, 'info');
      logSystem(`[INVENTORY-VERIFY] - إجمالي الكمية المشتراة: ${totalPurchased}`, 'info');
      logSystem(`[INVENTORY-VERIFY] - إجمالي الكمية المباعة: ${totalSold}`, 'info');
      logSystem(`[INVENTORY-VERIFY] - إجمالي الكمية المسترجعة: ${totalReturned}`, 'info');
      logSystem(`[INVENTORY-VERIFY] - الكمية المتوقعة: ${expectedQuantity}`, 'info');
      logSystem(`[INVENTORY-VERIFY] - الكمية الفعلية: ${inventory.current_quantity}`, 'info');

      // التحقق من تطابق الكمية المتوقعة مع الكمية الفعلية
      const isConsistent = expectedQuantity === inventory.current_quantity;

      if (!isConsistent) {
        console.warn(`[INVENTORY-VERIFY] تحذير: هناك تعارض في المخزون للصنف ${itemId} (${inventory.name}):`);
        console.warn(`[INVENTORY-VERIFY] - الكمية المتوقعة (${expectedQuantity}) لا تتطابق مع الكمية الفعلية (${inventory.current_quantity})`);
        logSystem(`[INVENTORY-VERIFY] تحذير: هناك تعارض في المخزون للصنف ${itemId} (${inventory.name}):`, 'warning');
        logSystem(`[INVENTORY-VERIFY] - الكمية المتوقعة (${expectedQuantity}) لا تتطابق مع الكمية الفعلية (${inventory.current_quantity})`, 'warning');
      } else {
        console.log(`[INVENTORY-VERIFY] المخزون متسق للصنف ${itemId} (${inventory.name})`);
        logSystem(`[INVENTORY-VERIFY] المخزون متسق للصنف ${itemId} (${inventory.name})`, 'info');
      }

      return {
        success: true,
        itemId,
        name: inventory.name,
        currentQuantity: inventory.current_quantity,
        expectedQuantity,
        isConsistent,
        transactions: {
          totalPurchased,
          totalSold,
          totalReturned
        }
      };
    } catch (transactionError) {
      console.error(`[INVENTORY-VERIFY] خطأ في التحقق من المعاملات:`, transactionError);
      logSystem(`[INVENTORY-VERIFY] خطأ في التحقق من المعاملات: ${transactionError.message}`, 'error');
      return {
        success: false,
        itemId,
        name: inventory.name,
        currentQuantity: inventory.current_quantity,
        error: `خطأ في التحقق من المعاملات: ${transactionError.message}`
      };
    }
  } catch (error) {
    console.error(`[INVENTORY-VERIFY] خطأ في التحقق من حالة المخزون:`, error);
    logSystem(`[INVENTORY-VERIFY] خطأ في التحقق من حالة المخزون: ${error.message}`, 'error');
    return {
      success: false,
      error: error.message
    };
  }
}

// تصدير الوظائف
module.exports = {
  initialize,
  getInventoryForItem,
  getAllInventory,
  updateInventoryAfterPurchase,
  updateInventoryAfterSale,
  updateInventoryAfterReturn,
  syncInventoryWithItems,
  getSellingPriceForItem,
  updateSellingPrice,
  addItem,
  updateItem,
  deleteItem,
  updateInventoryItem,
  clearInventoryCache,
  verifyInventoryAfterReturn,
  verifyInventoryAfterSync
};