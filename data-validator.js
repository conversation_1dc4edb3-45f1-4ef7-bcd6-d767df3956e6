/**
 * وحدة التحقق من صحة البيانات
 * تقوم بالتحقق من صحة البيانات المدخلة قبل إدخالها في قاعدة البيانات
 */

const { logSystem, logError } = require('./error-handler');

/**
 * التحقق من صحة بيانات المعاملة
 * @param {Object} transaction - بيانات المعاملة
 * @returns {boolean} - صحة البيانات
 */
function validateTransaction(transaction) {
  try {
    // التحقق من وجود البيانات الأساسية
    if (!transaction) {
      logSystem('بيانات المعاملة غير موجودة', 'error');
      return false;
    }

    // التحقق من وجود معرف الصنف
    if (!transaction.item_id) {
      logSystem('معرف الصنف غير موجود في المعاملة', 'error');
      return false;
    }

    // التحقق من وجود نوع المعاملة
    if (!transaction.transaction_type) {
      logSystem('نوع المعاملة غير موجود', 'error');
      return false;
    }

    // التحقق من أن نوع المعاملة صحيح
    const validTypes = ['purchase', 'sale', 'receiving', 'withdrawal', 'return'];
    if (!validTypes.includes(transaction.transaction_type)) {
      logSystem(`نوع المعاملة غير صالح: ${transaction.transaction_type}`, 'error');
      return false;
    }

    // التحقق من وجود الكمية
    if (!transaction.quantity || isNaN(Number(transaction.quantity)) || Number(transaction.quantity) <= 0) {
      logSystem(`الكمية غير صالحة: ${transaction.quantity}`, 'error');
      return false;
    }

    // التحقق من وجود السعر
    if (!transaction.price || isNaN(Number(transaction.price)) || Number(transaction.price) < 0) {
      logSystem(`السعر غير صالح: ${transaction.price}`, 'error');
      return false;
    }

    // التحقق من وجود معرف العميل في حالة البيع أو الإرجاع
    if ((transaction.transaction_type === 'sale' || transaction.transaction_type === 'return') && !transaction.customer_id) {
      logSystem('معرف العميل مطلوب لعمليات البيع والإرجاع', 'error');
      return false;
    }

    // تسجيل نجاح التحقق
    logSystem(`تم التحقق من صحة بيانات المعاملة بنجاح: ${transaction.transaction_type} للصنف ${transaction.item_id}`, 'info');
    return true;
  } catch (error) {
    logError(error, 'validateTransaction');
    return false;
  }
}

/**
 * التحقق من صحة بيانات الصنف
 * @param {Object} item - بيانات الصنف
 * @returns {boolean} - صحة البيانات
 */
function validateItem(item) {
  try {
    // التحقق من وجود البيانات الأساسية
    if (!item) {
      logSystem('بيانات الصنف غير موجودة', 'error');
      return false;
    }

    // التحقق من وجود اسم الصنف
    if (!item.name || item.name.trim() === '') {
      logSystem('اسم الصنف غير موجود أو فارغ', 'error');
      return false;
    }

    // التحقق من وجود وحدة القياس
    if (!item.unit || item.unit.trim() === '') {
      logSystem('وحدة القياس غير موجودة أو فارغة', 'error');
      return false;
    }

    // تسجيل نجاح التحقق
    logSystem(`تم التحقق من صحة بيانات الصنف بنجاح: ${item.name}`, 'info');
    return true;
  } catch (error) {
    logError(error, 'validateItem');
    return false;
  }
}

/**
 * التحقق من صحة بيانات العميل
 * @param {Object} customer - بيانات العميل
 * @returns {boolean} - صحة البيانات
 */
function validateCustomer(customer) {
  try {
    // التحقق من وجود البيانات الأساسية
    if (!customer) {
      logSystem('بيانات العميل غير موجودة', 'error');
      return false;
    }

    // التحقق من وجود اسم العميل
    if (!customer.name || customer.name.trim() === '') {
      logSystem('اسم العميل غير موجود أو فارغ', 'error');
      return false;
    }

    // تسجيل نجاح التحقق
    logSystem(`تم التحقق من صحة بيانات العميل بنجاح: ${customer.name}`, 'info');
    return true;
  } catch (error) {
    logError(error, 'validateCustomer');
    return false;
  }
}

// تصدير الدوال
module.exports = {
  validateTransaction,
  validateItem,
  validateCustomer
};
