import React from 'react';
import './Button.css';

/**
 * مكون الزر
 * يوفر تصميمًا موحدًا للأزرار في التطبيق
 */
const Button = ({ 
  children, 
  variant = 'primary', 
  size = 'md', 
  icon,
  iconPosition = 'right',
  fullWidth = false,
  outlined = false,
  rounded = false,
  disabled = false,
  loading = false,
  className = '',
  onClick,
  ...props 
}) => {
  // تحديد فئات CSS
  const buttonClasses = [
    'app-button',
    `app-button-${variant}`,
    `app-button-${size}`,
    fullWidth ? 'app-button-full-width' : '',
    outlined ? 'app-button-outlined' : '',
    rounded ? 'app-button-rounded' : '',
    loading ? 'app-button-loading' : '',
    className
  ].filter(Boolean).join(' ');

  // معالج النقر
  const handleClick = (e) => {
    if (disabled || loading) {
      e.preventDefault();
      return;
    }
    
    if (onClick) {
      onClick(e);
    }
  };

  return (
    <button 
      className={buttonClasses}
      onClick={handleClick}
      disabled={disabled || loading}
      {...props}
    >
      {loading && (
        <span className="app-button-spinner"></span>
      )}
      
      {icon && iconPosition === 'right' && (
        <span className="app-button-icon app-button-icon-right">
          {icon}
        </span>
      )}
      
      <span className="app-button-text">{children}</span>
      
      {icon && iconPosition === 'left' && (
        <span className="app-button-icon app-button-icon-left">
          {icon}
        </span>
      )}
    </button>
  );
};

export default Button;
