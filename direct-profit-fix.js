/**
 * إصلاح مباشر لمشكلة الأرباح 5445
 */

console.log('🔧 إصلاح مباشر لمشكلة الأرباح 5445...');

try {
  // استيراد مدير المعاملات
  const transactionManager = require('./unified-transaction-manager');
  
  console.log('✅ تم استيراد مدير المعاملات');
  
  // حساب الأرباح
  const calculatedProfit = transactionManager.recalculateTotalProfits();
  console.log(`💰 الأرباح المحسوبة: ${calculatedProfit}`);
  
  // حفظ الأرباح
  const saveResult = transactionManager.updateProfitTotalInDatabase(calculatedProfit);
  
  if (saveResult) {
    console.log('✅ تم حفظ الأرباح بنجاح');
    
    // التحقق من الحفظ
    const DatabaseManager = require('./database-singleton');
    const db = DatabaseManager.getDatabase();
    
    if (db) {
      const verifyStmt = db.prepare('SELECT profit_total FROM cashbox WHERE id = 1');
      const verifyResult = verifyStmt.get();
      
      if (verifyResult) {
        const savedProfit = Number(verifyResult.profit_total);
        console.log(`💾 الأرباح المحفوظة: ${savedProfit}`);
        
        if (Math.abs(savedProfit - calculatedProfit) < 0.01) {
          console.log('🎉 تم إصلاح مشكلة الأرباح بنجاح!');
        } else {
          console.log(`❌ تباين في الأرباح: محسوب ${calculatedProfit}, محفوظ ${savedProfit}`);
        }
      } else {
        console.log('❌ لم يتم العثور على بيانات الخزينة');
      }
    } else {
      console.log('❌ قاعدة البيانات غير متصلة');
    }
  } else {
    console.log('❌ فشل في حفظ الأرباح');
  }
  
} catch (error) {
  console.error('❌ خطأ:', error.message);
}
