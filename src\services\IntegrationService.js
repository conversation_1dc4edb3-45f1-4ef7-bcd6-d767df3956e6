/**
 * خدمة التكامل مع منظومة المبيعات عبر الشبكة المحلية (LAN)
 * تتعامل مع عمليات المزامنة والاتصال بين منظومة المخزن ومنظومة المبيعات
 * تم تصميمها للعمل في بيئة الشبكة المحلية بدلاً من الإنترنت لتحسين الأداء والأمان
 */
import axios from 'axios';
import salesApiService from './salesApi';

/**
 * تنسيق عنوان URL للتأكد من أنه يعمل على الشبكة المحلية
 * @param {string} url - عنوان URL الأصلي
 * @returns {string} - عنوان URL المنسق
 */
const formatUrl = (url) => {
  if (!url) return '';

  let formattedUrl = url;

  // إذا لم يبدأ العنوان بـ http:// أو https://، أضف http://
  if (!formattedUrl.startsWith('http://') && !formattedUrl.startsWith('https://')) {
    formattedUrl = `http://${formattedUrl}`;
  }

  // إذا لم يحتوي العنوان على رقم المنفذ، أضف المنفذ الافتراضي 3000
  if (!formattedUrl.includes(':', formattedUrl.indexOf('//') + 2)) {
    formattedUrl = `${formattedUrl}:3000`;
  }

  return formattedUrl;
};

/**
 * اختبار الاتصال بمنظومة المبيعات عبر الشبكة المحلية أو الإنترنت
 * @param {string} url - عنوان IP أو اسم المضيف أو URL
 * @param {string} apiKey - مفتاح API للمصادقة
 * @param {boolean} isInternet - هل الاتصال عبر الإنترنت
 * @returns {Promise<{success: boolean, message: string, deviceInfo: object}>} - نتيجة اختبار الاتصال
 */
export const testConnection = async (url, apiKey, isInternet = false) => {
  try {
    // التحقق من صحة المدخلات
    if (!url || !apiKey) {
      return { success: false, message: 'يرجى إدخال عنوان URL أو اسم المضيف ومفتاح API' };
    }

    console.log(`اختبار الاتصال عبر ${isInternet ? 'الإنترنت' : 'الشبكة المحلية'}: ${url}`);

    // تحديث إعدادات خدمة API
    salesApiService.updateConfig({
      baseUrl: formatUrl(url),
      apiKey: apiKey,
      isInternet: isInternet,
      timeout: isInternet ? 30000 : 10000 // 30 ثانية للإنترنت، 10 ثوانٍ للشبكة المحلية
    });

    // استخدام خدمة API لاختبار الاتصال
    const result = await salesApiService.testConnection();
    return result;
  } catch (error) {
    console.error('Connection test error:', error);
    // في حالة وجود خطأ في الشبكة أو أي خطأ آخر
    return {
      success: false,
      message: `حدث خطأ أثناء اختبار الاتصال: ${error.message || 'خطأ غير معروف'}`
    };
  }
};

/**
 * مزامنة بيانات المخزون مع منظومة المبيعات عبر الشبكة المحلية أو الإنترنت
 * @param {string} url - عنوان IP أو اسم المضيف أو URL
 * @param {string} apiKey - مفتاح API للمصادقة
 * @param {Object} inventoryData - بيانات المخزون الحالية
 * @param {boolean} isInternet - هل الاتصال عبر الإنترنت
 * @returns {Promise<{success: boolean, message: string, data: Object}>} - نتيجة المزامنة
 */
export const syncInventory = async (url, apiKey, inventoryData, isInternet = false) => {
  try {
    // التحقق من صحة المدخلات
    if (!url || !apiKey) {
      return { success: false, message: 'يرجى إدخال عنوان URL أو اسم المضيف ومفتاح API', data: null };
    }

    console.log(`مزامنة المخزون عبر ${isInternet ? 'الإنترنت' : 'الشبكة المحلية'}: ${url}`);
    console.log(`عدد العناصر للمزامنة: ${Array.isArray(inventoryData) ? inventoryData.length : 'غير معروف'}`);

    // تحديث إعدادات خدمة API
    salesApiService.updateConfig({
      baseUrl: formatUrl(url),
      apiKey: apiKey,
      isInternet: isInternet,
      timeout: isInternet ? 30000 : 10000 // 30 ثانية للإنترنت، 10 ثوانٍ للشبكة المحلية
    });

    // استخدام خدمة API لمزامنة المخزون
    const result = await salesApiService.syncInventory(inventoryData);
    return result;
  } catch (error) {
    console.error('Sync error:', error);
    // في حالة وجود خطأ في الشبكة أو أي خطأ آخر
    return {
      success: false,
      message: `حدث خطأ أثناء المزامنة: ${error.message || 'خطأ غير معروف'}`,
      data: null
    };
  }
};

/**
 * الحصول على بيانات المبيعات من منظومة المبيعات عبر الشبكة المحلية أو الإنترنت
 * @param {string} url - عنوان IP أو اسم المضيف أو URL
 * @param {string} apiKey - مفتاح API للمصادقة
 * @param {string} lastSyncTime - وقت آخر مزامنة (ISO string)
 * @param {boolean} isInternet - هل الاتصال عبر الإنترنت
 * @returns {Promise<{success: boolean, message: string, sales: Array}>} - بيانات المبيعات
 */
export const getSalesData = async (url, apiKey, lastSyncTime, isInternet = false) => {
  try {
    // التحقق من صحة المدخلات
    if (!url || !apiKey) {
      return { success: false, message: 'يرجى إدخال عنوان URL أو اسم المضيف ومفتاح API', sales: [] };
    }

    console.log(`الحصول على بيانات المبيعات عبر ${isInternet ? 'الإنترنت' : 'الشبكة المحلية'}: ${url}`);
    console.log(`آخر وقت مزامنة: ${lastSyncTime || 'لم يتم المزامنة من قبل'}`);

    // تحديث إعدادات خدمة API
    salesApiService.updateConfig({
      baseUrl: formatUrl(url),
      apiKey: apiKey,
      isInternet: isInternet,
      timeout: isInternet ? 30000 : 10000 // 30 ثانية للإنترنت، 10 ثوانٍ للشبكة المحلية
    });

    // استخدام خدمة API للحصول على بيانات المبيعات
    const params = {
      startDate: lastSyncTime || new Date(0).toISOString(),
      endDate: new Date().toISOString()
    };

    const result = await salesApiService.getSales(params);

    // تنسيق النتيجة لتتوافق مع التنسيق المتوقع
    if (result.success) {
      return {
        success: true,
        message: 'تم الحصول على بيانات المبيعات بنجاح',
        sales: result.data?.sales || []
      };
    } else {
      return {
        success: false,
        message: result.message || 'فشل الحصول على بيانات المبيعات',
        sales: []
      };
    }
  } catch (error) {
    console.error('Get sales data error:', error);
    // في حالة وجود خطأ في الشبكة أو أي خطأ آخر
    return {
      success: false,
      message: `حدث خطأ أثناء الحصول على بيانات المبيعات: ${error.message || 'خطأ غير معروف'}`,
      sales: []
    };
  }
};

/**
 * تحديث حالة المزامنة عبر الشبكة المحلية أو الإنترنت
 * @param {string} url - عنوان IP أو اسم المضيف أو URL
 * @param {string} apiKey - مفتاح API للمصادقة
 * @param {string} syncId - معرف المزامنة
 * @param {string} status - حالة المزامنة (completed, failed)
 * @param {boolean} isInternet - هل الاتصال عبر الإنترنت
 * @returns {Promise<{success: boolean, message: string}>} - نتيجة تحديث الحالة
 */
export const updateSyncStatus = async (url, apiKey, syncId, status, isInternet = false) => {
  try {
    // التحقق من صحة المدخلات
    if (!url || !apiKey || !syncId) {
      return { success: false, message: 'بيانات غير كاملة لتحديث حالة المزامنة' };
    }

    console.log(`تحديث حالة المزامنة عبر ${isInternet ? 'الإنترنت' : 'الشبكة المحلية'}: ${url}`);
    console.log(`معرف المزامنة: ${syncId}, الحالة: ${status}`);

    // تحديث إعدادات خدمة API
    salesApiService.updateConfig({
      baseUrl: formatUrl(url),
      apiKey: apiKey,
      isInternet: isInternet,
      timeout: isInternet ? 30000 : 10000 // 30 ثانية للإنترنت، 10 ثوانٍ للشبكة المحلية
    });

    // إنشاء كائن المعاملة
    const transaction = {
      syncId,
      status,
      source: 'warehouse-system',
      timestamp: new Date().toISOString()
    };

    // استخدام خدمة API لإرسال المعاملة
    const result = await salesApiService.sendTransaction(transaction);

    if (result.success) {
      return {
        success: true,
        message: 'تم تحديث حالة المزامنة بنجاح'
      };
    } else {
      return {
        success: false,
        message: result.message || 'فشل تحديث حالة المزامنة'
      };
    }
  } catch (error) {
    console.error('Update sync status error:', error);
    // في حالة وجود خطأ في الشبكة أو أي خطأ آخر
    return {
      success: false,
      message: `حدث خطأ أثناء تحديث حالة المزامنة: ${error.message || 'خطأ غير معروف'}`
    };
  }
};
