/* تنسيق النوافذ المنبثقة الجديدة */

/* خلفية النافذة المنبثقة */
.popup-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1050;
  backdrop-filter: blur(3px);
}

/* حاوية النافذة المنبثقة */
.popup-container {
  background-color: var(--card-bg, #fff);
  border-radius: var(--border-radius-md, 8px);
  box-shadow: var(--shadow-lg, 0 5px 20px rgba(0, 0, 0, 0.2));
  width: 90%;
  max-width: 1000px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  animation: popup-in 0.3s ease-out;
  position: relative;
}

/* رأس النافذة المنبثقة */
.popup-header {
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  border-left: 1px solid var(--border-color, #eee);
  background-color: var(--bg-color, #f7f9fc);
  width: 250px;
  min-width: 250px;
}

/* تنسيق النافذة المنبثقة الأفقية */
.popup-horizontal {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.popup-horizontal-content {
  display: flex;
  flex-direction: row;
  height: 100%;
}

.popup-sidebar {
  width: 300px;
  padding: 20px;
  border-left: 1px solid var(--border-color, #eee);
  background-color: var(--bg-color, #f9f9f9);
}

.popup-main-content {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.popup-header h3 {
  margin: 0 0 15px 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-dark, #333);
}

/* زر الإغلاق */
.popup-close {
  position: absolute;
  top: 15px;
  left: 15px;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-light, #999);
  transition: color 0.2s;
  z-index: 10;
}

.popup-close:hover {
  color: var(--danger-color, #e74c3c);
}

/* جسم النافذة المنبثقة */
.popup-body {
  padding: 30px;
  overflow-y: auto;
  flex: 1;
  max-height: 90vh;
}

/* تذييل النافذة المنبثقة */
.popup-footer {
  padding: 15px 25px;
  border-top: 1px solid var(--border-color, #eee);
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  background-color: rgba(0, 0, 0, 0.01);
}

/* تنسيق الحقول */
.popup-form-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -15px;
  margin-bottom: 25px;
}

.popup-form-group {
  flex: 1;
  min-width: 250px;
  padding: 0 15px;
  margin-bottom: 20px;
}

.popup-form-group label {
  display: block;
  margin-bottom: 10px;
  font-weight: 600;
  color: var(--text-dark, #333);
  font-size: 15px;
}

.popup-form-control {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid var(--border-color, #ddd);
  border-radius: var(--border-radius-sm, 4px);
  font-size: 15px;
  transition: border-color 0.2s, box-shadow 0.2s;
  background-color: white;
  color: var(--text-color, #2c3e50);
  height: 45px;
}

.popup-form-control:focus {
  border-color: var(--primary-color, #1e3243);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb, 30, 50, 67), 0.1);
  outline: none;
}

.popup-form-control::placeholder {
  color: #999;
  opacity: 0.7;
}

textarea.popup-form-control {
  height: auto;
  min-height: 80px;
  resize: vertical;
}

.popup-form-select {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid var(--border-color, #ddd);
  border-radius: var(--border-radius-sm, 4px);
  font-size: 15px;
  transition: border-color 0.2s, box-shadow 0.2s;
  background-color: white;
  cursor: pointer;
  color: var(--text-color, #2c3e50);
  height: 45px;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: left 15px center;
  padding-left: 40px;
}

.popup-form-select:focus {
  border-color: var(--primary-color, #1e3243);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb, 30, 50, 67), 0.1);
  outline: none;
}

/* تنسيق الأزرار */
.popup-btn {
  padding: 10px 20px;
  border-radius: var(--border-radius-md, 4px);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  border: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  height: 45px;
  min-width: 120px;
}

.popup-btn-primary {
  background-color: var(--primary-color, #1e3243);
  color: white;
}

.popup-btn-primary:hover {
  background-color: var(--primary-dark, #162330);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md, 0 4px 8px rgba(0, 0, 0, 0.1));
}

.popup-btn-secondary {
  background-color: #f1f1f1;
  color: var(--text-dark, #333);
}

.popup-btn-secondary:hover {
  background-color: #e0e0e0;
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm, 0 2px 4px rgba(0, 0, 0, 0.05));
}

.popup-btn-danger {
  background-color: var(--danger-color, #e74c3c);
  color: white;
}

.popup-btn-danger:hover {
  background-color: #c0392b;
  transform: translateY(-2px);
  box-shadow: var(--shadow-md, 0 4px 8px rgba(0, 0, 0, 0.1));
}

/* تنسيق الجدول */
.popup-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 30px;
  border-radius: var(--border-radius-md, 8px);
  overflow: hidden;
  box-shadow: var(--shadow-sm, 0 2px 4px rgba(0, 0, 0, 0.05));
  border: 1px solid var(--border-color, #eee);
}

.popup-table th {
  background-color: #f8f9fa;
  padding: 15px 20px;
  text-align: right;
  font-weight: 600;
  color: var(--text-dark, #333);
  border-bottom: 2px solid var(--border-color, #eee);
  font-size: 15px;
}

.popup-table td {
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color, #eee);
  font-size: 14px;
  vertical-align: middle;
}

.popup-table tr:last-child td {
  border-bottom: none;
}

.popup-table tr:hover {
  background-color: rgba(0, 0, 0, 0.01);
}

.popup-table-container {
  margin: 30px 0;
}

/* تأثيرات الحركة */
@keyframes popup-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تنسيقات إضافية للنافذة المنبثقة الأفقية */
.popup-header-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 20px;
}

/* تنسيق قسم إضافة الصنف */
.item-add-section {
  background-color: var(--bg-color, #f9f9f9);
  padding: 25px;
  border-radius: var(--border-radius-md, 8px);
  margin-bottom: 30px;
  border: 1px solid var(--border-color, #eee);
  box-shadow: var(--shadow-sm, 0 2px 4px rgba(0, 0, 0, 0.05));
}

.item-add-section h4 {
  margin-bottom: 20px;
  font-size: 1.1rem;
  color: var(--text-dark, #333);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.item-add-section h4 svg {
  color: var(--primary-color, #1e3243);
}

.item-add-button {
  text-align: left;
  margin-top: 20px;
}

.popup-header-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 10px;
  color: var(--text-light, #5d7b8c);
  font-size: 0.9rem;
}

.popup-header-info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.popup-header-info-item svg {
  color: var(--primary-color, #1e3243);
  font-size: 1.1rem;
}

.popup-header-divider {
  height: 1px;
  background-color: var(--border-color, #eee);
  margin: 15px 0;
  opacity: 0.5;
}

/* تنسيق للعنوان الفرعي في النافذة المنبثقة */
.popup-subtitle {
  font-size: 0.9rem;
  color: var(--text-light, #5d7b8c);
  margin-top: -5px;
  margin-bottom: 20px;
}

/* تنسيق الحقول المطلوبة */
.popup-required {
  color: #e74c3c;
  margin-right: 3px;
}

/* تنسيق الرسائل */
.popup-alert {
  padding: 12px 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.popup-alert-success {
  background-color: rgba(46, 204, 113, 0.1);
  border: 1px solid #2ecc71;
  color: #27ae60;
}

.popup-alert-danger {
  background-color: rgba(231, 76, 60, 0.1);
  border: 1px solid #e74c3c;
  color: #c0392b;
}

.popup-alert-warning {
  background-color: rgba(243, 156, 18, 0.1);
  border: 1px solid #f39c12;
  color: #d35400;
}

.popup-alert-info {
  background-color: rgba(52, 152, 219, 0.1);
  border: 1px solid #3498db;
  color: #2980b9;
}

/* تنسيق للشاشات الصغيرة */
@media (max-width: 992px) {
  .popup-container {
    flex-direction: column;
    width: 95%;
    max-height: 95vh;
  }

  .popup-header {
    width: 100%;
    min-width: 100%;
    border-left: none;
    border-bottom: 1px solid var(--border-color, #eee);
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .popup-header h3 {
    margin: 0;
  }

  .popup-close {
    position: static;
  }
}

@media (max-width: 768px) {
  .popup-form-row {
    flex-direction: column;
  }

  .popup-form-group {
    min-width: 100%;
  }

  .popup-footer {
    flex-direction: column;
    gap: 10px;
  }

  .popup-footer button {
    width: 100%;
  }
}
