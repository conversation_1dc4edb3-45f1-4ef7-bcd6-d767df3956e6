/**
 * إصلاح مدير العملاء
 * يقوم بإصلاح مشاكل حذف العملاء وتحديث العملاء
 */

const { logSystem, logError } = require('./error-handler');
const DatabaseManager = require('./database-singleton');

/**
 * تهيئة إصلاح مدير العملاء
 * @returns {boolean} - نجاح العملية
 */
function initialize() {
  try {
    logSystem('بدء تهيئة إصلاح مدير العملاء', 'info');

    // الحصول على اتصال قاعدة البيانات
    const dbManager = DatabaseManager.getInstance();
    const db = dbManager.getConnection();

    if (!db) {
      logSystem('فشل في الحصول على اتصال قاعدة البيانات', 'error');
      return false;
    }

    // التحقق من وجود جدول العملاء
    const tableExistsStmt = db.prepare(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='customers'
    `);
    const tableExists = tableExistsStmt.get();

    if (!tableExists) {
      logSystem('جدول العملاء غير موجود', 'error');
      return false;
    }

    logSystem('تم تهيئة إصلاح مدير العملاء بنجاح', 'info');
    return true;
  } catch (error) {
    logError(error, 'customers-manager-fix.initialize');
    return false;
  }
}

/**
 * إصلاح حذف العملاء
 * @param {number} customerId - معرف العميل
 * @returns {Object} - نتيجة العملية
 */
function fixCustomerDeletion(customerId) {
  try {
    logSystem(`بدء إصلاح حذف العميل: ${customerId}`, 'info');

    // الحصول على اتصال قاعدة البيانات
    const dbManager = DatabaseManager.getInstance();
    const db = dbManager.getConnection();

    if (!db) {
      return {
        success: false,
        error: 'فشل في الحصول على اتصال قاعدة البيانات'
      };
    }

    // التحقق من وجود العميل
    const customerExistsStmt = db.prepare('SELECT * FROM customers WHERE id = ?');
    const customer = customerExistsStmt.get(customerId);

    if (!customer) {
      return {
        success: false,
        error: `العميل غير موجود: ${customerId}`
      };
    }

    // التحقق من وجود معاملات مرتبطة بالعميل
    const transactionsStmt = db.prepare('SELECT COUNT(*) as count FROM transactions WHERE customer_id = ?');
    const { count: transactionsCount } = transactionsStmt.get(customerId);

    if (transactionsCount > 0) {
      return {
        success: false,
        error: `لا يمكن حذف العميل لأنه مرتبط بـ ${transactionsCount} معاملة`,
        transactionsCount
      };
    }

    // حذف العميل
    const deleteStmt = db.prepare('DELETE FROM customers WHERE id = ?');
    const result = deleteStmt.run(customerId);

    if (result.changes === 0) {
      return {
        success: false,
        error: `فشل في حذف العميل: ${customerId}`
      };
    }

    logSystem(`تم حذف العميل بنجاح: ${customerId}`, 'info');
    return {
      success: true,
      message: `تم حذف العميل بنجاح: ${customerId}`
    };
  } catch (error) {
    logError(error, 'customers-manager-fix.fixCustomerDeletion');
    return {
      success: false,
      error: error.message
    };
  }
}

// تصدير الدوال
module.exports = {
  initialize,
  fixCustomerDeletion
};
