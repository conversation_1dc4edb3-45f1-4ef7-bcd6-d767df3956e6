/* تنسيق نافذة البحث عن الأصناف المنبثقة */

.item-search-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1100;
  backdrop-filter: blur(3px);
}

.item-search-container {
  background-color: var(--card-bg, #fff);
  border-radius: var(--border-radius-md, 8px);
  box-shadow: var(--shadow-lg, 0 5px 20px rgba(0, 0, 0, 0.2));
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: popup-in 0.3s ease-out;
}

.item-search-header {
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border-color, #eee);
  background-color: var(--bg-color, #f7f9fc);
}

.item-search-header h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-dark, #333);
}

.item-search-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-light, #999);
  transition: color 0.2s;
}

.item-search-close:hover {
  color: var(--danger-color, #e74c3c);
}

.item-search-body {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

.item-search-input {
  position: relative;
  margin-bottom: 20px;
}

.item-search-input input {
  width: 100%;
  padding: 12px 15px 12px 40px;
  border: 1px solid var(--border-color, #ddd);
  border-radius: var(--border-radius-sm, 4px);
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.item-search-input input:focus {
  outline: none;
  border-color: var(--primary-color, #3498db);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.item-search-input .search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-light, #999);
  font-size: 1.2rem;
}

.item-search-results {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
}

.item-card {
  border: 1px solid var(--border-color, #eee);
  border-radius: var(--border-radius-sm, 4px);
  padding: 15px;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s, border-color 0.2s;
  background-color: var(--card-bg, #fff);
}

.item-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border-color: var(--primary-color, #3498db);
}

.item-card.selected {
  border-color: var(--primary-color, #3498db);
  background-color: rgba(52, 152, 219, 0.05);
}

.item-name {
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--text-dark, #333);
  font-size: 1.1rem;
}

.item-details {
  display: flex;
  flex-direction: column;
  gap: 5px;
  font-size: 0.9rem;
}

.item-detail {
  display: flex;
  justify-content: space-between;
}

.item-detail-label {
  color: var(--text-light, #777);
}

.item-detail-value {
  font-weight: 500;
  color: var(--text-dark, #333);
}

.item-quantity {
  color: var(--success-color, #27ae60);
}

.item-quantity.low {
  color: var(--warning-color, #f39c12);
}

.item-quantity.out {
  color: var(--danger-color, #e74c3c);
}

.item-search-footer {
  padding: 15px 20px;
  border-top: 1px solid var(--border-color, #eee);
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  background-color: rgba(0, 0, 0, 0.01);
}

.item-search-btn {
  padding: 8px 16px;
  border-radius: var(--border-radius-sm, 4px);
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
  border: none;
}

.item-search-btn:active {
  transform: translateY(1px);
}

.item-search-btn-secondary {
  background-color: var(--bg-color, #f1f1f1);
  color: var(--text-dark, #333);
}

.item-search-btn-secondary:hover {
  background-color: var(--bg-color-hover, #e5e5e5);
}

.item-search-btn-primary {
  background-color: var(--primary-color, #3498db);
  color: white;
}

.item-search-btn-primary:hover {
  background-color: var(--primary-color-hover, #2980b9);
}

.item-search-btn-primary:disabled {
  background-color: var(--disabled-color, #bdc3c7);
  cursor: not-allowed;
}

/* تنسيق للشاشات الصغيرة */
@media (max-width: 768px) {
  .item-search-results {
    grid-template-columns: 1fr;
  }
}
