import React, { createContext } from 'react';
import { SettingsProvider } from './providers/SettingsProvider';
import { UsersProvider } from './providers/UsersProvider';
import { NotificationsProvider } from './providers/NotificationsProvider';
import { SyncProvider } from './providers/SyncProvider';
import { InventoryProvider } from './providers/InventoryProvider';
import { TransactionsProvider } from './providers/TransactionsProvider';
import { CustomersProvider } from './providers/CustomersProvider';
import { MachinesProvider } from './providers/MachinesProvider';
import { CashboxProvider } from './providers/CashboxProvider';

// إنشاء سياق التطبيق
export const AppContext = createContext();

/**
 * مزود سياق التطبيق
 * 
 * يقوم هذا المكون بتغليف التطبيق بجميع مزودي السياق المختلفة
 * 
 * @param {Object} props - خصائص المكون
 * @param {React.ReactNode} props.children - المكونات الفرعية
 * @returns {React.ReactElement} مزود سياق التطبيق
 */
export const AppProvider = ({ children }) => {
  return (
    <SettingsProvider>
      <UsersProvider>
        <NotificationsProvider>
          <SyncProvider>
            <InventoryProvider>
              <TransactionsProvider>
                <CustomersProvider>
                  <MachinesProvider>
                    <CashboxProvider>
                      {children}
                    </CashboxProvider>
                  </MachinesProvider>
                </CustomersProvider>
              </TransactionsProvider>
            </InventoryProvider>
          </SyncProvider>
        </NotificationsProvider>
      </UsersProvider>
    </SettingsProvider>
  );
};

// تصدير هوك useApp
export { useApp } from './hooks/useApp';

export default AppProvider;
