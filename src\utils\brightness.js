/**
 * وظائف التحكم في السطوع والتباين - نظام تلقائي يعتمد على وقت اليوم
 */

/**
 * الحصول على مستوى السطوع المناسب بناءً على وقت اليوم
 * @returns {number} مستوى السطوع المناسب (50-150)
 */
export const getAutoBrightnessLevel = () => {
  const now = new Date();
  const hours = now.getHours();

  // تحديد مستوى السطوع بناءً على وقت اليوم
  // الصباح الباكر (5-8): سطوع متوسط
  // النهار (9-16): سطوع عالي
  // المساء (17-20): سطوع متوسط
  // الليل (21-4): سطوع منخفض

  if (hours >= 5 && hours <= 8) {
    return 95; // سطوع متوسط للصباح الباكر
  } else if (hours >= 9 && hours <= 16) {
    return 110; // سطوع عالي للنهار
  } else if (hours >= 17 && hours <= 20) {
    return 90; // سطوع متوسط للمساء
  } else {
    return 80; // سطوع منخفض لليل
  }
};

/**
 * تطبيق مستوى السطوع على العناصر
 * @param {number|string} level - مستوى السطوع (50-150) أو "auto" للوضع التلقائي
 */
export const applyBrightness = (level) => {
  // التحقق مما إذا كان الوضع تلقائي
  const isAuto = level === 'auto';

  // الحصول على مستوى السطوع المناسب
  const brightnessLevel = isAuto ? getAutoBrightnessLevel() : level;

  // التأكد من أن المستوى ضمن النطاق المسموح
  const validLevel = Math.max(50, Math.min(150, brightnessLevel));

  // تحويل المستوى إلى قيمة مناسبة للفلتر
  const brightnessValue = validLevel / 100;
  const contrastValue = validLevel > 100 ? 1 + (validLevel - 100) / 200 : 1;

  // تحديث متغيرات CSS
  document.documentElement.style.setProperty('--brightness-factor', brightnessValue.toFixed(2));
  document.documentElement.style.setProperty('--contrast-factor', contrastValue.toFixed(2));

  // تطبيق الفلتر على العناصر الرئيسية
  const elements = document.querySelectorAll('.brightness-target');
  elements.forEach(element => {
    element.classList.add('brightness-enhanced');
  });

  // حفظ الإعداد في التخزين المحلي
  localStorage.setItem('brightness_mode', isAuto ? 'auto' : 'manual');
  localStorage.setItem('brightness_level', validLevel);

  return validLevel;
};

/**
 * استعادة مستوى السطوع المحفوظ
 * @returns {number} مستوى السطوع المستعاد
 */
export const restoreBrightness = () => {
  const brightnessMode = localStorage.getItem('brightness_mode') || 'auto';

  if (brightnessMode === 'auto') {
    // استخدام الوضع التلقائي
    return applyBrightness('auto');
  } else {
    // استخدام المستوى المحفوظ
    const savedLevel = localStorage.getItem('brightness_level');
    if (savedLevel) {
      return applyBrightness(parseInt(savedLevel, 10));
    }
    return applyBrightness('auto'); // استخدام الوضع التلقائي كافتراضي
  }
};

/**
 * إضافة فئة الهدف للسطوع إلى العناصر
 * @param {string} selector - محدد CSS للعناصر المستهدفة
 */
export const addBrightnessTarget = (selector) => {
  const elements = document.querySelectorAll(selector);
  elements.forEach(element => {
    element.classList.add('brightness-target');
  });
};

/**
 * تحديث السطوع التلقائي
 */
export const updateAutoBrightness = () => {
  const brightnessMode = localStorage.getItem('brightness_mode') || 'auto';
  if (brightnessMode === 'auto') {
    applyBrightness('auto');
  }
};

/**
 * تهيئة نظام السطوع
 * @param {string[]} selectors - مصفوفة من محددات CSS للعناصر المستهدفة
 */
export const initBrightness = (selectors = ['.main-content', '.navbar', '.card', '.table']) => {
  selectors.forEach(selector => {
    addBrightnessTarget(selector);
  });

  // تطبيق السطوع المناسب
  restoreBrightness();

  // إعداد مؤقت لتحديث السطوع كل ساعة
  setInterval(updateAutoBrightness, 60 * 60 * 1000);

  // تحديث السطوع عند تغيير التبويب أو استعادة النافذة
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
      updateAutoBrightness();
    }
  });
};
