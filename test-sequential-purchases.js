/**
 * اختبار عمليات الشراء المتتالية لضمان عدم وجود خصم مضاعف من الأرباح
 */

const path = require('path');
const fs = require('fs');

// تحديد مسار قاعدة البيانات
const dbPath = path.join(require('os').homedir(), 'AppData', 'Roaming', 'warehouse-management-system', 'wms-database', 'warehouse.db');

console.log('🧪 اختبار عمليات الشراء المتتالية...');
console.log(`📁 مسار قاعدة البيانات: ${dbPath}`);

// التحقق من وجود قاعدة البيانات
if (!fs.existsSync(dbPath)) {
  console.error('❌ قاعدة البيانات غير موجودة!');
  process.exit(1);
}

// استيراد المكتبات
const Database = require('better-sqlite3');

async function testSequentialPurchases() {
  let db;
  
  try {
    // الاتصال بقاعدة البيانات
    db = new Database(dbPath);
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // قراءة الخزينة الحالية
    const initialCashbox = db.prepare('SELECT * FROM cashbox LIMIT 1').get();
    
    if (!initialCashbox) {
      console.log('❌ لا توجد خزينة في قاعدة البيانات');
      return;
    }

    console.log('\n📊 بيانات الخزينة الأولية:');
    console.log(`   - الرصيد الافتتاحي: ${initialCashbox.initial_balance}`);
    console.log(`   - الرصيد الحالي: ${initialCashbox.current_balance}`);
    console.log(`   - إجمالي المبيعات: ${initialCashbox.sales_total}`);
    console.log(`   - إجمالي المشتريات: ${initialCashbox.purchases_total}`);
    console.log(`   - إجمالي مصاريف النقل: ${initialCashbox.transport_total || 0}`);
    console.log(`   - إجمالي الأرباح: ${initialCashbox.profit_total}`);

    // حساب الأرباح المتوقعة الأولية
    const initialExpectedProfit = (initialCashbox.sales_total || 0) - (initialCashbox.purchases_total || 0) - (initialCashbox.transport_total || 0);
    console.log(`   - الأرباح المتوقعة: ${initialExpectedProfit}`);

    // محاكاة عمليات شراء متتالية
    const purchases = [
      { amount: 1000, transport: 50, description: 'الشراء الأول' },
      { amount: 1500, transport: 75, description: 'الشراء الثاني' },
      { amount: 800, transport: 40, description: 'الشراء الثالث' }
    ];

    let currentCashbox = { ...initialCashbox };
    
    for (let i = 0; i < purchases.length; i++) {
      const purchase = purchases[i];
      console.log(`\n🛒 ${purchase.description}:`);
      console.log(`   - مبلغ الشراء: ${purchase.amount}`);
      console.log(`   - مصاريف النقل: ${purchase.transport}`);

      // حساب القيم الجديدة المتوقعة
      const newPurchasesTotal = (currentCashbox.purchases_total || 0) + purchase.amount;
      const newTransportTotal = (currentCashbox.transport_total || 0) + purchase.transport;
      const expectedProfitAfterPurchase = (currentCashbox.sales_total || 0) - newPurchasesTotal - newTransportTotal;

      console.log(`   - إجمالي المشتريات المتوقع: ${currentCashbox.purchases_total || 0} + ${purchase.amount} = ${newPurchasesTotal}`);
      console.log(`   - إجمالي مصاريف النقل المتوقع: ${currentCashbox.transport_total || 0} + ${purchase.transport} = ${newTransportTotal}`);
      console.log(`   - الأرباح المتوقعة: ${currentCashbox.sales_total || 0} - ${newPurchasesTotal} - ${newTransportTotal} = ${expectedProfitAfterPurchase}`);

      // تطبيق التحديث على قاعدة البيانات
      const updateStmt = db.prepare(`
        UPDATE cashbox
        SET current_balance = ?,
            purchases_total = purchases_total + ?,
            transport_total = transport_total + ?,
            profit_total = ?,
            updated_at = ?
        WHERE id = ?
      `);

      const updateResult = updateStmt.run(
        currentCashbox.initial_balance,  // الرصيد الحالي = الرصيد الافتتاحي (ثابت)
        purchase.amount,                 // إضافة مبلغ الشراء لإجمالي المشتريات
        purchase.transport,              // إضافة مصاريف النقل لإجمالي مصاريف النقل
        expectedProfitAfterPurchase,     // الأرباح الجديدة المحسوبة
        new Date().toISOString(),        // وقت التحديث
        currentCashbox.id                // معرف الخزينة
      );

      console.log(`   - نتيجة التحديث: ${updateResult.changes} صف محدث`);

      // قراءة الخزينة بعد التحديث
      const updatedCashbox = db.prepare('SELECT * FROM cashbox LIMIT 1').get();
      
      console.log(`   📊 بيانات الخزينة بعد ${purchase.description}:`);
      console.log(`      - الرصيد الحالي: ${updatedCashbox.current_balance}`);
      console.log(`      - إجمالي المشتريات: ${updatedCashbox.purchases_total}`);
      console.log(`      - إجمالي مصاريف النقل: ${updatedCashbox.transport_total}`);
      console.log(`      - إجمالي الأرباح: ${updatedCashbox.profit_total}`);

      // التحقق من صحة النتائج
      const actualProfitMatches = Math.abs(updatedCashbox.profit_total - expectedProfitAfterPurchase) < 0.01;
      const purchasesTotalMatches = updatedCashbox.purchases_total === newPurchasesTotal;
      const transportTotalMatches = updatedCashbox.transport_total === newTransportTotal;
      const currentBalanceMatches = updatedCashbox.current_balance === updatedCashbox.initial_balance;

      console.log(`   🧮 التحقق من صحة النتائج:`);
      console.log(`      - الرصيد الحالي = الرصيد الافتتاحي: ${currentBalanceMatches ? '✅' : '❌'}`);
      console.log(`      - إجمالي المشتريات صحيح: ${purchasesTotalMatches ? '✅' : '❌'}`);
      console.log(`      - إجمالي مصاريف النقل صحيح: ${transportTotalMatches ? '✅' : '❌'}`);
      console.log(`      - إجمالي الأرباح صحيح: ${actualProfitMatches ? '✅' : '❌'}`);

      if (!actualProfitMatches) {
        console.log(`      ⚠️  الأرباح الفعلية: ${updatedCashbox.profit_total}, المتوقعة: ${expectedProfitAfterPurchase}`);
      }

      // تحديث الخزينة الحالية للتكرار التالي
      currentCashbox = updatedCashbox;

      // فاصل بين العمليات
      console.log(`   ${'='.repeat(50)}`);
    }

    // النتيجة النهائية
    console.log('\n🎯 النتيجة النهائية:');
    
    const finalCashbox = db.prepare('SELECT * FROM cashbox LIMIT 1').get();
    const finalExpectedProfit = (finalCashbox.sales_total || 0) - (finalCashbox.purchases_total || 0) - (finalCashbox.transport_total || 0);
    const finalProfitMatches = Math.abs(finalCashbox.profit_total - finalExpectedProfit) < 0.01;

    console.log(`📊 بيانات الخزينة النهائية:`);
    console.log(`   - الرصيد الافتتاحي: ${finalCashbox.initial_balance}`);
    console.log(`   - الرصيد الحالي: ${finalCashbox.current_balance}`);
    console.log(`   - إجمالي المبيعات: ${finalCashbox.sales_total}`);
    console.log(`   - إجمالي المشتريات: ${finalCashbox.purchases_total}`);
    console.log(`   - إجمالي مصاريف النقل: ${finalCashbox.transport_total}`);
    console.log(`   - إجمالي الأرباح: ${finalCashbox.profit_total}`);

    console.log(`🧮 التحقق النهائي:`);
    console.log(`   - المعادلة: ${finalCashbox.sales_total} - ${finalCashbox.purchases_total} - ${finalCashbox.transport_total} = ${finalExpectedProfit}`);
    console.log(`   - الأرباح الفعلية: ${finalCashbox.profit_total}`);
    console.log(`   - النتيجة: ${finalProfitMatches ? '✅ صحيحة' : '❌ خاطئة'}`);

    // حساب التغيير الإجمالي
    const totalPurchaseAmount = purchases.reduce((sum, p) => sum + p.amount, 0);
    const totalTransportAmount = purchases.reduce((sum, p) => sum + p.transport, 0);
    const expectedProfitChange = -(totalPurchaseAmount + totalTransportAmount);
    const actualProfitChange = finalCashbox.profit_total - initialCashbox.profit_total;

    console.log(`📈 تحليل التغيير:`);
    console.log(`   - إجمالي مبلغ المشتريات: ${totalPurchaseAmount}`);
    console.log(`   - إجمالي مصاريف النقل: ${totalTransportAmount}`);
    console.log(`   - التغيير المتوقع في الأرباح: ${expectedProfitChange}`);
    console.log(`   - التغيير الفعلي في الأرباح: ${actualProfitChange}`);
    console.log(`   - التطابق: ${Math.abs(actualProfitChange - expectedProfitChange) < 0.01 ? '✅' : '❌'}`);

    if (finalProfitMatches && Math.abs(actualProfitChange - expectedProfitChange) < 0.01) {
      console.log('\n🎉 نجح الاختبار! عمليات الشراء المتتالية تعمل بشكل صحيح.');
      console.log('✅ لا يوجد خصم مضاعف من الأرباح.');
      console.log('✅ المعادلة تطبق بشكل صحيح في جميع العمليات.');
    } else {
      console.log('\n❌ فشل الاختبار! هناك مشكلة في حساب الأرباح.');
      console.log('⚠️  يحتاج النظام لمراجعة إضافية.');
    }

    return {
      success: finalProfitMatches && Math.abs(actualProfitChange - expectedProfitChange) < 0.01,
      finalCashbox,
      expectedProfit: finalExpectedProfit,
      actualProfit: finalCashbox.profit_total,
      profitChange: {
        expected: expectedProfitChange,
        actual: actualProfitChange
      }
    };

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error);
    return { success: false, error: error.message };
  } finally {
    if (db) {
      db.close();
      console.log('🔒 تم إغلاق الاتصال بقاعدة البيانات');
    }
  }
}

// تشغيل الاختبار
testSequentialPurchases()
  .then(result => {
    console.log('\n📋 نتيجة الاختبار:', result);
    console.log('\n🏁 انتهى الاختبار');
  })
  .catch(error => {
    console.error('❌ خطأ في تشغيل الاختبار:', error);
  });
