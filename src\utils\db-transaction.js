/**
 * وظيفة مساعدة لإدارة معاملات قاعدة البيانات
 * تضمن هذه الوظيفة تنفيذ العمليات داخل معاملة قاعدة بيانات واحدة
 * وتقوم بالتراجع عن التغييرات في حالة حدوث خطأ
 *
 * @param {Object} db - كائن قاعدة البيانات
 * @param {Function} callback - دالة تحتوي على العمليات التي يجب تنفيذها داخل المعاملة
 * @returns {Promise} - وعد بنتيجة العملية
 */
async function withTransaction(db, callback) {
  // التحقق من وجود كائن قاعدة البيانات
  if (!db) {
    throw new Error('كائن قاعدة البيانات مطلوب');
  }

  // التحقق من أن الدالة موجودة
  if (typeof callback !== 'function') {
    throw new Error('الدالة مطلوبة');
  }

  try {
    // بدء المعاملة
    db.exec('BEGIN TRANSACTION');

    try {
      // تنفيذ العمليات
      const result = await callback();

      // إنهاء المعاملة بنجاح
      db.exec('COMMIT');

      return result;
    } catch (innerError) {
      // التراجع عن التغييرات في حالة حدوث خطأ
      db.exec('ROLLBACK');
      console.error('خطأ في معاملة قاعدة البيانات (تم التراجع):', innerError);
      throw innerError;
    }
  } catch (error) {
    // التعامل مع أخطاء بدء المعاملة أو التراجع عنها
    console.error('خطأ في إدارة معاملة قاعدة البيانات:', error);
    throw error;
  }
}

module.exports = withTransaction;
