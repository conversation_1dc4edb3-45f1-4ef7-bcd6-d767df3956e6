/**
 * اختبار التحديث الفوري للأرباح بعد عمليات الشراء
 * 
 * هذا الملف يحتوي على دوال اختبار للتحقق من أن الأرباح تتحدث فورياً
 * بعد عمليات الشراء والاسترجاع، وليس فقط بعد عمليات البيع
 */

// دالة لمراقبة أحداث تحديث الأرباح بعد المشتريات
function monitorPurchaseProfitsEvents() {
  console.log('🎯 بدء مراقبة أحداث تحديث الأرباح بعد المشتريات...');
  
  const eventsToMonitor = [
    'profits-updated',
    'auto-profits-updated', 
    'dashboard-profits-updated',
    'direct-update',
    'cashbox-updated-ui'
  ];
  
  eventsToMonitor.forEach(eventType => {
    window.addEventListener(eventType, (event) => {
      if (event.detail && (event.detail.transaction_type === 'purchase' || event.detail.transaction_type === 'return')) {
        console.log(`📊 [${eventType}] حدث مستلم لمعاملة ${event.detail.transaction_type}:`, {
          type: eventType,
          transaction_type: event.detail.transaction_type,
          amount: event.detail.amount,
          transport_cost: event.detail.transport_cost,
          profit: event.detail.profit,
          total_profit: event.detail.total_profit,
          timestamp: event.detail.timestamp
        });
        
        // تسجيل خاص لأحداث الأرباح
        if (eventType.includes('profit')) {
          console.log(`💰 تحديث أرباح بعد ${event.detail.transaction_type} - النوع: ${eventType}`, event.detail);
        }
      }
    });
  });
  
  console.log('✅ تم تفعيل مراقبة أحداث تحديث الأرباح بعد المشتريات');
}

// دالة لاختبار تحديث الأرباح بعد الشراء
async function testPurchaseProfitsUpdate() {
  console.log('🧪 اختبار تحديث الأرباح بعد عملية شراء...');
  
  try {
    // الحصول على حالة الأرباح قبل العملية
    if (window.api && window.api.invoke) {
      const cashboxBefore = await window.api.invoke('get-cashbox');
      console.log('📊 حالة الأرباح قبل الشراء:', {
        profit_total: cashboxBefore.profit_total,
        current_balance: cashboxBefore.current_balance,
        purchases_total: cashboxBefore.purchases_total
      });
      
      // محاكاة عملية شراء
      const testPurchase = new CustomEvent('profits-updated', {
        detail: {
          transaction_type: 'purchase',
          amount: 1000,
          transport_cost: 100,
          profit: 0,
          total_profit: cashboxBefore.profit_total,
          auto_update: true,
          timestamp: new Date().toISOString(),
          test: true
        }
      });
      
      console.log('🛒 محاكاة حدث شراء...');
      window.dispatchEvent(testPurchase);
      
      // محاكاة حدث التحديث التلقائي
      setTimeout(() => {
        const autoUpdateEvent = new CustomEvent('auto-profits-updated', {
          detail: {
            transaction_type: 'purchase',
            amount: 1000,
            transport_cost: 100,
            profit: 0,
            total_profit: cashboxBefore.profit_total,
            auto_update: true,
            timestamp: new Date().toISOString(),
            test: true
          }
        });
        
        window.dispatchEvent(autoUpdateEvent);
        console.log('✅ تم إرسال حدث التحديث التلقائي للشراء');
      }, 500);
      
      // محاكاة تحديث Dashboard
      setTimeout(() => {
        const dashboardEvent = new CustomEvent('dashboard-profits-updated', {
          detail: {
            profits: {
              quarterly: cashboxBefore.profit_total * 0.25,
              halfYearly: cashboxBefore.profit_total * 0.5,
              threeQuarters: cashboxBefore.profit_total * 0.75,
              yearly: cashboxBefore.profit_total
            },
            timestamp: new Date().toISOString(),
            test: true
          }
        });
        
        window.dispatchEvent(dashboardEvent);
        console.log('✅ تم إرسال حدث تحديث أرباح Dashboard');
      }, 1000);
      
    } else {
      console.warn('⚠️ window.api غير متوفر، لا يمكن إجراء الاختبار الكامل');
    }
    
  } catch (error) {
    console.error('❌ خطأ في اختبار تحديث الأرباح بعد الشراء:', error);
  }
}

// دالة لاختبار تحديث الأرباح بعد الاسترجاع
async function testReturnProfitsUpdate() {
  console.log('🧪 اختبار تحديث الأرباح بعد عملية استرجاع...');
  
  try {
    // الحصول على حالة الأرباح قبل العملية
    if (window.api && window.api.invoke) {
      const cashboxBefore = await window.api.invoke('get-cashbox');
      console.log('📊 حالة الأرباح قبل الاسترجاع:', {
        profit_total: cashboxBefore.profit_total,
        current_balance: cashboxBefore.current_balance,
        returns_total: cashboxBefore.returns_total || 0
      });
      
      const returnProfit = 200; // ربح مقدر للاسترجاع
      const newProfitTotal = Math.max(0, cashboxBefore.profit_total - returnProfit);
      
      // محاكاة عملية استرجاع
      const testReturn = new CustomEvent('profits-updated', {
        detail: {
          transaction_type: 'return',
          amount: 800,
          profit: -returnProfit,
          total_profit: newProfitTotal,
          auto_update: true,
          timestamp: new Date().toISOString(),
          test: true
        }
      });
      
      console.log('↩️ محاكاة حدث استرجاع...');
      window.dispatchEvent(testReturn);
      
      // محاكاة حدث التحديث التلقائي
      setTimeout(() => {
        const autoUpdateEvent = new CustomEvent('auto-profits-updated', {
          detail: {
            transaction_type: 'return',
            amount: 800,
            profit: -returnProfit,
            total_profit: newProfitTotal,
            auto_update: true,
            timestamp: new Date().toISOString(),
            test: true
          }
        });
        
        window.dispatchEvent(autoUpdateEvent);
        console.log('✅ تم إرسال حدث التحديث التلقائي للاسترجاع');
      }, 500);
      
    } else {
      console.warn('⚠️ window.api غير متوفر، لا يمكن إجراء الاختبار الكامل');
    }
    
  } catch (error) {
    console.error('❌ خطأ في اختبار تحديث الأرباح بعد الاسترجاع:', error);
  }
}

// دالة لفحص بطاقات الأرباح في Dashboard
function checkDashboardProfitsCards() {
  console.log('🔍 فحص حالة بطاقات الأرباح في Dashboard...');
  
  try {
    // البحث عن بطاقة الربح السنوي
    const profitCards = document.querySelectorAll('.stat-card');
    let yearlyProfitCard = null;
    
    profitCards.forEach(card => {
      const title = card.querySelector('.stat-card-title');
      if (title && title.textContent.includes('الربح السنوي')) {
        yearlyProfitCard = card;
      }
    });
    
    if (yearlyProfitCard) {
      const value = yearlyProfitCard.querySelector('.stat-card-value');
      const subtitle = yearlyProfitCard.querySelector('.stat-card-subtitle');
      
      console.log('📊 بطاقة الربح السنوي:', {
        value: value ? value.textContent : 'غير موجود',
        subtitle: subtitle ? subtitle.textContent : 'غير موجود',
        hasAutoUpdateText: subtitle ? subtitle.textContent.includes('تلقائياً') : false
      });
      
      return true;
    } else {
      console.warn('⚠️ لم يتم العثور على بطاقة الربح السنوي');
      return false;
    }
    
  } catch (error) {
    console.error('❌ خطأ في فحص بطاقات الأرباح:', error);
    return false;
  }
}

// دالة لاختبار شامل
async function runComprehensivePurchaseProfitsTest() {
  console.log('🚀 بدء الاختبار الشامل لتحديث الأرباح بعد المشتريات...');
  
  // 1. بدء المراقبة
  monitorPurchaseProfitsEvents();
  
  // 2. فحص الحالة الحالية
  await new Promise(resolve => setTimeout(resolve, 1000));
  checkDashboardProfitsCards();
  
  // 3. اختبار تحديث الأرباح بعد الشراء
  await new Promise(resolve => setTimeout(resolve, 2000));
  await testPurchaseProfitsUpdate();
  
  // 4. اختبار تحديث الأرباح بعد الاسترجاع
  await new Promise(resolve => setTimeout(resolve, 4000));
  await testReturnProfitsUpdate();
  
  // 5. فحص النتائج النهائية
  await new Promise(resolve => setTimeout(resolve, 6000));
  const finalCheck = checkDashboardProfitsCards();
  
  console.log('🏁 انتهى الاختبار الشامل. النتيجة:', finalCheck ? '✅ نجح' : '❌ فشل');
  
  console.log(`
📋 ملخص الإصلاح:
✅ تم إضافة إرسال إشعارات تحديث الأرباح بعد عمليات الشراء
✅ تم إضافة إرسال إشعارات تحديث الأرباح بعد عمليات الاسترجاع
✅ تم إضافة مستمعين إضافيين في Dashboard لمعاملات الشراء
✅ الآن يجب أن تتحدث الأرباح فورياً بعد جميع أنواع المعاملات
  `);
}

// دالة لعرض تعليمات الاستخدام
function showPurchaseProfitsTestInstructions() {
  console.log(`
🎯 تعليمات اختبار تحديث الأرباح بعد المشتريات:

📋 الدوال المتاحة:
1. monitorPurchaseProfitsEvents() - مراقبة أحداث الأرباح
2. testPurchaseProfitsUpdate() - اختبار تحديث الأرباح بعد الشراء
3. testReturnProfitsUpdate() - اختبار تحديث الأرباح بعد الاسترجاع
4. checkDashboardProfitsCards() - فحص بطاقات الأرباح
5. runComprehensivePurchaseProfitsTest() - اختبار شامل

🚀 للبدء السريع:
runComprehensivePurchaseProfitsTest()

🔧 المشكلة المُصلحة:
- كانت الأرباح لا تتحدث فورياً بعد عمليات الشراء والاسترجاع
- الآن تم إضافة إرسال إشعارات تحديث الأرباح لجميع أنواع المعاملات
- Dashboard يستمع الآن لجميع أحداث تحديث الأرباح
  `);
}

// تصدير الدوال للاستخدام العام
if (typeof window !== 'undefined') {
  window.purchaseProfitsTest = {
    monitor: monitorPurchaseProfitsEvents,
    testPurchase: testPurchaseProfitsUpdate,
    testReturn: testReturnProfitsUpdate,
    checkCards: checkDashboardProfitsCards,
    runAll: runComprehensivePurchaseProfitsTest,
    help: showPurchaseProfitsTestInstructions
  };
  
  console.log('🔧 تم تحميل أدوات اختبار تحديث الأرباح بعد المشتريات');
  console.log('📖 استخدم window.purchaseProfitsTest.help() لعرض التعليمات');
}

// عرض التعليمات تلقائياً
showPurchaseProfitsTestInstructions();
