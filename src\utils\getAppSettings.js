/**
 * وظيفة للحصول على إعدادات التطبيق بطريقة آمنة
 * تستخدم هذه الوظيفة للحصول على إعدادات التطبيق من السياق بطريقة آمنة
 * بدون استخدام React Hooks خارج مكونات React
 */

// إعدادات افتراضية للتطبيق
const defaultSettings = {
  systemName: 'نظام إدارة المخازن',
  address: 'العنوان الافتراضي',
  phone: '',
  logoUrl: '',
  currency: 'د ل'
};

// متغير عام لتخزين إعدادات التطبيق
let cachedSettings = null;

/**
 * تعيين إعدادات التطبيق
 * @param {Object} settings - إعدادات التطبيق
 */
export const setAppSettings = (settings) => {
  cachedSettings = { ...defaultSettings, ...settings };
  // تخزين الإعدادات في نافذة المتصفح للوصول إليها من أي مكان
  if (typeof window !== 'undefined') {
    window.appSettings = cachedSettings;
  }
};

/**
 * الحصول على إعدادات التطبيق
 * @returns {Object} إعدادات التطبيق
 */
export const getAppSettings = () => {
  // إذا كانت الإعدادات مخزنة في الذاكرة المؤقتة، نستخدمها
  if (cachedSettings) {
    return cachedSettings;
  }
  
  // إذا كانت الإعدادات مخزنة في نافذة المتصفح، نستخدمها
  if (typeof window !== 'undefined' && window.appSettings) {
    cachedSettings = window.appSettings;
    return cachedSettings;
  }
  
  // إذا لم تكن الإعدادات متاحة، نستخدم الإعدادات الافتراضية
  return defaultSettings;
};

export default getAppSettings;
