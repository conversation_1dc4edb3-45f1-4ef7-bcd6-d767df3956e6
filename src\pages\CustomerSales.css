/* أنماط صفحة مبيعات العملاء */
.customer-sales-page {
  padding: 20px;
}

.customer-sales-header {
  margin-bottom: 30px;
}

.customer-sales-header h1 {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 10px;
}

.customer-sales-header p {
  font-size: 1rem;
  color: var(--text-light);
}

/* أنماط البحث */
.search-container {
  position: relative;
  margin-bottom: 20px;
  max-width: 500px;
}

.search-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-light);
}

.search-input {
  width: 100%;
  padding: 10px 15px 10px 40px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  font-size: 0.95rem;
}

.search-input:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

/* أنماط قائمة العملاء */
.customers-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.customer-card {
  background-color: var(--bg-light);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
  position: relative;
}

.customer-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.customer-card-header {
  padding: 15px;
  background-color: var(--primary-color);
  color: white;
}

.customer-card-body {
  padding: 15px;
}

.customer-name {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 5px;
  color: white;
}

.customer-type {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  gap: 5px;
}

.customer-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.customer-info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-dark);
  font-size: 0.9rem;
}

.customer-info-item svg {
  color: var(--primary-color);
  font-size: 1rem;
}

.customer-balance {
  margin-top: 15px;
  padding: 10px;
  border-radius: var(--border-radius-sm);
  background-color: rgba(var(--primary-rgb), 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.balance-label {
  font-size: 0.9rem;
  color: var(--text-dark);
  font-weight: 500;
}

.balance-value {
  font-size: 1rem;
  font-weight: 700;
}

.balance-value.positive {
  color: var(--success-color);
}

.balance-value.negative {
  color: var(--danger-color);
}

.customer-actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

/* أنماط قائمة المخزون */
.inventory-table-container {
  margin-bottom: 30px;
}

.item-name {
  font-weight: 600;
  color: var(--text-dark);
}

.item-unit {
  color: var(--text-light);
  font-size: 0.9rem;
}

.item-quantity {
  font-weight: 500;
}

.item-quantity.out-of-stock {
  color: var(--danger-color);
}

.item-quantity.low-stock {
  color: var(--warning-color);
}

.item-price {
  font-weight: 500;
}

.item-actions {
  display: flex;
  gap: 5px;
}

/* أنماط نافذة إضافة عملية بيع */
.sale-form {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group.full-width {
  grid-column: span 2;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-dark);
}

.form-control {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: 0.95rem;
}

.form-control:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.form-text {
  font-size: 0.8rem;
  color: var(--text-light);
  margin-top: 5px;
}

.form-actions {
  grid-column: span 2;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 10px;
}

.total-price-display {
  padding: 10px;
  background-color: rgba(var(--primary-rgb), 0.05);
  border: 1px solid rgba(var(--primary-rgb), 0.1);
  border-radius: var(--border-radius-sm);
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--primary-color);
  text-align: center;
}

.required {
  color: var(--danger-color);
  margin-right: 3px;
}

.item-info {
  background-color: rgba(var(--primary-rgb), 0.05);
  border-radius: var(--border-radius-sm);
  padding: 15px;
  margin-bottom: 20px;
  grid-column: span 2;
}

.item-info-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.item-info-name {
  font-weight: 600;
  color: var(--text-dark);
}

.item-info-unit {
  color: var(--text-light);
}

.item-info-quantity {
  display: flex;
  align-items: center;
  gap: 5px;
}

.item-info-quantity.low-stock {
  color: var(--warning-color);
}

.profit-summary {
  background-color: rgba(var(--success-rgb), 0.05);
  border-radius: var(--border-radius-sm);
  padding: 15px;
  margin-top: 20px;
  grid-column: span 2;
}

.profit-summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.profit-summary-title {
  font-weight: 600;
  color: var(--text-dark);
}

.profit-summary-value {
  font-weight: 700;
  color: var(--success-color);
}

/* تنسيقات للشاشات الصغيرة */
@media (max-width: 768px) {
  .customers-list {
    grid-template-columns: 1fr;
  }
  
  .sale-form {
    grid-template-columns: 1fr;
  }
  
  .form-group.full-width {
    grid-column: span 1;
  }
  
  .form-actions {
    grid-column: span 1;
  }
  
  .item-info {
    grid-column: span 1;
  }
  
  .profit-summary {
    grid-column: span 1;
  }
}
