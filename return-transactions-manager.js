/**
 * مدير عمليات الإرجاع
 * يتعامل مع إنشاء وتحديث وحذف عمليات الإرجاع
 */

// استيراد المكتبات اللازمة
const { logSystem, logError } = require('./error-handler');
const eventSystem = require('./event-system');
console.log('[RETURN-MANAGER] تم استيراد نظام الأحداث بنجاح');
const inventoryManager = require('./inventory-manager');
const cashboxManager = require('./cashbox-manager');

// اتصال قاعدة البيانات
let db = null;

// التخزين المؤقت لعمليات الإرجاع
let returnTransactionsCache = null;
let returnTransactionsCacheTimestamp = 0;
const CACHE_EXPIRY = 60000; // مدة صلاحية التخزين المؤقت بالمللي ثانية (1 دقيقة)

/**
 * تهيئة مدير عمليات الإرجاع
 * @param {Object} database - اتصال قاعدة البيانات
 * @returns {boolean} - نجاح العملية
 */
function initialize(database) {
  try {
    console.log(`[RETURN-MANAGER] بدء تهيئة مدير عمليات الإرجاع...`);

    if (!database) {
      console.error(`[RETURN-MANAGER] خطأ: لم يتم توفير اتصال قاعدة البيانات`);
      throw new Error('لم يتم توفير اتصال قاعدة البيانات');
    }

    // التحقق من صحة اتصال قاعدة البيانات
    try {
      // محاولة تنفيذ استعلام بسيط للتحقق من صحة الاتصال
      const testQuery = database.prepare('SELECT 1');
      const testResult = testQuery.get();

      if (!testResult) {
        console.error(`[RETURN-MANAGER] خطأ: فشل في التحقق من صحة اتصال قاعدة البيانات`);
        throw new Error('فشل في التحقق من صحة اتصال قاعدة البيانات');
      }

      console.log(`[RETURN-MANAGER] تم التحقق من صحة اتصال قاعدة البيانات بنجاح`);
    } catch (dbTestError) {
      console.error(`[RETURN-MANAGER] خطأ في التحقق من صحة اتصال قاعدة البيانات:`, dbTestError);
      logError(dbTestError, 'initialize - dbTest');
      throw new Error(`فشل في التحقق من صحة اتصال قاعدة البيانات: ${dbTestError.message}`);
    }

    // تعيين اتصال قاعدة البيانات
    db = database;
    console.log(`[RETURN-MANAGER] تم تعيين اتصال قاعدة البيانات بنجاح`);
    logSystem('تم تهيئة مدير عمليات الإرجاع بنجاح', 'info');

    // إنشاء جدول عمليات الإرجاع إذا لم يكن موجودًا
    try {
      createReturnTransactionsTable();
      console.log(`[RETURN-MANAGER] تم التحقق من جدول عمليات الإرجاع بنجاح`);
    } catch (tableError) {
      console.error(`[RETURN-MANAGER] خطأ في إنشاء جدول عمليات الإرجاع:`, tableError);
      logError(tableError, 'initialize - createTable');
      // لا نريد إيقاف عملية التهيئة إذا فشل إنشاء الجدول، ولكن نسجل الخطأ
    }

    // التحقق من وجود مدير المخزون
    if (!inventoryManager) {
      console.warn(`[RETURN-MANAGER] تحذير: مدير المخزون غير متوفر`);
      logSystem('تحذير: مدير المخزون غير متوفر', 'warning');
    } else {
      console.log(`[RETURN-MANAGER] تم التحقق من وجود مدير المخزون بنجاح`);
    }

    // التحقق من وجود مدير الخزينة
    if (!cashboxManager) {
      console.warn(`[RETURN-MANAGER] تحذير: مدير الخزينة غير متوفر`);
      logSystem('تحذير: مدير الخزينة غير متوفر', 'warning');
    } else {
      console.log(`[RETURN-MANAGER] تم التحقق من وجود مدير الخزينة بنجاح`);
    }

    // التحقق من وجود نظام الأحداث
    if (!eventSystem) {
      console.warn(`[RETURN-MANAGER] تحذير: نظام الأحداث غير متوفر`);
      logSystem('تحذير: نظام الأحداث غير متوفر', 'warning');
    } else {
      console.log(`[RETURN-MANAGER] تم التحقق من وجود نظام الأحداث بنجاح`);
    }

    console.log(`[RETURN-MANAGER] تم تهيئة مدير عمليات الإرجاع بنجاح`);
    return true;
  } catch (error) {
    console.error(`[RETURN-MANAGER] خطأ في تهيئة مدير عمليات الإرجاع:`, error);
    logError(error, 'initialize');
    return false;
  }
}

/**
 * إنشاء جدول عمليات الإرجاع إذا لم يكن موجودًا
 */
function createReturnTransactionsTable() {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    console.log(`[RETURN-MANAGER] التحقق من وجود جدول عمليات الإرجاع...`);

    // التحقق من وجود الجدول
    const tableExistsStmt = db.prepare(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='return_transactions'
    `);
    const tableExists = tableExistsStmt.get();

    if (!tableExists) {
      console.log(`[RETURN-MANAGER] جدول عمليات الإرجاع غير موجود، جاري إنشاؤه...`);
      logSystem('جدول عمليات الإرجاع غير موجود، جاري إنشاؤه...', 'info');

      try {
        // إنشاء الجدول
        db.exec(`
          CREATE TABLE IF NOT EXISTS return_transactions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            return_id TEXT NOT NULL UNIQUE,
            original_transaction_id TEXT,
            item_id INTEGER NOT NULL,
            quantity INTEGER NOT NULL,
            price REAL NOT NULL DEFAULT 0,
            total_price REAL NOT NULL DEFAULT 0,
            customer_id INTEGER,
            invoice_number TEXT,
            notes TEXT,
            user_id INTEGER,
            return_date TEXT NOT NULL,
            inventory_updated INTEGER DEFAULT 0,
            FOREIGN KEY (item_id) REFERENCES items(id) ON DELETE CASCADE,
            FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
          );
        `);

        // إنشاء الفهارس بشكل منفصل لتجنب الأخطاء
        try {
          db.exec(`CREATE INDEX IF NOT EXISTS idx_return_transactions_item_id ON return_transactions(item_id);`);
        } catch (indexError1) {
          console.error(`[RETURN-MANAGER] خطأ في إنشاء فهرس item_id:`, indexError1);
        }

        try {
          db.exec(`CREATE INDEX IF NOT EXISTS idx_return_transactions_customer_id ON return_transactions(customer_id);`);
        } catch (indexError2) {
          console.error(`[RETURN-MANAGER] خطأ في إنشاء فهرس customer_id:`, indexError2);
        }

        try {
          db.exec(`CREATE INDEX IF NOT EXISTS idx_return_transactions_return_date ON return_transactions(return_date);`);
        } catch (indexError3) {
          console.error(`[RETURN-MANAGER] خطأ في إنشاء فهرس return_date:`, indexError3);
        }

        try {
          db.exec(`CREATE INDEX IF NOT EXISTS idx_return_transactions_original_transaction ON return_transactions(original_transaction_id);`);
        } catch (indexError4) {
          console.error(`[RETURN-MANAGER] خطأ في إنشاء فهرس original_transaction_id:`, indexError4);
        }

        // التحقق من إنشاء الجدول بنجاح
        const verifyTableStmt = db.prepare(`
          SELECT name FROM sqlite_master
          WHERE type='table' AND name='return_transactions'
        `);
        const verifyTableExists = verifyTableStmt.get();

        if (verifyTableExists) {
          console.log(`[RETURN-MANAGER] تم إنشاء جدول عمليات الإرجاع بنجاح`);
          logSystem('تم إنشاء جدول عمليات الإرجاع بنجاح', 'info');
        } else {
          console.error(`[RETURN-MANAGER] فشل في إنشاء جدول عمليات الإرجاع`);
          logSystem('فشل في إنشاء جدول عمليات الإرجاع', 'error');
        }
      } catch (createError) {
        console.error(`[RETURN-MANAGER] خطأ في إنشاء جدول عمليات الإرجاع:`, createError);
        logError(createError, 'createReturnTransactionsTable - create');
        throw createError;
      }
    } else {
      console.log(`[RETURN-MANAGER] جدول عمليات الإرجاع موجود بالفعل`);
    }
  } catch (error) {
    console.error(`[RETURN-MANAGER] خطأ في التحقق من وجود جدول عمليات الإرجاع:`, error);
    logError(error, 'createReturnTransactionsTable');
    throw error;
  }
}

/**
 * إنشاء معرف فريد لعملية الإرجاع
 * @returns {string} - معرف فريد
 */
function generateReturnId() {
  const now = new Date();
  const year = now.getFullYear().toString().slice(-2);
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');
  const hours = now.getHours().toString().padStart(2, '0');
  const minutes = now.getMinutes().toString().padStart(2, '0');
  const seconds = now.getSeconds().toString().padStart(2, '0');
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');

  return `RET-${year}${month}${day}-${hours}${minutes}${seconds}-${random}`;
}

/**
 * التحقق من حالة المخزون بعد عملية الإرجاع
 * @param {number} itemId - معرف الصنف
 * @returns {Promise<Object>} - نتيجة العملية
 */
async function verifyInventoryAfterReturn(itemId) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    console.log(`[RETURN-MANAGER] التحقق من حالة المخزون بعد عملية الإرجاع للصنف ${itemId}`);

    // استيراد مدير المخزون
    const inventoryManager = require('./inventory-manager');

    // التحقق من وجود دالة verifyInventoryAfterReturn
    if (typeof inventoryManager.verifyInventoryAfterReturn !== 'function') {
      console.error(`[RETURN-MANAGER] دالة verifyInventoryAfterReturn غير موجودة في مدير المخزون`);
      return {
        success: false,
        error: 'دالة verifyInventoryAfterReturn غير موجودة في مدير المخزون'
      };
    }

    // استدعاء دالة التحقق من حالة المخزون
    const result = inventoryManager.verifyInventoryAfterReturn(itemId);

    console.log(`[RETURN-MANAGER] نتيجة التحقق من حالة المخزون:`, result);

    return result;
  } catch (error) {
    console.error(`[RETURN-MANAGER] خطأ في التحقق من حالة المخزون:`, error);
    logError(error, 'verifyInventoryAfterReturn');
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * إنشاء عملية إرجاع جديدة
 * @param {Object} returnData - بيانات عملية الإرجاع
 * @returns {Promise<Object>} - نتيجة العملية
 */
async function createReturnTransaction(returnData) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    console.log(`[RETURN-MANAGER] بدء إنشاء عملية إرجاع جديدة:`, returnData);
    logSystem(`[RETURN-MANAGER] بدء إنشاء عملية إرجاع جديدة: ${JSON.stringify(returnData)}`, 'info');

    // التحقق من صحة البيانات
    if (!returnData.item_id) throw new Error('معرف الصنف مطلوب');
    if (!returnData.quantity || returnData.quantity <= 0) throw new Error('الكمية يجب أن تكون أكبر من صفر');
    if (!returnData.customer_id) throw new Error('معرف العميل مطلوب');

    // تحويل المعرفات إلى أرقام للتأكد من صحتها
    returnData.item_id = parseInt(returnData.item_id);
    returnData.quantity = parseInt(returnData.quantity);
    returnData.customer_id = parseInt(returnData.customer_id);

    if (isNaN(returnData.item_id) || returnData.item_id <= 0) {
      throw new Error(`معرف الصنف غير صالح: ${returnData.item_id}`);
    }

    if (isNaN(returnData.quantity) || returnData.quantity <= 0) {
      throw new Error(`الكمية غير صالحة: ${returnData.quantity}`);
    }

    if (isNaN(returnData.customer_id) || returnData.customer_id <= 0) {
      throw new Error(`معرف العميل غير صالح: ${returnData.customer_id}`);
    }

    // التحقق من أن الكمية المسترجعة لا تتجاوز الكمية المباعة للعميل
    const availableForReturn = await getAvailableQuantityForReturn(
      returnData.item_id,
      returnData.customer_id,
      returnData.original_transaction_id
    );

    console.log(`[RETURN-MANAGER] الكمية المتاحة للإرجاع: ${availableForReturn}`);

    if (returnData.quantity > availableForReturn) {
      throw new Error(`الكمية المطلوب استرجاعها (${returnData.quantity}) تتجاوز الكمية المتاحة للاسترجاع (${availableForReturn})`);
    }

    // إنشاء معرف فريد لعملية الإرجاع
    const returnId = generateReturnId();
    console.log(`[RETURN-MANAGER] تم إنشاء معرف فريد لعملية الإرجاع: ${returnId}`);

    // تحديث المخزون
    let inventoryUpdated = 0;
    try {
      console.log(`[RETURN-MANAGER] جاري تحديث المخزون للصنف ${returnData.item_id} بكمية ${returnData.quantity}`);

      // تحويل المعرفات إلى أرقام للتأكد من صحتها
      const itemId = parseInt(returnData.item_id);
      const quantity = parseInt(returnData.quantity);

      if (isNaN(itemId) || itemId <= 0) {
        throw new Error(`معرف الصنف غير صالح: ${returnData.item_id}`);
      }

      if (isNaN(quantity) || quantity <= 0) {
        throw new Error(`الكمية غير صالحة: ${returnData.quantity}`);
      }

      // محاولة تحديث المخزون باستخدام الطريقة الأولى
      try {
        console.log(`[RETURN-MANAGER] محاولة تحديث المخزون باستخدام updateInventoryAfterReturn`);
        const inventoryResult = await inventoryManager.updateInventoryAfterReturn(itemId, quantity);

        console.log(`[RETURN-MANAGER] نتيجة تحديث المخزون:`, inventoryResult);

        if (inventoryResult && inventoryResult.success) {
          inventoryUpdated = 1;
          console.log(`[RETURN-MANAGER] تم تحديث المخزون بنجاح. الكمية الجديدة: ${inventoryResult.newQuantity}`);
        } else {
          const errorMsg = inventoryResult ? inventoryResult.error : 'سبب غير معروف';
          console.warn(`[RETURN-MANAGER] فشل في تحديث المخزون: ${errorMsg}`);
          throw new Error(`فشل في تحديث المخزون: ${errorMsg}`);
        }
      } catch (primaryMethodError) {
        console.warn(`[RETURN-MANAGER] فشل في تحديث المخزون باستخدام الطريقة الأولى:`, primaryMethodError);
        logSystem(`فشل في تحديث المخزون باستخدام الطريقة الأولى: ${primaryMethodError.message}`, 'warning');

        // محاولة تحديث المخزون باستخدام الطريقة البديلة
        try {
          console.log(`[RETURN-MANAGER] محاولة تحديث المخزون باستخدام الطريقة البديلة (IPC)`);

          // استخدام IPC لتحديث المخزون
          const { ipcMain } = require('electron');

          // إنشاء وعد لانتظار نتيجة استدعاء IPC
          const updateResult = await new Promise((resolve, reject) => {
            try {
              // استدعاء معالج IPC المباشر
              const result = ipcMain.emit('update-inventory-after-return-direct', null, { itemId, quantity });

              if (result) {
                console.log(`[RETURN-MANAGER] تم استدعاء معالج IPC لتحديث المخزون بنجاح`);
                resolve({ success: true });
              } else {
                console.warn(`[RETURN-MANAGER] فشل في استدعاء معالج IPC لتحديث المخزون`);
                reject(new Error('فشل في استدعاء معالج IPC لتحديث المخزون'));
              }
            } catch (ipcError) {
              console.error(`[RETURN-MANAGER] خطأ في استدعاء معالج IPC:`, ipcError);
              reject(ipcError);
            }
          });

          if (updateResult && updateResult.success) {
            inventoryUpdated = 1;
            console.log(`[RETURN-MANAGER] تم تحديث المخزون بنجاح باستخدام الطريقة البديلة`);
          } else {
            throw new Error('فشل في تحديث المخزون باستخدام الطريقة البديلة');
          }
        } catch (alternativeMethodError) {
          console.error(`[RETURN-MANAGER] فشل في تحديث المخزون باستخدام الطريقة البديلة:`, alternativeMethodError);
          logError(alternativeMethodError, 'createReturnTransaction - updateInventory - alternativeMethod');

          // محاولة ثالثة: تحديث المخزون مباشرة في قاعدة البيانات
          try {
            console.log(`[RETURN-MANAGER] محاولة تحديث المخزون مباشرة في قاعدة البيانات`);

            // الحصول على معلومات المخزون الحالية
            const getInventoryStmt = db.prepare(`
              SELECT i.id, i.name, inv.current_quantity, inv.item_id
              FROM items i
              LEFT JOIN inventory inv ON i.id = inv.item_id
              WHERE i.id = ?
            `);

            const inventory = getInventoryStmt.get(itemId);

            if (!inventory) {
              throw new Error(`الصنف غير موجود: ${itemId}`);
            }

            // الكمية الحالية في المخزون
            const currentQuantity = inventory.current_quantity || 0;

            // حساب الكمية الجديدة (إضافة الكمية المسترجعة إلى المخزون)
            const newQuantity = currentQuantity + quantity;

            console.log(`[RETURN-MANAGER] تحديث المخزون مباشرة للصنف ${itemId} (${inventory.name}):`);
            console.log(`[RETURN-MANAGER] - الكمية الحالية: ${currentQuantity}`);
            console.log(`[RETURN-MANAGER] - الكمية المسترجعة: ${quantity}`);
            console.log(`[RETURN-MANAGER] - الكمية الجديدة: ${newQuantity}`);

            // التحقق من وجود سجل في المخزون
            if (inventory.item_id) {
              // تحديث سجل المخزون الموجود
              const updateStmt = db.prepare(`
                UPDATE inventory
                SET current_quantity = ?, last_updated = ?
                WHERE item_id = ?
              `);

              updateStmt.run(
                newQuantity,
                new Date().toISOString(),
                itemId
              );
            } else {
              // إنشاء سجل مخزون جديد
              const insertStmt = db.prepare(`
                INSERT INTO inventory (item_id, current_quantity, last_updated)
                VALUES (?, ?, ?)
              `);

              insertStmt.run(
                itemId,
                newQuantity,
                new Date().toISOString()
              );
            }

            inventoryUpdated = 1;
            console.log(`[RETURN-MANAGER] تم تحديث المخزون بنجاح مباشرة في قاعدة البيانات. الكمية الجديدة: ${newQuantity}`);
          } catch (directDbError) {
            console.error(`[RETURN-MANAGER] فشل في تحديث المخزون مباشرة في قاعدة البيانات:`, directDbError);
            logError(directDbError, 'createReturnTransaction - updateInventory - directDb');
            throw directDbError;
          }
        }
      }
    } catch (inventoryError) {
      console.error(`[RETURN-MANAGER] خطأ في تحديث المخزون:`, inventoryError);
      logError(inventoryError, 'createReturnTransaction - updateInventory');
      throw inventoryError; // إعادة رمي الخطأ لإيقاف العملية
    }

    // بدء معاملة قاعدة البيانات
    db.exec('BEGIN TRANSACTION');

    try {
      // إنشاء عملية الإرجاع في قاعدة البيانات
      const now = new Date().toISOString();
      const insertStmt = db.prepare(`
        INSERT INTO return_transactions (
          return_id,
          original_transaction_id,
          item_id,
          quantity,
          price,
          total_price,
          customer_id,
          invoice_number,
          notes,
          user_id,
          return_date,
          inventory_updated
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      const result = insertStmt.run(
        returnId,
        returnData.original_transaction_id || null,
        returnData.item_id,
        returnData.quantity,
        returnData.price || 0,
        returnData.total_price || (returnData.quantity * (returnData.price || 0)),
        returnData.customer_id,
        returnData.invoice_number || null,
        returnData.notes || null,
        returnData.user_id || null,
        returnData.return_date || now,
        inventoryUpdated
      );

      console.log(`[RETURN-MANAGER] نتيجة إنشاء عملية الإرجاع:`, result);

      if (result.changes <= 0) {
        throw new Error('فشل في إنشاء عملية الإرجاع في قاعدة البيانات');
      }

      // الحصول على عملية الإرجاع المنشأة
      const getReturnStmt = db.prepare(`
        SELECT * FROM return_transactions WHERE return_id = ?
      `);
      const returnTransaction = getReturnStmt.get(returnId);

      // إنهاء المعاملة
      db.exec('COMMIT');

      // مسح التخزين المؤقت
      console.log(`[RETURN-MANAGER] بدء عملية مسح التخزين المؤقت بعد إنشاء عملية إرجاع`);

      // مسح التخزين المؤقت للمخزون بشكل مباشر
      try {
        console.log(`[RETURN-MANAGER] محاولة مسح التخزين المؤقت للمخزون بشكل مباشر`);
        const cacheResult = inventoryManager.clearInventoryCache();
        if (cacheResult) {
          console.log(`[RETURN-MANAGER] تم مسح التخزين المؤقت للمخزون بنجاح`);
          logSystem('[RETURN-MANAGER] تم مسح التخزين المؤقت للمخزون بنجاح', 'info');
        } else {
          console.warn(`[RETURN-MANAGER] تحذير: قد يكون هناك مشكلة في مسح التخزين المؤقت للمخزون`);
          logSystem('[RETURN-MANAGER] تحذير: قد يكون هناك مشكلة في مسح التخزين المؤقت للمخزون', 'warning');
        }
      } catch (directCacheError) {
        console.error(`[RETURN-MANAGER] خطأ في مسح التخزين المؤقت للمخزون بشكل مباشر:`, directCacheError);
        logSystem(`[RETURN-MANAGER] خطأ في مسح التخزين المؤقت للمخزون بشكل مباشر: ${directCacheError.message}`, 'error');

        // محاولة استخدام معالج IPC كخطة بديلة
        try {
          console.log(`[RETURN-MANAGER] محاولة مسح التخزين المؤقت للمخزون باستخدام معالج IPC`);
          const { ipcMain } = require('electron');
          ipcMain.emit('clear-inventory-cache');
          console.log(`[RETURN-MANAGER] تم استدعاء معالج مسح التخزين المؤقت للمخزون`);
          logSystem('[RETURN-MANAGER] تم استدعاء معالج مسح التخزين المؤقت للمخزون', 'info');
        } catch (ipcError) {
          console.error(`[RETURN-MANAGER] خطأ في استدعاء معالج مسح التخزين المؤقت للمخزون:`, ipcError);
          logSystem(`[RETURN-MANAGER] خطأ في استدعاء معالج مسح التخزين المؤقت للمخزون: ${ipcError.message}`, 'error');
        }
      }

      // التحقق من حالة المخزون بعد الإرجاع
      try {
        console.log(`[RETURN-MANAGER] التحقق من حالة المخزون بعد الإرجاع للصنف ${returnData.item_id}`);
        const updatedInventory = inventoryManager.getInventoryForItem(returnData.item_id, true);
        if (updatedInventory) {
          console.log(`[RETURN-MANAGER] حالة المخزون بعد الإرجاع للصنف ${returnData.item_id}:`, {
            name: updatedInventory.name,
            current_quantity: updatedInventory.current_quantity
          });
        } else {
          console.warn(`[RETURN-MANAGER] تحذير: لم يتم العثور على معلومات المخزون بعد الإرجاع للصنف ${returnData.item_id}`);
        }
      } catch (verifyError) {
        console.error(`[RETURN-MANAGER] خطأ في التحقق من حالة المخزون بعد الإرجاع:`, verifyError);
      }

      // مسح التخزين المؤقت لعمليات الإرجاع
      console.log(`[RETURN-MANAGER] مسح التخزين المؤقت لعمليات الإرجاع`);
      clearReturnTransactionsCache();

      // تحديث الخزينة بعد عملية الإرجاع
      try {
        console.log(`[RETURN-MANAGER] تحديث الخزينة بعد عملية الإرجاع`);

        const cashboxResult = await cashboxManager.updateCashboxAfterReturn({
          total_price: returnData.total_price || (returnData.quantity * (returnData.price || 0)),
          customer: returnData.customer || `العميل ${returnData.customer_id}`,
          customer_id: returnData.customer_id,
          notes: returnData.notes || `إرجاع للعميل ${returnData.customer_id}`,
          user_id: returnData.user_id
        });

        console.log(`[RETURN-MANAGER] نتيجة تحديث الخزينة:`, cashboxResult);

        if (!cashboxResult || !cashboxResult.success) {
          console.warn(`[RETURN-MANAGER] تحذير: فشل في تحديث الخزينة بعد عملية الإرجاع:`, cashboxResult ? cashboxResult.error : 'سبب غير معروف');
          // لا نريد إيقاف العملية إذا فشل تحديث الخزينة، ولكن نسجل تحذيرًا
        }
      } catch (cashboxError) {
        console.error(`[RETURN-MANAGER] خطأ في تحديث الخزينة بعد عملية الإرجاع:`, cashboxError);
        logError(cashboxError, 'createReturnTransaction - updateCashbox');
        // لا نريد إيقاف العملية إذا فشل تحديث الخزينة
      }

      // إرسال إشعار بإنشاء عملية إرجاع جديدة
      try {
        // الحصول على معلومات المخزون المحدثة
        const updatedInventory = inventoryManager.getInventoryForItem(returnData.item_id, true);

        // إشعار بإضافة عملية إرجاع
        eventSystem.notifyReturnTransactionAdded({
          id: returnTransaction.id,
          return_id: returnTransaction.return_id,
          item_id: returnTransaction.item_id,
          quantity: returnTransaction.quantity,
          customer_id: returnTransaction.customer_id,
          return_date: returnTransaction.return_date
        });

        // إشعار بتحديث المخزون
        eventSystem.notifyInventoryUpdated({
          itemId: returnData.item_id,
          name: returnData.item_name || `الصنف ${returnData.item_id}`,
          current_quantity: updatedInventory ? updatedInventory.current_quantity : null,
          operation: 'return',
          quantity: returnData.quantity,
          timestamp: new Date().toISOString()
        });

        // إشعار عام بالحاجة للتحديث
        eventSystem.sendEvent(eventSystem.EventTypes.REFRESH_NEEDED, {
          target: 'all',
          timestamp: new Date().toISOString()
        });

        console.log(`[RETURN-MANAGER] تم إرسال إشعارات التحديث بنجاح`);
        logSystem(`[RETURN-MANAGER] تم إرسال إشعارات التحديث بنجاح`, 'info');
      } catch (notifyError) {
        console.warn(`[RETURN-MANAGER] فشل في إرسال إشعار بإنشاء عملية إرجاع:`, notifyError);
        logSystem(`[RETURN-MANAGER] فشل في إرسال إشعار بإنشاء عملية إرجاع: ${notifyError.message}`, 'warning');
        // لا نريد إيقاف العملية إذا فشل إرسال الإشعار
      }

      return {
        success: true,
        returnTransaction,
        inventoryUpdated: inventoryUpdated === 1
      };
    } catch (dbError) {
      // التراجع عن المعاملة في حالة حدوث خطأ
      try {
        db.exec('ROLLBACK');
      } catch (rollbackError) {
        console.error(`[RETURN-MANAGER] خطأ في التراجع عن المعاملة:`, rollbackError);
        logError(rollbackError, 'createReturnTransaction - rollback');
      }
      console.error(`[RETURN-MANAGER] خطأ في إنشاء عملية الإرجاع في قاعدة البيانات:`, dbError);
      logError(dbError, 'createReturnTransaction - database');
      throw dbError;
    }
  } catch (error) {
    console.error(`[RETURN-MANAGER] خطأ في إنشاء عملية إرجاع:`, error);
    logError(error, 'createReturnTransaction');
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * الحصول على الكمية المتاحة للإرجاع
 * @param {number} itemId - معرف الصنف
 * @param {number} customerId - معرف العميل
 * @param {string} originalTransactionId - معرف المعاملة الأصلية (اختياري)
 * @returns {Promise<number>} - الكمية المتاحة للإرجاع
 */
async function getAvailableQuantityForReturn(itemId, customerId, originalTransactionId = null) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // التحقق من صحة المدخلات
    itemId = parseInt(itemId);
    customerId = parseInt(customerId);

    if (isNaN(itemId) || itemId <= 0) throw new Error('معرف الصنف غير صالح');
    if (isNaN(customerId) || customerId <= 0) throw new Error('معرف العميل غير صالح');

    console.log(`[RETURN-MANAGER] الحصول على الكمية المتاحة للإرجاع للصنف ${itemId} والعميل ${customerId}`);

    // إذا تم تحديد معرف المعاملة الأصلية، نتحقق من الكمية المتاحة للإرجاع لهذه المعاملة فقط
    if (originalTransactionId) {
      const originalTransactionStmt = db.prepare(`
        SELECT quantity FROM transactions
        WHERE transaction_id = ? AND item_id = ? AND customer_id = ? AND transaction_type = 'sale'
      `);
      const originalTransaction = originalTransactionStmt.get(originalTransactionId, itemId, customerId);

      if (!originalTransaction) {
        return 0; // المعاملة الأصلية غير موجودة
      }

      // الحصول على إجمالي الكمية المسترجعة لهذه المعاملة
      const returnedQuantityStmt = db.prepare(`
        SELECT SUM(quantity) as totalReturned FROM return_transactions
        WHERE original_transaction_id = ? AND item_id = ? AND customer_id = ?
      `);
      const returnedQuantityResult = returnedQuantityStmt.get(originalTransactionId, itemId, customerId);
      const totalReturned = returnedQuantityResult ? returnedQuantityResult.totalReturned || 0 : 0;

      return Math.max(0, originalTransaction.quantity - totalReturned);
    }

    // الحصول على إجمالي الكمية المباعة للعميل
    const soldQuantityStmt = db.prepare(`
      SELECT SUM(quantity) as totalSold FROM transactions
      WHERE item_id = ? AND customer_id = ? AND transaction_type = 'sale'
    `);
    const soldQuantityResult = soldQuantityStmt.get(itemId, customerId);
    const totalSold = soldQuantityResult ? soldQuantityResult.totalSold || 0 : 0;

    // الحصول على إجمالي الكمية المسترجعة للعميل
    const returnedQuantityStmt = db.prepare(`
      SELECT SUM(quantity) as totalReturned FROM return_transactions
      WHERE item_id = ? AND customer_id = ?
    `);
    const returnedQuantityResult = returnedQuantityStmt.get(itemId, customerId);
    const totalReturned = returnedQuantityResult ? returnedQuantityResult.totalReturned || 0 : 0;

    // حساب الكمية المتاحة للإرجاع
    const availableForReturn = Math.max(0, totalSold - totalReturned);

    console.log(`[RETURN-MANAGER] إجمالي المبيعات: ${totalSold}, إجمالي المسترجعات: ${totalReturned}, المتاح للإرجاع: ${availableForReturn}`);

    return availableForReturn;
  } catch (error) {
    console.error(`[RETURN-MANAGER] خطأ في الحصول على الكمية المتاحة للإرجاع:`, error);
    logError(error, 'getAvailableQuantityForReturn');
    return 0;
  }
}

/**
 * الحصول على جميع عمليات الإرجاع
 * @param {Object} filters - فلاتر التصفية
 * @returns {Array} - قائمة عمليات الإرجاع
 */
function getAllReturnTransactions(filters = null) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // التحقق من وجود البيانات في التخزين المؤقت
    if (!filters && returnTransactionsCache &&
        (Date.now() - returnTransactionsCacheTimestamp < CACHE_EXPIRY)) {
      console.log(`[RETURN-MANAGER] استخدام بيانات عمليات الإرجاع من التخزين المؤقت`);
      return returnTransactionsCache;
    }

    console.log(`[RETURN-MANAGER] الحصول على عمليات الإرجاع مع الفلاتر:`, filters);

    // بناء استعلام مع الفلاتر
    let query = `
      SELECT r.*, i.name as item_name, i.unit, c.name as customer_name, u.username as user_name
      FROM return_transactions r
      LEFT JOIN items i ON r.item_id = i.id
      LEFT JOIN customers c ON r.customer_id = c.id
      LEFT JOIN users u ON r.user_id = u.id
    `;

    const params = [];
    const whereConditions = [];

    // إضافة الفلاتر إذا كانت موجودة
    if (filters) {
      // فلتر معرف الصنف
      if (filters.item_id) {
        whereConditions.push('r.item_id = ?');
        params.push(filters.item_id);
      }

      // فلتر معرف العميل
      if (filters.customer_id) {
        whereConditions.push('r.customer_id = ?');
        params.push(filters.customer_id);
      }

      // فلتر تاريخ البداية
      if (filters.start_date) {
        whereConditions.push('r.return_date >= ?');
        params.push(filters.start_date);
      }

      // فلتر تاريخ النهاية
      if (filters.end_date) {
        whereConditions.push('r.return_date <= ?');
        params.push(filters.end_date);
      }

      // فلتر رقم الفاتورة
      if (filters.invoice_number) {
        whereConditions.push('r.invoice_number = ?');
        params.push(filters.invoice_number);
      }
    }

    // إضافة شروط WHERE إذا كانت موجودة
    if (whereConditions.length > 0) {
      query += ' WHERE ' + whereConditions.join(' AND ');
    }

    // إضافة الترتيب
    query += ' ORDER BY r.return_date DESC';

    // تنفيذ الاستعلام
    const stmt = db.prepare(query);
    const returnTransactions = stmt.all(...params);

    console.log(`[RETURN-MANAGER] تم الحصول على ${returnTransactions.length} عملية إرجاع`);

    // تخزين النتيجة في التخزين المؤقت إذا لم تكن هناك فلاتر
    if (!filters) {
      returnTransactionsCache = returnTransactions;
      returnTransactionsCacheTimestamp = Date.now();
    }

    return returnTransactions;
  } catch (error) {
    console.error(`[RETURN-MANAGER] خطأ في الحصول على عمليات الإرجاع:`, error);
    logError(error, 'getAllReturnTransactions');
    return [];
  }
}

/**
 * الحصول على عملية إرجاع بواسطة المعرف
 * @param {number|string} returnId - معرف عملية الإرجاع
 * @returns {Object|null} - بيانات عملية الإرجاع
 */
function getReturnTransactionById(returnId) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    console.log(`[RETURN-MANAGER] الحصول على عملية الإرجاع بالمعرف: ${returnId}`);

    // التحقق من نوع المعرف
    let query;
    if (typeof returnId === 'number' || !isNaN(parseInt(returnId))) {
      // البحث بواسطة معرف الصف
      query = `
        SELECT r.*, i.name as item_name, i.unit, c.name as customer_name, u.username as user_name
        FROM return_transactions r
        LEFT JOIN items i ON r.item_id = i.id
        LEFT JOIN customers c ON r.customer_id = c.id
        LEFT JOIN users u ON r.user_id = u.id
        WHERE r.id = ?
      `;
    } else {
      // البحث بواسطة معرف الإرجاع
      query = `
        SELECT r.*, i.name as item_name, i.unit, c.name as customer_name, u.username as user_name
        FROM return_transactions r
        LEFT JOIN items i ON r.item_id = i.id
        LEFT JOIN customers c ON r.customer_id = c.id
        LEFT JOIN users u ON r.user_id = u.id
        WHERE r.return_id = ?
      `;
    }

    const stmt = db.prepare(query);
    const returnTransaction = stmt.get(returnId);

    if (returnTransaction) {
      console.log(`[RETURN-MANAGER] تم العثور على عملية الإرجاع: ${returnTransaction.return_id}`);
    } else {
      console.log(`[RETURN-MANAGER] لم يتم العثور على عملية الإرجاع بالمعرف: ${returnId}`);
    }

    return returnTransaction || null;
  } catch (error) {
    console.error(`[RETURN-MANAGER] خطأ في الحصول على عملية الإرجاع:`, error);
    logError(error, 'getReturnTransactionById');
    return null;
  }
}

/**
 * الحصول على عمليات الإرجاع للعميل
 * @param {number} customerId - معرف العميل
 * @returns {Array} - قائمة عمليات الإرجاع
 */
function getCustomerReturnTransactions(customerId) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // التحقق من صحة المدخلات
    customerId = parseInt(customerId);
    if (isNaN(customerId) || customerId <= 0) throw new Error('معرف العميل غير صالح');

    console.log(`[RETURN-MANAGER] الحصول على عمليات الإرجاع للعميل: ${customerId}`);

    // استعلام للحصول على عمليات الإرجاع للعميل
    const query = `
      SELECT r.*, i.name as item_name, i.unit
      FROM return_transactions r
      LEFT JOIN items i ON r.item_id = i.id
      WHERE r.customer_id = ?
      ORDER BY r.return_date DESC
    `;

    const stmt = db.prepare(query);
    const returnTransactions = stmt.all(customerId);

    console.log(`[RETURN-MANAGER] تم الحصول على ${returnTransactions.length} عملية إرجاع للعميل ${customerId}`);

    return returnTransactions;
  } catch (error) {
    console.error(`[RETURN-MANAGER] خطأ في الحصول على عمليات الإرجاع للعميل:`, error);
    logError(error, 'getCustomerReturnTransactions');
    return [];
  }
}

/**
 * مسح التخزين المؤقت لعمليات الإرجاع
 * @returns {boolean} - نجاح العملية
 */
function clearReturnTransactionsCache() {
  try {
    console.log(`[RETURN-MANAGER] بدء عملية مسح التخزين المؤقت لعمليات الإرجاع`);

    // حفظ حالة التخزين المؤقت قبل المسح للتشخيص
    const hadCache = returnTransactionsCache !== null;
    const cacheTimestamp = returnTransactionsCacheTimestamp;

    // مسح التخزين المؤقت
    returnTransactionsCache = null;
    returnTransactionsCacheTimestamp = 0;

    // التحقق من نجاح عملية المسح
    if (returnTransactionsCache !== null) {
      console.warn(`[RETURN-MANAGER] تحذير: فشل في مسح التخزين المؤقت لعمليات الإرجاع`);
      logSystem('تحذير: فشل في مسح التخزين المؤقت لعمليات الإرجاع', 'warning');

      // محاولة إعادة المسح
      returnTransactionsCache = null;

      if (returnTransactionsCache !== null) {
        console.error(`[RETURN-MANAGER] خطأ: فشل في مسح التخزين المؤقت لعمليات الإرجاع حتى بعد المحاولة الثانية`);
        logSystem('خطأ: فشل في مسح التخزين المؤقت لعمليات الإرجاع حتى بعد المحاولة الثانية', 'error');
        return false;
      }
    }

    console.log(`[RETURN-MANAGER] تم مسح التخزين المؤقت لعمليات الإرجاع بنجاح (كان موجودًا: ${hadCache}, الطابع الزمني: ${new Date(cacheTimestamp).toISOString()})`);
    logSystem('تم مسح التخزين المؤقت لعمليات الإرجاع بنجاح', 'info');
    return true;
  } catch (error) {
    console.error(`[RETURN-MANAGER] خطأ في مسح التخزين المؤقت لعمليات الإرجاع:`, error);
    logError(error, 'clearReturnTransactionsCache');

    // محاولة المسح بطريقة أخرى في حالة الخطأ
    try {
      returnTransactionsCache = null;
      returnTransactionsCacheTimestamp = 0;
      console.log(`[RETURN-MANAGER] تم مسح التخزين المؤقت لعمليات الإرجاع بطريقة بديلة`);
      return true;
    } catch (fallbackError) {
      console.error(`[RETURN-MANAGER] فشل في مسح التخزين المؤقت لعمليات الإرجاع حتى بالطريقة البديلة:`, fallbackError);
      return false;
    }
  }
}

/**
 * الحصول على اتصال قاعدة البيانات
 * @returns {Object} - اتصال قاعدة البيانات
 */
function getDatabase() {
  return db;
}

/**
 * التحقق من حالة نظام الإرجاع
 * @returns {Object} - حالة نظام الإرجاع
 */
function checkReturnSystemStatus() {
  try {
    console.log(`[RETURN-MANAGER] التحقق من حالة نظام الإرجاع...`);

    const status = {
      initialized: db !== null,
      tableExists: false,
      inventoryManagerAvailable: inventoryManager !== null && typeof inventoryManager.updateInventoryAfterReturn === 'function',
      cashboxManagerAvailable: cashboxManager !== null && typeof cashboxManager.updateCashboxAfterReturn === 'function',
      eventSystemAvailable: eventSystem !== null,
      errors: []
    };

    // التحقق من وجود جدول عمليات الإرجاع
    if (db) {
      try {
        const tableExistsStmt = db.prepare(`
          SELECT name FROM sqlite_master
          WHERE type='table' AND name='return_transactions'
        `);
        const tableExists = tableExistsStmt.get();
        status.tableExists = tableExists !== undefined;
      } catch (tableError) {
        console.error(`[RETURN-MANAGER] خطأ في التحقق من وجود جدول عمليات الإرجاع:`, tableError);
        status.errors.push(`خطأ في التحقق من وجود جدول عمليات الإرجاع: ${tableError.message}`);
      }
    } else {
      status.errors.push('قاعدة البيانات غير مهيأة');
    }

    // التحقق من وجود مدير المخزون
    if (!status.inventoryManagerAvailable) {
      status.errors.push('مدير المخزون غير متوفر أو دالة updateInventoryAfterReturn غير موجودة');
    }

    // التحقق من وجود مدير الخزينة
    if (!status.cashboxManagerAvailable) {
      status.errors.push('مدير الخزينة غير متوفر أو دالة updateCashboxAfterReturn غير موجودة');
    }

    // التحقق من وجود نظام الأحداث
    if (!status.eventSystemAvailable) {
      status.errors.push('نظام الأحداث غير متوفر');
    }

    // تحديد الحالة العامة للنظام
    status.isReady = status.initialized && status.tableExists && status.inventoryManagerAvailable;

    console.log(`[RETURN-MANAGER] حالة نظام الإرجاع:`, status);

    return status;
  } catch (error) {
    console.error(`[RETURN-MANAGER] خطأ في التحقق من حالة نظام الإرجاع:`, error);
    return {
      initialized: false,
      tableExists: false,
      inventoryManagerAvailable: false,
      cashboxManagerAvailable: false,
      eventSystemAvailable: false,
      isReady: false,
      errors: [`خطأ في التحقق من حالة نظام الإرجاع: ${error.message}`]
    };
  }
}

/**
 * إصلاح نظام الإرجاع
 * @returns {Object} - نتيجة عملية الإصلاح
 */
function repairReturnSystem() {
  try {
    console.log(`[RETURN-MANAGER] بدء إصلاح نظام الإرجاع...`);

    const status = checkReturnSystemStatus();
    const repairs = [];

    // إذا لم تكن قاعدة البيانات مهيأة، لا يمكن إصلاح النظام
    if (!status.initialized) {
      return {
        success: false,
        message: 'لا يمكن إصلاح نظام الإرجاع: قاعدة البيانات غير مهيأة',
        repairs: []
      };
    }

    // إنشاء جدول عمليات الإرجاع إذا لم يكن موجودًا
    if (!status.tableExists) {
      try {
        createReturnTransactionsTable();
        repairs.push('تم إنشاء جدول عمليات الإرجاع');
      } catch (tableError) {
        console.error(`[RETURN-MANAGER] خطأ في إنشاء جدول عمليات الإرجاع:`, tableError);
        return {
          success: false,
          message: `فشل في إصلاح نظام الإرجاع: ${tableError.message}`,
          repairs
        };
      }
    }

    // مسح التخزين المؤقت
    try {
      clearReturnTransactionsCache();
      repairs.push('تم مسح التخزين المؤقت لعمليات الإرجاع');
    } catch (cacheError) {
      console.warn(`[RETURN-MANAGER] تحذير: فشل في مسح التخزين المؤقت لعمليات الإرجاع:`, cacheError);
      // لا نريد إيقاف عملية الإصلاح إذا فشل مسح التخزين المؤقت
    }

    // التحقق من حالة النظام بعد الإصلاح
    const newStatus = checkReturnSystemStatus();

    return {
      success: newStatus.isReady,
      message: newStatus.isReady ? 'تم إصلاح نظام الإرجاع بنجاح' : 'تم إجراء بعض الإصلاحات، ولكن النظام ما زال غير جاهز',
      repairs,
      status: newStatus
    };
  } catch (error) {
    console.error(`[RETURN-MANAGER] خطأ في إصلاح نظام الإرجاع:`, error);
    return {
      success: false,
      message: `فشل في إصلاح نظام الإرجاع: ${error.message}`,
      repairs: []
    };
  }
}

// تصدير الوظائف
module.exports = {
  initialize,
  createReturnTransaction,
  getAvailableQuantityForReturn,
  getAllReturnTransactions,
  getReturnTransactionById,
  getCustomerReturnTransactions,
  clearReturnTransactionsCache,
  checkReturnSystemStatus,
  repairReturnSystem,
  verifyInventoryAfterReturn,
  getDatabase
};
