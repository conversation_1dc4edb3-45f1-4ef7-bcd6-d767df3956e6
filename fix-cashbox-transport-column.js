/**
 * سكريبت لإصلاح هيكل جدول الخزينة وإضافة عمود transport_total
 * يتم تشغيله لضمان وجود جميع الأعمدة المطلوبة في جدول الخزينة
 */

const { logError, logSystem } = require('./error-handler');
const DatabaseManager = require('./database-singleton');

/**
 * إصلاح هيكل جدول الخزينة
 */
function fixCashboxTransportColumn() {
  try {
    console.log('🔧 بدء إصلاح هيكل جدول الخزينة...');
    
    // الحصول على قاعدة البيانات
    const db = DatabaseManager.getDatabase();
    if (!db) {
      throw new Error('قاعدة البيانات غير متصلة');
    }

    // التحقق من معلومات الجدول الحالي
    console.log('📋 فحص هيكل جدول الخزينة الحالي...');
    const tableInfo = db.prepare("PRAGMA table_info(cashbox)").all();
    const existingColumns = tableInfo.map(col => col.name);
    
    console.log('✅ الأعمدة الموجودة حالياً:', existingColumns);

    // قائمة الأعمدة المطلوبة
    const requiredColumns = [
      { name: 'id', type: 'INTEGER PRIMARY KEY AUTOINCREMENT', required: true },
      { name: 'initial_balance', type: 'REAL NOT NULL', required: true },
      { name: 'current_balance', type: 'REAL NOT NULL', required: true },
      { name: 'profit_total', type: 'REAL', default: '0' },
      { name: 'sales_total', type: 'REAL', default: '0' },
      { name: 'purchases_total', type: 'REAL', default: '0' },
      { name: 'returns_total', type: 'REAL', default: '0' },
      { name: 'transport_total', type: 'REAL', default: '0' },
      { name: 'created_at', type: 'DATETIME', default: 'CURRENT_TIMESTAMP' },
      { name: 'updated_at', type: 'DATETIME', default: 'CURRENT_TIMESTAMP' }
    ];

    console.log('🔍 التحقق من الأعمدة المطلوبة...');

    let columnsAdded = 0;
    let hasTransportColumn = false;

    // إضافة الأعمدة المفقودة
    for (const column of requiredColumns) {
      if (!existingColumns.includes(column.name)) {
        if (column.required) {
          console.log(`❌ العمود المطلوب ${column.name} مفقود ولا يمكن إضافته تلقائياً`);
          continue;
        }

        try {
          console.log(`➕ إضافة العمود المفقود: ${column.name}`);
          const alterQuery = `ALTER TABLE cashbox ADD COLUMN ${column.name} ${column.type} DEFAULT ${column.default}`;
          db.prepare(alterQuery).run();
          console.log(`✅ تم إضافة العمود: ${column.name}`);
          columnsAdded++;
          
          if (column.name === 'transport_total') {
            hasTransportColumn = true;
          }
        } catch (error) {
          console.error(`❌ خطأ في إضافة العمود ${column.name}:`, error.message);
        }
      } else {
        console.log(`✅ العمود ${column.name} موجود`);
        if (column.name === 'transport_total') {
          hasTransportColumn = true;
        }
      }
    }

    // التحقق من وجود بيانات في الخزينة
    console.log('📊 فحص بيانات الخزينة...');
    const cashboxData = db.prepare('SELECT * FROM cashbox LIMIT 1').get();
    
    if (cashboxData) {
      console.log('✅ تم العثور على بيانات الخزينة:', {
        id: cashboxData.id,
        initial_balance: cashboxData.initial_balance,
        current_balance: cashboxData.current_balance,
        transport_total: cashboxData.transport_total || 'غير محدد'
      });

      // تحديث القيم الافتراضية للأعمدة الجديدة إذا كانت NULL
      if (columnsAdded > 0) {
        console.log('🔄 تحديث القيم الافتراضية...');

        const updateQuery = `
          UPDATE cashbox
          SET
            profit_total = COALESCE(profit_total, 0),
            sales_total = COALESCE(sales_total, 0),
            purchases_total = COALESCE(purchases_total, 0),
            returns_total = COALESCE(returns_total, 0),
            transport_total = COALESCE(transport_total, 0),
            updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `;

        const updateResult = db.prepare(updateQuery).run(cashboxData.id);
        console.log(`✅ تم تحديث ${updateResult.changes} سجل`);
      }
    } else {
      console.log('⚠️ لا توجد بيانات في جدول الخزينة');
    }

    // اختبار تحديث عمود transport_total
    if (hasTransportColumn && cashboxData) {
      console.log('🧪 اختبار تحديث عمود transport_total...');
      
      try {
        const testUpdateQuery = `
          UPDATE cashbox 
          SET transport_total = COALESCE(transport_total, 0),
              updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `;
        
        const testResult = db.prepare(testUpdateQuery).run(cashboxData.id);
        console.log(`✅ اختبار تحديث transport_total نجح، عدد الصفوف المتأثرة: ${testResult.changes}`);
      } catch (testError) {
        console.error('❌ فشل اختبار تحديث transport_total:', testError.message);
        return {
          success: false,
          message: `فشل اختبار تحديث transport_total: ${testError.message}`
        };
      }
    }

    // عرض النتيجة النهائية
    console.log('\n📋 ملخص الإصلاح:');
    console.log(`✅ تم إضافة ${columnsAdded} عمود جديد`);
    console.log(`✅ عمود transport_total: ${hasTransportColumn ? 'موجود' : 'غير موجود'}`);
    
    if (columnsAdded > 0) {
      console.log('🎉 تم إصلاح هيكل جدول الخزينة بنجاح!');
      logSystem(`تم إصلاح هيكل جدول الخزينة وإضافة ${columnsAdded} عمود جديد`, 'info');
    } else {
      console.log('✅ هيكل جدول الخزينة صحيح ولا يحتاج إصلاح');
    }

    return {
      success: true,
      columnsAdded,
      hasTransportColumn,
      message: `تم إصلاح هيكل جدول الخزينة بنجاح. تم إضافة ${columnsAdded} عمود جديد.`
    };

  } catch (error) {
    console.error('❌ خطأ في إصلاح هيكل جدول الخزينة:', error);
    logError(error, 'fixCashboxTransportColumn');
    
    return {
      success: false,
      message: `خطأ في إصلاح هيكل جدول الخزينة: ${error.message}`
    };
  }
}

/**
 * اختبار عمليات الخزينة بعد الإصلاح
 */
function testCashboxOperations() {
  try {
    console.log('\n🧪 اختبار عمليات الخزينة...');
    
    const db = DatabaseManager.getDatabase();
    if (!db) {
      throw new Error('قاعدة البيانات غير متصلة');
    }

    // اختبار قراءة بيانات الخزينة
    const cashbox = db.prepare('SELECT * FROM cashbox LIMIT 1').get();
    if (!cashbox) {
      console.log('⚠️ لا توجد بيانات خزينة للاختبار');
      return { success: true, message: 'لا توجد بيانات خزينة للاختبار' };
    }

    console.log('📊 بيانات الخزينة الحالية:');
    console.log(`   - الرصيد الافتتاحي: ${cashbox.initial_balance}`);
    console.log(`   - الرصيد الحالي: ${cashbox.current_balance}`);
    console.log(`   - إجمالي المبيعات: ${cashbox.sales_total || 0}`);
    console.log(`   - إجمالي المشتريات: ${cashbox.purchases_total || 0}`);
    console.log(`   - إجمالي مصاريف النقل: ${cashbox.transport_total || 0}`);
    console.log(`   - إجمالي الأرباح: ${cashbox.profit_total || 0}`);

    // اختبار تحديث عمود transport_total
    console.log('🔄 اختبار تحديث مصاريف النقل...');
    
    const testTransportCost = 100;
    const updateQuery = `
      UPDATE cashbox 
      SET transport_total = transport_total + ?,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;
    
    const updateResult = db.prepare(updateQuery).run(testTransportCost, cashbox.id);
    console.log(`✅ تم تحديث مصاريف النقل بنجاح، عدد الصفوف المتأثرة: ${updateResult.changes}`);

    // التراجع عن التحديث التجريبي
    const rollbackQuery = `
      UPDATE cashbox 
      SET transport_total = transport_total - ?,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;
    
    db.prepare(rollbackQuery).run(testTransportCost, cashbox.id);
    console.log('🔄 تم التراجع عن التحديث التجريبي');

    console.log('✅ جميع اختبارات الخزينة نجحت!');
    
    return {
      success: true,
      message: 'جميع اختبارات الخزينة نجحت'
    };

  } catch (error) {
    console.error('❌ خطأ في اختبار عمليات الخزينة:', error);
    logError(error, 'testCashboxOperations');
    
    return {
      success: false,
      message: `خطأ في اختبار عمليات الخزينة: ${error.message}`
    };
  }
}

module.exports = {
  fixCashboxTransportColumn,
  testCashboxOperations
};

// تشغيل الإصلاح إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  console.log('🚀 بدء إصلاح هيكل جدول الخزينة...');
  
  const fixResult = fixCashboxTransportColumn();
  console.log('\n📋 نتيجة الإصلاح:', fixResult);
  
  if (fixResult.success) {
    const testResult = testCashboxOperations();
    console.log('\n📋 نتيجة الاختبار:', testResult);
  }
  
  console.log('\n🏁 انتهى الإصلاح');
}
