import React, { useState, useEffect, useRef } from 'react';
import { FaSave, FaDatabase, FaFileExport, FaFileImport, FaCog, FaBuilding, FaImage, FaWarehouse, FaLink, FaSync, FaCheck, FaTimes, FaSearch, FaNetworkWired, FaClock, FaExclamationTriangle, FaDownload } from 'react-icons/fa';
import { useApp } from '../context/AppContext';
import { discoverDevices, testDeviceConnection } from '../services/NetworkDiscoveryService';
import UpdateManager from '../components/settings/UpdateManager';
import TabManager from '../components/TabManager';
// تم إزالة استيراد DatabaseAdapter لأنه غير مستخدم
// تم إزالة استيراد ملف data-migration لأنه غير مطلوب مع SQLite

const Settings = () => {
  const { settings, updateSettings, testIntegrationConnection, syncWithSalesSystem, syncStatus } = useApp();
  // Variable no utilizada - considere eliminarla o usarla
  const [formData, setFormData] = useState({
    systemName: 'شركة أثاث غروب',
    address: 'للتصميم و تصنيع الأثاث والديكورات',
    phone: '+218 92-3000151',
    logoUrl: '',
    lowStockNotification: true,
    defaultMinimumQuantity: 5,
    integrationEnabled: false,
    integrationUrl: '',
    integrationApiKey: '',
    syncInterval: 30,
    isInternetConnection: false
    // تم إزالة خاصية السطوع واستبدالها بنظام تلقائي
  });
  // Variable no utilizada - considere eliminarla o usarla
  const [alert, setAlert] = useState({ show: false, type: '', message: '' });

  // مرجع للنموذج
  const formRef = useRef(null);

  // مرجع لمؤقت التنبيهات
  const alertTimeoutRef = useRef(null);

  // مرجع لتتبع ما إذا كان المكون لا يزال مثبتًا
  const isMounted = useRef(true);

  // تنظيف المؤقتات عند إلغاء تحميل المكون
  useEffect(() => {
    // تعيين isMounted إلى true عند تحميل المكون
    isMounted.current = true;

    // إعادة تمكين حقول النموذج عند تحميل المكون
    setTimeout(() => {
      enableFormFields();
    }, 100);

    return () => {
      isMounted.current = false;
      if (alertTimeoutRef.current) {
        clearTimeout(alertTimeoutRef.current);
        alertTimeoutRef.current = null;
      }
    };
  }, []);
  // Variable no utilizada - considere eliminarla o usarla
  const [backupInProgress, setBackupInProgress] = useState(false);
  // Variable no utilizada - considere eliminarla o usarla
  const [restoreInProgress, setRestoreInProgress] = useState(false);
  // Variable no utilizada - considere eliminarla o usarla
  const [connectionStatus, setConnectionStatus] = useState({ testing: false, success: null, message: '' });

  // تم إزالة حالة مصادقة Google Drive

  // حالة اكتشاف الأجهزة على الشبكة المحلية
  // Variable no utilizada - considere eliminarla o usarla
  const [discoveryStatus, setDiscoveryStatus] = useState({
    discovering: false,
    devices: [],
    error: null
  });

  // حالة قاعدة البيانات
  // Variable no utilizada - considere eliminarla o usarla
  const [databaseStatus, setDatabaseStatus] = useState({
    currentType: 'nedb',
    migrating: false,
    validating: false,
    migrationResult: null,
    validationResult: null,
    error: null
  });

  // وظيفة لإعادة تمكين حقول النموذج
  const enableFormFields = () => {
    if (formRef.current && isMounted.current) {
      // الحصول على جميع حقول الإدخال في النموذج
      const inputs = formRef.current.querySelectorAll('input, select, textarea');

      // إعادة تمكين جميع الحقول
      inputs.forEach(input => {
        input.disabled = false;

        // إزالة أي خصائص أخرى قد تمنع التحرير
        input.readOnly = false;

        // إزالة أي أنماط CSS قد تمنع التحرير
        input.style.pointerEvents = 'auto';
        input.style.backgroundColor = input.type === 'file' ? 'white' : '#f9f9f9';

        // التأكد من أن الحقل قابل للنقر
        input.style.cursor = 'auto';
      });

      console.log('تم إعادة تمكين جميع حقول النموذج');
    }
  };

  // تحميل الإعدادات الحالية عند تحميل الصفحة
  useEffect(() => {
    if (settings) {
      setFormData({
        systemName: settings.systemName || 'شركة أثاث غروب',
        address: settings.address || 'للتصميم و تصنيع الأثاث والديكورات',
        phone: settings.phone || '+218 92-3000151',
        logoUrl: settings.logoUrl || '',
        lowStockNotification: settings.lowStockNotification !== undefined ? settings.lowStockNotification : true,
        defaultMinimumQuantity: settings.defaultMinimumQuantity || 5,
        integrationEnabled: settings.integrationEnabled || false,
        integrationUrl: settings.integrationUrl || '',
        integrationApiKey: settings.integrationApiKey || '',
        syncInterval: settings.syncInterval || 30,
        isInternetConnection: settings.isInternetConnection || false
        // تم إزالة خاصية السطوع واستبدالها بنظام تلقائي
      });
    }

    // تحديث حالة قاعدة البيانات - النظام يستخدم SQLite فقط
    setDatabaseStatus(prev => ({
      ...prev,
      currentType: 'sqlite'
    }));

    // إعادة تمكين حقول النموذج
    // استخدام مؤقتات متعددة لضمان تمكين الحقول
    setTimeout(() => {
      enableFormFields();
    }, 100);

    setTimeout(() => {
      enableFormFields();
    }, 500);

    setTimeout(() => {
      enableFormFields();
    }, 1000);
  }, [settings]);

  // تم إزالة وظيفة التحقق من حالة مصادقة Google Drive

  // وظائف إدارة قاعدة البيانات

  // تغيير نوع قاعدة البيانات - تم إزالة هذه الوظيفة لأن النظام يستخدم SQLite فقط
  const handleDatabaseTypeChange = (type) => {
    try {
      if (type !== 'sqlite') {
        showAlert('warning', 'النظام يدعم SQLite فقط');
        return;
      }

      setDatabaseStatus(prev => ({
        ...prev,
        currentType: 'sqlite',
        error: null
      }));

      showAlert('success', 'النظام يستخدم SQLite بالفعل');
    } catch (error) {
      console.error('خطأ:', error);
      showAlert('danger', `خطأ: ${error.message}`);
    }
  };

  // ترحيل البيانات من NeDB إلى SQLite - تم إزالة هذه الوظيفة لأن النظام يستخدم SQLite فقط
  const handleMigrateData = async () => {
    try {
      showAlert('info', 'النظام يستخدم SQLite بالفعل. لا حاجة للترحيل.');

      setDatabaseStatus(prev => ({
        ...prev,
        currentType: 'sqlite',
        migrationResult: { success: true, message: 'النظام يستخدم SQLite بالفعل. لا حاجة للترحيل.' }
      }));
    } catch (error) {
      console.error('خطأ:', error);
      showAlert('danger', `خطأ: ${error.message}`);
    }
  };

  // التحقق من صحة البيانات المرحلة - تم إزالة هذه الوظيفة لأن النظام يستخدم SQLite فقط
  const handleValidateMigration = async () => {
    try {
      showAlert('info', 'النظام يستخدم SQLite بالفعل. لا حاجة للتحقق من الترحيل.');

      setDatabaseStatus(prev => ({
        ...prev,
        validationResult: { success: true, message: 'النظام يستخدم SQLite بالفعل. لا حاجة للتحقق من الترحيل.' }
      }));
    } catch (error) {
      console.error('خطأ:', error);
      showAlert('danger', `خطأ: ${error.message}`);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // وظيفة لمعالجة تغيير شعار المنظومة
  const handleLogoChange = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    console.log('تم اختيار ملف:', file.name, 'النوع:', file.type, 'الحجم:', file.size);

    // التحقق من نوع الملف
    if (!file.type.match('image.*')) {
      showAlert('danger', 'يرجى اختيار ملف صورة صالح');
      console.error('نوع الملف غير صالح:', file.type);
      return;
    }

    // التحقق من حجم الملف (الحد الأقصى 2 ميجابايت)
    if (file.size > 2 * 1024 * 1024) {
      showAlert('danger', 'حجم الصورة كبير جدًا. يرجى اختيار صورة أقل من 2 ميجابايت');
      console.error('حجم الملف كبير جدًا:', file.size);
      return;
    }

    // إظهار رسالة للمستخدم أثناء المعالجة
    showAlert('info', 'جاري معالجة الصورة...');

    // إنشاء قارئ ملفات
    const reader = new FileReader();

    // معالجة أخطاء القراءة
    reader.onerror = (error) => {
      console.error('خطأ في قراءة الملف:', error);
      showAlert('danger', 'حدث خطأ أثناء قراءة الملف');
    };

    reader.onload = (event) => {
      try {
        console.log('تم قراءة الملف بنجاح');

        // تحديث حالة النموذج بعنوان URL للصورة
        const logoUrl = event.target.result;
        console.log('تم إنشاء Data URL للصورة، الطول:', logoUrl.length);

        // تحميل الصورة مسبقًا للتأكد من صحتها
        const img = new Image();
        img.onload = () => {
          console.log('تم تحميل الصورة بنجاح، جاري تحديث النظام...');

          // تحديث حالة النموذج
          setFormData(prev => ({
            ...prev,
            logoUrl: logoUrl
          }));
          console.log('تم تحديث حالة النموذج بالصورة الجديدة');

          // تحديث الإعدادات مباشرة في السياق
          const updatedSettings = {
            ...settings,
            logoUrl: logoUrl
          };
          console.log('جاري تحديث الإعدادات في السياق...');

          // استدعاء وظيفة تحديث الإعدادات
          updateSettings(updatedSettings)
            .then(result => {
              console.log('تم تحديث الإعدادات بنجاح:', result);

              // تحديث الإعدادات في التخزين المحلي أيضًا للتأكد من التوافق
              try {
                localStorage.setItem('settings', JSON.stringify(updatedSettings));
                console.log('تم تحديث الإعدادات في التخزين المحلي');
              } catch (storageError) {
                console.error('خطأ في تحديث التخزين المحلي:', storageError);
              }

              // عرض رسالة للمستخدم
              showAlert('success', 'تم تحديث شعار المنظومة بنجاح');

              // إعادة تحميل الصفحة لتطبيق التغييرات
              setTimeout(() => {
                window.location.reload();
              }, 1500);
            })
            .catch(error => {
              console.error('خطأ في تحديث الإعدادات:', error);
              showAlert('danger', 'حدث خطأ أثناء تحديث شعار المنظومة');
            });
        };

        img.onerror = () => {
          console.error('فشل في تحميل الصورة، الصورة غير صالحة');
          showAlert('danger', 'الصورة غير صالحة، يرجى اختيار صورة أخرى');
        };

        // تعيين مصدر الصورة للتحقق من صحتها
        img.src = logoUrl;
      } catch (error) {
        console.error('خطأ في معالجة الصورة:', error);
        showAlert('danger', 'حدث خطأ أثناء معالجة الصورة');
      }
    };

    // قراءة الملف كـ Data URL
    console.log('جاري قراءة الملف كـ Data URL...');
    reader.readAsDataURL(file);
  };

  // اختبار الاتصال بمنظومة المبيعات
  const testConnection = async () => {
    if (!formData.integrationUrl || !formData.integrationApiKey) {
      showAlert('danger', 'يرجى إدخال عنوان URL أو اسم المضيف ومفتاح API قبل اختبار الاتصال');
      return;
    }

    setConnectionStatus({ testing: true, success: null, message: 'جاري اختبار الاتصال...' });

    try {
      console.log(`اختبار الاتصال عبر ${formData.isInternetConnection ? 'الإنترنت' : 'الشبكة المحلية'}: ${formData.integrationUrl}`);

      // استخدام وظيفة اختبار الاتصال الفعلية من سياق التطبيق
      const result = await testIntegrationConnection(
        formData.integrationUrl,
        formData.integrationApiKey,
        formData.isInternetConnection
      );

      setConnectionStatus({
        testing: false,
        success: result.success,
        message: result.message
      });

      if (result.success) {
        showAlert('success', `تم الاتصال بنجاح بمنظومة المبيعات عبر ${formData.isInternetConnection ? 'الإنترنت' : 'الشبكة المحلية'}`);
      }
    } catch (error) {
      console.error('Connection test error:', error);
      setConnectionStatus({
        testing: false,
        success: false,
        message: 'حدث خطأ أثناء اختبار الاتصال: ' + error.message
      });
    }
  };

  // اكتشاف الأجهزة على الشبكة المحلية
  const discoverNetworkDevices = async () => {
    // إذا كان الاتصال عبر الإنترنت، لا يمكن اكتشاف الأجهزة
    if (formData.isInternetConnection) {
      showAlert('warning', 'لا يمكن اكتشاف الأجهزة عند استخدام الاتصال عبر الإنترنت');
      return;
    }

    setDiscoveryStatus({
      discovering: true,
      devices: [],
      error: null
    });

    try {
      // استخدام الشبكة الفرعية الافتراضية 192.168.1
      const devices = await discoverDevices('192.168.1', 3000, 3010);

      setDiscoveryStatus({
        discovering: false,
        devices: devices,
        error: null
      });

      if (devices.length === 0) {
        showAlert('info', 'لم يتم العثور على أجهزة على الشبكة المحلية');
      } else {
        showAlert('success', `تم العثور على ${devices.length} جهاز على الشبكة المحلية`);
      }
    } catch (error) {
      console.error('Device discovery error:', error);

      setDiscoveryStatus({
        discovering: false,
        devices: [],
        error: error.message || 'حدث خطأ أثناء اكتشاف الأجهزة'
      });

      showAlert('danger', 'حدث خطأ أثناء اكتشاف الأجهزة: ' + error.message);
    }
  };

  // استخدام جهاز مكتشف
  const useDiscoveredDevice = (device) => {
    if (!device || !device.ip) {
      return;
    }

    const deviceUrl = device.port ? `${device.ip}:${device.port}` : device.ip;

    setFormData(prev => ({
      ...prev,
      integrationUrl: deviceUrl,
      isInternetConnection: false // تعيين نوع الاتصال إلى الشبكة المحلية عند استخدام جهاز مكتشف
    }));

    showAlert('info', `تم اختيار الجهاز: ${device.name || device.ip}`);
  };

  // تم إزالة وظائف مصادقة Google Drive

  const handleSubmit = (e) => {
    e.preventDefault();

    try {
      console.log('حفظ الإعدادات:', formData);

      // تحديث الإعدادات في السياق (بما في ذلك اسم المنظومة وشعارها)
      const updatedSettings = updateSettings({
        systemName: formData.systemName,
        address: formData.address,
        phone: formData.phone,
        logoUrl: formData.logoUrl,
        lowStockNotification: formData.lowStockNotification,
        defaultMinimumQuantity: parseInt(formData.defaultMinimumQuantity),
        integrationEnabled: formData.integrationEnabled,
        integrationUrl: formData.integrationUrl,
        integrationApiKey: formData.integrationApiKey,
        syncInterval: parseInt(formData.syncInterval),
        isInternetConnection: formData.isInternetConnection
      });

      console.log('تم تحديث الإعدادات:', updatedSettings);

      // إذا كان الربط مع منظومة المبيعات مفعلاً، قم بتشغيل المزامنة
      if (formData.integrationEnabled && formData.integrationUrl && formData.integrationApiKey) {
        console.log('تشغيل المزامنة بعد حفظ الإعدادات');

        // تأخير قصير قبل تشغيل المزامنة لضمان تحديث الإعدادات أولاً
        setTimeout(() => {
          syncWithSalesSystem({
            enabled: true,
            url: formData.integrationUrl,
            apiKey: formData.integrationApiKey,
            isInternet: formData.isInternetConnection
          });
        }, 500);
      }

      // تم إزالة إعدادات النسخ الاحتياطي التلقائي على Google Drive

      showAlert('success', 'تم حفظ الإعدادات بنجاح');
    } catch (err) {
      console.error('Error saving settings:', err);
      showAlert('danger', 'فشل في حفظ الإعدادات: ' + err.message);
    }
  };

  const handleBackup = async () => {
    setBackupInProgress(true);
    showAlert('info', 'جاري إنشاء النسخة الاحتياطية...');

    try {
      // استخدام وظيفة النسخ الاحتياطي من قاعدة البيانات
      const date = new Date().toISOString().split('T')[0];
      const fileName = `warehouse_backup_${date}.json`;

      // إنشاء ملف النسخ الاحتياطي
      const backupData = {
        items: localStorage.getItem('items'),
        inventory: localStorage.getItem('inventory'),
        transactions: localStorage.getItem('transactions'),
        users: localStorage.getItem('users'),
        settings: localStorage.getItem('settings'),
        timestamp: new Date().toISOString()
      };

      // تحويل البيانات إلى JSON
      const jsonData = JSON.stringify(backupData);

      // إنشاء رابط تنزيل
      const blob = new Blob([jsonData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      setBackupInProgress(false);
      showAlert('success', 'تم إنشاء النسخة الاحتياطية بنجاح');
    } catch (error) {
      console.error('Error creating backup:', error);
      setBackupInProgress(false);
      showAlert('danger', 'فشل في إنشاء النسخة الاحتياطية: ' + error.message);
    }
  };

  const handleRestore = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    setRestoreInProgress(true);
    showAlert('info', 'جاري استعادة النسخة الاحتياطية...');

    const reader = new FileReader();
    reader.onload = (event) => {
      try {
        const backupData = JSON.parse(event.target.result);

        // التحقق من صحة ملف النسخة الاحتياطية
        if (!backupData.timestamp) {
          throw new Error('ملف النسخة الاحتياطية غير صالح أو تالف');
        }

        // استعادة البيانات
        if (backupData.items) localStorage.setItem('items', backupData.items);
        if (backupData.inventory) localStorage.setItem('inventory', backupData.inventory);
        if (backupData.transactions) localStorage.setItem('transactions', backupData.transactions);
        if (backupData.users) localStorage.setItem('users', backupData.users);
        if (backupData.settings) localStorage.setItem('settings', backupData.settings);

        setRestoreInProgress(false);
        showAlert('success', 'تم استعادة النسخة الاحتياطية بنجاح. يرجى إعادة تشغيل التطبيق.');

        // إظهار رسالة تأكيد لإعادة تشغيل التطبيق
        setTimeout(() => {
          if (window.confirm('هل ترغب في إعادة تشغيل التطبيق الآن لتطبيق التغييرات؟')) {
            window.location.reload();
          }
        }, 1000);
      } catch (err) {
        console.error('Error restoring backup:', err);
        setRestoreInProgress(false);
        showAlert('danger', 'فشل في استعادة النسخة الاحتياطية: ' + (err.message || 'خطأ غير معروف'));
      }
    };

    reader.readAsText(file);
  };



  const showAlert = (type, message) => {
    if (isMounted.current) {
      setAlert({ show: true, type, message });

      // تنظيف أي مؤقت سابق
      if (alertTimeoutRef.current) {
        clearTimeout(alertTimeoutRef.current);
      }

      // إخفاء التنبيه بعد 3 ثوان
      alertTimeoutRef.current = setTimeout(() => {
        if (isMounted.current) {
          setAlert({ show: false, type: '', message: '' });
          alertTimeoutRef.current = null;
        }
      }, 3000);
    }
  };

  // أنماط CSS للأيقونة الدوارة
  const spinAnimation = `
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `;

  return (
    <div className="settings-page">
      <style>{spinAnimation}</style>
      {/* مكون إدارة علامات التبويب */}
      <TabManager />

      {alert.show && (
        <div className={`alert alert-${alert.type}`}>
          {alert.message}
        </div>
      )}

      <div className="dashboard-welcome">
        <h1>إعدادات النظام</h1>
        <p>قم بتخصيص إعدادات النظام حسب احتياجاتك</p>
      </div>

      <form ref={formRef} onSubmit={handleSubmit}>
        <div className="settings-container">
          {/* إعدادات اسم وشعار المنظومة */}
          <div className="settings-card">
            <h3>
              <FaWarehouse />
              اسم وشعار المنظومة
            </h3>
            <div className="form-group">
              <label className="form-label">اسم المنظومة</label>
              <input
                type="text"
                name="systemName"
                className="form-control"
                value={formData.systemName}
                onChange={(e) => {
                  const newValue = e.target.value;
                  setFormData(prev => ({
                    ...prev,
                    systemName: newValue
                  }));
                  const updatedSettings = {
                    ...settings,
                    systemName: newValue
                  };
                  updateSettings(updatedSettings);
                }}
                placeholder="أدخل اسم المنظومة"
                style={{
                  backgroundColor: '#f9f9f9',
                  fontWeight: 'bold'
                }}
              />
              <small style={{ color: '#7f8c8d', display: 'block', marginTop: '4px', fontSize: '0.85rem' }}>
                سيظهر هذا الاسم في الفواتير والتقارير
              </small>
            </div>

            <div className="form-group">
              <label className="form-label">عنوان المنظومة</label>
              <input
                type="text"
                name="address"
                className="form-control"
                value={formData.address}
                onChange={(e) => {
                  const newValue = e.target.value;
                  setFormData(prev => ({
                    ...prev,
                    address: newValue
                  }));
                  const updatedSettings = {
                    ...settings,
                    address: newValue
                  };
                  updateSettings(updatedSettings);
                }}
                placeholder="أدخل عنوان المنظومة"
              />
              <small style={{ color: '#7f8c8d', display: 'block', marginTop: '4px', fontSize: '0.85rem' }}>
                سيظهر هذا العنوان في الفواتير والتقارير
              </small>
            </div>

            <div className="form-group">
              <label className="form-label">رقم الهاتف</label>
              <input
                type="text"
                name="phone"
                className="form-control"
                value={formData.phone}
                onChange={(e) => {
                  const newValue = e.target.value;
                  setFormData(prev => ({
                    ...prev,
                    phone: newValue
                  }));
                  const updatedSettings = {
                    ...settings,
                    phone: newValue
                  };
                  updateSettings(updatedSettings);
                }}
                placeholder="أدخل رقم الهاتف"
              />
              <small style={{ color: '#7f8c8d', display: 'block', marginTop: '4px', fontSize: '0.85rem' }}>
                سيظهر رقم الهاتف في الفواتير والتقارير
              </small>
            </div>

            <div className="form-group" style={{ marginTop: '16px' }}>
              <label className="form-label">شعار المنظومة</label>
              <div style={{ display: 'flex', alignItems: 'center', gap: '16px', marginTop: '8px' }}>
                <div style={{
                  width: '100px',
                  height: '100px',
                  borderRadius: '8px',
                  border: '1px solid #ddd',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  overflow: 'hidden',
                  backgroundColor: '#f9f9f9'
                }}>
                  {formData.logoUrl ? (
                    <img
                      src={formData.logoUrl}
                      alt="شعار المنظومة"
                      style={{ maxWidth: '100%', maxHeight: '100%' }}
                      onError={(e) => {
                        console.error('فشل في تحميل الصورة في الواجهة');
                        e.target.src = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+PHJlY3Qgd2lkdGg9IjEwMCIgaGVpZ2h0PSIxMDAiIGZpbGw9IiNlZWUiLz48dGV4dCB4PSI1MCIgeT0iNTAiIGZvbnQtc2l6ZT0iMTQiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGFsaWdubWVudC1iYXNlbGluZT0ibWlkZGxlIiBmb250LWZhbWlseT0ic2Fucy1zZXJpZiIgZmlsbD0iIzk5OSI+بدون صورة</dGV4dD48L3N2Zz4=';
                      }}
                      key={formData.logoUrl ? `logo-${Date.now()}` : 'no-image'}
                    />
                  ) : (
                    <FaImage size={32} color="#ccc" />
                  )}
                </div>
                <div>
                  <input
                    type="file"
                    name="logoFile"
                    className="form-control"
                    onChange={handleLogoChange}
                    accept="image/*"
                    style={{ marginBottom: '8px' }}
                  />
                  <small style={{ color: '#7f8c8d', display: 'block', fontSize: '0.85rem' }}>
                    اختر ملف صورة للشعار (يفضل صورة مربعة بحجم 200×200 بكسل)
                  </small>
                </div>
              </div>
            </div>
          </div>

          {/* إعدادات المخزون والمظهر */}
          <div className="settings-card">
            <h3>
              <FaCog />
              إعدادات المخزون والمظهر
            </h3>
            <div className="form-group">
              <label className="form-label">
                الحد الأدنى الافتراضي للمخزون
              </label>
              <input
                type="number"
                name="defaultMinimumQuantity"
                className="form-control"
                value={formData.defaultMinimumQuantity}
                onChange={(e) => {
                  // تحديث حالة النموذج أولاً
                  const newValue = parseInt(e.target.value) || 1;

                  console.log("تغيير الحد الأدنى الافتراضي للمخزون:", newValue);

                  // تحديث حالة النموذج
                  setFormData(prev => ({
                    ...prev,
                    defaultMinimumQuantity: newValue
                  }));

                  // تحديث الإعدادات مباشرة في السياق
                  const updatedSettings = {
                    ...settings,
                    defaultMinimumQuantity: newValue
                  };

                  // استدعاء وظيفة تحديث الإعدادات
                  updateSettings(updatedSettings);
                }}
                min="1"
              />
              <small style={{ color: '#7f8c8d', display: 'block', marginTop: '4px', fontSize: '0.85rem' }}>
                سيتم استخدام هذه القيمة كحد أدنى افتراضي عند إضافة أصناف جديدة
              </small>
            </div>

            <div className="form-group">
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <label className="form-label" style={{ marginBottom: 0, marginLeft: '8px' }}>
                  تفعيل تنبيهات انخفاض المخزون
                </label>
                <label className="toggle-switch">
                  <input
                    type="checkbox"
                    name="lowStockNotification"
                    checked={formData.lowStockNotification}
                    onChange={(e) => {
                      // تحديث حالة النموذج أولاً
                      const newValue = e.target.checked;

                      console.log("تغيير حالة تنبيهات انخفاض المخزون:", newValue);

                      // تحديث حالة النموذج
                      setFormData(prev => ({
                        ...prev,
                        lowStockNotification: newValue
                      }));

                      // تحديث الإعدادات مباشرة في السياق
                      const updatedSettings = {
                        ...settings,
                        lowStockNotification: newValue
                      };

                      // استدعاء وظيفة تحديث الإعدادات
                      updateSettings(updatedSettings);

                      // عرض رسالة للمستخدم
                      showAlert(
                        'success',
                        newValue
                          ? 'تم تفعيل تنبيهات انخفاض المخزون بنجاح'
                          : 'تم إلغاء تفعيل تنبيهات انخفاض المخزون'
                      );
                    }}
                  />
                  <span className="toggle-slider"></span>
                </label>
              </div>
            </div>

            {/* معلومات عن نظام الإضاءة التلقائي */}
            <div className="form-group" style={{ marginTop: '20px' }}>
              <div style={{
                padding: '12px 16px',
                borderRadius: '8px',
                backgroundColor: 'var(--info-light)',
                border: '1px solid var(--info-color)',
                display: 'flex',
                alignItems: 'flex-start',
                gap: '12px'
              }}>
                <div style={{ color: 'var(--info-color)', fontSize: '1.25rem', marginTop: '2px' }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="12" y1="16" x2="12" y2="12"></line>
                    <line x1="12" y1="8" x2="12.01" y2="8"></line>
                  </svg>
                </div>
                <div>
                  <h4 style={{ margin: '0 0 8px 0', color: 'var(--info-color)', fontSize: '1rem' }}>
                    نظام الإضاءة التلقائي
                  </h4>
                  <p style={{ margin: '0', fontSize: '0.9rem', lineHeight: '1.5' }}>
                    تم تفعيل نظام إضاءة تلقائي يعتمد على وقت اليوم لتوفير أفضل مستوى سطوع:
                  </p>
                  <ul style={{ margin: '8px 0 0 0', paddingRight: '20px', fontSize: '0.9rem' }}>
                    <li>الصباح الباكر (5-8): سطوع متوسط</li>
                    <li>النهار (9-16): سطوع عالي</li>
                    <li>المساء (17-20): سطوع متوسط</li>
                    <li>الليل (21-4): سطوع منخفض</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* إعدادات الربط مع منظومة المبيعات عبر الشبكة المحلية أو الإنترنت */}
          <div className="settings-card">
            <h3>
              <FaLink />
              ربط منظومة المبيعات عبر الشبكة المحلية أو الإنترنت
            </h3>
            <div className="form-group">
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
                <label className="form-label" style={{ marginBottom: 0, marginLeft: '8px' }}>
                  تفعيل الربط مع منظومة المبيعات
                </label>
                <label className="toggle-switch">
                  <input
                    type="checkbox"
                    name="integrationEnabled"
                    checked={formData.integrationEnabled}
                    onChange={(e) => {
                      // تحديث حالة النموذج أولاً
                      const newValue = e.target.checked;

                      console.log("تغيير حالة تفعيل الربط:", newValue);

                      // تحديث حالة النموذج
                      setFormData(prev => ({
                        ...prev,
                        integrationEnabled: newValue
                      }));

                      // تحديث الإعدادات مباشرة في السياق
                      const updatedSettings = {
                        ...settings,
                        integrationEnabled: newValue
                      };

                      // استدعاء وظيفة تحديث الإعدادات
                      updateSettings(updatedSettings);

                      // طباعة الإعدادات المحدثة للتأكد
                      console.log("الإعدادات المحدثة:", updatedSettings);

                      // تفعيل/إلغاء تفعيل المزامنة مباشرة عند تغيير الحالة
                      if (formData.integrationUrl && formData.integrationApiKey) {
                        console.log("محاولة تفعيل/إلغاء تفعيل المزامنة:", newValue);

                        // استدعاء وظيفة المزامنة مع تمرير الخيارات
                        syncWithSalesSystem({
                          enabled: newValue,
                          url: formData.integrationUrl,
                          apiKey: formData.integrationApiKey,
                          isInternet: formData.isInternetConnection
                        });

                        // عرض رسالة للمستخدم
                        showAlert(
                          'success',
                          newValue
                            ? 'تم تفعيل الربط مع منظومة المبيعات بنجاح'
                            : 'تم إلغاء تفعيل الربط مع منظومة المبيعات'
                        );
                      } else if (newValue) {
                        // إذا كان المستخدم يحاول تفعيل الربط ولكن البيانات غير مكتملة
                        showAlert('warning', 'يرجى إدخال عنوان IP/المضيف ومفتاح API قبل تفعيل الربط');
                      }
                    }}
                  />
                  <span className="toggle-slider"></span>
                </label>
              </div>

              {formData.integrationEnabled && (
                <>
                  <div className="form-group">
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                      <label className="form-label" style={{ margin: 0 }}>عنوان URL أو اسم المضيف</label>
                      <button
                        type="button"
                        className="btn btn-sm"
                        onClick={discoverNetworkDevices}
                        disabled={discoveryStatus.discovering || formData.isInternetConnection}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '4px',
                          padding: '4px 8px',
                          backgroundColor: '#3498db',
                          color: 'white',
                          border: 'none',
                          borderRadius: '4px',
                          fontSize: '0.8rem',
                          opacity: formData.isInternetConnection ? 0.5 : 1
                        }}
                      >
                        {discoveryStatus.discovering ? (
                          <>
                            <FaSync style={{ animation: 'spin 1s linear infinite' }} />
                            جاري البحث...
                          </>
                        ) : (
                          <>
                            <FaSearch />
                            اكتشاف الأجهزة
                          </>
                        )}
                      </button>
                    </div>

                    <input
                      type="text"
                      name="integrationUrl"
                      className="form-control"
                      value={formData.integrationUrl}
                      onChange={handleInputChange}
                      placeholder="مثال: ************* أو https://sales-api.example.com"
                    />

                    <div style={{ marginTop: '8px', display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <input
                          type="radio"
                          id="connectionTypeLocal"
                          name="connectionType"
                          value="local"
                          checked={!formData.isInternetConnection}
                          onChange={() => {
                            setFormData(prev => ({
                              ...prev,
                              isInternetConnection: false
                            }));
                          }}
                          style={{ marginLeft: '4px' }}
                        />
                        <label htmlFor="connectionTypeLocal" style={{ margin: 0, cursor: 'pointer' }}>
                          شبكة محلية (LAN)
                        </label>
                      </div>
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <input
                          type="radio"
                          id="connectionTypeInternet"
                          name="connectionType"
                          value="internet"
                          checked={formData.isInternetConnection}
                          onChange={() => {
                            setFormData(prev => ({
                              ...prev,
                              isInternetConnection: true
                            }));
                          }}
                          style={{ marginLeft: '4px' }}
                        />
                        <label htmlFor="connectionTypeInternet" style={{ margin: 0, cursor: 'pointer' }}>
                          شبكة دولية (إنترنت)
                        </label>
                      </div>
                    </div>

                    {/* عرض الأجهزة المكتشفة */}
                    {discoveryStatus.devices.length > 0 && (
                      <div style={{
                        marginTop: '8px',
                        border: '1px solid #e0e0e0',
                        borderRadius: '4px',
                        maxHeight: '150px',
                        overflowY: 'auto'
                      }}>
                        <div style={{ padding: '8px', backgroundColor: '#f8f9fa', borderBottom: '1px solid #e0e0e0' }}>
                          <strong>الأجهزة المكتشفة:</strong>
                        </div>
                        <ul style={{ listStyle: 'none', margin: 0, padding: 0 }}>
                          {discoveryStatus.devices.map((device, index) => (
                            <li
                              key={index}
                              style={{
                                padding: '8px 12px',
                                borderBottom: index < discoveryStatus.devices.length - 1 ? '1px solid #e0e0e0' : 'none',
                                cursor: 'pointer',
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center'
                              }}
                              onClick={() => useDiscoveredDevice(device)}
                            >
                              <div>
                                <strong>{device.name || 'جهاز غير معروف'}</strong>
                                <div style={{ fontSize: '0.8rem', color: '#7f8c8d' }}>
                                  {device.ip}:{device.port}
                                </div>
                              </div>
                              <button
                                type="button"
                                style={{
                                  backgroundColor: '#27ae60',
                                  color: 'white',
                                  border: 'none',
                                  borderRadius: '4px',
                                  padding: '4px 8px',
                                  fontSize: '0.8rem'
                                }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  useDiscoveredDevice(device);
                                }}
                              >
                                استخدام
                              </button>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    <small style={{ color: '#7f8c8d', display: 'block', marginTop: '4px', fontSize: '0.85rem' }}>
                      أدخل عنوان URL أو اسم المضيف لمنظومة المبيعات
                      <br />
                      <strong>ملاحظات للشبكة المحلية (LAN):</strong>
                      <ul style={{ marginTop: '5px', paddingRight: '20px', marginBottom: '5px' }}>
                        <li>يمكنك إدخال عنوان IP مثل: <code>*************</code></li>
                        <li>أو اسم المضيف مثل: <code>sales-server</code></li>
                        <li>يمكنك تحديد المنفذ مثل: <code>*************:3000</code></li>
                        <li>إذا لم تحدد المنفذ، سيتم استخدام المنفذ الافتراضي 3000</li>
                        <li>مهلة الاتصال: 10 ثوانٍ</li>
                      </ul>
                      <strong>ملاحظات للشبكة الدولية (الإنترنت):</strong>
                      <ul style={{ marginTop: '5px', paddingRight: '20px', marginBottom: '5px' }}>
                        <li>يمكنك إدخال عنوان URL مثل: <code>https://sales-api.example.com</code></li>
                        <li>يفضل استخدام HTTPS للاتصال الآمن عبر الإنترنت</li>
                        <li>يمكنك تحديد المنفذ مثل: <code>https://sales-api.example.com:443</code></li>
                        <li>مهلة الاتصال: 30 ثانية</li>
                      </ul>
                      <strong>للاختبار:</strong> يمكنك استخدام الخادم المحلي: <code>localhost:3001</code>
                    </small>
                  </div>

                  <div className="form-group">
                    <label className="form-label">مفتاح API</label>
                    <input
                      type="password"
                      name="integrationApiKey"
                      className="form-control"
                      value={formData.integrationApiKey}
                      onChange={handleInputChange}
                      placeholder="أدخل مفتاح API للوصول إلى منظومة المبيعات"
                    />
                    <small style={{ color: '#7f8c8d', display: 'block', marginTop: '4px', fontSize: '0.85rem' }}>
                      مفتاح API المستخدم للمصادقة مع منظومة المبيعات
                      <br />
                      <strong>للاختبار:</strong> استخدم مفتاح API: <code>test-api-key-123</code>
                    </small>
                  </div>

                  <div className="form-group">
                    <label className="form-label">فترة المزامنة (بالدقائق)</label>
                    <input
                      type="number"
                      name="syncInterval"
                      className="form-control"
                      value={formData.syncInterval}
                      onChange={handleInputChange}
                      min="5"
                      max="1440"
                    />
                    <small style={{ color: '#7f8c8d', display: 'block', marginTop: '4px', fontSize: '0.85rem' }}>
                      الفترة الزمنية بين عمليات المزامنة التلقائية (5 دقائق على الأقل)
                    </small>
                  </div>

                  <div className="form-group">
                    <button
                      type="button"
                      className="btn btn-info"
                      onClick={testConnection}
                      disabled={connectionStatus.testing || !formData.integrationUrl || !formData.integrationApiKey}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px',
                        padding: '8px 16px',
                        backgroundColor: 'var(--secondary-color)',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        transition: 'all 0.2s'
                      }}
                    >
                      {connectionStatus.testing ? (
                        <>
                          <FaSync style={{ animation: 'spin 1s linear infinite' }} />
                          جاري اختبار الاتصال...
                        </>
                      ) : (
                        <>
                          <FaLink />
                          اختبار الاتصال
                        </>
                      )}
                    </button>

                    {connectionStatus.success !== null && !connectionStatus.testing && (
                      <div style={{
                        marginTop: '8px',
                        padding: '8px 12px',
                        borderRadius: '4px',
                        backgroundColor: connectionStatus.success ? 'rgba(46, 204, 113, 0.1)' : 'rgba(231, 76, 60, 0.1)',
                        color: connectionStatus.success ? '#27ae60' : '#c0392b',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px'
                      }}>
                        {connectionStatus.success ? (
                          <FaCheck style={{ color: '#27ae60' }} />
                        ) : (
                          <FaTimes style={{ color: '#c0392b' }} />
                        )}
                        {connectionStatus.message}
                      </div>
                    )}
                  </div>

                  {/* حالة المزامنة */}
                  <div className="form-group" style={{ marginTop: '16px' }}>
                    <h4 style={{ fontSize: '1rem', marginBottom: '8px' }}>حالة المزامنة</h4>
                    <div style={{
                      padding: '16px',
                      borderRadius: '8px',
                      backgroundColor: 'rgba(38, 56, 69, 0.05)',
                      border: '1px solid rgba(38, 56, 69, 0.2)'
                    }}>
                      {/* مؤشر حالة المزامنة */}
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginBottom: '16px',
                        padding: '12px',
                        borderRadius: '8px',
                        backgroundColor: syncStatus.syncing
                          ? 'rgba(52, 152, 219, 0.1)'
                          : syncStatus.lastSyncStatus === 'success'
                            ? 'rgba(46, 204, 113, 0.1)'
                            : syncStatus.lastSyncStatus === 'error'
                              ? 'rgba(231, 76, 60, 0.1)'
                              : 'rgba(189, 195, 199, 0.1)',
                        border: `1px solid ${syncStatus.syncing
                          ? 'rgba(52, 152, 219, 0.3)'
                          : syncStatus.lastSyncStatus === 'success'
                            ? 'rgba(46, 204, 113, 0.3)'
                            : syncStatus.lastSyncStatus === 'error'
                              ? 'rgba(231, 76, 60, 0.3)'
                              : 'rgba(189, 195, 199, 0.3)'}`
                      }}>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '12px'
                        }}>
                          {syncStatus.syncing ? (
                            <FaSync style={{
                              animation: 'spin 1s linear infinite',
                              color: 'var(--primary-color)',
                              fontSize: '24px'
                            }} />
                          ) : syncStatus.lastSyncStatus === 'success' ? (
                            <FaCheck style={{
                              color: '#27ae60',
                              fontSize: '24px'
                            }} />
                          ) : syncStatus.lastSyncStatus === 'error' ? (
                            <FaTimes style={{
                              color: '#c0392b',
                              fontSize: '24px'
                            }} />
                          ) : (
                            <FaSync style={{
                              color: '#7f8c8d',
                              fontSize: '24px'
                            }} />
                          )}
                          <div>
                            <div style={{
                              fontWeight: 'bold',
                              color: syncStatus.syncing
                                ? 'var(--primary-color)'
                                : syncStatus.lastSyncStatus === 'success'
                                  ? '#27ae60'
                                  : syncStatus.lastSyncStatus === 'error'
                                    ? '#c0392b'
                                    : '#7f8c8d'
                            }}>
                              {syncStatus.syncing
                                ? 'جاري المزامنة...'
                                : syncStatus.lastSyncStatus === 'success'
                                  ? 'تمت المزامنة بنجاح'
                                  : syncStatus.lastSyncStatus === 'error'
                                    ? 'فشلت المزامنة'
                                    : 'لم تتم المزامنة بعد'}
                            </div>
                            <div style={{
                              fontSize: '0.85rem',
                              color: '#7f8c8d',
                              marginTop: '4px'
                            }}>
                              {syncStatus.lastSyncTime ?
                                `آخر مزامنة: ${new Date(syncStatus.lastSyncTime).toLocaleString('ar-LY')}` :
                                'لم تتم المزامنة بعد'}
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* رسالة الخطأ */}
                      {syncStatus.error && (
                        <div style={{
                          marginTop: '8px',
                          marginBottom: '16px',
                          padding: '12px',
                          backgroundColor: 'rgba(231, 76, 60, 0.1)',
                          borderRadius: '8px',
                          color: '#c0392b',
                          display: 'flex',
                          alignItems: 'flex-start',
                          gap: '8px'
                        }}>
                          <FaExclamationTriangle style={{ marginTop: '2px' }} />
                          <div>
                            <strong>خطأ في المزامنة:</strong>
                            <div style={{ marginTop: '4px' }}>{syncStatus.error}</div>
                          </div>
                        </div>
                      )}

                      {/* زر المزامنة اليدوية */}
                      <div style={{ marginTop: '12px', display: 'flex', justifyContent: 'center' }}>
                        <button
                          type="button"
                          className="btn btn-secondary"
                          onClick={() => syncWithSalesSystem({
                            enabled: true,
                            url: formData.integrationUrl,
                            apiKey: formData.integrationApiKey,
                            isInternet: formData.isInternetConnection
                          })}
                          disabled={syncStatus.syncing || !formData.integrationUrl || !formData.integrationApiKey}
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '8px',
                            padding: '10px 20px',
                            backgroundColor: 'var(--secondary-color)',
                            color: 'white',
                            border: 'none',
                            borderRadius: '4px',
                            cursor: 'pointer',
                            fontSize: '1rem',
                            opacity: syncStatus.syncing || !formData.integrationUrl || !formData.integrationApiKey ? 0.7 : 1
                          }}
                        >
                          {syncStatus.syncing ? (
                            <>
                              <FaSync style={{ animation: 'spin 1s linear infinite' }} />
                              جاري المزامنة...
                            </>
                          ) : (
                            <>
                              <FaSync />
                              مزامنة يدوية
                            </>
                          )}
                        </button>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* تحديثات النظام وقاعدة البيانات */}
          <div className="settings-card">
            <h3>
              <FaDownload />
              تحديثات النظام وقاعدة البيانات
            </h3>
            <UpdateManager />
          </div>

          {/* النسخ الاحتياطي والاستعادة */}
          <div className="settings-card backup-card">
            <h3>
              <FaDatabase />
              النسخ الاحتياطي والاستعادة
            </h3>

            {/* علامات التبويب للنسخ الاحتياطي */}
            <ul className="nav nav-tabs" style={{ marginBottom: '20px' }}>
              <li className="nav-item">
                <a className="nav-link active" data-toggle="tab" href="#local-backup">
                  <FaDatabase className="ml-1" />
                  النسخ الاحتياطي المحلي
                </a>
              </li>
            </ul>

            {/* محتوى علامات التبويب */}
            <div className="tab-content">
              {/* النسخ الاحتياطي المحلي */}
              <div className="tab-pane fade show active" id="local-backup">
                <div className="backup-options">
                  <div className="backup-option backup-option-export">
                    <div style={{
                      width: '64px',
                      height: '64px',
                      borderRadius: '50%',
                      backgroundColor: backupInProgress ? 'rgba(52, 152, 219, 0.1)' : 'rgba(38, 56, 69, 0.1)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginBottom: '16px',
                      color: 'var(--primary-color)',
                      transition: 'all 0.3s'
                    }}>
                      {backupInProgress ? (
                        <FaSync size={24} style={{ animation: 'spin 1s linear infinite' }} />
                      ) : (
                        <FaFileExport size={24} />
                      )}
                    </div>
                    <h4 style={{ fontSize: '1.1rem', marginBottom: '8px' }}>إنشاء نسخة احتياطية</h4>
                    <p style={{ marginBottom: '16px', color: '#7f8c8d' }}>
                      قم بإنشاء نسخة احتياطية من جميع بيانات النظام وحفظها على جهازك
                    </p>
                    <div style={{ marginBottom: '16px', color: '#7f8c8d', fontSize: '0.85rem' }}>
                      <strong>ملاحظة:</strong> يتم حفظ جميع البيانات في ملف JSON يمكن استخدامه لاستعادة النظام لاحقًا
                    </div>
                    <button
                      type="button"
                      className="btn btn-primary"
                      onClick={handleBackup}
                      disabled={backupInProgress}
                      style={{
                        padding: '10px 20px',
                        borderRadius: '4px',
                        backgroundColor: 'var(--primary-color)',
                        color: 'white',
                        border: 'none',
                        cursor: backupInProgress ? 'not-allowed' : 'pointer',
                        transition: 'all 0.2s',
                        opacity: backupInProgress ? 0.7 : 1,
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px'
                      }}
                    >
                      {backupInProgress ? (
                        <>
                          <FaSync style={{ animation: 'spin 1s linear infinite' }} />
                          جاري إنشاء النسخة الاحتياطية...
                        </>
                      ) : (
                        <>
                          <FaFileExport />
                          إنشاء نسخة احتياطية
                        </>
                      )}
                    </button>
                  </div>

                  <div className="backup-option backup-option-import">
                    <div style={{
                      width: '64px',
                      height: '64px',
                      borderRadius: '50%',
                      backgroundColor: restoreInProgress ? 'rgba(52, 152, 219, 0.1)' : 'rgba(232, 122, 77, 0.1)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginBottom: '16px',
                      color: restoreInProgress ? 'var(--primary-color)' : 'var(--accent-color)',
                      transition: 'all 0.3s'
                    }}>
                      {restoreInProgress ? (
                        <FaSync size={24} style={{ animation: 'spin 1s linear infinite' }} />
                      ) : (
                        <FaFileImport size={24} />
                      )}
                    </div>
                    <h4 style={{ fontSize: '1.1rem', marginBottom: '8px' }}>استعادة نسخة احتياطية</h4>
                    <p style={{ marginBottom: '16px', color: '#7f8c8d' }}>
                      قم باستعادة بيانات النظام من نسخة احتياطية سابقة
                    </p>
                    <div style={{ marginBottom: '16px', color: '#7f8c8d', fontSize: '0.85rem' }}>
                      <strong>تنبيه:</strong> سيتم استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية
                    </div>
                    <div style={{ position: 'relative' }}>
                      <label style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px',
                        padding: '10px 20px',
                        borderRadius: '4px',
                        backgroundColor: 'var(--accent-color)',
                        color: 'white',
                        cursor: restoreInProgress ? 'not-allowed' : 'pointer',
                        transition: 'all 0.2s',
                        opacity: restoreInProgress ? 0.7 : 1
                      }}>
                        {restoreInProgress ? (
                          <>
                            <FaSync style={{ animation: 'spin 1s linear infinite' }} />
                            جاري استعادة النسخة الاحتياطية...
                          </>
                        ) : (
                          <>
                            <FaFileImport />
                            اختر ملف النسخة الاحتياطية
                          </>
                        )}
                        <input
                          type="file"
                          accept=".json"
                          onChange={handleRestore}
                          disabled={restoreInProgress}
                          style={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            opacity: 0,
                            width: '100%',
                            height: '100%',
                            cursor: restoreInProgress ? 'not-allowed' : 'pointer'
                          }}
                        />
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              {/* تم إزالة قسم النسخ الاحتياطي على Google Drive */}
            </div>
          </div>
        </div>

        <div className="settings-save-container">
          <button
            type="submit"
            className="settings-save-button"
          >
            <FaSave />
            حفظ الإعدادات
          </button>
        </div>
      </form>
    </div>
  );
};

export default Settings;
