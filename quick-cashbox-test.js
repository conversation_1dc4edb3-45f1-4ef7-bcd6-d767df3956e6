/**
 * اختبار سريع لآلية خصم مبالغ المشتريات من الرصيد الحالي
 * 
 * هذا السكريبت يقوم باختبار سريع للتأكد من عمل الإصلاحات
 */

console.log('⚡ اختبار سريع لآلية خصم مبالغ المشتريات من الرصيد الحالي...');

// التحقق من وجود window.api
if (typeof window !== 'undefined' && window.api) {
  
  // اختبار سريع
  async function quickCashboxTest() {
    try {
      console.log('🚀 بدء الاختبار السريع...');
      
      // 1. تعيين رصيد ابتدائي
      console.log('\n1️⃣ تعيين رصيد ابتدائي (5000)...');
      const initialResult = await window.api.cashbox.updateInitialBalance(5000);
      if (initialResult.success) {
        console.log(`✅ الرصيد الابتدائي: ${initialResult.cashbox.initial_balance}`);
        console.log(`✅ الرصيد الحالي: ${initialResult.cashbox.current_balance}`);
      } else {
        console.error('❌ فشل في تعيين الرصيد الابتدائي');
        return;
      }
      
      // 2. اختبار عملية شراء
      console.log('\n2️⃣ اختبار عملية شراء (1000)...');
      const beforePurchase = await window.api.cashbox.getCashbox();
      console.log(`📊 الرصيد قبل الشراء: ${beforePurchase.current_balance}`);
      
      const purchaseResult = await window.api.cashbox.addTransaction({
        type: 'purchase',
        amount: 1000,
        source: 'test',
        notes: 'اختبار شراء سريع'
      });
      
      if (purchaseResult.success) {
        console.log(`📊 الرصيد بعد الشراء: ${purchaseResult.cashbox.current_balance}`);
        console.log(`📊 إجمالي المشتريات: ${purchaseResult.cashbox.purchases_total}`);
        
        const expectedBalance = 5000 - 1000; // 4000
        if (purchaseResult.cashbox.current_balance === expectedBalance) {
          console.log('✅ تم خصم مبلغ الشراء من الرصيد الحالي بنجاح!');
        } else {
          console.error(`❌ خطأ في خصم مبلغ الشراء! المتوقع: ${expectedBalance}, الفعلي: ${purchaseResult.cashbox.current_balance}`);
        }
      } else {
        console.error('❌ فشل في عملية الشراء');
        return;
      }
      
      // 3. اختبار عملية بيع
      console.log('\n3️⃣ اختبار عملية بيع (800)...');
      const beforeSale = await window.api.cashbox.getCashbox();
      console.log(`📊 الرصيد قبل البيع: ${beforeSale.current_balance}`);
      
      const saleResult = await window.api.cashbox.addTransaction({
        type: 'sale',
        amount: 800,
        source: 'test',
        notes: 'اختبار بيع سريع'
      });
      
      if (saleResult.success) {
        console.log(`📊 الرصيد بعد البيع: ${saleResult.cashbox.current_balance}`);
        console.log(`📊 إجمالي المبيعات: ${saleResult.cashbox.sales_total}`);
        
        const expectedBalance = 4000 + 800; // 4800
        if (saleResult.cashbox.current_balance === expectedBalance) {
          console.log('✅ تم إضافة مبلغ البيع للرصيد الحالي بنجاح!');
        } else {
          console.error(`❌ خطأ في إضافة مبلغ البيع! المتوقع: ${expectedBalance}, الفعلي: ${saleResult.cashbox.current_balance}`);
        }
      } else {
        console.error('❌ فشل في عملية البيع');
        return;
      }
      
      // 4. النتيجة النهائية
      console.log('\n📊 النتيجة النهائية:');
      const finalCashbox = await window.api.cashbox.getCashbox();
      console.log(`الرصيد الابتدائي: ${finalCashbox.initial_balance}`);
      console.log(`الرصيد الحالي: ${finalCashbox.current_balance}`);
      console.log(`إجمالي المبيعات: ${finalCashbox.sales_total}`);
      console.log(`إجمالي المشتريات: ${finalCashbox.purchases_total}`);
      console.log(`إجمالي الأرباح: ${finalCashbox.profit_total}`);
      
      // التحقق من صحة الحسابات
      const expectedFinalBalance = 5000 - 1000 + 800; // 4800
      const expectedProfit = 800 - 1000; // -200
      
      if (finalCashbox.current_balance === expectedFinalBalance && finalCashbox.profit_total === expectedProfit) {
        console.log('\n🎉 الاختبار السريع نجح! آلية خصم مبالغ المشتريات تعمل بشكل صحيح');
      } else {
        console.log('\n⚠️ هناك مشكلة في الحسابات. يرجى مراجعة الإصلاحات');
      }
      
    } catch (error) {
      console.error('❌ خطأ في الاختبار السريع:', error);
    }
  }
  
  // تشغيل الاختبار
  quickCashboxTest();
  
} else {
  console.error('❌ window.api غير متوفر');
  console.log('💡 يجب تشغيل هذا السكريبت من داخل التطبيق');
  console.log('');
  console.log('📋 لتشغيل الاختبار السريع:');
  console.log('1. افتح التطبيق');
  console.log('2. افتح وحدة التحكم (F12)');
  console.log('3. انسخ والصق الكود التالي:');
  console.log('');
  console.log('// اختبار سريع');
  console.log('(async () => {');
  console.log('  const initial = await window.api.cashbox.updateInitialBalance(5000);');
  console.log('  console.log("رصيد ابتدائي:", initial.cashbox.current_balance);');
  console.log('  ');
  console.log('  const purchase = await window.api.cashbox.addTransaction({type: "purchase", amount: 1000, source: "test"});');
  console.log('  console.log("بعد الشراء:", purchase.cashbox.current_balance, "متوقع: 4000");');
  console.log('  ');
  console.log('  const sale = await window.api.cashbox.addTransaction({type: "sale", amount: 800, source: "test"});');
  console.log('  console.log("بعد البيع:", sale.cashbox.current_balance, "متوقع: 4800");');
  console.log('})();');
}
