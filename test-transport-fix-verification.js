/**
 * اختبار التحقق من إصلاح منطق مصاريف النقل
 * 
 * هذا الملف يختبر أن مصاريف النقل:
 * 1. في عمليات الشراء: تخصم من الرصيد الحالي فقط
 * 2. في عمليات البيع: لا تخصم من الأرباح مرة أخرى
 */

const { calculateProfit, calculateProfitWithTransport } = require('./src/utils/profitCalculator');

console.log('🧪 بدء اختبار التحقق من إصلاح منطق مصاريف النقل...\n');

// اختبار 1: حساب الربح الأساسي بدون مصاريف النقل
console.log('📋 اختبار 1: حساب الربح الأساسي');
const sellingPrice = 1200;
const avgPrice = 1000;
const quantity = 1;

const basicProfit = calculateProfit(sellingPrice, avgPrice, quantity);
console.log(`سعر البيع: ${sellingPrice}`);
console.log(`متوسط سعر الشراء: ${avgPrice}`);
console.log(`الكمية: ${quantity}`);
console.log(`الربح المحسوب: ${basicProfit}`);
console.log(`المتوقع: ${(sellingPrice - avgPrice) * quantity}`);
console.log(`✅ النتيجة: ${basicProfit === 200 ? 'نجح' : 'فشل'}\n`);

// اختبار 2: سيناريو المشكلة الأصلية
console.log('📋 اختبار 2: سيناريو المشكلة الأصلية');
console.log('السيناريو: شراء قطعة واحدة بمصاريف نقل، ثم بيعها');
console.log('');

// عملية الشراء
const purchasePrice = 1000;
const purchaseQuantity = 1;
const transportCost = 50;
const totalPurchaseCost = purchasePrice + transportCost;

console.log('🛒 عملية الشراء:');
console.log(`سعر الشراء: ${purchasePrice}`);
console.log(`مصاريف النقل: ${transportCost}`);
console.log(`إجمالي التكلفة: ${totalPurchaseCost}`);
console.log(`تأثير على الرصيد الحالي: -${totalPurchaseCost}`);
console.log(`تأثير على الأرباح: 0 (لا تؤثر)`);
console.log('');

// عملية البيع
const salePrice = 1200;
const saleQuantity = 1;

console.log('💰 عملية البيع (الطريقة الجديدة المصححة):');
console.log(`سعر البيع: ${salePrice}`);
console.log(`متوسط سعر الشراء: ${purchasePrice}`);
console.log(`الكمية: ${saleQuantity}`);

const correctProfit = calculateProfit(salePrice, purchasePrice, saleQuantity);
console.log(`الربح المحسوب (بدون خصم مصاريف النقل مرة أخرى): ${correctProfit}`);
console.log(`تأثير على الرصيد الحالي: +${salePrice * saleQuantity}`);
console.log(`تأثير على الأرباح: +${correctProfit}`);
console.log('');

console.log('📊 النتيجة النهائية:');
console.log(`إجمالي الربح الصحيح: ${correctProfit}`);
console.log(`مصاريف النقل تم خصمها من الرصيد الحالي في عملية الشراء`);
console.log(`ولم تخصم مرة أخرى من الأرباح في عملية البيع`);
console.log('');

// اختبار 3: مقارنة مع الطريقة القديمة الخاطئة
console.log('📋 اختبار 3: مقارنة مع الطريقة القديمة الخاطئة');
const oldWayProfit = calculateProfitWithTransport(salePrice, purchasePrice, saleQuantity, transportCost);
console.log(`الطريقة القديمة (خاطئة): ${oldWayProfit}`);
console.log(`الطريقة الجديدة (صحيحة): ${correctProfit}`);
console.log(`الفرق: ${correctProfit - oldWayProfit}`);
console.log(`✅ الإصلاح: ${correctProfit > oldWayProfit ? 'نجح - الربح أعلى الآن' : 'فشل'}\n`);

console.log('🎉 انتهى الاختبار!');
console.log('');
console.log('📝 ملخص الإصلاح:');
console.log('1. ✅ مصاريف النقل في عمليات الشراء تخصم من الرصيد الحالي فقط');
console.log('2. ✅ مصاريف النقل لا تخصم مرة أخرى من الأرباح في عمليات البيع');
console.log('3. ✅ حساب الأرباح أصبح أكثر دقة ووضوحاً');
console.log('4. ✅ تم إزالة الخصم المزدوج لمصاريف النقل');
