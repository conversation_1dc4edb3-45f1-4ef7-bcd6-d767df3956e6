import React, { useState, useEffect } from 'react';
import './ProfitTestPage.css';

const ProfitTestPage = () => {
  const [cashboxData, setCashboxData] = useState(null);
  const [salesData, setSalesData] = useState([]);
  const [fixResults, setFixResults] = useState(null);
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(false);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString('ar-EG');
    const newLog = { timestamp, message, type };
    setLogs(prev => [...prev, newLog]);
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const loadCashboxData = async () => {
    try {
      setLoading(true);
      addLog('🔄 تحميل بيانات الخزينة...');
      
      const cashbox = await window.api.getCashbox();
      
      if (cashbox.exists) {
        setCashboxData(cashbox);
        addLog(`✅ تم تحميل بيانات الخزينة - الأرباح الحالية: ${cashbox.profit_total}`, 'success');
      } else {
        addLog('❌ لا توجد بيانات خزينة', 'error');
      }
    } catch (error) {
      addLog(`❌ خطأ في تحميل بيانات الخزينة: ${error.message}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  const checkSalesTransactions = async () => {
    try {
      setLoading(true);
      addLog('🔍 فحص معاملات البيع...');
      
      const transactions = await window.api.getTransactionsWithFilters({});
      const salesTransactions = transactions.filter(t => t.transaction_type === 'sale');
      
      addLog(`📊 عدد معاملات البيع: ${salesTransactions.length}`);
      
      if (salesTransactions.length > 0) {
        const processedSales = salesTransactions.slice(0, 5).map(transaction => {
          const expectedProfit = Math.max(0, (transaction.selling_price - transaction.price) * transaction.quantity);
          const recordedProfit = transaction.profit || 0;
          const isCorrect = Math.abs(recordedProfit - expectedProfit) < 0.01;
          
          addLog(`${isCorrect ? '✅' : '⚠️'} معاملة ${transaction.id}: ربح مسجل ${recordedProfit}, متوقع ${expectedProfit}`);
          
          return {
            ...transaction,
            expectedProfit,
            recordedProfit,
            isCorrect
          };
        });
        
        setSalesData(processedSales);
        
        const totalExpected = processedSales.reduce((sum, t) => sum + t.expectedProfit, 0);
        const totalRecorded = processedSales.reduce((sum, t) => sum + t.recordedProfit, 0);
        
        addLog(`📈 إجمالي الربح المتوقع: ${totalExpected}, المسجل: ${totalRecorded}`);
      } else {
        addLog('📭 لا توجد معاملات بيع', 'info');
        setSalesData([]);
      }
    } catch (error) {
      addLog(`❌ خطأ في فحص معاملات البيع: ${error.message}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  const fixProfits = async () => {
    try {
      setLoading(true);
      addLog('🛠️ بدء إصلاح الأرباح...');
      
      const result = await window.api.updateProfitValues();
      
      if (result.success) {
        setFixResults(result);
        addLog(`✅ تم إصلاح ${result.updatedCount} معاملة - الأرباح الجديدة: ${result.totalProfit}`, 'success');
      } else {
        addLog(`❌ فشل في إصلاح الأرباح: ${result.error}`, 'error');
      }
    } catch (error) {
      addLog(`❌ خطأ في إصلاح الأرباح: ${error.message}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  const checkAfterFix = async () => {
    try {
      addLog('🔍 التحقق من النتائج بعد الإصلاح...');
      
      const updatedCashbox = await window.api.getCashbox();
      
      if (updatedCashbox.exists && cashboxData) {
        const profitDifference = updatedCashbox.profit_total - cashboxData.profit_total;
        
        addLog(`📊 مقارنة النتائج: قبل ${cashboxData.profit_total} | بعد ${updatedCashbox.profit_total} | التغيير ${profitDifference}`, 'success');
        
        // تحديث البيانات الحالية
        setCashboxData(updatedCashbox);
      } else {
        addLog('❌ لا يمكن مقارنة النتائج - تأكد من تحميل بيانات الخزينة أولاً', 'error');
      }
    } catch (error) {
      addLog(`❌ خطأ في التحقق من النتائج: ${error.message}`, 'error');
    }
  };

  useEffect(() => {
    addLog('🚀 تم تحميل أداة اختبار إصلاح الأرباح');
    loadCashboxData();
  }, []);

  return (
    <div className="profit-test-page">
      <div className="page-header">
        <h1>🔧 اختبار إصلاح الأرباح</h1>
        <p>أداة لاختبار وإصلاح حساب الأرباح في النظام</p>
      </div>

      <div className="test-sections">
        {/* قسم بيانات الخزينة */}
        <div className="test-section">
          <h3>📊 بيانات الخزينة الحالية</h3>
          <button 
            className="test-button" 
            onClick={loadCashboxData}
            disabled={loading}
          >
            تحميل بيانات الخزينة
          </button>
          
          {cashboxData && (
            <div className="cashbox-data">
              <div className="data-item">
                <span>الرصيد الافتتاحي:</span>
                <span>{cashboxData.initial_balance}</span>
              </div>
              <div className="data-item">
                <span>الرصيد الحالي:</span>
                <span>{cashboxData.current_balance}</span>
              </div>
              <div className="data-item highlight">
                <span>إجمالي الأرباح:</span>
                <span>{cashboxData.profit_total}</span>
              </div>
              <div className="data-item">
                <span>إجمالي المبيعات:</span>
                <span>{cashboxData.sales_total}</span>
              </div>
              <div className="data-item">
                <span>إجمالي المشتريات:</span>
                <span>{cashboxData.purchases_total}</span>
              </div>
            </div>
          )}
        </div>

        {/* قسم فحص المعاملات */}
        <div className="test-section">
          <h3>🔍 فحص معاملات البيع</h3>
          <button 
            className="test-button" 
            onClick={checkSalesTransactions}
            disabled={loading}
          >
            فحص معاملات البيع
          </button>
          
          {salesData.length > 0 && (
            <div className="sales-data">
              {salesData.map((transaction, index) => (
                <div key={transaction.id} className={`transaction-item ${transaction.isCorrect ? 'correct' : 'incorrect'}`}>
                  <span>{transaction.isCorrect ? '✅' : '⚠️'}</span>
                  <span>معاملة {index + 1}</span>
                  <span>مسجل: {transaction.recordedProfit}</span>
                  <span>متوقع: {transaction.expectedProfit}</span>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* قسم الإصلاح */}
        <div className="test-section">
          <h3>🛠️ إصلاح الأرباح</h3>
          <div className="button-group">
            <button 
              className="test-button success" 
              onClick={fixProfits}
              disabled={loading}
            >
              إصلاح الأرباح
            </button>
            <button 
              className="test-button" 
              onClick={checkAfterFix}
              disabled={loading}
            >
              التحقق بعد الإصلاح
            </button>
          </div>
          
          {fixResults && (
            <div className="fix-results">
              <div className="result-item success">
                <span>✅ تم إصلاح الأرباح بنجاح!</span>
              </div>
              <div className="result-item">
                <span>عدد المعاملات المحدثة:</span>
                <span>{fixResults.updatedCount}</span>
              </div>
              <div className="result-item">
                <span>إجمالي الأرباح الجديد:</span>
                <span>{fixResults.totalProfit}</span>
              </div>
            </div>
          )}
        </div>

        {/* قسم السجل */}
        <div className="test-section">
          <h3>📝 سجل العمليات</h3>
          <button className="test-button" onClick={clearLogs}>
            مسح السجل
          </button>
          
          <div className="logs-container">
            {logs.map((log, index) => (
              <div key={index} className={`log-item ${log.type}`}>
                <span className="log-time">[{log.timestamp}]</span>
                <span className="log-message">{log.message}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfitTestPage;
