/**
 * وظائف مساعدة للتعامل مع الفواتير
 */

/**
 * تنسيق التاريخ (بالتنسيق الميلادي)
 * @param {string} dateString - سلسلة التاريخ
 * @returns {string} - التاريخ المنسق
 */
export const formatDate = (dateString) => {
  const date = new Date(dateString);
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();

  return `${year}/${month}/${day}`;
};

/**
 * طباعة الفاتورة
 * @param {Object} sale - بيانات عملية البيع
 * @param {Array} items - قائمة الأصناف المباعة
 * @param {Object} customer - بيانات العميل
 * @param {Object} settings - إعدادات النظام
 */
export const printInvoice = (sale, items, customer, settings) => {
  const printWindow = window.open('', '_blank', 'width=800,height=600,toolbar=0,scrollbars=1,status=0,menubar=0');

  if (!printWindow) {
    alert('يرجى السماح بالنوافذ المنبثقة لطباعة الفاتورة');
    return;
  }

  // الحصول على معلومات الشركة
  const companyName = settings.systemName || 'شركة أثاث غروب';
  const companyAddress = settings.address || 'للتصميم و تصنيع الأثاث والديكورات';
  const companyPhone = settings.phone || '';
  const companyLogo = settings.logoUrl || '';

  // تنسيق التاريخ
  const invoiceDate = formatDate(sale?.transaction_date || new Date().toISOString());

  // إنشاء رقم الفاتورة
  const invoiceNumber = sale?.invoice_number || `H${Math.floor(Math.random() * 100000).toString().padStart(5, '0')}`;

  // بيانات العميل
  const customerName = customer?.name || sale?.customer_name || 'مهندس ناصر - شركة ملاك';
  const customerPhone = customer?.phone || '+218 92-3000151';

  // إجمالي المبيعات
  const totalAmount = items.length > 0
    ? items.reduce((total, item) => total + ((item.quantity || 1) * (item.price || 0)), 0)
    : 0;

  // تعيين محتوى النافذة
  printWindow.document.open();
  printWindow.document.write(`
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>فاتورة رقم ${invoiceNumber}</title>
        <style>
          @page {
            size: A4;
            margin: 1cm;
          }
          @media print {
            html, body {
              width: 210mm;
              height: 297mm;
            }
          }
          body {
            font-family: Arial, sans-serif;
            direction: rtl;
            padding: 0;
            margin: 0;
            font-size: 14px;
            color: #333;
            background-color: white;
          }
          .invoice-container {
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
          }
          .invoice-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
          }
          .company-info {
            text-align: right;
          }
          .invoice-info {
            text-align: left;
          }
          .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
          }
          .company-address {
            font-size: 16px;
            color: #e67e22;
            margin-bottom: 10px;
          }
          .invoice-title {
            font-size: 18px;
            margin: 20px 0;
            font-weight: bold;
            text-align: center;
            color: #2c3e50;
          }
          .customer-info {
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
          }
          .customer-info div {
            margin-bottom: 5px;
          }
          .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
          }
          .invoice-table th, .invoice-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: right;
          }
          .invoice-table th {
            background-color: #f8f9fa;
            font-weight: bold;
          }
          .invoice-table tr:nth-child(even) {
            background-color: #f9f9f9;
          }
          .total-row {
            font-weight: bold;
            background-color: #f8f9fa !important;
          }
          .terms {
            margin-top: 30px;
            border-top: 1px solid #eee;
            padding-top: 10px;
          }
          .terms h3 {
            margin-bottom: 10px;
            color: #2c3e50;
          }
          .signature {
            margin-top: 50px;
            display: flex;
            justify-content: flex-end;
          }
          .signature-box {
            border-top: 1px solid #333;
            width: 200px;
            text-align: center;
            padding-top: 5px;
          }
          .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 12px;
            color: #777;
            border-top: 1px solid #eee;
            padding-top: 10px;
          }
          @media print {
            body {
              margin: 0;
              padding: 0;
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .no-print {
              display: none;
            }
            .invoice-container {
              width: 100%;
              max-width: 100%;
              padding: 0;
              margin: 0;
            }
            .invoice-table th {
              background-color: #f8f9fa !important;
            }
            .invoice-table tr:nth-child(even) {
              background-color: #f9f9f9 !important;
            }
            .total-row {
              background-color: #f8f9fa !important;
            }
          }
        </style>
      </head>
      <body>
        <div class="invoice-container">
          <div class="invoice-header">
            <div class="company-info">
              <div class="company-name">${companyName}</div>
              <div class="company-address">${companyAddress}</div>
              ${companyPhone ? `<div>هاتف: ${companyPhone}</div>` : ''}
            </div>
            <div class="invoice-info">
              <div>التاريخ: ${invoiceDate}</div>
              <div>فاتورة رقم: ${invoiceNumber}</div>
            </div>
          </div>

          <div class="customer-info">
            <div>
              <strong>صاحب الطلب:</strong> ${customerName}
            </div>
            <div>
              <strong>رقم الهاتف:</strong> ${customerPhone}
            </div>
          </div>

          <table class="invoice-table">
            <thead>
              <tr>
                <th>#</th>
                <th>وصف المنتج</th>
                <th>الكمية</th>
                <th>السعر</th>
                <th>المجموع</th>
              </tr>
            </thead>
            <tbody>
              ${items.map((item, index) => `
                  <tr>
                    <td>${index + 1}</td>
                    <td>${item.item_name || 'تصنيع أثاث شركة ملاك سوبرماركت'}</td>
                    <td>${item.quantity || 1}</td>
                    <td>${(item.price || 0).toFixed(2)}</td>
                    <td>${((item.quantity || 1) * (item.price || 0)).toFixed(2)}</td>
                  </tr>
                `).join('')}
              <tr class="total-row">
                <td colspan="4" style="text-align: left;">المبلغ المستلم:</td>
                <td>0.000</td>
              </tr>
              <tr class="total-row">
                <td colspan="4" style="text-align: left;">المبلغ الباقي:</td>
                <td>0.000</td>
              </tr>
              <tr class="total-row">
                <td colspan="4" style="text-align: left;">المبلغ الإجمالي:</td>
                <td>${totalAmount.toFixed(3)}</td>
              </tr>
            </tbody>
          </table>

          <div class="terms">
            <h3>بنود الاتفاق:</h3>
            <p>مدة العرض: 3 أيام من تاريخ اصدار الفاتورة.</p>
            <p>دفعة متقدمة بنسبة 50% عند الموافقة على العرض.</p>
            <p>دفعــة ثانية بنسبة 30% قبل 3 أيام من موعد التركيب.</p>
            <p>مدة التسليم: 45 يوم من تاريخ استلام الدفعة الأولى.</p>
            <p>هذا العرض شامل النقل والتنزيل الطابق الأرضي فقط.</p>
          </div>

          <div class="signature">
            <div class="signature-box">التوقيع</div>
          </div>

          <div class="footer">
            نشكركم على ثقتكم بنا ونتمنى لكم دوام التوفيق من خدماتنا، شكراً لكم.
          </div>
        </div>

        <script>
          window.onload = function() {
            // تأكد من تحميل الصفحة بشكل كامل قبل الطباعة
            if (document.readyState === 'complete') {
              setTimeout(printAndClose, 1000);
            } else {
              window.addEventListener('load', function() {
                setTimeout(printAndClose, 1000);
              });
            }

            function printAndClose() {
              try {
                window.print();
                // إغلاق النافذة بعد الطباعة
                setTimeout(function() {
                  window.close();
                }, 500);
              } catch (e) {
                console.error('خطأ في الطباعة:', e);
                alert('حدث خطأ أثناء الطباعة. يرجى المحاولة مرة أخرى.');
              }
            }
          };
        </script>
      </body>
    </html>
  `);

  printWindow.document.close();
};

/**
 * تصدير الفاتورة كملف PDF
 * @param {Object} sale - بيانات عملية البيع
 * @param {Array} items - قائمة الأصناف المباعة
 * @param {Object} customer - بيانات العميل
 * @param {Object} settings - إعدادات النظام
 * @param {Object} jsPDF - مكتبة jsPDF
 */
export const exportToPDF = (sale, items, customer, settings, jsPDF) => {
  // إنشاء مستند PDF جديد
  const doc = new jsPDF({
    orientation: 'p',
    unit: 'mm',
    format: 'a4',
    putOnlyUsedFonts: true,
    compress: true
  });

  // الحصول على معلومات الشركة
  const companyName = settings.systemName || 'شركة أثاث غروب';
  const companyAddress = settings.address || 'للتصميم و تصنيع الأثاث والديكورات';
  const companyPhone = settings.phone || '';

  // تنسيق التاريخ
  const invoiceDate = formatDate(sale?.transaction_date || new Date().toISOString());

  // إنشاء رقم الفاتورة
  const invoiceNumber = sale?.invoice_number || `H${Math.floor(Math.random() * 100000).toString().padStart(5, '0')}`;

  // بيانات العميل
  const customerName = customer?.name || sale?.customer_name || 'مهندس ناصر - شركة ملاك';
  const customerPhone = customer?.phone || '+218 92-3000151';

  // إجمالي المبيعات
  const totalAmount = items.length > 0
    ? items.reduce((total, item) => total + ((item.quantity || 1) * (item.price || 0)), 0)
    : 0;

  // إضافة معلومات الرأس
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(18);
  doc.text(companyName, 200, 20, { align: 'right' });

  doc.setFont('helvetica', 'normal');
  doc.setFontSize(14);
  doc.setTextColor(230, 126, 34); // لون برتقالي للعنوان
  doc.text(companyAddress, 200, 28, { align: 'right' });
  doc.setTextColor(0, 0, 0); // إعادة اللون للأسود

  if (companyPhone) {
    doc.setFontSize(12);
    doc.text(`هاتف: ${companyPhone}`, 200, 35, { align: 'right' });
  }

  // إضافة معلومات الفاتورة
  doc.setFontSize(12);
  doc.text(`التاريخ: ${invoiceDate}`, 30, 20);
  doc.text(`فاتورة رقم: ${invoiceNumber}`, 30, 28);

  // إضافة خط فاصل
  doc.setDrawColor(200, 200, 200);
  doc.line(10, 40, 200, 40);

  // إضافة معلومات العميل
  doc.setFontSize(12);
  doc.text(`صاحب الطلب: ${customerName}`, 200, 50, { align: 'right' });
  doc.text(`رقم الهاتف: ${customerPhone}`, 30, 50);

  // إضافة جدول المنتجات
  const tableColumn = ['المجموع', 'السعر', 'الكمية', 'وصف المنتج', '#'];

  // إعداد صفوف الجدول
  const tableRows = items.map((item, index) => [
    ((item.quantity || 1) * (item.price || 0)).toFixed(2),
    (item.price || 0).toFixed(2),
    item.quantity || 1,
    item.item_name || 'تصنيع أثاث شركة ملاك سوبرماركت',
    index + 1
  ]);

  doc.autoTable({
    head: [tableColumn],
    body: tableRows,
    startY: 60,
    theme: 'grid',
    styles: { halign: 'right', font: 'helvetica', fontSize: 10 },
    headStyles: { fillColor: [44, 62, 80], textColor: 255 },
    alternateRowStyles: { fillColor: [249, 249, 249] },
    margin: { top: 60 }
  });

  // الحصول على الموضع النهائي للجدول
  const finalY = doc.autoTable.previous.finalY;

  // إضافة إجماليات
  doc.setFontSize(12);
  doc.setFont('helvetica', 'bold');

  doc.text('المبلغ المستلم:', 150, finalY + 10, { align: 'right' });
  doc.text('0.000', 30, finalY + 10);

  doc.text('المبلغ الباقي:', 150, finalY + 20, { align: 'right' });
  doc.text('0.000', 30, finalY + 20);

  doc.text('المبلغ الإجمالي:', 150, finalY + 30, { align: 'right' });
  doc.text(`${totalAmount.toFixed(3)}`, 30, finalY + 30);

  // إضافة خط فاصل
  doc.setDrawColor(200, 200, 200);
  doc.line(10, finalY + 35, 200, finalY + 35);

  // إضافة بنود الاتفاق
  doc.setFontSize(14);
  doc.text('بنود الاتفاق:', 200, finalY + 45, { align: 'right' });

  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');
  doc.text('مدة العرض: 3 أيام من تاريخ اصدار الفاتورة.', 200, finalY + 55, { align: 'right' });
  doc.text('دفعة متقدمة بنسبة 50% عند الموافقة على العرض.', 200, finalY + 62, { align: 'right' });
  doc.text('دفعــة ثانية بنسبة 30% قبل 3 أيام من موعد التركيب.', 200, finalY + 69, { align: 'right' });
  doc.text('مدة التسليم: 45 يوم من تاريخ استلام الدفعة الأولى.', 200, finalY + 76, { align: 'right' });
  doc.text('هذا العرض شامل النقل والتنزيل الطابق الأرضي فقط.', 200, finalY + 83, { align: 'right' });

  // إضافة مكان للتوقيع
  doc.setFontSize(12);
  doc.text('التوقيع', 30, finalY + 100);
  doc.line(10, finalY + 98, 60, finalY + 98);

  // إضافة تذييل الصفحة
  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');
  doc.text('نشكركم على ثقتكم بنا ونتمنى لكم دوام التوفيق من خدماتنا، شكراً لكم.', 105, finalY + 120, { align: 'center' });

  // حفظ الملف
  doc.save(`فاتورة_${invoiceNumber}.pdf`);
};
