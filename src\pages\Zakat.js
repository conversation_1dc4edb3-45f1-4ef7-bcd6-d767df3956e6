import React, { useState, useEffect, useRef } from 'react';
import {
  FaPlus,
  FaEdit,
  FaTrash,
  FaCalculator,
  FaFilePdf,
  FaPrint,
  FaSearch,
  FaExclamationTriangle,
  FaTools,
  FaMoneyBillWave,
  FaPercentage,
  FaCalendarAlt,
  FaInfoCircle
} from 'react-icons/fa';
import * as machinesAPI from '../utils/machinesAPI';
import Card from '../components/Card';
import Button from '../components/Button';
import Modal from '../components/Modal';
import DataTable from '../components/DataTable';
import FormattedCurrency from '../components/FormattedCurrency';
import './Zakat.css';

/**
 * صفحة حساب زكاة الآلات والمعدات
 * تستخدم هذه الصفحة لإدارة الآلات والمعدات وحساب الزكاة عليها (2.5% من القيمة الحالية)
 */
const Zakat = () => {
  // حالة الآلات
  // Variable no utilizada - considere eliminarla o usarla
  const [filteredMachines, setFilteredMachines] = useState([]);
  // Variable no utilizada - considere eliminarla o usarla
  const [originalMachines, setOriginalMachines] = useState([]);
  // Variable no utilizada - considere eliminarla o usarla
  const [searchTerm, setSearchTerm] = useState('');

  // حالة النافذة المنبثقة
  // Variable no utilizada - considere eliminarla o usarla
  const [showModal, setShowModal] = useState(false);
  // Variable no utilizada - considere eliminarla o usarla
  const [modalMode, setModalMode] = useState('add'); // 'add' أو 'edit'
  // Variable no utilizada - considere eliminarla o usarla
  const [selectedMachine, setSelectedMachine] = useState(null);

  // حالة نموذج الآلة
  const [formData, setFormData] = useState({
    name: '',
    purchase_date: new Date().toISOString().split('T')[0],
    purchase_price: 0,
    current_value: 0,
    notes: ''
  });

  // حالة حساب الزكاة
  const [zakatCalculation, setZakatCalculation] = useState({
    totalValue: 0,
    zakatAmount: 0,
    calculationDate: new Date().toISOString()
  });

  // حالة التنبيه
  // Variable no utilizada - considere eliminarla o usarla
  const [alert, setAlert] = useState({
    show: false,
    type: '',
    message: ''
  });

  // حالة التحميل
  // Variable no utilizada - considere eliminarla o usarla
  const [loading, setLoading] = useState(true);

  // مرجع للتقرير للطباعة
  const reportRef = useRef(null);

  // مرجع لتخزين معرف المؤقت
  const alertTimerRef = useRef(null);

  // تحميل الآلات عند تحميل الصفحة
  useEffect(() => {
    loadMachines();

    // وظيفة التنظيف عند إزالة المكون
    return () => {
      // إلغاء أي مؤقتات نشطة
      if (alertTimerRef.current) {
        clearTimeout(alertTimerRef.current);
        alertTimerRef.current = null;
      }
    };
  }, []);

  // تصفية الآلات بناءً على مصطلح البحث
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredMachines(originalMachines);
    } else {
      const filtered = originalMachines.filter(machine =>
        machine.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredMachines(filtered);
    }
  }, [originalMachines, searchTerm]);

  // تحميل الآلات من قاعدة البيانات
  const loadMachines = async () => {
    try {
      setLoading(true);
      console.log('جاري تحميل الآلات من قاعدة البيانات...');

      // استخدام machinesAPI.getAllMachines
      const machinesData = await machinesAPI.getAllMachines();
      console.log('تم تحميل الآلات:', machinesData);

      if (machinesData.length === 0) {
        console.log('لا توجد آلات في قاعدة البيانات');
        setFilteredMachines([]);
        setOriginalMachines([]);
        showAlert('info', 'لا توجد آلات مسجلة. يمكنك إضافة آلات جديدة.');
        return;
      }

      // تحديث حالة الآلات
      setFilteredMachines(machinesData);
      setOriginalMachines(machinesData);
      console.log('تم تحديث حالة الآلات بنجاح');
    } catch (error) {
      console.error('خطأ في تحميل الآلات:', error);
      setFilteredMachines([]);
      setOriginalMachines([]);
      showAlert('danger', 'فشل في تحميل بيانات الآلات');
    } finally {
      setLoading(false);
    }
  };

  // حساب الزكاة
  const calculateZakat = () => {
    if (filteredMachines.length === 0) {
      showAlert('warning', 'لا توجد آلات لحساب الزكاة عليها');
      return;
    }

    // حساب إجمالي القيمة الحالية للآلات
    const totalValue = filteredMachines.reduce((sum, machine) => sum + machine.current_value, 0);

    // حساب مقدار الزكاة (2.5% من إجمالي القيمة)
    const zakatAmount = totalValue * 0.025;

    // تحديث حالة حساب الزكاة
    setZakatCalculation({
      totalValue,
      zakatAmount,
      calculationDate: new Date().toISOString()
    });

    showAlert('success', 'تم حساب الزكاة بنجاح');
  };

  // معالجة تغيير قيم النموذج
  const handleInputChange = (e) => {
    const { name, value } = e.target;

    console.log(`تغيير قيمة الحقل: ${name} = ${value}`);

    // تحويل القيم العددية
    if (name === 'purchase_price' || name === 'current_value') {
      const numValue = parseFloat(value) || 0;
      console.log(`تحويل القيمة العددية: ${value} => ${numValue}`);
      setFormData(prev => {
        const newData = { ...prev, [name]: numValue };
        console.log('بيانات النموذج الجديدة:', newData);
        return newData;
      });
    } else {
      setFormData(prev => {
        const newData = { ...prev, [name]: value };
        console.log('بيانات النموذج الجديدة:', newData);
        return newData;
      });
    }
  };

  // معالجة إضافة آلة جديدة
  const handleAddMachine = () => {
    setModalMode('add');
    setSelectedMachine(null);
    setFormData({
      name: '',
      purchase_date: new Date().toISOString().split('T')[0],
      purchase_price: 0,
      current_value: 0,
      notes: ''
    });
    setShowModal(true);
  };

  // معالجة تعديل آلة
  const handleEditMachine = (machine) => {
    setModalMode('edit');
    setSelectedMachine(machine);
    setFormData({
      name: machine.name,
      purchase_date: machine.purchase_date,
      purchase_price: machine.purchase_price,
      current_value: machine.current_value,
      notes: machine.notes || ''
    });
    setShowModal(true);
  };

  // معالجة حذف آلة
  const handleDeleteMachine = async (machineId) => {
    if (!machineId) {
      showAlert('danger', 'معرف الآلة غير صالح');
      return;
    }

    if (window.confirm('هل أنت متأكد من حذف هذه الآلة؟')) {
      try {
        setLoading(true);

        console.log('جاري حذف الآلة مع المعرف:', machineId);

        // استخدام machinesAPI.deleteMachine
        await machinesAPI.deleteMachine(machineId);
        console.log('تم حذف الآلة بنجاح مع المعرف:', machineId);
        showAlert('success', 'تم حذف الآلة بنجاح');

        // إعادة تحميل الآلات بعد الحذف
        await loadMachines();
      } catch (error) {
        console.error('خطأ في حذف الآلة:', error);
        showAlert('danger', `فشل في حذف الآلة: ${error.message || 'خطأ غير معروف'}`);
      } finally {
        setLoading(false);
      }
    }
  };

  // معالجة إرسال النموذج
  const handleSubmit = async (e) => {
    if (e) e.preventDefault();
    console.log('تم إرسال النموذج مع البيانات:', formData);

    // التحقق من صحة البيانات
    if (!formData.name || !formData.name.trim()) {
      showAlert('danger', 'الرجاء إدخال اسم الآلة');
      return;
    }

    if (!formData.purchase_date) {
      showAlert('danger', 'الرجاء إدخال تاريخ الشراء');
      return;
    }

    if (!formData.purchase_price || formData.purchase_price <= 0) {
      showAlert('danger', 'الرجاء إدخال سعر شراء صحيح (أكبر من صفر)');
      return;
    }

    if (!formData.current_value || formData.current_value <= 0) {
      showAlert('danger', 'الرجاء إدخال القيمة الحالية الصحيحة (أكبر من صفر)');
      return;
    }

    setLoading(true);

    try {
      // تحويل البيانات إلى الأنواع المناسبة
      const machineData = {
        name: String(formData.name).trim(),
        purchase_date: String(formData.purchase_date),
        purchase_price: Number(formData.purchase_price),
        current_value: Number(formData.current_value),
        notes: String(formData.notes || '').trim()
      };

      console.log('البيانات المعالجة للإرسال:', machineData);

      if (modalMode === 'add') {
        // إضافة آلة جديدة
        console.log('جاري إضافة آلة جديدة...');

        try {
          // استخدام machinesAPI.addMachine
          const result = await machinesAPI.addMachine(machineData);
          console.log('تمت إضافة الآلة بنجاح:', result);
          showAlert('success', 'تمت إضافة الآلة بنجاح');

          // إعادة تحميل الآلات بعد الإضافة
          await loadMachines();

          // إغلاق النافذة المنبثقة وإعادة تعيين النموذج
          setShowModal(false);
          setFormData({
            name: '',
            purchase_date: new Date().toISOString().split('T')[0],
            purchase_price: 0,
            current_value: 0,
            notes: ''
          });
        } catch (addError) {
          console.error('خطأ في إضافة الآلة:', addError);
          showAlert('danger', `فشل في إضافة الآلة: ${addError.message || 'خطأ غير معروف'}`);
        }
      } else {
        // تحديث آلة موجودة
        console.log('جاري تحديث الآلة مع المعرف:', selectedMachine.id);

        try {
          // استخدام machinesAPI.updateMachine
          const result = await machinesAPI.updateMachine({
            ...machineData,
            _id: selectedMachine._id || selectedMachine.id // استخدام _id إذا كان موجودًا، وإلا استخدام id
          });
          console.log('تم تحديث الآلة بنجاح:', result);
          showAlert('success', 'تم تحديث الآلة بنجاح');

          // إعادة تحميل الآلات بعد التحديث
          await loadMachines();

          // إغلاق النافذة المنبثقة وإعادة تعيين النموذج
          setShowModal(false);
          setFormData({
            name: '',
            purchase_date: new Date().toISOString().split('T')[0],
            purchase_price: 0,
            current_value: 0,
            notes: ''
          });
        } catch (updateError) {
          console.error('خطأ في تحديث الآلة:', updateError);
          showAlert('danger', `فشل في تحديث الآلة: ${updateError.message || 'خطأ غير معروف'}`);
        }
      }
    } catch (error) {
      console.error('خطأ في حفظ الآلة:', error);
      showAlert('danger', `فشل في حفظ الآلة: ${error.message || 'خطأ غير معروف'}`);
    } finally {
      setLoading(false);
    }
  };

  // طباعة تقرير الزكاة
  const printZakatReport = () => {
    if (filteredMachines.length === 0) {
      showAlert('warning', 'لا توجد آلات لطباعة تقرير الزكاة');
      return;
    }

    if (zakatCalculation.totalValue === 0) {
      showAlert('warning', 'الرجاء حساب الزكاة أولاً');
      return;
    }

    const printWindow = window.open('', '_blank');

    if (!printWindow) {
      showAlert('danger', 'يرجى السماح بالنوافذ المنبثقة لطباعة التقرير');
      return;
    }

    const today = formatDate(new Date());
    const calculationDate = formatDate(zakatCalculation.calculationDate);

    printWindow.document.write(`
      <html dir="rtl">
        <head>
          <title>تقرير زكاة الآلات والمعدات</title>
          <meta charset="UTF-8">
          <style>
            body {
              font-family: Arial, sans-serif;
              padding: 20px;
              direction: rtl;
              color: #333;
              line-height: 1.6;
            }

            .container {
              max-width: 1000px;
              margin: 0 auto;
              padding: 20px;
              border: 1px solid #ddd;
              box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
              background-color: #fff;
            }

            .logo {
              text-align: center;
              margin-bottom: 20px;
            }

            .logo img {
              max-height: 80px;
            }

            .report-header {
              text-align: center;
              margin-bottom: 30px;
              padding-bottom: 20px;
              border-bottom: 2px solid #3498db;
            }

            .report-header h1 {
              color: #2c3e50;
              margin-bottom: 10px;
              font-size: 24px;
            }

            .report-header h2 {
              color: #3498db;
              margin-bottom: 10px;
              font-size: 18px;
            }

            .report-header p {
              color: #7f8c8d;
              font-size: 14px;
            }

            .report-info {
              display: flex;
              justify-content: space-between;
              margin-bottom: 20px;
              padding: 15px;
              background-color: #f9f9f9;
              border-radius: 5px;
            }

            .report-info-item {
              flex: 1;
            }

            .report-info-item h3 {
              font-size: 14px;
              color: #7f8c8d;
              margin-bottom: 5px;
            }

            .report-info-item p {
              font-size: 16px;
              color: #2c3e50;
              font-weight: bold;
            }

            table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 30px;
            }

            th, td {
              padding: 12px 15px;
              text-align: right;
              border-bottom: 1px solid #ddd;
            }

            th {
              background-color: #3498db;
              color: white;
              font-weight: bold;
              white-space: nowrap;
            }

            tr:nth-child(even) {
              background-color: #f9f9f9;
            }

            tr:hover {
              background-color: #f1f1f1;
            }

            .summary {
              margin-top: 30px;
              padding: 20px;
              background-color: #f9f9f9;
              border-radius: 5px;
              border-right: 4px solid #3498db;
            }

            .summary h3 {
              color: #2c3e50;
              margin-bottom: 15px;
              font-size: 18px;
            }

            .summary-grid {
              display: grid;
              grid-template-columns: repeat(3, 1fr);
              gap: 20px;
              margin-bottom: 20px;
            }

            .summary-item {
              background-color: white;
              padding: 15px;
              border-radius: 5px;
              box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
              text-align: center;
            }

            .summary-item h4 {
              color: #7f8c8d;
              margin-bottom: 10px;
              font-size: 14px;
            }

            .summary-item p {
              color: #2c3e50;
              font-size: 18px;
              font-weight: bold;
            }

            .summary-item.highlight p {
              color: #3498db;
            }

            .footer {
              margin-top: 50px;
              text-align: center;
              font-size: 12px;
              color: #7f8c8d;
              padding-top: 20px;
              border-top: 1px solid #ddd;
            }

            @media print {
              body {
                padding: 0;
                background-color: white;
              }

              .container {
                box-shadow: none;
                border: none;
                padding: 0;
              }

              button {
                display: none;
              }

              @page {
                size: A4;
                margin: 1.5cm;
              }
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="report-header">
              <h1>تقرير حساب زكاة الآلات والمعدات</h1>
              <h2>شركة أثاث غروب</h2>
              <p>للتصميم و تصنيع الأثاث والديكورات</p>
            </div>

            <div class="report-info">
              <div class="report-info-item">
                <h3>تاريخ التقرير</h3>
                <p>${today}</p>
              </div>
              <div class="report-info-item">
                <h3>تاريخ حساب الزكاة</h3>
                <p>${calculationDate}</p>
              </div>
              <div class="report-info-item">
                <h3>عدد الآلات</h3>
                <p>${filteredMachines.length}</p>
              </div>
            </div>

            <table>
              <thead>
                <tr>
                  <th>#</th>
                  <th>اسم الآلة</th>
                  <th>تاريخ الشراء</th>
                  <th>سعر الشراء (د ل)</th>
                  <th>القيمة الحالية (د ل)</th>
                  <th>ملاحظات</th>
                </tr>
              </thead>
              <tbody>
                ${filteredMachines.map((machine, index) => `
                  <tr>
                    <td>${index + 1}</td>
                    <td>${machine.name}</td>
                    <td>${formatDate(machine.purchase_date)}</td>
                    <td>${machine.purchase_price.toLocaleString()}</td>
                    <td>${machine.current_value.toLocaleString()}</td>
                    <td>${machine.notes || '-'}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>

            <div class="summary">
              <h3>ملخص حساب الزكاة</h3>

              <div class="summary-grid">
                <div class="summary-item">
                  <h4>إجمالي قيمة الآلات والمعدات</h4>
                  <p>${zakatCalculation.totalValue.toLocaleString()} د ل</p>
                </div>

                <div class="summary-item">
                  <h4>نسبة الزكاة</h4>
                  <p>2.5%</p>
                </div>

                <div class="summary-item highlight">
                  <h4>مقدار الزكاة المستحقة</h4>
                  <p>${zakatCalculation.zakatAmount.toLocaleString()} د ل</p>
                </div>
              </div>
            </div>

            <div class="footer">
              <p>نظام إدارة المخازن © ${new Date().getFullYear()}</p>
              <p>هذا التقرير تم إنشاؤه بواسطة نظام إدارة المخازن</p>
            </div>
          </div>

          <script>
            window.onload = function() {
              setTimeout(function() {
                window.print();
              }, 500);
            };
          </script>
        </body>
      </html>
    `);

    printWindow.document.close();
  };

  // تصدير تقرير الزكاة كملف PDF
  const exportZakatReportAsPDF = () => {
    showAlert('info', 'جاري تطوير هذه الميزة...');
  };

  // عرض تنبيه
  const showAlert = (type, message) => {
    setAlert({ show: true, type, message });

    // إلغاء المؤقت السابق إذا كان موجودًا
    if (alertTimerRef.current) {
      clearTimeout(alertTimerRef.current);
    }

    // إخفاء التنبيه بعد 3 ثوان
    alertTimerRef.current = setTimeout(() => {
      setAlert({ show: false, type: '', message: '' });
      alertTimerRef.current = null;
    }, 3000);
  };

  // تنسيق التاريخ (بالتنسيق الميلادي)
  const formatDate = (dateString) => {
    if (!dateString) return '-';

    try {
      const date = new Date(dateString);
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      return `${year}/${month}/${day}`;
    } catch (error) {
      console.error('خطأ في تنسيق التاريخ:', error);
      return dateString;
    }
  };

  // عرض مؤشر التحميل
  if (loading) {
    return (
      <div className="loading-container">
        <div className="spinner"></div>
        <p>جاري التحميل...</p>
      </div>
    );
  }

  return (
    <div className="zakat-page">
      <div className="zakat-header">
        <h1>حساب زكاة الآلات والمعدات</h1>
        <p>إدارة الآلات والمعدات وحساب الزكاة عليها (2.5% من القيمة الحالية)</p>
      </div>

      {/* عرض التنبيه */}
      {alert.show && (
        <div className={`alert alert-${alert.type}`}>
          {alert.message}
        </div>
      )}

      {/* أدوات التحكم */}
      <Card>
        <div className="search-container">
          <FaSearch className="search-icon" />
          <input
            type="text"
            className="search-input"
            placeholder="بحث عن آلة..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="control-buttons">
          <Button
            variant="primary"
            icon={<FaPlus />}
            onClick={handleAddMachine}
          >
            إضافة آلة جديدة
          </Button>

          <Button
            variant="accent"
            icon={<FaCalculator />}
            onClick={calculateZakat}
            disabled={filteredMachines.length === 0}
          >
            حساب الزكاة
          </Button>

          <Button
            variant="secondary"
            icon={<FaPrint />}
            onClick={printZakatReport}
            disabled={filteredMachines.length === 0 || zakatCalculation.totalValue === 0}
          >
            طباعة التقرير
          </Button>

          <Button
            variant="light"
            icon={<FaFilePdf />}
            onClick={exportZakatReportAsPDF}
            disabled={filteredMachines.length === 0 || zakatCalculation.totalValue === 0}
          >
            تصدير PDF
          </Button>
        </div>
      </Card>

      {/* جدول الآلات */}
      <Card
        title="قائمة الآلات والمعدات"
        icon={<FaTools />}
        className="machines-table-container"
      >
        <DataTable
          columns={[
            {
              header: '#',
              accessor: 'index',
              cell: (_, index) => index + 1,
              style: { width: '50px' }
            },
            {
              header: 'اسم الآلة',
              accessor: 'name',
              cell: (row) => (
                <div className="machine-name">{row.name}</div>
              )
            },
            {
              header: 'تاريخ الشراء',
              accessor: 'purchase_date',
              cell: (row) => (
                <div className="machine-date">
                  {formatDate(row.purchase_date)}
                </div>
              )
            },
            {
              header: 'سعر الشراء',
              accessor: 'purchase_price',
              cell: (row) => (
                <div className="machine-price">
                  <FormattedCurrency amount={row.purchase_price} />
                </div>
              )
            },
            {
              header: 'القيمة الحالية',
              accessor: 'current_value',
              cell: (row) => (
                <div className="machine-price">
                  <FormattedCurrency amount={row.current_value} />
                </div>
              )
            },
            {
              header: 'ملاحظات',
              accessor: 'notes',
              cell: (row) => (
                <div className="machine-notes" title={row.notes || ''}>
                  {row.notes || '-'}
                </div>
              )
            },
            {
              header: 'الإجراءات',
              accessor: 'actions',
              cell: (row) => (
                <div className="machine-actions">
                  <Button
                    variant="primary"
                    size="sm"
                    icon={<FaEdit />}
                    title="تعديل"
                    onClick={() => handleEditMachine(row)}
                  />
                  <Button
                    variant="danger"
                    size="sm"
                    icon={<FaTrash />}
                    title="حذف"
                    onClick={() => handleDeleteMachine(row._id || row.id)}
                  />
                </div>
              )
            }
          ]}
          data={filteredMachines}
          pagination={true}
          pageSize={10}
          searchable={false}
          emptyMessage={
            searchTerm
              ? `لا توجد نتائج للبحث عن "${searchTerm}"`
              : 'لا توجد آلات مسجلة. قم بإضافة آلات أولاً.'
          }
        />
      </Card>

      {/* ملخص حساب الزكاة */}
      {zakatCalculation.totalValue > 0 && (
        <Card
          title="ملخص حساب الزكاة"
          icon={<FaCalculator />}
          ref={reportRef}
        >
          <div className="zakat-stats">
            <div className="zakat-stat-card">
              <div className="zakat-stat-icon">
                <FaMoneyBillWave />
              </div>
              <div className="zakat-stat-value">
                <FormattedCurrency amount={zakatCalculation.totalValue} />
              </div>
              <div className="zakat-stat-label">إجمالي قيمة الآلات والمعدات</div>
            </div>

            <div className="zakat-stat-card">
              <div className="zakat-stat-icon">
                <FaPercentage />
              </div>
              <div className="zakat-stat-value">2.5%</div>
              <div className="zakat-stat-label">نسبة الزكاة</div>
            </div>

            <div className="zakat-stat-card">
              <div className="zakat-stat-icon">
                <FaCalculator />
              </div>
              <div className="zakat-stat-value">
                <FormattedCurrency amount={zakatCalculation.zakatAmount} />
              </div>
              <div className="zakat-stat-label">مقدار الزكاة المستحقة</div>
            </div>
          </div>

          <div className="zakat-info">
            <FaInfoCircle />
            <span>تم حساب الزكاة بتاريخ: {formatDate(zakatCalculation.calculationDate)}</span>
          </div>
        </Card>
      )}

      {/* نافذة إضافة/تعديل آلة */}
      {showModal && (
        <Modal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          title={modalMode === 'add' ? 'إضافة آلة جديدة' : 'تعديل بيانات الآلة'}
          size="lg"
          footer={
            <>
              <Button
                variant="light"
                onClick={() => setShowModal(false)}
              >
                إلغاء
              </Button>
              <Button
                variant="primary"
                onClick={handleSubmit}
              >
                {modalMode === 'add' ? 'إضافة' : 'حفظ التغييرات'}
              </Button>
            </>
          }
        >
          <form className="machine-form" onSubmit={(e) => e.preventDefault()}>
            <div className="form-group full-width">
              <label className="form-label">اسم الآلة <span className="required">*</span></label>
              <input
                type="text"
                name="name"
                className="form-control"
                value={formData.name}
                onChange={handleInputChange}
                required
                placeholder="أدخل اسم الآلة"
              />
            </div>

            <div className="form-group">
              <label className="form-label">تاريخ الشراء <span className="required">*</span></label>
              <input
                type="date"
                name="purchase_date"
                className="form-control"
                value={formData.purchase_date}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="form-group">
              <label className="form-label">سعر الشراء <span className="required">*</span></label>
              <input
                type="number"
                name="purchase_price"
                className="form-control"
                value={formData.purchase_price}
                onChange={handleInputChange}
                min="0.01"
                step="0.01"
                required
                placeholder="0.00"
              />
            </div>

            <div className="form-group full-width">
              <label className="form-label">القيمة الحالية <span className="required">*</span></label>
              <input
                type="number"
                name="current_value"
                className="form-control"
                value={formData.current_value}
                onChange={handleInputChange}
                min="0.01"
                step="0.01"
                required
                placeholder="0.00"
              />
              <small className="form-text">
                القيمة الحالية للآلة هي التي يتم حساب الزكاة عليها (2.5%)
              </small>
            </div>

            <div className="form-group full-width">
              <label className="form-label">ملاحظات</label>
              <textarea
                name="notes"
                className="form-control"
                value={formData.notes}
                onChange={handleInputChange}
                rows="3"
                placeholder="ملاحظات إضافية (اختياري)"
              ></textarea>
            </div>
          </form>
        </Modal>
      )}
    </div>
  );
};

export default Zakat;
