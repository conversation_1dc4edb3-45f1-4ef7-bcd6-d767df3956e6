import React from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

// تسجيل المكونات المطلوبة للرسم البياني
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

/**
 * مكون الرسم البياني للأرباح الشهرية
 * @param {Object} props - خصائص المكون
 * @param {Object} props.data - بيانات الرسم البياني
 * @param {Array} props.data.labels - تسميات الأشهر
 * @param {Array} props.data.profit - قيم الأرباح
 * @param {Array} props.data.profitMargin - قيم هامش الربح
 * @returns {JSX.Element} - مكون الرسم البياني
 */
const MonthlyProfitChart = ({ data }) => {
  // التأكد من وجود البيانات
  if (!data || !data.labels || !data.profit || !data.profitMargin) {
    return (
      <div className="chart-placeholder">
        <p>لا توجد بيانات كافية لعرض الرسم البياني</p>
      </div>
    );
  }

  // إعداد خيارات الرسم البياني
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          font: {
            family: 'Cairo, sans-serif',
            size: 12
          }
        }
      },
      title: {
        display: true,
        text: 'تطور الأرباح الشهرية',
        font: {
          family: 'Cairo, sans-serif',
          size: 16,
          weight: 'bold'
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.y !== null) {
              if (context.dataset.yAxisID === 'y') {
                label += new Intl.NumberFormat('ar-LY', {
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 0
                }).format(context.parsed.y) + ' د.ل';
              } else {
                label += new Intl.NumberFormat('ar-LY', {
                  style: 'percent',
                  minimumFractionDigits: 1,
                  maximumFractionDigits: 1
                }).format(context.parsed.y / 100);
              }
            }
            return label;
          }
        }
      }
    },
    scales: {
      x: {
        ticks: {
          font: {
            family: 'Cairo, sans-serif',
            size: 11
          }
        }
      },
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        beginAtZero: true,
        ticks: {
          font: {
            family: 'Cairo, sans-serif',
            size: 11
          },
          callback: function(value) {
            return new Intl.NumberFormat('ar-LY', {
              minimumFractionDigits: 0,
              maximumFractionDigits: 0
            }).format(value) + ' د.ل';
          }
        }
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        beginAtZero: true,
        min: 0,
        max: 100,
        ticks: {
          font: {
            family: 'Cairo, sans-serif',
            size: 11
          },
          callback: function(value) {
            return new Intl.NumberFormat('ar-LY', {
              style: 'percent',
              minimumFractionDigits: 0,
              maximumFractionDigits: 0
            }).format(value / 100);
          }
        },
        grid: {
          drawOnChartArea: false
        }
      }
    }
  };

  // إعداد بيانات الرسم البياني
  const chartData = {
    labels: data.labels,
    datasets: [
      {
        label: 'الأرباح',
        data: data.profit,
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.5)',
        yAxisID: 'y',
        tension: 0.3
      },
      {
        label: 'هامش الربح',
        data: data.profitMargin,
        borderColor: 'rgb(255, 159, 64)',
        backgroundColor: 'rgba(255, 159, 64, 0.5)',
        yAxisID: 'y1',
        tension: 0.3
      }
    ]
  };

  return (
    <div className="chart-container" style={{ height: '300px', width: '100%' }}>
      <Line options={options} data={chartData} />
    </div>
  );
};

export default MonthlyProfitChart;
