/* أنماط التجاوب مع مختلف أحجام الشاشات */

/* الشاشات الكبيرة (أكبر من 1200 بكسل) */
@media (min-width: 1201px) {
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .settings-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* الشاشات المتوسطة (992 إلى 1200 بكسل) */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }
  
  .settings-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .backup-card {
    grid-column: span 2;
  }
  
  .main-content-full {
    padding: var(--spacing-md);
  }
  
  .table-responsive {
    overflow-x: auto;
  }
}

/* الشاشات الصغيرة (768 إلى 991 بكسل) */
@media (max-width: 991px) {
  .navbar-menu {
    max-width: 100%;
    overflow-x: auto;
    justify-content: flex-start;
    padding-bottom: 5px;
  }
  
  .navbar-menu-item {
    padding: 0 var(--spacing-xs);
    min-width: auto;
  }
  
  .navbar-menu-item .menu-text {
    font-size: 0.8rem;
  }
  
  .dashboard-welcome h1 {
    font-size: 1.5rem;
  }
  
  .page-title {
    font-size: 1.5rem;
  }
  
  .stat-card-value {
    font-size: 1.5rem;
  }
}

/* الشاشات الصغيرة جداً (أقل من 768 بكسل) */
@media (max-width: 767px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .settings-grid {
    grid-template-columns: 1fr;
  }
  
  .backup-card {
    grid-column: span 1;
  }
  
  .navbar {
    height: auto;
    flex-direction: column;
    padding: var(--spacing-xs) 0;
  }
  
  .navbar-brand {
    margin-bottom: var(--spacing-xs);
  }
  
  .navbar-menu {
    width: 100%;
    overflow-x: auto;
    justify-content: flex-start;
    margin-bottom: var(--spacing-xs);
  }
  
  .navbar-user {
    width: 100%;
    justify-content: flex-end;
    padding: 0 var(--spacing-sm);
  }
  
  .main-content-full {
    margin-top: 120px;
    padding: var(--spacing-sm);
  }
  
  .modal-dialog {
    width: 95%;
    margin: 10px auto;
  }
  
  .form-row {
    flex-direction: column;
  }
  
  .form-group {
    width: 100% !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
  
  .btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }
  
  .table th,
  .table td {
    padding: var(--spacing-xs);
    font-size: 0.9rem;
  }
  
  .dashboard-welcome {
    padding: var(--spacing-md);
  }
  
  .dashboard-welcome h1 {
    font-size: 1.3rem;
  }
  
  .dashboard-welcome p {
    font-size: 0.9rem;
  }
  
  .page-title {
    font-size: 1.3rem;
  }
  
  .page-subtitle {
    font-size: 0.9rem;
  }
  
  .chart-title {
    font-size: 1rem;
  }
}

/* الهواتف الصغيرة (أقل من 576 بكسل) */
@media (max-width: 575px) {
  .navbar-menu-item {
    padding: 0 var(--spacing-xs);
  }
  
  .navbar-menu-item .menu-icon {
    margin-left: 0;
  }
  
  .navbar-menu-item .menu-text {
    display: none;
  }
  
  .stat-card {
    padding: var(--spacing-sm);
  }
  
  .stat-card-icon {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
  
  .stat-card-value {
    font-size: 1.3rem;
  }
  
  .stat-card-title {
    font-size: 0.8rem;
  }
  
  .table th,
  .table td {
    padding: 5px;
    font-size: 0.8rem;
  }
  
  .badge {
    font-size: 0.7rem;
    padding: 2px 5px;
  }
  
  .main-content-full {
    padding: var(--spacing-xs);
  }
  
  .chart-container,
  .table-container {
    margin-bottom: var(--spacing-md);
  }
  
  .chart-header {
    padding: var(--spacing-xs);
  }
  
  .modal-header,
  .modal-body,
  .modal-footer {
    padding: var(--spacing-sm);
  }
  
  .form-label {
    font-size: 0.9rem;
  }
  
  .form-control {
    font-size: 0.9rem;
    padding: 0.3rem 0.5rem;
  }
}
