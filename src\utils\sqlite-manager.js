/**
 * مدير قاعدة بيانات SQLite
 * يوفر واجهة للتعامل مع قاعدة بيانات SQLite
 */

// استيراد المكتبات اللازمة
// ملاحظة: يجب تثبيت مكتبة better-sqlite3 أولاً
// يمكن تثبيتها باستخدام الأمر: yarn add better-sqlite3
let Database;
let path;
let fs;
let app;

// محاولة استيراد المكتبات
try {
  Database = require('better-sqlite3');
  path = require('path');
  fs = require('fs');
  const electron = require('electron');
  app = electron.app || (electron.remote && electron.remote.app);
} catch (error) {
  console.warn('لم يتم العثور على مكتبة better-sqlite3 أو electron. سيتم استخدام وضع المحاكاة.');
}

// استخدام نمط Singleton للتأكد من وجود نسخة واحدة فقط من مدير قاعدة البيانات
class SQLiteManager {
  constructor() {
    this.db = null;
    this.dbPath = '';
    this.isInitialized = false;
  }

  /**
   * تهيئة قاعدة البيانات
   * @param {Object} options - خيارات التهيئة
   * @returns {Object} - كائن قاعدة البيانات
   */
  initialize(options = {}) {
    try {
      // التحقق من توفر المكتبات اللازمة
      if (!Database || !path || !fs || !app) {
        console.warn('لم يتم العثور على المكتبات اللازمة. سيتم استخدام وضع المحاكاة.');
        this.isInitialized = true;
        return {
          success: true,
          message: 'تم تهيئة مدير قاعدة بيانات SQLite بنجاح (وضع المحاكاة)'
        };
      }

      // إغلاق الاتصال الحالي إذا كان مفتوحًا
      if (this.db) {
        try {
          this.db.close();
          console.log('تم إغلاق الاتصال السابق بقاعدة البيانات');
        } catch (closeError) {
          console.warn('خطأ في إغلاق الاتصال السابق بقاعدة البيانات:', closeError);
        }
        this.db = null;
      }

      // تحديد مسار قاعدة البيانات
      const dbDir = options.dbDir || path.join(app.getPath('userData'), 'wms-database');

      // التأكد من وجود المجلد
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true });
      }

      this.dbPath = path.join(dbDir, options.dbName || 'warehouse.db');

      console.log('مسار قاعدة البيانات:', this.dbPath);

      // إنشاء نسخة احتياطية قبل فتح قاعدة البيانات (إذا كانت موجودة)
      if (fs.existsSync(this.dbPath)) {
        try {
          const backupDir = path.join(app.getPath('userData'), 'backups');
          if (!fs.existsSync(backupDir)) {
            fs.mkdirSync(backupDir, { recursive: true });
          }

          const date = new Date().toISOString().replace(/:/g, '-').split('.')[0];
          const backupPath = path.join(backupDir, `auto_backup_${date}.db`);

          fs.copyFileSync(this.dbPath, backupPath);
          console.log(`تم إنشاء نسخة احتياطية في: ${backupPath}`);
        } catch (backupError) {
          console.warn('خطأ في إنشاء نسخة احتياطية:', backupError);
        }
      }

      // إنشاء اتصال بقاعدة البيانات
      try {
        this.db = new Database(this.dbPath, {
          verbose: console.log
        });

        // تمكين الدعم للمفاتيح الأجنبية
        this.db.pragma('foreign_keys = ON');

        // تحسين أداء قاعدة البيانات
        this.db.pragma('journal_mode = WAL');
        this.db.pragma('synchronous = NORMAL');
        this.db.pragma('temp_store = MEMORY');
        this.db.pragma('cache_size = 10000');

        console.log('تم إنشاء اتصال بقاعدة البيانات بنجاح');
      } catch (dbError) {
        console.error('خطأ في إنشاء اتصال بقاعدة البيانات:', dbError);

        // محاولة إصلاح قاعدة البيانات
        try {
          console.log('محاولة إصلاح قاعدة البيانات...');

          // إنشاء قاعدة بيانات جديدة
          this.db = new Database(this.dbPath, {
            verbose: console.log
          });

          console.log('تم إصلاح قاعدة البيانات بنجاح');
        } catch (repairError) {
          console.error('فشل في إصلاح قاعدة البيانات:', repairError);
          throw repairError;
        }
      }

      // إنشاء الجداول إذا لم تكن موجودة
      this.createTables();

      // إنشاء الفهارس
      this.createIndexes();

      // التحقق من سلامة قاعدة البيانات
      try {
        const integrityCheck = this.db.pragma('integrity_check');
        console.log('نتيجة فحص سلامة قاعدة البيانات:', integrityCheck);

        if (integrityCheck !== 'ok') {
          console.warn('تم اكتشاف مشاكل في سلامة قاعدة البيانات');
        }
      } catch (integrityError) {
        console.warn('خطأ في فحص سلامة قاعدة البيانات:', integrityError);
      }

      // تعيين حالة التهيئة
      this.isInitialized = true;

      console.log('تم تهيئة مدير قاعدة بيانات SQLite بنجاح');

      return {
        success: true,
        message: 'تم تهيئة مدير قاعدة بيانات SQLite بنجاح'
      };
    } catch (error) {
      console.error('خطأ في تهيئة مدير قاعدة بيانات SQLite:', error);

      // استخدام وضع المحاكاة في حالة الخطأ
      this.isInitialized = true;

      return {
        success: false,
        message: `خطأ في تهيئة مدير قاعدة بيانات SQLite: ${error.message}`,
        error: error
      };
    }
  }

  /**
   * إنشاء جداول قاعدة البيانات
   */
  createTables() {
    try {
      // التحقق من توفر قاعدة البيانات
      if (!this.db) {
        console.warn('لم يتم تهيئة قاعدة البيانات. سيتم استخدام وضع المحاكاة.');
        return {
          success: true,
          message: 'تم إنشاء جداول قاعدة البيانات بنجاح (وضع المحاكاة)'
        };
      }

      console.log('إنشاء جداول قاعدة البيانات...');

      // إنشاء جدول المستخدمين
      this.db.exec(`
        CREATE TABLE IF NOT EXISTS users (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          username TEXT UNIQUE NOT NULL,
          password TEXT NOT NULL,
          full_name TEXT,
          role TEXT NOT NULL,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // إنشاء جدول الأصناف
      this.db.exec(`
        CREATE TABLE IF NOT EXISTS items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          unit TEXT,
          minimum_quantity INTEGER DEFAULT 0,
          avg_price REAL DEFAULT 0,
          selling_price REAL DEFAULT 0,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // إنشاء جدول المخزون
      this.db.exec(`
        CREATE TABLE IF NOT EXISTS inventory (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          item_id INTEGER NOT NULL,
          current_quantity INTEGER DEFAULT 0,
          last_updated TEXT DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (item_id) REFERENCES items (id)
        )
      `);

      // إنشاء جدول المعاملات
      this.db.exec(`
        CREATE TABLE IF NOT EXISTS transactions (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          transaction_id TEXT UNIQUE NOT NULL,
          item_id INTEGER,
          transaction_type TEXT NOT NULL,
          quantity INTEGER NOT NULL,
          price REAL DEFAULT 0,
          total_price REAL DEFAULT 0,
          profit REAL DEFAULT 0,
          customer TEXT,
          invoice_number TEXT,
          notes TEXT,
          transaction_date TEXT DEFAULT CURRENT_TIMESTAMP,
          user_id INTEGER,
          FOREIGN KEY (item_id) REFERENCES items (id),
          FOREIGN KEY (user_id) REFERENCES users (id)
        )
      `);

      // إنشاء جدول العملاء
      this.db.exec(`
        CREATE TABLE IF NOT EXISTS customers (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          contact_person TEXT,
          phone TEXT,
          email TEXT,
          address TEXT,
          customer_type TEXT DEFAULT 'normal',
          parent_id INTEGER,
          credit_limit REAL DEFAULT 0,
          balance REAL DEFAULT 0,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // إنشاء جدول الآلات
      this.db.exec(`
        CREATE TABLE IF NOT EXISTS machines (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          purchase_date TEXT NOT NULL,
          purchase_price REAL NOT NULL,
          current_value REAL NOT NULL,
          notes TEXT,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // إنشاء جدول الإعدادات
      this.db.exec(`
        CREATE TABLE IF NOT EXISTS settings (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          key TEXT UNIQUE NOT NULL,
          value TEXT,
          updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
      `);

      console.log('تم إنشاء جداول قاعدة البيانات بنجاح');

      return {
        success: true,
        message: 'تم إنشاء جداول قاعدة البيانات بنجاح'
      };
    } catch (error) {
      console.error('خطأ في إنشاء جداول قاعدة البيانات:', error);

      return {
        success: false,
        message: `خطأ في إنشاء جداول قاعدة البيانات: ${error.message}`,
        error: error
      };
    }
  }

  /**
   * إنشاء فهارس قاعدة البيانات
   */
  createIndexes() {
    try {
      // التحقق من توفر قاعدة البيانات
      if (!this.db) {
        console.warn('لم يتم تهيئة قاعدة البيانات. سيتم استخدام وضع المحاكاة.');
        return {
          success: true,
          message: 'تم إنشاء فهارس قاعدة البيانات بنجاح (وضع المحاكاة)'
        };
      }

      console.log('إنشاء فهارس قاعدة البيانات...');

      // إنشاء فهارس للأصناف
      this.db.exec('CREATE INDEX IF NOT EXISTS idx_items_name ON items(name)');

      // إنشاء فهارس للمخزون
      this.db.exec('CREATE INDEX IF NOT EXISTS idx_inventory_item_id ON inventory(item_id)');

      // إنشاء فهارس للمعاملات
      this.db.exec('CREATE INDEX IF NOT EXISTS idx_transactions_transaction_id ON transactions(transaction_id)');
      this.db.exec('CREATE INDEX IF NOT EXISTS idx_transactions_item_id ON transactions(item_id)');
      this.db.exec('CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(transaction_type)');
      this.db.exec('CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(transaction_date)');
      this.db.exec('CREATE INDEX IF NOT EXISTS idx_transactions_customer ON transactions(customer)');
      this.db.exec('CREATE INDEX IF NOT EXISTS idx_transactions_invoice ON transactions(invoice_number)');

      // إنشاء فهارس للعملاء
      this.db.exec('CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(name)');
      this.db.exec('CREATE INDEX IF NOT EXISTS idx_customers_type ON customers(customer_type)');
      this.db.exec('CREATE INDEX IF NOT EXISTS idx_customers_parent ON customers(parent_id)');

      // إنشاء فهارس للآلات
      this.db.exec('CREATE INDEX IF NOT EXISTS idx_machines_name ON machines(name)');

      // إنشاء فهارس للإعدادات
      this.db.exec('CREATE INDEX IF NOT EXISTS idx_settings_key ON settings(key)');

      console.log('تم إنشاء فهارس قاعدة البيانات بنجاح');

      return {
        success: true,
        message: 'تم إنشاء فهارس قاعدة البيانات بنجاح'
      };
    } catch (error) {
      console.error('خطأ في إنشاء فهارس قاعدة البيانات:', error);

      return {
        success: false,
        message: `خطأ في إنشاء فهارس قاعدة البيانات: ${error.message}`,
        error: error
      };
    }
  }

  /**
   * إغلاق اتصال قاعدة البيانات
   */
  close() {
    try {
      // التحقق من توفر قاعدة البيانات
      if (!this.db) {
        console.warn('لم يتم تهيئة قاعدة البيانات. سيتم استخدام وضع المحاكاة.');
        this.isInitialized = false;
        return {
          success: true,
          message: 'تم إغلاق اتصال قاعدة البيانات بنجاح (وضع المحاكاة)'
        };
      }

      console.log('إغلاق اتصال قاعدة البيانات...');

      // إغلاق اتصال قاعدة البيانات
      this.db.close();

      // إعادة تعيين المتغيرات
      this.db = null;
      this.isInitialized = false;

      console.log('تم إغلاق اتصال قاعدة البيانات بنجاح');

      return {
        success: true,
        message: 'تم إغلاق اتصال قاعدة البيانات بنجاح'
      };
    } catch (error) {
      console.error('خطأ في إغلاق اتصال قاعدة البيانات:', error);

      // إعادة تعيين المتغيرات في حالة الخطأ
      this.db = null;
      this.isInitialized = false;

      return {
        success: false,
        message: `خطأ في إغلاق اتصال قاعدة البيانات: ${error.message}`,
        error: error
      };
    }
  }

  /**
   * التحقق من حالة التهيئة
   * @returns {Boolean} - حالة التهيئة
   */
  isReady() {
    return this.isInitialized && this.db !== null;
  }

  /**
   * إنشاء نسخة احتياطية من قاعدة البيانات
   * @param {String} backupPath - مسار النسخة الاحتياطية
   * @returns {Object} - نتيجة العملية
   */
  backup(backupPath) {
    try {
      // التحقق من توفر قاعدة البيانات
      if (!this.db) {
        console.warn('لم يتم تهيئة قاعدة البيانات. سيتم استخدام وضع المحاكاة.');
        return {
          success: true,
          message: `تم إنشاء نسخة احتياطية بنجاح في المسار: ${backupPath} (وضع المحاكاة)`
        };
      }

      // إذا لم يتم تحديد مسار النسخة الاحتياطية، استخدم المسار الافتراضي
      if (!backupPath) {
        const date = new Date().toISOString().replace(/:/g, '-').split('.')[0];
        backupPath = path.join(path.dirname(this.dbPath), `backup_${date}.db`);
      }

      console.log(`إنشاء نسخة احتياطية في المسار: ${backupPath}`);

      // إنشاء نسخة احتياطية باستخدام وظيفة backup
      const backup = this.db.backup(backupPath);

      // انتظار انتهاء النسخ الاحتياطي
      backup.step(-1);

      console.log('تم إنشاء نسخة احتياطية بنجاح');

      return {
        success: true,
        message: `تم إنشاء نسخة احتياطية بنجاح في المسار: ${backupPath}`,
        path: backupPath
      };
    } catch (error) {
      console.error('خطأ في إنشاء نسخة احتياطية:', error);

      return {
        success: false,
        message: `خطأ في إنشاء نسخة احتياطية: ${error.message}`,
        error: error
      };
    }
  }

  /**
   * الحصول على الأصناف
   * @returns {Array} - قائمة الأصناف
   */
  getItems() {
    try {
      // التحقق من توفر قاعدة البيانات
      if (!this.db) {
        console.warn('لم يتم تهيئة قاعدة البيانات. سيتم استخدام وضع المحاكاة.');
        return [];
      }

      console.log('الحصول على الأصناف...');

      // التحقق من وجود جدول الأصناف
      try {
        const tableCheck = this.db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='items'").get();
        if (!tableCheck) {
          console.warn('جدول الأصناف غير موجود. سيتم إنشاؤه...');
          this.createTables();
        }
      } catch (tableError) {
        console.error('خطأ في التحقق من وجود جدول الأصناف:', tableError);
        // إعادة تهيئة قاعدة البيانات
        this.createTables();
      }

      // استعلام الأصناف
      const stmt = this.db.prepare('SELECT * FROM items ORDER BY id DESC');
      const items = stmt.all();

      console.log(`تم الحصول على ${items.length} صنف بنجاح`);

      // التحقق من وجود بيانات المخزون لكل صنف
      for (const item of items) {
        try {
          const inventoryCheck = this.db.prepare('SELECT * FROM inventory WHERE item_id = ?').get(item.id);
          if (!inventoryCheck) {
            console.warn(`لا يوجد سجل مخزون للصنف ${item.id}. سيتم إنشاؤه...`);
            const inventoryStmt = this.db.prepare(`
              INSERT INTO inventory (item_id, current_quantity, last_updated)
              VALUES (?, ?, ?)
            `);
            inventoryStmt.run(
              item.id,
              0,
              new Date().toISOString()
            );
          }
        } catch (inventoryError) {
          console.error(`خطأ في التحقق من وجود سجل مخزون للصنف ${item.id}:`, inventoryError);
        }
      }

      return items;
    } catch (error) {
      console.error('خطأ في الحصول على الأصناف:', error);
      return [];
    }
  }

  /**
   * الحصول على الأصناف مع التحميل التدريجي
   * @param {Number} page - رقم الصفحة
   * @param {Number} pageSize - حجم الصفحة
   * @param {Object} filters - فلاتر التصفية
   * @returns {Object} - نتيجة الاستعلام
   */
  getItemsPaginated(page = 1, pageSize = 20, filters = {}) {
    try {
      // التحقق من توفر قاعدة البيانات
      if (!this.db) {
        console.warn('لم يتم تهيئة قاعدة البيانات. سيتم استخدام وضع المحاكاة.');
        return { items: [], totalCount: 0 };
      }

      console.log(`الحصول على الأصناف للصفحة ${page} بحجم ${pageSize}...`);

      // حساب معلمات التحميل التدريجي
      const offset = (page - 1) * pageSize;

      // بناء استعلام مع الفلاتر
      let query = 'SELECT * FROM items';
      let countQuery = 'SELECT COUNT(*) as count FROM items';
      const params = [];
      const whereConditions = [];

      // إضافة فلتر البحث
      if (filters.searchTerm) {
        whereConditions.push('name LIKE ?');
        params.push(`%${filters.searchTerm}%`);
      }

      // إضافة فلتر الوحدة
      if (filters.unit) {
        whereConditions.push('unit = ?');
        params.push(filters.unit);
      }

      // إضافة شروط WHERE إذا كانت موجودة
      if (whereConditions.length > 0) {
        query += ' WHERE ' + whereConditions.join(' AND ');
        countQuery += ' WHERE ' + whereConditions.join(' AND ');
      }

      // إضافة الترتيب والتحديد
      query += ' ORDER BY id DESC LIMIT ? OFFSET ?';

      // إضافة معلمات التحميل التدريجي
      params.push(pageSize, offset);

      // تنفيذ الاستعلام
      const stmt = this.db.prepare(query);
      const items = stmt.all(...params);

      // الحصول على العدد الإجمالي
      const countStmt = this.db.prepare(countQuery);
      const countParams = params.slice(0, params.length - 2); // إزالة معلمات LIMIT و OFFSET
      const { count } = countStmt.get(...countParams);

      console.log(`تم الحصول على ${items.length} صنف من إجمالي ${count} بنجاح`);

      return {
        items,
        totalCount: count
      };
    } catch (error) {
      console.error('خطأ في الحصول على الأصناف مع التحميل التدريجي:', error);
      return { items: [], totalCount: 0 };
    }
  }

  /**
   * إضافة صنف جديد
   * @param {Object} item - بيانات الصنف
   * @returns {Object} - الصنف المضاف
   */
  addItem(item) {
    try {
      // التحقق من توفر قاعدة البيانات
      if (!this.db) {
        console.warn('لم يتم تهيئة قاعدة البيانات. سيتم استخدام وضع المحاكاة.');
        return { ...item, id: Date.now() };
      }

      console.log('إضافة صنف جديد:', item);

      // التحقق من وجود جدول الأصناف
      try {
        const tableCheck = this.db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='items'").get();
        if (!tableCheck) {
          console.warn('جدول الأصناف غير موجود. سيتم إنشاؤه...');
          this.createTables();
        }
      } catch (tableError) {
        console.error('خطأ في التحقق من وجود جدول الأصناف:', tableError);
        // إعادة تهيئة قاعدة البيانات
        this.createTables();
      }

      // تنظيف بيانات الصنف
      const cleanedItem = {
        name: String(item.name || '').trim(),
        unit: String(item.unit || 'قطعة').trim(),
        minimum_quantity: Number(item.minimum_quantity || 0),
        avg_price: Number(item.avg_price || 0),
        selling_price: Number(item.selling_price || 0),
        created_at: new Date().toISOString()
      };

      console.log('بيانات الصنف بعد التنظيف:', cleanedItem);

      // بدء معاملة
      const transaction = this.db.transaction(() => {
        // إضافة الصنف
        const stmt = this.db.prepare(`
          INSERT INTO items (name, unit, minimum_quantity, avg_price, selling_price, created_at)
          VALUES (?, ?, ?, ?, ?, ?)
        `);

        const result = stmt.run(
          cleanedItem.name,
          cleanedItem.unit,
          cleanedItem.minimum_quantity,
          cleanedItem.avg_price,
          cleanedItem.selling_price,
          cleanedItem.created_at
        );

        // الحصول على معرف الصنف المضاف
        const itemId = result.lastInsertRowid;

        // التحقق من وجود جدول المخزون
        try {
          const inventoryTableCheck = this.db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='inventory'").get();
          if (!inventoryTableCheck) {
            console.warn('جدول المخزون غير موجود. سيتم إنشاؤه...');
            this.db.exec(`
              CREATE TABLE IF NOT EXISTS inventory (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                item_id INTEGER NOT NULL,
                current_quantity INTEGER DEFAULT 0,
                last_updated TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (item_id) REFERENCES items (id)
              )
            `);
          }
        } catch (inventoryTableError) {
          console.error('خطأ في التحقق من وجود جدول المخزون:', inventoryTableError);
          // إنشاء جدول المخزون
          this.db.exec(`
            CREATE TABLE IF NOT EXISTS inventory (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              item_id INTEGER NOT NULL,
              current_quantity INTEGER DEFAULT 0,
              last_updated TEXT DEFAULT CURRENT_TIMESTAMP,
              FOREIGN KEY (item_id) REFERENCES items (id)
            )
          `);
        }

        // إضافة سجل المخزون
        const inventoryStmt = this.db.prepare(`
          INSERT INTO inventory (item_id, current_quantity, last_updated)
          VALUES (?, ?, ?)
        `);

        inventoryStmt.run(
          itemId,
          0,
          new Date().toISOString()
        );

        // الحصول على الصنف المضاف
        const getItemStmt = this.db.prepare('SELECT * FROM items WHERE id = ?');
        const newItem = getItemStmt.get(itemId);

        if (!newItem) {
          throw new Error(`لم يتم العثور على الصنف بعد إضافته. المعرف: ${itemId}`);
        }

        return newItem;
      });

      try {
        // تنفيذ المعاملة
        const newItem = transaction();
        console.log('تم إضافة الصنف بنجاح:', newItem);
        return newItem;
      } catch (transactionError) {
        console.error('خطأ في تنفيذ معاملة إضافة الصنف:', transactionError);

        // محاولة إضافة الصنف بدون معاملة
        console.log('محاولة إضافة الصنف بدون معاملة...');

        // إضافة الصنف
        const stmt = this.db.prepare(`
          INSERT INTO items (name, unit, minimum_quantity, avg_price, selling_price, created_at)
          VALUES (?, ?, ?, ?, ?, ?)
        `);

        const result = stmt.run(
          cleanedItem.name,
          cleanedItem.unit,
          cleanedItem.minimum_quantity,
          cleanedItem.avg_price,
          cleanedItem.selling_price,
          cleanedItem.created_at
        );

        // الحصول على معرف الصنف المضاف
        const itemId = result.lastInsertRowid;

        // إضافة سجل المخزون
        const inventoryStmt = this.db.prepare(`
          INSERT INTO inventory (item_id, current_quantity, last_updated)
          VALUES (?, ?, ?)
        `);

        inventoryStmt.run(
          itemId,
          0,
          new Date().toISOString()
        );

        // الحصول على الصنف المضاف
        const getItemStmt = this.db.prepare('SELECT * FROM items WHERE id = ?');
        const newItem = getItemStmt.get(itemId);

        console.log('تم إضافة الصنف بنجاح (بدون معاملة):', newItem);
        return newItem;
      }
    } catch (error) {
      console.error('خطأ في إضافة الصنف:', error);

      // إعادة محاولة إضافة الصنف بعد إعادة تهيئة قاعدة البيانات
      try {
        console.log('إعادة محاولة إضافة الصنف بعد إعادة تهيئة قاعدة البيانات...');

        // إعادة تهيئة قاعدة البيانات
        this.createTables();

        // تنظيف بيانات الصنف
        const cleanedItem = {
          name: String(item.name || '').trim(),
          unit: String(item.unit || 'قطعة').trim(),
          minimum_quantity: Number(item.minimum_quantity || 0),
          avg_price: Number(item.avg_price || 0),
          selling_price: Number(item.selling_price || 0),
          created_at: new Date().toISOString()
        };

        // إضافة الصنف
        const stmt = this.db.prepare(`
          INSERT INTO items (name, unit, minimum_quantity, avg_price, selling_price, created_at)
          VALUES (?, ?, ?, ?, ?, ?)
        `);

        const result = stmt.run(
          cleanedItem.name,
          cleanedItem.unit,
          cleanedItem.minimum_quantity,
          cleanedItem.avg_price,
          cleanedItem.selling_price,
          cleanedItem.created_at
        );

        // الحصول على معرف الصنف المضاف
        const itemId = result.lastInsertRowid;

        // إضافة سجل المخزون
        const inventoryStmt = this.db.prepare(`
          INSERT INTO inventory (item_id, current_quantity, last_updated)
          VALUES (?, ?, ?)
        `);

        inventoryStmt.run(
          itemId,
          0,
          new Date().toISOString()
        );

        // الحصول على الصنف المضاف
        const getItemStmt = this.db.prepare('SELECT * FROM items WHERE id = ?');
        const newItem = getItemStmt.get(itemId);

        console.log('تم إضافة الصنف بنجاح (بعد إعادة المحاولة):', newItem);
        return newItem;
      } catch (retryError) {
        console.error('فشل في إعادة محاولة إضافة الصنف:', retryError);
        throw error; // إعادة رمي الخطأ الأصلي
      }
    }
  }

  /**
   * تحديث صنف
   * @param {Object} item - بيانات الصنف
   * @returns {Object} - الصنف المحدث
   */
  updateItem(item) {
    try {
      // التحقق من توفر قاعدة البيانات
      if (!this.db) {
        console.warn('لم يتم تهيئة قاعدة البيانات. سيتم استخدام وضع المحاكاة.');
        return { ...item };
      }

      console.log('تحديث الصنف:', item);

      // التحقق من وجود معرف الصنف
      if (!item.id) {
        throw new Error('معرف الصنف مطلوب للتحديث');
      }

      // بدء معاملة
      const transaction = this.db.transaction(() => {
        // تحديث الصنف
        const stmt = this.db.prepare(`
          UPDATE items
          SET name = ?, unit = ?, minimum_quantity = ?, avg_price = ?, selling_price = ?
          WHERE id = ?
        `);

        stmt.run(
          item.name,
          item.unit || 'قطعة',
          item.minimum_quantity || 0,
          item.avg_price || 0,
          item.selling_price || 0,
          item.id
        );

        // الحصول على الصنف المحدث
        const getItemStmt = this.db.prepare('SELECT * FROM items WHERE id = ?');
        return getItemStmt.get(item.id);
      });

      // تنفيذ المعاملة
      const updatedItem = transaction();

      console.log('تم تحديث الصنف بنجاح:', updatedItem);

      return updatedItem;
    } catch (error) {
      console.error('خطأ في تحديث الصنف:', error);
      throw error;
    }
  }

  /**
   * حذف صنف
   * @param {Number} id - معرف الصنف
   * @param {Boolean} forceDelete - إجبار الحذف حتى مع وجود معاملات مرتبطة
   * @returns {Object} - نتيجة الحذف
   */
  deleteItem(id, forceDelete = false) {
    try {
      // التحقق من توفر قاعدة البيانات
      if (!this.db) {
        console.warn('لم يتم تهيئة قاعدة البيانات. سيتم استخدام وضع المحاكاة.');
        return { success: true, message: 'تم حذف الصنف بنجاح (وضع المحاكاة)' };
      }

      console.log(`حذف الصنف بمعرف ${id}، إجبار الحذف: ${forceDelete}`);

      // التحقق من وجود معاملات مرتبطة بالصنف - تم تعطيل هذا التحقق
      const checkTransactionsStmt = this.db.prepare('SELECT COUNT(*) as count FROM transactions WHERE item_id = ?');
      const { count } = checkTransactionsStmt.get(id);

      console.log(`وجدت ${count} معاملة مرتبطة بالصنف ${id}. تم تفعيل خيار الحذف القسري تلقائيًا.`);

      // تم تعديل السلوك للسماح بحذف الأصناف التي لها معاملات بيع
      // تفعيل خيار الحذف القسري دائمًا
      // لا نحتاج لتعديل قيمة forceDelete هنا لأنها ستكون دائمًا true

      // بدء معاملة
      const transaction = this.db.transaction(() => {
        // تعطيل قيود المفتاح الأجنبي مؤقتًا
        this.db.pragma('foreign_keys = OFF');
        console.log('تم تعطيل قيود المفتاح الأجنبي مؤقتًا');

        // حذف جميع المعاملات المرتبطة بالصنف أولاً
        const deleteTransactionsStmt = this.db.prepare('DELETE FROM transactions WHERE item_id = ?');
        const transactionsResult = deleteTransactionsStmt.run(id);
        console.log(`تم حذف ${transactionsResult.changes} معاملة مرتبطة بالصنف ${id}`);

        // حذف المخزون المرتبط بالصنف
        const deleteInventoryStmt = this.db.prepare('DELETE FROM inventory WHERE item_id = ?');
        const inventoryResult = deleteInventoryStmt.run(id);
        console.log(`تم حذف سجل المخزون المرتبط بالصنف ${id}`);

        // حذف الصنف
        const deleteItemStmt = this.db.prepare('DELETE FROM items WHERE id = ?');
        const result = deleteItemStmt.run(id);

        // إعادة تفعيل قيود المفتاح الأجنبي
        this.db.pragma('foreign_keys = ON');
        console.log('تم إعادة تفعيل قيود المفتاح الأجنبي');

        return {
          success: result.changes > 0,
          message: result.changes > 0 ? 'تم حذف الصنف بنجاح' : 'لم يتم العثور على الصنف',
          deletedTransactions: transactionsResult.changes
        };
      });

      // تنفيذ المعاملة
      const result = transaction();

      console.log('نتيجة حذف الصنف:', result);

      return result;
    } catch (error) {
      console.error('خطأ في حذف الصنف:', error);

      return {
        success: false,
        message: `خطأ في حذف الصنف: ${error.message}`,
        error: error
      };
    }
  }

  /**
   * الحصول على المخزون
   * @returns {Array} - قائمة المخزون
   */
  getInventory() {
    try {
      // التحقق من توفر قاعدة البيانات
      if (!this.db) {
        console.warn('لم يتم تهيئة قاعدة البيانات. سيتم استخدام وضع المحاكاة.');
        return [];
      }

      console.log('الحصول على المخزون...');

      // استعلام المخزون مع معلومات الأصناف
      const stmt = this.db.prepare(`
        SELECT i.*, it.name as item_name, it.unit, it.minimum_quantity, it.avg_price, it.selling_price
        FROM inventory i
        JOIN items it ON i.item_id = it.id
        ORDER BY it.name
      `);

      const inventory = stmt.all();

      console.log(`تم الحصول على ${inventory.length} سجل مخزون بنجاح`);

      return inventory;
    } catch (error) {
      console.error('خطأ في الحصول على المخزون:', error);
      return [];
    }
  }

  /**
   * تحديث المخزون
   * @param {Number} itemId - معرف الصنف
   * @param {Object} updates - التحديثات
   * @returns {Object} - سجل المخزون المحدث
   */
  updateInventory(itemId, updates) {
    try {
      // التحقق من توفر قاعدة البيانات
      if (!this.db) {
        console.warn('لم يتم تهيئة قاعدة البيانات. سيتم استخدام وضع المحاكاة.');
        return { item_id: itemId, ...updates };
      }

      console.log(`تحديث المخزون للصنف ${itemId}:`, updates);

      // بدء معاملة
      const transaction = this.db.transaction(() => {
        // تحديث المخزون
        const stmt = this.db.prepare(`
          UPDATE inventory
          SET current_quantity = ?, last_updated = ?
          WHERE item_id = ?
        `);

        stmt.run(
          updates.current_quantity || 0,
          new Date().toISOString(),
          itemId
        );

        // الحصول على سجل المخزون المحدث
        const getInventoryStmt = this.db.prepare(`
          SELECT i.*, it.name as item_name, it.unit, it.minimum_quantity, it.avg_price, it.selling_price
          FROM inventory i
          JOIN items it ON i.item_id = it.id
          WHERE i.item_id = ?
        `);

        return getInventoryStmt.get(itemId);
      });

      // تنفيذ المعاملة
      const updatedInventory = transaction();

      console.log('تم تحديث المخزون بنجاح:', updatedInventory);

      return updatedInventory;
    } catch (error) {
      console.error('خطأ في تحديث المخزون:', error);
      throw error;
    }
  }

  /**
   * استعادة قاعدة البيانات من نسخة احتياطية
   * @param {String} backupPath - مسار النسخة الاحتياطية
   * @returns {Object} - نتيجة العملية
   */
  restore(backupPath) {
    try {
      // التحقق من توفر قاعدة البيانات
      if (!this.db) {
        console.warn('لم يتم تهيئة قاعدة البيانات. سيتم استخدام وضع المحاكاة.');
        return {
          success: true,
          message: `تم استعادة قاعدة البيانات بنجاح من المسار: ${backupPath} (وضع المحاكاة)`
        };
      }

      // التحقق من وجود ملف النسخة الاحتياطية
      if (!fs.existsSync(backupPath)) {
        return {
          success: false,
          message: `ملف النسخة الاحتياطية غير موجود: ${backupPath}`
        };
      }

      console.log(`استعادة قاعدة البيانات من المسار: ${backupPath}`);

      // إغلاق اتصال قاعدة البيانات الحالي
      this.db.close();

      // نسخ ملف النسخة الاحتياطية إلى مسار قاعدة البيانات
      fs.copyFileSync(backupPath, this.dbPath);

      // إعادة فتح اتصال قاعدة البيانات
      this.db = new Database(this.dbPath, {
        verbose: console.log
      });

      // تمكين الدعم للمفاتيح الأجنبية
      this.db.pragma('foreign_keys = ON');

      console.log('تم استعادة قاعدة البيانات بنجاح');

      return {
        success: true,
        message: `تم استعادة قاعدة البيانات بنجاح من المسار: ${backupPath}`
      };
    } catch (error) {
      console.error('خطأ في استعادة قاعدة البيانات:', error);

      // محاولة إعادة فتح اتصال قاعدة البيانات في حالة الخطأ
      try {
        this.db = new Database(this.dbPath, {
          verbose: console.log
        });

        // تمكين الدعم للمفاتيح الأجنبية
        this.db.pragma('foreign_keys = ON');
      } catch (reopenError) {
        console.error('خطأ في إعادة فتح اتصال قاعدة البيانات:', reopenError);
      }

      return {
        success: false,
        message: `خطأ في استعادة قاعدة البيانات: ${error.message}`,
        error: error
      };
    }
  }

  /**
   * الحصول على المعاملات
   * @param {Object} filters - فلاتر التصفية
   * @returns {Array} - قائمة المعاملات
   */
  getTransactions(filters = null) {
    try {
      // التحقق من توفر قاعدة البيانات
      if (!this.db) {
        console.warn('لم يتم تهيئة قاعدة البيانات. سيتم استخدام وضع المحاكاة.');
        return [];
      }

      console.log('الحصول على المعاملات مع الفلاتر:', filters);

      // بناء استعلام مع الفلاتر
      let query = `
        SELECT t.*, i.name as item_name, i.unit
        FROM transactions t
        LEFT JOIN items i ON t.item_id = i.id
      `;

      const params = [];
      const whereConditions = [];

      // إضافة الفلاتر إذا كانت موجودة
      if (filters) {
        // فلتر نوع المعاملة
        if (filters.transaction_type) {
          whereConditions.push('t.transaction_type = ?');
          params.push(filters.transaction_type);
        }

        // فلتر معرف الصنف
        if (filters.item_id) {
          whereConditions.push('t.item_id = ?');
          params.push(filters.item_id);
        }

        // فلتر العميل
        if (filters.customer) {
          whereConditions.push('t.customer LIKE ?');
          params.push(`%${filters.customer}%`);
        }

        // فلتر رقم الفاتورة
        if (filters.invoice_number) {
          whereConditions.push('t.invoice_number LIKE ?');
          params.push(`%${filters.invoice_number}%`);
        }

        // فلتر تاريخ البداية
        if (filters.start_date) {
          whereConditions.push('t.transaction_date >= ?');
          params.push(filters.start_date);
        }

        // فلتر تاريخ النهاية
        if (filters.end_date) {
          whereConditions.push('t.transaction_date <= ?');
          params.push(filters.end_date);
        }
      }

      // إضافة شروط WHERE إذا كانت موجودة
      if (whereConditions.length > 0) {
        query += ' WHERE ' + whereConditions.join(' AND ');
      }

      // إضافة الترتيب
      query += ' ORDER BY t.transaction_date DESC';

      // تنفيذ الاستعلام
      const stmt = this.db.prepare(query);
      const transactions = stmt.all(...params);

      console.log(`تم الحصول على ${transactions.length} معاملة بنجاح`);

      return transactions;
    } catch (error) {
      console.error('خطأ في الحصول على المعاملات:', error);
      return [];
    }
  }

  /**
   * الحصول على المعاملات مع التحميل التدريجي
   * @param {Object} queryParams - معلمات الاستعلام
   * @returns {Object} - نتيجة الاستعلام
   */
  getTransactionsPaginated(queryParams = {}) {
    try {
      // التحقق من توفر قاعدة البيانات
      if (!this.db) {
        console.warn('لم يتم تهيئة قاعدة البيانات. سيتم استخدام وضع المحاكاة.');
        return { transactions: [], totalCount: 0 };
      }

      const { skip = 0, limit = 20, ...filters } = queryParams;

      console.log(`الحصول على المعاملات مع التحميل التدريجي (skip: ${skip}, limit: ${limit})`, filters);

      // بناء استعلام مع الفلاتر
      let query = `
        SELECT t.*, i.name as item_name, i.unit
        FROM transactions t
        LEFT JOIN items i ON t.item_id = i.id
      `;

      let countQuery = 'SELECT COUNT(*) as count FROM transactions t';

      const params = [];
      const whereConditions = [];

      // إضافة الفلاتر إذا كانت موجودة
      if (filters) {
        // فلتر نوع المعاملة
        if (filters.transaction_type) {
          whereConditions.push('t.transaction_type = ?');
          params.push(filters.transaction_type);
        }

        // فلتر معرف الصنف
        if (filters.item_id) {
          whereConditions.push('t.item_id = ?');
          params.push(filters.item_id);
        }

        // فلتر العميل
        if (filters.customer) {
          whereConditions.push('t.customer LIKE ?');
          params.push(`%${filters.customer}%`);
        }

        // فلتر رقم الفاتورة
        if (filters.invoice_number) {
          whereConditions.push('t.invoice_number LIKE ?');
          params.push(`%${filters.invoice_number}%`);
        }

        // فلتر تاريخ البداية
        if (filters.start_date) {
          whereConditions.push('t.transaction_date >= ?');
          params.push(filters.start_date);
        }

        // فلتر تاريخ النهاية
        if (filters.end_date) {
          whereConditions.push('t.transaction_date <= ?');
          params.push(filters.end_date);
        }
      }

      // إضافة شروط WHERE إذا كانت موجودة
      if (whereConditions.length > 0) {
        query += ' WHERE ' + whereConditions.join(' AND ');
        countQuery += ' WHERE ' + whereConditions.join(' AND ');
      }

      // إضافة الترتيب والتحديد
      query += ' ORDER BY t.transaction_date DESC LIMIT ? OFFSET ?';

      // إضافة معلمات التحميل التدريجي
      const queryParamsWithPagination = [...params, limit, skip];

      // تنفيذ الاستعلام
      const stmt = this.db.prepare(query);
      const transactions = stmt.all(...queryParamsWithPagination);

      // الحصول على العدد الإجمالي
      const countStmt = this.db.prepare(countQuery);
      const { count } = countStmt.get(...params);

      console.log(`تم الحصول على ${transactions.length} معاملة من إجمالي ${count} بنجاح`);

      return {
        transactions,
        totalCount: count
      };
    } catch (error) {
      console.error('خطأ في الحصول على المعاملات مع التحميل التدريجي:', error);
      return { transactions: [], totalCount: 0 };
    }
  }

  /**
   * إضافة معاملة جديدة
   * @param {Object} transaction - بيانات المعاملة
   * @returns {Object} - المعاملة المضافة
   */
  addTransaction(transaction) {
    try {
      // التحقق من توفر قاعدة البيانات
      if (!this.db) {
        console.warn('لم يتم تهيئة قاعدة البيانات. سيتم استخدام وضع المحاكاة.');
        return { ...transaction, id: Date.now(), transaction_id: `T${Date.now()}` };
      }

      console.log('إضافة معاملة جديدة:', transaction);

      // بدء معاملة
      const dbTransaction = this.db.transaction(() => {
        // إنشاء معرف معاملة فريد
        // Ensure unique transaction_id
const { generateTransactionId } = require('./transaction-id');
const transactionId = transaction.transaction_id || generateTransactionId();

        // إضافة المعاملة
        const stmt = this.db.prepare(`
          INSERT INTO transactions (
            transaction_id, item_id, transaction_type, quantity, price, total_price,
            profit, customer, invoice_number, notes, transaction_date, user_id
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        const result = stmt.run(
          transactionId,
          transaction.item_id,
          transaction.transaction_type,
          transaction.quantity,
          transaction.price || 0,
          transaction.total_price || 0,
          transaction.profit || 0,
          transaction.customer || null,
          transaction.invoice_number || null,
          transaction.notes || null,
          transaction.transaction_date || new Date().toISOString(),
          transaction.user_id || null
        );

        // تحديث المخزون
        if (transaction.item_id) {
          // الحصول على المخزون الحالي
          const getInventoryStmt = this.db.prepare('SELECT current_quantity FROM inventory WHERE item_id = ?');
          const inventory = getInventoryStmt.get(transaction.item_id);

          if (inventory) {
            let newQuantity = inventory.current_quantity;

            // تحديث الكمية بناءً على نوع المعاملة
            if (transaction.transaction_type === 'purchase') {
              newQuantity += transaction.quantity;
            } else if (transaction.transaction_type === 'sale') {
              newQuantity -= transaction.quantity;
            } else if (transaction.transaction_type === 'return') {
              // منطق المرتجعات: زيادة الكمية في المخزون عند استرجاع صنف
              newQuantity += transaction.quantity;
            }

            // تحديث المخزون
            const updateInventoryStmt = this.db.prepare(`
              UPDATE inventory
              SET current_quantity = ?, last_updated = ?
              WHERE item_id = ?
            `);

            updateInventoryStmt.run(
              newQuantity,
              new Date().toISOString(),
              transaction.item_id
            );

            // تحديث متوسط سعر الشراء إذا كانت المعاملة شراء
            if (transaction.transaction_type === 'purchase' && transaction.price) {
              const getItemStmt = this.db.prepare('SELECT avg_price FROM items WHERE id = ?');
              const item = getItemStmt.get(transaction.item_id);

              if (item) {
                // حساب متوسط السعر الجديد
                const oldTotalValue = item.avg_price * inventory.current_quantity;
                const newValue = transaction.price * transaction.quantity;
                const newTotalQuantity = inventory.current_quantity + transaction.quantity;
                const newAvgPrice = (oldTotalValue + newValue) / newTotalQuantity;

                // تحديث متوسط السعر
                const updateItemStmt = this.db.prepare(`
                  UPDATE items
                  SET avg_price = ?
                  WHERE id = ?
                `);

                updateItemStmt.run(
                  newAvgPrice,
                  transaction.item_id
                );
              }
            }
          }
        }

        // الحصول على المعاملة المضافة
        const getTransactionStmt = this.db.prepare(`
          SELECT t.*, i.name as item_name, i.unit
          FROM transactions t
          LEFT JOIN items i ON t.item_id = i.id
          WHERE t.id = ?
        `);

        return getTransactionStmt.get(result.lastInsertRowid);
      });

      // تنفيذ المعاملة
      const newTransaction = dbTransaction();

      console.log('تم إضافة المعاملة بنجاح:', newTransaction);

      return newTransaction;
    } catch (error) {
      console.error('خطأ في إضافة المعاملة:', error);
      throw error;
    }
  }

  /**
   * إضافة عميل جديد
   * @param {Object} customer - بيانات العميل
   * @returns {Object} - العميل المضاف
   */
  addCustomer(customer) {
    try {
      // التحقق من توفر قاعدة البيانات
      if (!this.db) {
        console.warn('لم يتم تهيئة قاعدة البيانات. سيتم استخدام وضع المحاكاة.');
        return { ...customer, id: Date.now() };
      }

      console.log('إضافة عميل جديد:', customer);

      // التحقق من وجود جدول العملاء
      try {
        const tableCheck = this.db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='customers'").get();
        if (!tableCheck) {
          console.warn('جدول العملاء غير موجود. سيتم إنشاؤه...');
          this.createTables();
        }
      } catch (tableError) {
        console.error('خطأ في التحقق من وجود جدول العملاء:', tableError);
        // إعادة تهيئة قاعدة البيانات
        this.createTables();
      }

      // تنظيف بيانات العميل
      const cleanedCustomer = {
        name: String(customer.name || '').trim(),
        contact_person: String(customer.contact_person || '').trim(),
        phone: String(customer.phone || '').trim(),
        email: String(customer.email || '').trim(),
        address: String(customer.address || '').trim(),
        customer_type: String(customer.customer_type || 'normal').trim(),
        parent_id: customer.parent_id || null,
        credit_limit: Number(customer.credit_limit || 0),
        balance: Number(customer.balance || 0),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      console.log('بيانات العميل بعد التنظيف:', cleanedCustomer);

      // معالجة خاصة للعملاء الفرعيين
      if (cleanedCustomer.customer_type === 'sub' && cleanedCustomer.parent_id) {
        console.log('إضافة عميل فرعي مع العميل الدائم:', cleanedCustomer.parent_id);

        // التحقق من وجود العميل الدائم
        try {
          const parentCheck = this.db.prepare('SELECT * FROM customers WHERE id = ?').get(cleanedCustomer.parent_id);
          if (!parentCheck) {
            console.warn(`العميل الدائم غير موجود: ${cleanedCustomer.parent_id}`);
          } else {
            console.log('تم العثور على العميل الدائم:', parentCheck.name);
          }
        } catch (parentError) {
          console.error('خطأ في التحقق من وجود العميل الدائم:', parentError);
        }
      }

      // بدء معاملة
      const transaction = this.db.transaction(() => {
        // إضافة العميل
        const stmt = this.db.prepare(`
          INSERT INTO customers (
            name, contact_person, phone, email, address,
            customer_type, parent_id, credit_limit, balance,
            created_at, updated_at
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        const result = stmt.run(
          cleanedCustomer.name,
          cleanedCustomer.contact_person,
          cleanedCustomer.phone,
          cleanedCustomer.email,
          cleanedCustomer.address,
          cleanedCustomer.customer_type,
          cleanedCustomer.parent_id,
          cleanedCustomer.credit_limit,
          cleanedCustomer.balance,
          cleanedCustomer.created_at,
          cleanedCustomer.updated_at
        );

        // الحصول على معرف العميل المضاف
        const customerId = result.lastInsertRowid;

        // الحصول على العميل المضاف
        const getCustomerStmt = this.db.prepare('SELECT * FROM customers WHERE id = ?');
        const newCustomer = getCustomerStmt.get(customerId);

        if (!newCustomer) {
          throw new Error(`لم يتم العثور على العميل بعد إضافته. المعرف: ${customerId}`);
        }

        return {
          ...newCustomer,
          _id: newCustomer.id.toString(),
          id: newCustomer.id.toString(),
          sales_history: [],
          total_sales: 0,
          total_profit: 0
        };
      });

      try {
        // تنفيذ المعاملة
        const newCustomer = transaction();
        console.log('تم إضافة العميل بنجاح:', newCustomer);
        return newCustomer;
      } catch (transactionError) {
        console.error('خطأ في تنفيذ معاملة إضافة العميل:', transactionError);

        // محاولة إضافة العميل بدون معاملة
        console.log('محاولة إضافة العميل بدون معاملة...');

        // إضافة العميل
        const stmt = this.db.prepare(`
          INSERT INTO customers (
            name, contact_person, phone, email, address,
            customer_type, parent_id, credit_limit, balance,
            created_at, updated_at
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        const result = stmt.run(
          cleanedCustomer.name,
          cleanedCustomer.contact_person,
          cleanedCustomer.phone,
          cleanedCustomer.email,
          cleanedCustomer.address,
          cleanedCustomer.customer_type,
          cleanedCustomer.parent_id,
          cleanedCustomer.credit_limit,
          cleanedCustomer.balance,
          cleanedCustomer.created_at,
          cleanedCustomer.updated_at
        );

        // الحصول على معرف العميل المضاف
        const customerId = result.lastInsertRowid;

        // الحصول على العميل المضاف
        const getCustomerStmt = this.db.prepare('SELECT * FROM customers WHERE id = ?');
        const newCustomer = getCustomerStmt.get(customerId);

        const processedCustomer = {
          ...newCustomer,
          _id: newCustomer.id.toString(),
          id: newCustomer.id.toString(),
          sales_history: [],
          total_sales: 0,
          total_profit: 0
        };

        console.log('تم إضافة العميل بنجاح (بدون معاملة):', processedCustomer);
        return processedCustomer;
      }
    } catch (error) {
      console.error('خطأ في إضافة العميل:', error);
      throw error;
    }
  }

  /**
   * تحديث عميل
   * @param {Object} customer - بيانات العميل
   * @returns {Object} - العميل المحدث
   */
  updateCustomer(customer) {
    try {
      // التحقق من توفر قاعدة البيانات
      if (!this.db) {
        console.warn('لم يتم تهيئة قاعدة البيانات. سيتم استخدام وضع المحاكاة.');
        return { ...customer };
      }

      console.log('تحديث العميل:', customer);

      // التحقق من وجود معرف العميل
      if (!customer.id && !customer._id) {
        throw new Error('معرف العميل مطلوب للتحديث');
      }

      // تنظيف بيانات العميل
      const cleanedCustomer = {
        name: String(customer.name || '').trim(),
        contact_person: String(customer.contact_person || '').trim(),
        phone: String(customer.phone || '').trim(),
        email: String(customer.email || '').trim(),
        address: String(customer.address || '').trim(),
        customer_type: String(customer.customer_type || 'normal').trim(),
        parent_id: customer.parent_id || null,
        credit_limit: Number(customer.credit_limit || 0),
        balance: Number(customer.balance || 0),
        updated_at: new Date().toISOString()
      };

      // معالجة خاصة للعملاء الفرعيين
      if (cleanedCustomer.customer_type === 'sub' && cleanedCustomer.parent_id) {
        console.log('تحديث عميل فرعي مع العميل الدائم:', cleanedCustomer.parent_id);

        // التحقق من وجود العميل الدائم
        try {
          const parentCheck = this.db.prepare('SELECT * FROM customers WHERE id = ?').get(cleanedCustomer.parent_id);
          if (!parentCheck) {
            console.warn(`العميل الدائم غير موجود: ${cleanedCustomer.parent_id}`);
          } else {
            console.log('تم العثور على العميل الدائم:', parentCheck.name);
          }
        } catch (parentError) {
          console.error('خطأ في التحقق من وجود العميل الدائم:', parentError);
        }
      }

      // الحصول على معرف العميل
      const customerId = customer.id || customer._id;

      // بدء معاملة
      const transaction = this.db.transaction(() => {
        // تحديث العميل
        const stmt = this.db.prepare(`
          UPDATE customers
          SET name = ?, contact_person = ?, phone = ?, email = ?, address = ?,
              customer_type = ?, parent_id = ?, credit_limit = ?, balance = ?, updated_at = ?
          WHERE id = ?
        `);

        stmt.run(
          cleanedCustomer.name,
          cleanedCustomer.contact_person,
          cleanedCustomer.phone,
          cleanedCustomer.email,
          cleanedCustomer.address,
          cleanedCustomer.customer_type,
          cleanedCustomer.parent_id,
          cleanedCustomer.credit_limit,
          cleanedCustomer.balance,
          cleanedCustomer.updated_at,
          customerId
        );

        // الحصول على العميل المحدث
        const getCustomerStmt = this.db.prepare('SELECT * FROM customers WHERE id = ?');
        const updatedCustomer = getCustomerStmt.get(customerId);

        if (!updatedCustomer) {
          throw new Error(`لم يتم العثور على العميل بعد تحديثه. المعرف: ${customerId}`);
        }

        // إضافة حقول إضافية للتوافق مع الواجهة الأمامية
        return {
          ...updatedCustomer,
          _id: updatedCustomer.id.toString(),
          id: updatedCustomer.id.toString(),
          sales_history: customer.sales_history || [],
          total_sales: customer.total_sales || 0,
          total_profit: customer.total_profit || 0
        };
      });

      // تنفيذ المعاملة
      const updatedCustomer = transaction();

      console.log('تم تحديث العميل بنجاح:', updatedCustomer);

      return updatedCustomer;
    } catch (error) {
      console.error('خطأ في تحديث العميل:', error);
      throw error;
    }
  }

  /**
   * حذف عميل
   * @param {Number} id - معرف العميل
   * @returns {Object} - نتيجة الحذف
   */
  deleteCustomer(id) {
    try {
      // التحقق من توفر قاعدة البيانات
      if (!this.db) {
        console.warn('لم يتم تهيئة قاعدة البيانات. سيتم استخدام وضع المحاكاة.');
        return { success: true, message: 'تم حذف العميل بنجاح (وضع المحاكاة)' };
      }

      console.log(`حذف العميل بمعرف ${id}`);

      // التحقق من وجود معاملات مرتبطة بالعميل
      const checkTransactionsStmt = this.db.prepare('SELECT COUNT(*) as count FROM transactions WHERE customer = ?');
      const { count } = checkTransactionsStmt.get(id);

      if (count > 0) {
        console.warn(`العميل مرتبط بـ ${count} معاملة. سيتم حذف المعاملات المرتبطة.`);
      }

      // بدء معاملة
      const transaction = this.db.transaction(() => {
        // حذف المعاملات المرتبطة بالعميل
        if (count > 0) {
          const deleteTransactionsStmt = this.db.prepare('DELETE FROM transactions WHERE customer = ?');
          deleteTransactionsStmt.run(id);
        }

        // حذف العميل
        const deleteCustomerStmt = this.db.prepare('DELETE FROM customers WHERE id = ?');
        const result = deleteCustomerStmt.run(id);

        return {
          success: result.changes > 0,
          message: result.changes > 0 ? 'تم حذف العميل بنجاح' : 'لم يتم العثور على العميل',
          deletedTransactions: count
        };
      });

      // تنفيذ المعاملة
      const result = transaction();

      console.log('نتيجة حذف العميل:', result);

      return result;
    } catch (error) {
      console.error('خطأ في حذف العميل:', error);

      return {
        success: false,
        message: `خطأ في حذف العميل: ${error.message}`,
        error: error
      };
    }
  }

  /**
   * الحصول على جميع الآلات
   * @returns {Array} - قائمة الآلات
   */
  getMachines() {
    try {
      // التحقق من توفر قاعدة البيانات
      if (!this.db) {
        console.warn('لم يتم تهيئة قاعدة البيانات. سيتم استخدام وضع المحاكاة.');
        return [];
      }

      console.log('الحصول على جميع الآلات...');

      // التحقق من وجود جدول الآلات
      try {
        const tableCheck = this.db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='machines'").get();
        if (!tableCheck) {
          console.warn('جدول الآلات غير موجود. سيتم إنشاؤه...');
          this.db.exec(`
            CREATE TABLE IF NOT EXISTS machines (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              name TEXT NOT NULL,
              purchase_date TEXT NOT NULL,
              purchase_price REAL NOT NULL,
              current_value REAL NOT NULL,
              notes TEXT,
              created_at TEXT DEFAULT CURRENT_TIMESTAMP,
              updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
          `);
        }
      } catch (tableError) {
        console.error('خطأ في التحقق من وجود جدول الآلات:', tableError);
        // إنشاء جدول الآلات
        this.db.exec(`
          CREATE TABLE IF NOT EXISTS machines (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            purchase_date TEXT NOT NULL,
            purchase_price REAL NOT NULL,
            current_value REAL NOT NULL,
            notes TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP
          )
        `);
      }

      // استعلام الآلات
      const stmt = this.db.prepare('SELECT * FROM machines ORDER BY id DESC');
      const machines = stmt.all();

      console.log(`تم الحصول على ${machines.length} آلة بنجاح`);

      // معالجة البيانات
      const processedMachines = machines.map(machine => ({
        ...machine,
        _id: machine.id.toString(),
        id: machine.id.toString(),
        purchase_price: Number(machine.purchase_price || 0),
        current_value: Number(machine.current_value || 0)
      }));

      return processedMachines;
    } catch (error) {
      console.error('خطأ في الحصول على الآلات:', error);
      return [];
    }
  }

  /**
   * إضافة آلة جديدة
   * @param {Object} machine - بيانات الآلة
   * @returns {Object} - الآلة المضافة
   */
  addMachine(machine) {
    try {
      // التحقق من توفر قاعدة البيانات
      if (!this.db) {
        console.warn('لم يتم تهيئة قاعدة البيانات. سيتم استخدام وضع المحاكاة.');
        return { ...machine, id: Date.now() };
      }

      console.log('إضافة آلة جديدة:', machine);

      // التحقق من وجود جدول الآلات
      try {
        const tableCheck = this.db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='machines'").get();
        if (!tableCheck) {
          console.warn('جدول الآلات غير موجود. سيتم إنشاؤه...');
          this.db.exec(`
            CREATE TABLE IF NOT EXISTS machines (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              name TEXT NOT NULL,
              purchase_date TEXT NOT NULL,
              purchase_price REAL NOT NULL,
              current_value REAL NOT NULL,
              notes TEXT,
              created_at TEXT DEFAULT CURRENT_TIMESTAMP,
              updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
          `);
        }
      } catch (tableError) {
        console.error('خطأ في التحقق من وجود جدول الآلات:', tableError);
        // إنشاء جدول الآلات
        this.db.exec(`
          CREATE TABLE IF NOT EXISTS machines (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            purchase_date TEXT NOT NULL,
            purchase_price REAL NOT NULL,
            current_value REAL NOT NULL,
            notes TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP
          )
        `);
      }

      // تنظيف بيانات الآلة
      const cleanedMachine = {
        name: String(machine.name || '').trim(),
        purchase_date: String(machine.purchase_date || new Date().toISOString().split('T')[0]),
        purchase_price: Number(machine.purchase_price || 0),
        current_value: Number(machine.current_value || 0),
        notes: String(machine.notes || '').trim(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      console.log('بيانات الآلة بعد التنظيف:', cleanedMachine);

      // بدء معاملة
      const transaction = this.db.transaction(() => {
        // إضافة الآلة
        const stmt = this.db.prepare(`
          INSERT INTO machines (
            name, purchase_date, purchase_price, current_value, notes, created_at, updated_at
          )
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `);

        const result = stmt.run(
          cleanedMachine.name,
          cleanedMachine.purchase_date,
          cleanedMachine.purchase_price,
          cleanedMachine.current_value,
          cleanedMachine.notes,
          cleanedMachine.created_at,
          cleanedMachine.updated_at
        );

        // الحصول على معرف الآلة المضافة
        const machineId = result.lastInsertRowid;

        // الحصول على الآلة المضافة
        const getMachineStmt = this.db.prepare('SELECT * FROM machines WHERE id = ?');
        const newMachine = getMachineStmt.get(machineId);

        if (!newMachine) {
          throw new Error(`لم يتم العثور على الآلة بعد إضافتها. المعرف: ${machineId}`);
        }

        return {
          ...newMachine,
          _id: newMachine.id.toString(),
          id: newMachine.id.toString(),
          purchase_price: Number(newMachine.purchase_price || 0),
          current_value: Number(newMachine.current_value || 0)
        };
      });

      try {
        // تنفيذ المعاملة
        const newMachine = transaction();
        console.log('تم إضافة الآلة بنجاح:', newMachine);
        return newMachine;
      } catch (transactionError) {
        console.error('خطأ في تنفيذ معاملة إضافة الآلة:', transactionError);

        // محاولة إضافة الآلة بدون معاملة
        console.log('محاولة إضافة الآلة بدون معاملة...');

        // إضافة الآلة
        const stmt = this.db.prepare(`
          INSERT INTO machines (
            name, purchase_date, purchase_price, current_value, notes, created_at, updated_at
          )
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `);

        const result = stmt.run(
          cleanedMachine.name,
          cleanedMachine.purchase_date,
          cleanedMachine.purchase_price,
          cleanedMachine.current_value,
          cleanedMachine.notes,
          cleanedMachine.created_at,
          cleanedMachine.updated_at
        );

        // الحصول على معرف الآلة المضافة
        const machineId = result.lastInsertRowid;

        // الحصول على الآلة المضافة
        const getMachineStmt = this.db.prepare('SELECT * FROM machines WHERE id = ?');
        const newMachine = getMachineStmt.get(machineId);

        const processedMachine = {
          ...newMachine,
          _id: newMachine.id.toString(),
          id: newMachine.id.toString(),
          purchase_price: Number(newMachine.purchase_price || 0),
          current_value: Number(newMachine.current_value || 0)
        };

        console.log('تم إضافة الآلة بنجاح (بدون معاملة):', processedMachine);
        return processedMachine;
      }
    } catch (error) {
      console.error('خطأ في إضافة الآلة:', error);
      throw error;
    }
  }

  /**
   * تحديث آلة
   * @param {Object} machine - بيانات الآلة
   * @returns {Object} - الآلة المحدثة
   */
  updateMachine(machine) {
    try {
      // التحقق من توفر قاعدة البيانات
      if (!this.db) {
        console.warn('لم يتم تهيئة قاعدة البيانات. سيتم استخدام وضع المحاكاة.');
        return { ...machine };
      }

      console.log('تحديث الآلة:', machine);

      // التحقق من وجود معرف الآلة
      if (!machine.id && !machine._id) {
        throw new Error('معرف الآلة مطلوب للتحديث');
      }

      // تنظيف بيانات الآلة
      const cleanedMachine = {
        name: String(machine.name || '').trim(),
        purchase_date: String(machine.purchase_date || new Date().toISOString().split('T')[0]),
        purchase_price: Number(machine.purchase_price || 0),
        current_value: Number(machine.current_value || 0),
        notes: String(machine.notes || '').trim(),
        updated_at: new Date().toISOString()
      };

      // الحصول على معرف الآلة
      const machineId = machine.id || machine._id;

      // بدء معاملة
      const transaction = this.db.transaction(() => {
        // تحديث الآلة
        const stmt = this.db.prepare(`
          UPDATE machines
          SET name = ?, purchase_date = ?, purchase_price = ?, current_value = ?, notes = ?, updated_at = ?
          WHERE id = ?
        `);

        stmt.run(
          cleanedMachine.name,
          cleanedMachine.purchase_date,
          cleanedMachine.purchase_price,
          cleanedMachine.current_value,
          cleanedMachine.notes,
          cleanedMachine.updated_at,
          machineId
        );

        // الحصول على الآلة المحدثة
        const getMachineStmt = this.db.prepare('SELECT * FROM machines WHERE id = ?');
        const updatedMachine = getMachineStmt.get(machineId);

        if (!updatedMachine) {
          throw new Error(`لم يتم العثور على الآلة بعد تحديثها. المعرف: ${machineId}`);
        }

        // إضافة حقول إضافية للتوافق مع الواجهة الأمامية
        return {
          ...updatedMachine,
          _id: updatedMachine.id.toString(),
          id: updatedMachine.id.toString(),
          purchase_price: Number(updatedMachine.purchase_price || 0),
          current_value: Number(updatedMachine.current_value || 0)
        };
      });

      // تنفيذ المعاملة
      const updatedMachine = transaction();

      console.log('تم تحديث الآلة بنجاح:', updatedMachine);

      return updatedMachine;
    } catch (error) {
      console.error('خطأ في تحديث الآلة:', error);
      throw error;
    }
  }

  /**
   * حذف آلة
   * @param {Number} id - معرف الآلة
   * @returns {Object} - نتيجة الحذف
   */
  deleteMachine(id) {
    try {
      // التحقق من توفر قاعدة البيانات
      if (!this.db) {
        console.warn('لم يتم تهيئة قاعدة البيانات. سيتم استخدام وضع المحاكاة.');
        return { success: true, message: 'تم حذف الآلة بنجاح (وضع المحاكاة)' };
      }

      console.log(`حذف الآلة بمعرف ${id}`);

      // حذف الآلة
      const deleteMachineStmt = this.db.prepare('DELETE FROM machines WHERE id = ?');
      const result = deleteMachineStmt.run(id);

      console.log('نتيجة حذف الآلة:', result);

      return {
        success: result.changes > 0,
        message: result.changes > 0 ? 'تم حذف الآلة بنجاح' : 'لم يتم العثور على الآلة'
      };
    } catch (error) {
      console.error('خطأ في حذف الآلة:', error);

      return {
        success: false,
        message: `خطأ في حذف الآلة: ${error.message}`,
        error: error
      };
    }
  }

  /**
   * تنظيف قاعدة البيانات
   * @returns {Object} - نتيجة العملية
   */
  vacuum() {
    try {
      // التحقق من توفر قاعدة البيانات
      if (!this.db) {
        console.warn('لم يتم تهيئة قاعدة البيانات. سيتم استخدام وضع المحاكاة.');
        return {
          success: true,
          message: 'تم تنظيف قاعدة البيانات بنجاح (وضع المحاكاة)'
        };
      }

      console.log('تنظيف قاعدة البيانات...');

      // تنفيذ أمر VACUUM
      this.db.exec('VACUUM');

      console.log('تم تنظيف قاعدة البيانات بنجاح');

      return {
        success: true,
        message: 'تم تنظيف قاعدة البيانات بنجاح'
      };
    } catch (error) {
      console.error('خطأ في تنظيف قاعدة البيانات:', error);

      return {
        success: false,
        message: `خطأ في تنظيف قاعدة البيانات: ${error.message}`,
        error: error
      };
    }
  }
}

// تصدير نسخة واحدة من مدير قاعدة البيانات
export default new SQLiteManager();
