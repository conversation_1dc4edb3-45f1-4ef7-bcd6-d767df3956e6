/**
 * أداة إصلاح مشكلة سعر البيع في المعاملات
 * تقوم بإصلاح المعاملات التي لها سعر بيع = 0 وإعادة حساب الأرباح
 */

const { logError, logSystem } = require('./error-handler');
const DatabaseManager = require('./database-singleton');

/**
 * إصلاح مشكلة سعر البيع في المعاملات
 */
async function fixSellingPriceIssue() {
  console.log('🔧 بدء إصلاح مشكلة سعر البيع في المعاملات...');
  
  const fixResult = {
    timestamp: new Date().toISOString(),
    success: false,
    steps: [],
    errors: [],
    statistics: {
      totalSalesTransactions: 0,
      transactionsWithZeroSellingPrice: 0,
      transactionsFixed: 0,
      profitsRecalculated: 0
    }
  };

  try {
    // الحصول على اتصال قاعدة البيانات
    const dbManager = DatabaseManager.getInstance();
    const db = dbManager.getConnection();

    if (!db) {
      throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
    }

    console.log('✅ تم الحصول على اتصال قاعدة البيانات');

    // 1. فحص معاملات البيع الحالية
    await analyzeSalesTransactions(db, fixResult);

    // 2. إصلاح المعاملات التي لها سعر بيع = 0
    await fixZeroSellingPriceTransactions(db, fixResult);

    // 3. إعادة حساب الأرباح للمعاملات المصلحة
    await recalculateProfitsForFixedTransactions(db, fixResult);

    // 4. تحديث إجمالي الأرباح في الخزينة
    await updateTotalProfitsInCashbox(db, fixResult);

    fixResult.success = true;
    console.log('✅ تم إكمال إصلاح مشكلة سعر البيع بنجاح');

  } catch (error) {
    console.error('❌ خطأ في إصلاح مشكلة سعر البيع:', error);
    fixResult.errors.push(`خطأ عام: ${error.message}`);
    logError(error, 'fixSellingPriceIssue');
  }

  return fixResult;
}

/**
 * فحص معاملات البيع الحالية
 */
async function analyzeSalesTransactions(db, fixResult) {
  console.log('🔍 فحص معاملات البيع الحالية...');

  try {
    // إحصائيات معاملات البيع
    const totalSalesQuery = db.prepare(`
      SELECT COUNT(*) as total_count
      FROM transactions
      WHERE transaction_type = 'sale'
    `);
    
    const zeroSellingPriceQuery = db.prepare(`
      SELECT COUNT(*) as zero_count
      FROM transactions
      WHERE transaction_type = 'sale' AND (selling_price = 0 OR selling_price IS NULL)
    `);

    const totalSales = totalSalesQuery.get();
    const zeroSellingPrice = zeroSellingPriceQuery.get();

    fixResult.statistics.totalSalesTransactions = totalSales.total_count;
    fixResult.statistics.transactionsWithZeroSellingPrice = zeroSellingPrice.zero_count;

    console.log(`📊 إجمالي معاملات البيع: ${totalSales.total_count}`);
    console.log(`📊 معاملات بسعر بيع صفر: ${zeroSellingPrice.zero_count}`);

    fixResult.steps.push(`✅ تم فحص ${totalSales.total_count} معاملة بيع`);
    fixResult.steps.push(`⚠️ وُجد ${zeroSellingPrice.zero_count} معاملة بسعر بيع صفر`);

  } catch (error) {
    const errorMsg = `خطأ في فحص معاملات البيع: ${error.message}`;
    fixResult.errors.push(errorMsg);
    console.error('❌', errorMsg);
  }
}

/**
 * إصلاح المعاملات التي لها سعر بيع = 0
 */
async function fixZeroSellingPriceTransactions(db, fixResult) {
  console.log('🔧 إصلاح المعاملات التي لها سعر بيع صفر...');

  try {
    // الحصول على المعاملات التي لها سعر بيع صفر
    const zeroSellingPriceTransactions = db.prepare(`
      SELECT t.id, t.item_id, t.quantity, t.price, t.selling_price, t.total_price,
             i.name as item_name, inv.selling_price as inventory_selling_price
      FROM transactions t
      JOIN items i ON t.item_id = i.id
      LEFT JOIN inventory inv ON t.item_id = inv.item_id
      WHERE t.transaction_type = 'sale' 
        AND (t.selling_price = 0 OR t.selling_price IS NULL)
    `).all();

    if (zeroSellingPriceTransactions.length === 0) {
      fixResult.steps.push('✅ لا توجد معاملات تحتاج إصلاح سعر البيع');
      console.log('✅ لا توجد معاملات تحتاج إصلاح سعر البيع');
      return;
    }

    console.log(`🔧 تم العثور على ${zeroSellingPriceTransactions.length} معاملة تحتاج إصلاح`);

    // إصلاح المعاملات
    const updateSellingPriceStmt = db.prepare(`
      UPDATE transactions 
      SET selling_price = ?, total_price = ?
      WHERE id = ?
    `);

    let fixedCount = 0;

    // بدء معاملة قاعدة البيانات
    const transaction = db.transaction(() => {
      for (const tx of zeroSellingPriceTransactions) {
        try {
          let correctSellingPrice = 0;
          let correctTotalPrice = 0;

          // محاولة الحصول على سعر البيع الصحيح
          if (tx.inventory_selling_price && tx.inventory_selling_price > 0) {
            // استخدام سعر البيع من المخزون
            correctSellingPrice = tx.inventory_selling_price;
          } else if (tx.total_price && tx.total_price > 0 && tx.quantity > 0) {
            // حساب سعر البيع من إجمالي السعر
            correctSellingPrice = tx.total_price / tx.quantity;
          } else if (tx.price && tx.price > 0) {
            // استخدام سعر الشراء + هامش ربح 20%
            correctSellingPrice = tx.price * 1.2;
          } else {
            console.warn(`لا يمكن تحديد سعر البيع للمعاملة ${tx.id} (${tx.item_name})`);
            continue;
          }

          // حساب إجمالي السعر الصحيح
          correctTotalPrice = correctSellingPrice * tx.quantity;

          // تحديث المعاملة
          updateSellingPriceStmt.run(correctSellingPrice, correctTotalPrice, tx.id);

          console.log(`✅ تم إصلاح المعاملة ${tx.id} (${tx.item_name}): سعر البيع ${correctSellingPrice}, الإجمالي ${correctTotalPrice}`);
          fixedCount++;

        } catch (error) {
          console.error(`خطأ في إصلاح المعاملة ${tx.id}:`, error);
        }
      }
    });

    transaction();

    fixResult.statistics.transactionsFixed = fixedCount;
    fixResult.steps.push(`✅ تم إصلاح ${fixedCount} معاملة`);
    console.log(`✅ تم إصلاح ${fixedCount} معاملة`);

  } catch (error) {
    const errorMsg = `خطأ في إصلاح المعاملات: ${error.message}`;
    fixResult.errors.push(errorMsg);
    console.error('❌', errorMsg);
  }
}

/**
 * إعادة حساب الأرباح للمعاملات المصلحة
 */
async function recalculateProfitsForFixedTransactions(db, fixResult) {
  console.log('🔧 إعادة حساب الأرباح للمعاملات المصلحة...');

  try {
    // الحصول على جميع معاملات البيع لإعادة حساب أرباحها
    const salesTransactions = db.prepare(`
      SELECT t.id, t.item_id, t.quantity, t.selling_price, t.profit,
             i.name as item_name, inv.avg_price
      FROM transactions t
      JOIN items i ON t.item_id = i.id
      LEFT JOIN inventory inv ON t.item_id = inv.item_id
      WHERE t.transaction_type = 'sale' AND t.selling_price > 0
    `).all();

    if (salesTransactions.length === 0) {
      fixResult.steps.push('⚠️ لا توجد معاملات بيع لإعادة حساب أرباحها');
      return;
    }

    console.log(`🔧 إعادة حساب الأرباح لـ ${salesTransactions.length} معاملة بيع`);

    // إعادة حساب الأرباح
    const updateProfitStmt = db.prepare(`
      UPDATE transactions 
      SET profit = ?
      WHERE id = ?
    `);

    let recalculatedCount = 0;

    // بدء معاملة قاعدة البيانات
    const transaction = db.transaction(() => {
      for (const tx of salesTransactions) {
        try {
          let correctProfit = 0;

          if (tx.avg_price && tx.avg_price > 0) {
            // حساب الربح: (سعر البيع - متوسط سعر الشراء) × الكمية
            correctProfit = Math.max(0, (tx.selling_price - tx.avg_price) * tx.quantity);
          } else {
            // استخدام 20% من سعر البيع كربح تقديري
            correctProfit = tx.selling_price * tx.quantity * 0.2;
          }

          // تحديث الربح إذا كان مختلفاً
          if (Math.abs(correctProfit - (tx.profit || 0)) > 0.01) {
            updateProfitStmt.run(correctProfit, tx.id);
            console.log(`✅ تم تحديث ربح المعاملة ${tx.id} (${tx.item_name}): من ${tx.profit || 0} إلى ${correctProfit}`);
            recalculatedCount++;
          }

        } catch (error) {
          console.error(`خطأ في إعادة حساب ربح المعاملة ${tx.id}:`, error);
        }
      }
    });

    transaction();

    fixResult.statistics.profitsRecalculated = recalculatedCount;
    fixResult.steps.push(`✅ تم إعادة حساب الأرباح لـ ${recalculatedCount} معاملة`);
    console.log(`✅ تم إعادة حساب الأرباح لـ ${recalculatedCount} معاملة`);

  } catch (error) {
    const errorMsg = `خطأ في إعادة حساب الأرباح: ${error.message}`;
    fixResult.errors.push(errorMsg);
    console.error('❌', errorMsg);
  }
}

/**
 * تحديث إجمالي الأرباح في الخزينة
 */
async function updateTotalProfitsInCashbox(db, fixResult) {
  console.log('🔧 تحديث إجمالي الأرباح في الخزينة...');

  try {
    // حساب إجمالي الأرباح من جميع معاملات البيع
    const totalProfitQuery = db.prepare(`
      SELECT COALESCE(SUM(profit), 0) as total_profit
      FROM transactions
      WHERE transaction_type = 'sale'
    `);

    const result = totalProfitQuery.get();
    const totalProfit = Number(result.total_profit || 0);

    console.log(`📊 إجمالي الأرباح المحسوب: ${totalProfit}`);

    // تحديث إجمالي الأرباح في الخزينة
    const updateCashboxStmt = db.prepare(`
      UPDATE cashbox 
      SET profit_total = ?, updated_at = ?
      WHERE id = 1
    `);

    const updateResult = updateCashboxStmt.run(totalProfit, new Date().toISOString());

    if (updateResult.changes > 0) {
      // التحقق من حفظ القيمة
      const verifyStmt = db.prepare('SELECT profit_total FROM cashbox WHERE id = 1');
      const verifyResult = verifyStmt.get();
      const savedValue = verifyResult ? verifyResult.profit_total : 0;

      if (Math.abs(savedValue - totalProfit) < 0.01) {
        fixResult.steps.push(`✅ تم تحديث إجمالي الأرباح في الخزينة إلى: ${totalProfit}`);
        console.log(`✅ تم تحديث إجمالي الأرباح في الخزينة إلى: ${totalProfit}`);
      } else {
        throw new Error(`فشل في حفظ إجمالي الأرباح: متوقع ${totalProfit} لكن تم حفظ ${savedValue}`);
      }
    } else {
      throw new Error('فشل في تحديث إجمالي الأرباح في الخزينة');
    }

  } catch (error) {
    const errorMsg = `خطأ في تحديث إجمالي الأرباح: ${error.message}`;
    fixResult.errors.push(errorMsg);
    console.error('❌', errorMsg);
  }
}

/**
 * طباعة تقرير الإصلاح
 */
function printFixReport(fixResult) {
  console.log('\n' + '='.repeat(60));
  console.log('📋 تقرير إصلاح مشكلة سعر البيع والأرباح');
  console.log('='.repeat(60));
  
  console.log(`🕐 وقت الإصلاح: ${fixResult.timestamp}`);
  console.log(`✅ نجح الإصلاح: ${fixResult.success ? 'نعم' : 'لا'}`);
  
  console.log('\n📊 الإحصائيات:');
  console.log(`- إجمالي معاملات البيع: ${fixResult.statistics.totalSalesTransactions}`);
  console.log(`- معاملات بسعر بيع صفر: ${fixResult.statistics.transactionsWithZeroSellingPrice}`);
  console.log(`- معاملات تم إصلاحها: ${fixResult.statistics.transactionsFixed}`);
  console.log(`- أرباح تم إعادة حسابها: ${fixResult.statistics.profitsRecalculated}`);
  
  if (fixResult.steps.length > 0) {
    console.log('\n🔧 خطوات الإصلاح المنجزة:');
    fixResult.steps.forEach((step, index) => {
      console.log(`${index + 1}. ${step}`);
    });
  }
  
  if (fixResult.errors.length > 0) {
    console.log('\n❌ الأخطاء التي حدثت:');
    fixResult.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error}`);
    });
  }
  
  console.log('\n' + '='.repeat(60));
}

module.exports = {
  fixSellingPriceIssue,
  printFixReport
};
