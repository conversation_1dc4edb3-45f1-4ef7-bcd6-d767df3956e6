/**
 * اختبار سريع لإصلاح مصاريف النقل
 * يمكن تشغيله لاختبار الإصلاح بسرعة
 */

const { calculateProfitWithTransport } = require('./utils/profitCalculator');

console.log('🧪 اختبار سريع لإصلاح مصاريف النقل\n');

// اختبار 1: حساب الربح مع مصاريف النقل في البيع
console.log('1️⃣ اختبار حساب الربح في البيع مع مصاريف النقل:');
const sellingPrice = 150;
const costPrice = 100;
const quantity = 5;
const transportCostPerUnit = 10;

const profit = calculateProfitWithTransport(sellingPrice, costPrice, quantity, transportCostPerUnit);
const expectedProfit = (150 - 100 - 10) * 5; // 40 * 5 = 200

console.log(`   سعر البيع: ${sellingPrice}`);
console.log(`   سعر التكلفة: ${costPrice}`);
console.log(`   مصاريف النقل لكل وحدة: ${transportCostPerUnit}`);
console.log(`   الكمية: ${quantity}`);
console.log(`   الربح المحسوب: ${profit}`);
console.log(`   الربح المتوقع: ${expectedProfit}`);

if (Math.abs(profit - expectedProfit) < 0.01) {
  console.log('   ✅ النتيجة صحيحة - مصاريف النقل تخصم من الأرباح في البيع\n');
} else {
  console.log('   ❌ النتيجة خاطئة\n');
}

// اختبار 2: محاكاة عملية شراء
console.log('2️⃣ اختبار عملية الشراء:');
const purchaseAmount = 1000;
const purchaseTransportCost = 100;
const initialBalance = 10000;

const newBalance = initialBalance - purchaseAmount - purchaseTransportCost;
const purchasesTotal = purchaseAmount; // فقط مبلغ الشراء، بدون مصاريف النقل
const transportTotal = purchaseTransportCost;
const profitChange = 0; // لا تتأثر الأرباح

console.log(`   الرصيد الأولي: ${initialBalance}`);
console.log(`   مبلغ الشراء: ${purchaseAmount}`);
console.log(`   مصاريف النقل: ${purchaseTransportCost}`);
console.log(`   الرصيد الجديد: ${newBalance} (${initialBalance} - ${purchaseAmount} - ${purchaseTransportCost})`);
console.log(`   إجمالي المشتريات: ${purchasesTotal} (فقط مبلغ الشراء)`);
console.log(`   إجمالي مصاريف النقل: ${transportTotal}`);
console.log(`   تغيير الأرباح: ${profitChange} (لا تتأثر)`);

if (newBalance === 8900 && purchasesTotal === 1000 && transportTotal === 100 && profitChange === 0) {
  console.log('   ✅ النتيجة صحيحة - مصاريف النقل في الشراء تخصم من الرصيد فقط\n');
} else {
  console.log('   ❌ النتيجة خاطئة\n');
}

// اختبار 3: سيناريو متكامل
console.log('3️⃣ سيناريو متكامل:');
console.log('   الخطوة 1: شراء 10 وحدات بسعر 100 مع مصاريف نقل 50');
console.log('   الخطوة 2: بيع 5 وحدات بسعر 150');

// الشراء
let balance = 10000;
const purchaseQty = 10;
const purchasePrice = 100;
const purchaseTransport = 50;

balance -= (purchaseQty * purchasePrice + purchaseTransport);
const avgCostPerUnit = purchasePrice; // متوسط سعر الشراء
const transportPerUnit = purchaseTransport / purchaseQty; // 5 لكل وحدة

console.log(`   بعد الشراء - الرصيد: ${balance}`);
console.log(`   متوسط التكلفة لكل وحدة: ${avgCostPerUnit}`);
console.log(`   مصاريف النقل لكل وحدة: ${transportPerUnit}`);

// البيع
const saleQty = 5;
const salePrice = 150;
const saleAmount = saleQty * salePrice;
const saleProfit = (salePrice - avgCostPerUnit - transportPerUnit) * saleQty;

balance += saleAmount;

console.log(`   بعد البيع - الرصيد: ${balance}`);
console.log(`   مبلغ البيع: ${saleAmount}`);
console.log(`   ربح البيع: ${saleProfit} = (${salePrice} - ${avgCostPerUnit} - ${transportPerUnit}) × ${saleQty}`);

const expectedFinalBalance = 10000 - 1050 + 750; // 9700
const expectedSaleProfit = (150 - 100 - 5) * 5; // 225

if (Math.abs(balance - expectedFinalBalance) < 0.01 && Math.abs(saleProfit - expectedSaleProfit) < 0.01) {
  console.log('   ✅ السيناريو المتكامل صحيح\n');
} else {
  console.log('   ❌ السيناريو المتكامل خاطئ\n');
}

console.log('🎉 انتهى الاختبار السريع');
console.log('\n📋 ملخص الإصلاح:');
console.log('✅ في البيع: مصاريف النقل تخصم من الأرباح');
console.log('✅ في الشراء: مصاريف النقل تخصم من الرصيد الحالي فقط');
console.log('✅ الأرباح تحسب بدقة مع مراعاة التكلفة الحقيقية للمنتج');
