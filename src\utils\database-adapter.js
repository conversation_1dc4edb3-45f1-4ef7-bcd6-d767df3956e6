/**
 * طبقة وسيطة لقاعدة البيانات SQLite
 * توفر واجهة موحدة للتعامل مع قاعدة البيانات
 */

// تعريف وظيفة createLimitedCache بدلاً من استيرادها
const createLimitedCache = (maxSize = 100) => {
  const cache = new Map();

  return {
    get: (key) => {
      const item = cache.get(key);
      if (!item) return null;

      if (item.expiry && Date.now() > item.expiry) {
        cache.delete(key);
        return null;
      }

      return item.value;
    },
    set: (key, value, ttl = null) => {
      // إذا وصل التخزين المؤقت إلى الحد الأقصى، قم بإزالة أقدم عنصر
      if (cache.size >= maxSize) {
        const oldestKey = cache.keys().next().value;
        cache.delete(oldestKey);
      }

      cache.set(key, {
        value,
        expiry: ttl ? Date.now() + ttl : null
      });
    },
    clear: () => {
      cache.clear();
    },
    size: () => {
      return cache.size;
    }
  };
};

// تعريف مدير SQLite للواجهة الأمامية
// يستخدم IPC للتواصل مع العمليات الخلفية بدلاً من استخدام المكتبات مباشرة
const sqliteManager = {
  // تهيئة قاعدة البيانات
  initialize: () => {
    console.log('تهيئة قاعدة البيانات SQLite...');
    return { success: true, message: 'تم تهيئة قاعدة بيانات SQLite بنجاح' };
  },

  // الحصول على الأصناف
  getItems: () => {
    try {
      return window.api.items.getAll();
    } catch (error) {
      console.error('خطأ في الحصول على الأصناف:', error);
      return [];
    }
  },

  // الحصول على الأصناف مع التحميل التدريجي
  getItemsPaginated: (page, pageSize, filters) => {
    try {
      return window.api.items.getPaginated(page, pageSize, filters);
    } catch (error) {
      console.error('خطأ في الحصول على الأصناف مع التحميل التدريجي:', error);
      return { items: [], totalCount: 0 };
    }
  },

  // إضافة صنف جديد
  addItem: (item) => {
    try {
      return window.api.items.add(item);
    } catch (error) {
      console.error('خطأ في إضافة صنف جديد:', error);
      throw error;
    }
  },

  // تحديث صنف
  updateItem: (item) => {
    try {
      console.log('تحديث الصنف في database-adapter.js:', item);
      // استخراج معرف الصنف والتحديثات
      const itemId = item.id || item._id;

      // التحقق من وجود معرف الصنف
      if (!itemId) {
        console.error('معرف الصنف غير موجود:', item);
        throw new Error('معرف الصنف غير موجود');
      }

      // تمرير معرف الصنف والتحديثات بشكل منفصل
      return window.api.items.update(itemId, item);
    } catch (error) {
      console.error('خطأ في تحديث صنف:', error);
      throw error;
    }
  },

  // حذف صنف
  deleteItem: (id, forceDelete) => {
    try {
      return window.api.items.delete(id, forceDelete);
    } catch (error) {
      console.error('خطأ في حذف صنف:', error);
      throw error;
    }
  },

  // الحصول على المخزون
  getInventory: () => {
    try {
      return window.api.inventory.getAll();
    } catch (error) {
      console.error('خطأ في الحصول على المخزون:', error);
      return [];
    }
  },

  // تحديث المخزون
  updateInventory: (itemId, updates) => {
    try {
      return window.api.inventory.update(itemId, updates);
    } catch (error) {
      console.error('خطأ في تحديث المخزون:', error);
      throw error;
    }
  },

  // الحصول على المعاملات
  getTransactions: (filters) => {
    try {
      return window.api.transactions.getAll(filters);
    } catch (error) {
      console.error('خطأ في الحصول على المعاملات:', error);
      return [];
    }
  },

  // إضافة معاملة
  addTransaction: (transaction) => {
    try {
      return window.api.transactions.add(transaction);
    } catch (error) {
      console.error('خطأ في إضافة معاملة:', error);
      throw error;
    }
  },

  // الحصول على العملاء
  getCustomers: () => {
    try {
      return window.api.customers.getAll();
    } catch (error) {
      console.error('خطأ في الحصول على العملاء:', error);
      return [];
    }
  },

  // الحصول على عميل بواسطة المعرف
  getCustomerById: (customerId) => {
    try {
      console.log('الحصول على عميل بواسطة المعرف:', customerId);
      return window.api.customers.getById(customerId);
    } catch (error) {
      console.error('خطأ في الحصول على العميل بواسطة المعرف:', error);
      return null;
    }
  },

  // إضافة عميل
  addCustomer: (customer) => {
    try {
      return window.api.customers.add(customer);
    } catch (error) {
      console.error('خطأ في إضافة عميل:', error);
      throw error;
    }
  },

  // تحديث عميل
  updateCustomer: (customer) => {
    try {
      return window.api.customers.update(customer);
    } catch (error) {
      console.error('خطأ في تحديث عميل:', error);
      throw error;
    }
  },

  // حذف عميل
  deleteCustomer: (id) => {
    try {
      return window.api.customers.delete(id);
    } catch (error) {
      console.error('خطأ في حذف عميل:', error);
      throw error;
    }
  },

  // الحصول على المستخدمين
  getUsers: () => {
    try {
      return window.api.users.getAll();
    } catch (error) {
      console.error('خطأ في الحصول على المستخدمين:', error);
      return [];
    }
  },

  // إضافة مستخدم
  addUser: (user) => {
    try {
      return window.api.users.add(user);
    } catch (error) {
      console.error('خطأ في إضافة مستخدم:', error);
      throw error;
    }
  },

  // تحديث مستخدم
  updateUser: (user) => {
    try {
      return window.api.users.update(user);
    } catch (error) {
      console.error('خطأ في تحديث مستخدم:', error);
      throw error;
    }
  },

  // حذف مستخدم
  deleteUser: (id) => {
    try {
      return window.api.users.delete(id);
    } catch (error) {
      console.error('خطأ في حذف مستخدم:', error);
      throw error;
    }
  },

  // تسجيل الدخول
  login: (credentials) => {
    try {
      return window.api.users.login(credentials);
    } catch (error) {
      console.error('خطأ في تسجيل الدخول:', error);
      throw error;
    }
  },

  // إنشاء نسخة احتياطية
  backup: () => {
    try {
      return window.api.database.backup();
    } catch (error) {
      console.error('خطأ في إنشاء نسخة احتياطية:', error);
      throw error;
    }
  },

  // استعادة قاعدة البيانات
  restore: (backupPath) => {
    try {
      return window.api.database.restore(backupPath);
    } catch (error) {
      console.error('خطأ في استعادة قاعدة البيانات:', error);
      throw error;
    }
  }
};

// إنشاء تخزين مؤقت للبيانات المستخدمة بكثرة
const itemsCache = createLimitedCache(200);
const inventoryCache = createLimitedCache(200);
const transactionsCache = createLimitedCache(500);
const customersCache = createLimitedCache(200);
const usersCache = createLimitedCache(50);

/**
 * طبقة وسيطة لقاعدة البيانات
 */
const DatabaseAdapter = {
  /**
   * تهيئة قاعدة البيانات
   * @returns {Promise} - وعد بنتيجة التهيئة
   */
  initialize: () => {
    return sqliteManager.initialize();
  },

  /**
   * الحصول على الأصناف
   * @returns {Promise<Array>} - وعد بقائمة الأصناف
   */
  getItems: async () => {
    // التحقق من وجود البيانات في التخزين المؤقت
    const cachedItems = itemsCache.get('all_items');
    if (cachedItems) {
      console.log('استخدام بيانات الأصناف من التخزين المؤقت');
      return cachedItems;
    }

    try {
      // استخدام SQLite
      const items = sqliteManager.getItems();

      // تخزين البيانات في التخزين المؤقت
      itemsCache.set('all_items', items);

      return items;
    } catch (error) {
      console.error('خطأ في الحصول على الأصناف:', error);
      throw error;
    }
  },

  /**
   * الحصول على الأصناف مع التحميل التدريجي
   * @param {Number} page - رقم الصفحة
   * @param {Number} pageSize - حجم الصفحة
   * @param {Object} filters - فلاتر التصفية
   * @returns {Promise<Object>} - وعد بنتيجة الاستعلام
   */
  getItemsPaginated: (page = 1, pageSize = 20, filters = {}) => {
    // إنشاء مفتاح فريد للتخزين المؤقت
    const cacheKey = `items_page_${page}_size_${pageSize}_${JSON.stringify(filters)}`;

    // التحقق من وجود البيانات في التخزين المؤقت
    const cachedItems = itemsCache.get(cacheKey);
    if (cachedItems) {
      console.log(`استخدام بيانات الأصناف للصفحة ${page} من التخزين المؤقت`);
      return cachedItems;
    }

    try {
      // استخدام SQLite
      const result = sqliteManager.getItemsPaginated(page, pageSize, filters);

      // تخزين البيانات في التخزين المؤقت
      itemsCache.set(cacheKey, result);

      return result;
    } catch (error) {
      console.error(`خطأ في الحصول على الأصناف للصفحة ${page}:`, error);
      throw error;
    }
  },

  /**
   * إضافة صنف جديد
   * @param {Object} item - بيانات الصنف
   * @returns {Promise<Object>} - وعد بالصنف المضاف
   */
  addItem: async (item) => {
    try {
      // استخدام SQLite
      const result = await sqliteManager.addItem(item);

      // مسح التخزين المؤقت للأصناف
      itemsCache.clear();

      return result;
    } catch (error) {
      console.error('خطأ في إضافة الصنف:', error);
      throw error;
    }
  },

  /**
   * تحديث صنف
   * @param {Object} item - بيانات الصنف
   * @returns {Promise<Object>} - وعد بالصنف المحدث
   */
  updateItem: async (item) => {
    try {
      // استخدام SQLite
      const result = await sqliteManager.updateItem(item);

      // مسح التخزين المؤقت للأصناف
      itemsCache.clear();

      return result;
    } catch (error) {
      console.error('خطأ في تحديث الصنف:', error);
      throw error;
    }
  },

  /**
   * حذف صنف
   * @param {String|Number} id - معرف الصنف
   * @param {Boolean} forceDelete - إجبار الحذف حتى مع وجود معاملات مرتبطة
   * @returns {Promise<Object>} - وعد بنتيجة الحذف
   */
  deleteItem: async (id, forceDelete = false) => {
    try {
      // استخدام SQLite
      const result = await sqliteManager.deleteItem(id, forceDelete);

      // مسح التخزين المؤقت للأصناف
      itemsCache.clear();

      return result;
    } catch (error) {
      console.error('خطأ في حذف الصنف:', error);
      throw error;
    }
  },

  /**
   * الحصول على المخزون
   * @returns {Promise<Array>} - وعد بقائمة المخزون
   */
  getInventory: async () => {
    // التحقق من وجود البيانات في التخزين المؤقت
    const cachedInventory = inventoryCache.get('all_inventory');
    if (cachedInventory) {
      console.log('استخدام بيانات المخزون من التخزين المؤقت');
      return cachedInventory;
    }

    try {
      // استخدام SQLite
      const inventory = await sqliteManager.getInventory();

      // تخزين البيانات في التخزين المؤقت
      inventoryCache.set('all_inventory', inventory);

      return inventory;
    } catch (error) {
      console.error('خطأ في الحصول على المخزون:', error);
      throw error;
    }
  },

  /**
   * تحديث المخزون
   * @param {String|Number} itemId - معرف الصنف
   * @param {Object} updates - التحديثات
   * @returns {Promise<Object>} - وعد بنتيجة التحديث
   */
  updateInventory: async (itemId, updates) => {
    try {
      // استخدام SQLite
      const result = await sqliteManager.updateInventory(itemId, updates);

      // مسح التخزين المؤقت للمخزون
      inventoryCache.clear();

      return result;
    } catch (error) {
      console.error('خطأ في تحديث المخزون:', error);
      throw error;
    }
  },

  /**
   * الحصول على المعاملات
   * @param {Object} filters - فلاتر التصفية
   * @returns {Promise<Array>} - وعد بقائمة المعاملات
   */
  getTransactions: async (filters = null) => {
    // إنشاء مفتاح فريد للتخزين المؤقت
    const cacheKey = `transactions_${JSON.stringify(filters || 'all')}`;

    // التحقق من وجود البيانات في التخزين المؤقت
    const cachedTransactions = transactionsCache.get(cacheKey);
    if (cachedTransactions) {
      console.log('استخدام بيانات المعاملات من التخزين المؤقت');
      return cachedTransactions;
    }

    try {
      // استخدام SQLite
      const transactions = await sqliteManager.getTransactions(filters);

      // تخزين البيانات في التخزين المؤقت
      transactionsCache.set(cacheKey, transactions);

      return transactions;
    } catch (error) {
      console.error('خطأ في الحصول على المعاملات:', error);
      throw error;
    }
  },

  /**
   * إضافة معاملة جديدة
   * @param {Object} transaction - بيانات المعاملة
   * @returns {Promise<Object>} - وعد بالمعاملة المضافة
   */
  addTransaction: async (transaction) => {
    try {
      // استخدام SQLite
      const result = await sqliteManager.addTransaction(transaction);

      // مسح التخزين المؤقت للمعاملات والمخزون
      transactionsCache.clear();
      inventoryCache.clear();

      return result;
    } catch (error) {
      console.error('خطأ في إضافة المعاملة:', error);
      throw error;
    }
  },

  /**
   * الحصول على العملاء
   * @returns {Promise<Array>} - وعد بقائمة العملاء
   */
  getCustomers: async () => {
    // التحقق من وجود البيانات في التخزين المؤقت
    const cachedCustomers = customersCache.get('all_customers');
    if (cachedCustomers) {
      console.log('استخدام بيانات العملاء من التخزين المؤقت');
      return cachedCustomers;
    }

    try {
      // استخدام SQLite
      const customers = await sqliteManager.getCustomers();

      // تخزين البيانات في التخزين المؤقت
      customersCache.set('all_customers', customers);

      return customers;
    } catch (error) {
      console.error('خطأ في الحصول على العملاء:', error);
      throw error;
    }
  },

  /**
   * الحصول على عميل بواسطة المعرف
   * @param {String|Number} customerId - معرف العميل
   * @returns {Promise<Object|null>} - وعد بالعميل أو null إذا لم يتم العثور عليه
   */
  getCustomerById: async (customerId) => {
    // التحقق من وجود البيانات في التخزين المؤقت
    const cacheKey = `customer_${customerId}`;
    const cachedCustomer = customersCache.get(cacheKey);
    if (cachedCustomer) {
      console.log(`استخدام بيانات العميل ${customerId} من التخزين المؤقت`);
      return cachedCustomer;
    }

    try {
      console.log(`الحصول على العميل بالمعرف ${customerId} من قاعدة البيانات`);

      // محاولة استخدام window.api.customers.getById
      let customer = null;

      if (window.api.customers && typeof window.api.customers.getById === 'function') {
        console.log('استخدام window.api.customers.getById()');
        try {
          customer = await window.api.customers.getById(customerId);
        } catch (apiError) {
          console.error('خطأ في استدعاء window.api.customers.getById:', apiError);
          console.log('محاولة استخدام window.api.invoke كبديل...');
          customer = await window.api.invoke('get-customer-by-id', customerId);
        }
      } else {
        console.log('استخدام window.api.invoke("get-customer-by-id")');
        customer = await window.api.invoke('get-customer-by-id', customerId);
      }

      // إذا لم يتم العثور على العميل، نحاول البحث في قائمة العملاء المحلية
      if (!customer) {
        console.log('لم يتم العثور على العميل في قاعدة البيانات، البحث في قائمة العملاء المحلية');
        const allCustomers = await DatabaseAdapter.getCustomers();

        // تحويل customerId إلى نص للمقارنة المتسقة
        const customerIdStr = String(customerId);

        customer = allCustomers.find(c =>
          (c.id && String(c.id) === customerIdStr) ||
          (c._id && String(c._id) === customerIdStr)
        );
      }

      if (customer) {
        // تخزين العميل في التخزين المؤقت
        customersCache.set(cacheKey, customer);
      }

      return customer;
    } catch (error) {
      console.error(`خطأ في الحصول على العميل بالمعرف ${customerId}:`, error);
      return null;
    }
  },

  /**
   * إضافة عميل جديد
   * @param {Object} customer - بيانات العميل
   * @returns {Promise<Object>} - وعد بالعميل المضاف
   */
  addCustomer: async (customer) => {
    try {
      // استخدام SQLite
      const result = await sqliteManager.addCustomer(customer);

      // مسح التخزين المؤقت للعملاء
      customersCache.clear();

      return result;
    } catch (error) {
      console.error('خطأ في إضافة العميل:', error);
      throw error;
    }
  },

  /**
   * تحديث عميل
   * @param {Object} customer - بيانات العميل
   * @returns {Promise<Object>} - وعد بالعميل المحدث
   */
  updateCustomer: async (customer) => {
    try {
      // استخدام SQLite
      const result = await sqliteManager.updateCustomer(customer);

      // مسح التخزين المؤقت للعملاء
      customersCache.clear();

      return result;
    } catch (error) {
      console.error('خطأ في تحديث العميل:', error);
      throw error;
    }
  },

  /**
   * حذف عميل
   * @param {String|Number} id - معرف العميل
   * @returns {Promise<Object>} - وعد بنتيجة الحذف
   */
  deleteCustomer: async (id) => {
    try {
      // استخدام SQLite
      const result = await sqliteManager.deleteCustomer(id);

      // مسح التخزين المؤقت للعملاء
      customersCache.clear();

      return result;
    } catch (error) {
      console.error('خطأ في حذف العميل:', error);
      throw error;
    }
  },

  /**
   * الحصول على المستخدمين
   * @returns {Promise<Array>} - وعد بقائمة المستخدمين
   */
  getUsers: async () => {
    // التحقق من وجود البيانات في التخزين المؤقت
    const cachedUsers = usersCache.get('all_users');
    if (cachedUsers) {
      console.log('استخدام بيانات المستخدمين من التخزين المؤقت');
      return cachedUsers;
    }

    try {
      // استخدام SQLite
      const users = await sqliteManager.getUsers();

      // تخزين البيانات في التخزين المؤقت
      usersCache.set('all_users', users);

      return users;
    } catch (error) {
      console.error('خطأ في الحصول على المستخدمين:', error);
      throw error;
    }
  },

  /**
   * إضافة مستخدم جديد
   * @param {Object} user - بيانات المستخدم
   * @returns {Promise<Object>} - وعد بالمستخدم المضاف
   */
  addUser: async (user) => {
    try {
      // استخدام SQLite
      const result = await sqliteManager.addUser(user);

      // مسح التخزين المؤقت للمستخدمين
      usersCache.clear();

      return result;
    } catch (error) {
      console.error('خطأ في إضافة المستخدم:', error);
      throw error;
    }
  },

  /**
   * تحديث مستخدم
   * @param {Object} user - بيانات المستخدم
   * @returns {Promise<Object>} - وعد بالمستخدم المحدث
   */
  updateUser: async (user) => {
    try {
      // استخدام SQLite
      const result = await sqliteManager.updateUser(user);

      // مسح التخزين المؤقت للمستخدمين
      usersCache.clear();

      return result;
    } catch (error) {
      console.error('خطأ في تحديث المستخدم:', error);
      throw error;
    }
  },

  /**
   * حذف مستخدم
   * @param {String|Number} id - معرف المستخدم
   * @returns {Promise<Object>} - وعد بنتيجة الحذف
   */
  deleteUser: async (id) => {
    try {
      // استخدام SQLite
      const result = await sqliteManager.deleteUser(id);

      // مسح التخزين المؤقت للمستخدمين
      usersCache.clear();

      return result;
    } catch (error) {
      console.error('خطأ في حذف المستخدم:', error);
      throw error;
    }
  },

  /**
   * تسجيل الدخول
   * @param {Object} credentials - بيانات تسجيل الدخول
   * @returns {Promise<Object>} - وعد بنتيجة تسجيل الدخول
   */
  login: async (credentials) => {
    try {
      // استخدام SQLite
      const result = await sqliteManager.login(credentials);

      return result;
    } catch (error) {
      console.error('خطأ في تسجيل الدخول:', error);
      throw error;
    }
  },

  /**
   * إنشاء نسخة احتياطية من قاعدة البيانات
   * @returns {Promise<Object>} - وعد بنتيجة النسخ الاحتياطي
   */
  backupDatabase: () => {
    try {
      // استخدام SQLite
      const result = sqliteManager.backup();

      return result;
    } catch (error) {
      console.error('خطأ في إنشاء نسخة احتياطية من قاعدة البيانات:', error);
      throw error;
    }
  },

  /**
   * استعادة قاعدة البيانات من نسخة احتياطية
   * @param {String} backupPath - مسار النسخة الاحتياطية
   * @returns {Promise<Object>} - وعد بنتيجة الاستعادة
   */
  restoreDatabase: (backupPath) => {
    try {
      // استخدام SQLite
      const result = sqliteManager.restore(backupPath);

      // مسح جميع التخزين المؤقت
      itemsCache.clear();
      inventoryCache.clear();
      transactionsCache.clear();
      customersCache.clear();
      usersCache.clear();

      return result;
    } catch (error) {
      console.error('خطأ في استعادة قاعدة البيانات:', error);
      throw error;
    }
  },

  /**
   * إفراغ قاعدة البيانات
   * @param {Object} options - خيارات الإفراغ
   * @returns {Promise<Object>} - وعد بنتيجة الإفراغ
   */
  clearDatabase: async (options = {}) => {
    try {
      // استخدام SQLite
      const result = await sqliteManager.clearDatabase(options);

      // مسح جميع التخزين المؤقت
      itemsCache.clear();
      inventoryCache.clear();
      transactionsCache.clear();
      customersCache.clear();
      usersCache.clear();

      return result;
    } catch (error) {
      console.error('خطأ في إفراغ قاعدة البيانات:', error);
      throw error;
    }
  },

  /**
   * مسح جميع التخزين المؤقت
   */
  clearCache: () => {
    // مسح جميع التخزين المؤقت
    itemsCache.clear();
    inventoryCache.clear();
    transactionsCache.clear();
    customersCache.clear();
    usersCache.clear();

    return {
      success: true,
      message: 'تم مسح التخزين المؤقت بنجاح'
    };
  }
};

export default DatabaseAdapter;
