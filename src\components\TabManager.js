import React, { useEffect } from 'react';

/**
 * مكون لإدارة علامات التبويب
 * يقوم بتفعيل وظيفة التبديل بين علامات التبويب
 */
const TabManager = () => {
  useEffect(() => {
    // تفعيل التبديل بين علامات التبويب
    const handleTabClick = (e) => {
      e.preventDefault();
      
      // الحصول على الهدف من الرابط
      const targetId = e.currentTarget.getAttribute('href').substring(1);
      
      // إزالة الفئة النشطة من جميع الروابط
      document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
      });
      
      // إضافة الفئة النشطة للرابط الحالي
      e.currentTarget.classList.add('active');
      
      // إخفاء جميع محتويات التبويب
      document.querySelectorAll('.tab-pane').forEach(pane => {
        pane.classList.remove('show', 'active');
      });
      
      // إظهار محتوى التبويب المستهدف
      const targetPane = document.getElementById(targetId);
      if (targetPane) {
        targetPane.classList.add('show', 'active');
      }
    };
    
    // إضافة مستمع الحدث لجميع روابط التبويب
    document.querySelectorAll('.nav-link[data-toggle="tab"]').forEach(link => {
      link.addEventListener('click', handleTabClick);
    });
    
    // إزالة مستمع الحدث عند تفكيك المكون
    return () => {
      document.querySelectorAll('.nav-link[data-toggle="tab"]').forEach(link => {
        link.removeEventListener('click', handleTabClick);
      });
    };
  }, []);
  
  return null; // هذا المكون لا يعرض أي شيء
};

export default TabManager;
