/* أنماط صفحة الزكاة */
.zakat-page {
  padding: 20px;
}

.zakat-header {
  margin-bottom: 30px;
}

.zakat-header h1 {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 10px;
}

.zakat-header p {
  font-size: 1rem;
  color: var(--text-light);
}

/* أنماط البحث */
.search-container {
  position: relative;
  margin-bottom: 20px;
  max-width: 500px;
}

.search-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-light);
}

.search-input {
  width: 100%;
  padding: 10px 15px 10px 40px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  font-size: 0.95rem;
}

.search-input:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

/* أنماط أزرار التحكم */
.control-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
}

/* أنماط جدول الآلات */
.machines-table-container {
  margin-bottom: 30px;
}

.machine-name {
  font-weight: 600;
  color: var(--text-dark);
}

.machine-date {
  font-size: 0.9rem;
  color: var(--text-dark);
}

.machine-price {
  font-weight: 500;
}

.machine-notes {
  font-size: 0.9rem;
  color: var(--text-light);
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.machine-actions {
  display: flex;
  gap: 5px;
}

/* أنماط ملخص الزكاة */
.zakat-summary {
  background-color: var(--bg-light);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  padding: 20px;
  margin-bottom: 30px;
}

.zakat-summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.zakat-summary-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-dark);
}

.zakat-summary-date {
  font-size: 0.9rem;
  color: var(--text-light);
}

.zakat-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.zakat-stat-card {
  background-color: white;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  padding: 20px;
  text-align: center;
  transition: transform 0.2s, box-shadow 0.2s;
}

.zakat-stat-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.zakat-stat-icon {
  font-size: 2rem;
  color: var(--primary-color);
  margin-bottom: 10px;
}

.zakat-stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 5px;
}

.zakat-stat-label {
  font-size: 0.9rem;
  color: var(--text-light);
}

.zakat-info {
  background-color: rgba(var(--info-rgb), 0.1);
  border-radius: var(--border-radius-md);
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--info-color);
}

/* أنماط نافذة إضافة/تعديل آلة */
.machine-form {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group.full-width {
  grid-column: span 2;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-dark);
}

.form-control {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: 0.95rem;
}

.form-control:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.form-text {
  font-size: 0.8rem;
  color: var(--text-light);
  margin-top: 5px;
}

.form-actions {
  grid-column: span 2;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 10px;
}

.required {
  color: var(--danger-color);
  margin-right: 3px;
}

/* أنماط تقرير الزكاة */
.zakat-report {
  background-color: white;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  padding: 30px;
  margin-bottom: 30px;
}

.zakat-report-header {
  text-align: center;
  margin-bottom: 30px;
}

.zakat-report-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 10px;
}

.zakat-report-subtitle {
  font-size: 1.2rem;
  color: var(--text-dark);
  margin-bottom: 5px;
}

.zakat-report-date {
  font-size: 0.9rem;
  color: var(--text-light);
}

.zakat-report-footer {
  text-align: center;
  margin-top: 30px;
  font-size: 0.9rem;
  color: var(--text-light);
}

/* تنسيقات للشاشات الصغيرة */
@media (max-width: 768px) {
  .machine-form {
    grid-template-columns: 1fr;
  }
  
  .form-group.full-width {
    grid-column: span 1;
  }
  
  .form-actions {
    grid-column: span 1;
  }
  
  .zakat-stats {
    grid-template-columns: 1fr;
  }
}
