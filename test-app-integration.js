/**
 * ملف اختبار تكامل مع التطبيق لاختبار حساب الأرباح
 */

// محاولة تشغيل التطبيق واختبار دوال حساب الأرباح
async function testProfitCalculation() {
  try {
    console.log('=== اختبار تكامل حساب الأرباح مع التطبيق ===\n');

    // محاولة استيراد دوال حساب الأرباح
    let calculateProfit, calculateProfitWithTransport;
    
    try {
      const profitCalculator = require('./utils/profitCalculator');
      calculateProfit = profitCalculator.calculateProfit;
      calculateProfitWithTransport = profitCalculator.calculateProfitWithTransport;
      console.log('✅ تم استيراد دوال حساب الأرباح بنجاح');
    } catch (error) {
      console.log('❌ فشل في استيراد دوال حساب الأرباح:', error.message);
      return;
    }

    // اختبار دوال حساب الأرباح
    console.log('\n1. اختبار دالة calculateProfit:');
    
    const testResult1 = calculateProfit(1200, 1000, 1);
    console.log(`حساب الربح: (1200 - 1000) × 1 = ${testResult1}`);
    console.log(`المتوقع: 200, النتيجة: ${testResult1}`);
    
    if (testResult1 === 200) {
      console.log('✅ دالة calculateProfit تعمل بشكل صحيح');
    } else {
      console.log('❌ دالة calculateProfit لا تعمل بشكل صحيح');
    }

    console.log('\n2. اختبار دالة calculateProfitWithTransport:');
    
    const testResult2 = calculateProfitWithTransport(1200, 1000, 1, 50);
    console.log(`حساب الربح مع مصاريف النقل: (1200 - 1000 - 50) × 1 = ${testResult2}`);
    console.log(`المتوقع: 150, النتيجة: ${testResult2}`);
    
    if (testResult2 === 150) {
      console.log('✅ دالة calculateProfitWithTransport تعمل بشكل صحيح');
    } else {
      console.log('❌ دالة calculateProfitWithTransport لا تعمل بشكل صحيح');
    }

    // محاولة الاتصال بقاعدة البيانات
    console.log('\n3. اختبار الاتصال بقاعدة البيانات:');
    
    try {
      const dbManager = require('./database-singleton').getInstance();
      const db = dbManager.getConnection();
      
      if (db) {
        console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
        
        // اختبار استعلام بسيط
        try {
          const result = db.prepare('SELECT COUNT(*) as count FROM transactions WHERE transaction_type = ?').get('sale');
          console.log(`عدد معاملات البيع في قاعدة البيانات: ${result.count}`);
          
          if (result.count > 0) {
            // اختبار عينة من معاملات البيع
            const sampleTransactions = db.prepare(`
              SELECT id, quantity, selling_price, price, profit, transport_cost
              FROM transactions 
              WHERE transaction_type = 'sale' 
              LIMIT 3
            `).all();
            
            console.log('\n4. اختبار عينة من معاملات البيع:');
            sampleTransactions.forEach((transaction, index) => {
              console.log(`\nمعاملة ${index + 1} (ID: ${transaction.id}):`);
              console.log(`  الكمية: ${transaction.quantity}`);
              console.log(`  سعر البيع: ${transaction.selling_price}`);
              console.log(`  سعر التكلفة: ${transaction.price}`);
              console.log(`  مصاريف النقل: ${transaction.transport_cost || 0}`);
              console.log(`  الربح المسجل: ${transaction.profit}`);
              
              // حساب الربح المتوقع
              let expectedProfit;
              if (transaction.transport_cost && transaction.transport_cost > 0) {
                const transportCostPerUnit = transaction.transport_cost / transaction.quantity;
                expectedProfit = calculateProfitWithTransport(
                  transaction.selling_price,
                  transaction.price,
                  transaction.quantity,
                  transportCostPerUnit
                );
              } else {
                expectedProfit = calculateProfit(
                  transaction.selling_price,
                  transaction.price,
                  transaction.quantity
                );
              }
              
              console.log(`  الربح المتوقع: ${expectedProfit}`);
              
              if (Math.abs(transaction.profit - expectedProfit) < 0.01) {
                console.log('  ✅ الربح محسوب بشكل صحيح');
              } else {
                console.log('  ⚠️  هناك اختلاف في حساب الربح');
              }
            });
          } else {
            console.log('لا توجد معاملات بيع في قاعدة البيانات للاختبار');
          }
          
        } catch (queryError) {
          console.log('❌ فشل في تنفيذ الاستعلام:', queryError.message);
        }
        
      } else {
        console.log('❌ فشل في الاتصال بقاعدة البيانات');
      }
      
    } catch (dbError) {
      console.log('❌ خطأ في الاتصال بقاعدة البيانات:', dbError.message);
    }

    // اختبار مدير المعاملات
    console.log('\n5. اختبار مدير المعاملات:');
    
    try {
      const transactionManager = require('./unified-transaction-manager');
      console.log('✅ تم استيراد مدير المعاملات بنجاح');
      
      // التحقق من وجود الدوال المطلوبة
      if (typeof transactionManager.createTransaction === 'function') {
        console.log('✅ دالة createTransaction موجودة');
      } else {
        console.log('❌ دالة createTransaction غير موجودة');
      }
      
    } catch (managerError) {
      console.log('❌ فشل في استيراد مدير المعاملات:', managerError.message);
    }

    console.log('\n=== انتهاء اختبار التكامل ===');
    console.log('يمكنك الآن تشغيل التطبيق والتحقق من عمل النظام.');

  } catch (error) {
    console.error('خطأ عام في الاختبار:', error);
  }
}

// تشغيل الاختبار
testProfitCalculation();
