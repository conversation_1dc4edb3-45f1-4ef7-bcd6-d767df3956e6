import React, { useState } from 'react';
import { FaCalendarAlt } from 'react-icons/fa';
import './DateRangePicker.css';

/**
 * مكون اختيار نطاق التاريخ
 * @param {Object} props - خصائص المكون
 * @param {Date} props.startDate - تاريخ البداية
 * @param {Date} props.endDate - تاريخ النهاية
 * @param {Function} props.onChange - دالة تنفذ عند تغيير النطاق
 * @returns {JSX.Element} - مكون اختيار نطاق التاريخ
 */
const DateRangePicker = ({ startDate, endDate, onChange }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [localStartDate, setLocalStartDate] = useState(startDate);
  const [localEndDate, setLocalEndDate] = useState(endDate);

  // تنسيق التاريخ للعرض
  const formatDateForDisplay = (date) => {
    if (!date) return '';
    return date.toISOString().split('T')[0];
  };

  // تنسيق التاريخ للإدخال
  const formatDateForInput = (date) => {
    if (!date) return '';
    return date.toISOString().split('T')[0];
  };

  // معالجة تغيير تاريخ البداية
  const handleStartDateChange = (e) => {
    const newDate = e.target.value ? new Date(e.target.value) : null;
    setLocalStartDate(newDate);
  };

  // معالجة تغيير تاريخ النهاية
  const handleEndDateChange = (e) => {
    const newDate = e.target.value ? new Date(e.target.value) : null;
    setLocalEndDate(newDate);
  };

  // معالجة تطبيق النطاق
  const handleApply = () => {
    if (localStartDate && localEndDate && localStartDate <= localEndDate) {
      onChange({ startDate: localStartDate, endDate: localEndDate });
    }
    setIsOpen(false);
  };

  // معالجة إلغاء التغييرات
  const handleCancel = () => {
    setLocalStartDate(startDate);
    setLocalEndDate(endDate);
    setIsOpen(false);
  };

  // معالجة اختيار نطاق محدد مسبقًا
  const handlePresetRange = (days) => {
    const end = new Date();
    const start = new Date();
    start.setDate(end.getDate() - days);
    
    setLocalStartDate(start);
    setLocalEndDate(end);
  };

  return (
    <div className="date-range-picker">
      <div 
        className="date-range-display"
        onClick={() => setIsOpen(!isOpen)}
      >
        <FaCalendarAlt className="calendar-icon" />
        <span className="date-range-text">
          {formatDateForDisplay(startDate)} - {formatDateForDisplay(endDate)}
        </span>
      </div>
      
      {isOpen && (
        <div className="date-range-dropdown">
          <div className="date-range-presets">
            <button onClick={() => handlePresetRange(7)}>آخر 7 أيام</button>
            <button onClick={() => handlePresetRange(30)}>آخر 30 يوم</button>
            <button onClick={() => handlePresetRange(90)}>آخر 3 أشهر</button>
            <button onClick={() => handlePresetRange(180)}>آخر 6 أشهر</button>
            <button onClick={() => handlePresetRange(365)}>آخر سنة</button>
          </div>
          
          <div className="date-range-inputs">
            <div className="date-input-group">
              <label>من:</label>
              <input 
                type="date" 
                value={formatDateForInput(localStartDate)} 
                onChange={handleStartDateChange}
              />
            </div>
            
            <div className="date-input-group">
              <label>إلى:</label>
              <input 
                type="date" 
                value={formatDateForInput(localEndDate)} 
                onChange={handleEndDateChange}
              />
            </div>
          </div>
          
          <div className="date-range-actions">
            <button 
              className="apply-btn"
              onClick={handleApply}
            >
              تطبيق
            </button>
            
            <button 
              className="cancel-btn"
              onClick={handleCancel}
            >
              إلغاء
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default DateRangePicker;
