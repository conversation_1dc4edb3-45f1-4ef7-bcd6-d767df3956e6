/**
 * فحص بنية جدول المعاملات
 */

const sqlite3 = require('sqlite3').verbose();

// مسار قاعدة البيانات
const dbPath = 'C:\\Users\\<USER>\\AppData\\Roaming\\warehouse-management-system\\wms-database.db';

console.log('🔍 فحص بنية جدول المعاملات');
console.log('=' .repeat(50));

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ خطأ في الاتصال بقاعدة البيانات:', err.message);
    return;
  }
  console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
  
  // فحص بنية جدول transactions
  db.all("PRAGMA table_info(transactions)", (err, columns) => {
    if (err) {
      console.error('❌ خطأ في فحص بنية الجدول:', err.message);
      db.close();
      return;
    }
    
    console.log('\n📊 بنية جدول transactions:');
    columns.forEach(column => {
      console.log(`   - ${column.name}: ${column.type} ${column.notnull ? '(NOT NULL)' : ''} ${column.pk ? '(PRIMARY KEY)' : ''}`);
    });
    
    // فحص عينة من البيانات
    db.all("SELECT * FROM transactions LIMIT 5", (err, rows) => {
      if (err) {
        console.error('❌ خطأ في قراءة البيانات:', err.message);
      } else {
        console.log('\n📈 عينة من البيانات:');
        if (rows.length > 0) {
          rows.forEach((row, index) => {
            console.log(`   السجل ${index + 1}:`, row);
          });
        } else {
          console.log('   لا توجد بيانات');
        }
      }
      
      // فحص أنواع المعاملات
      db.all("SELECT DISTINCT transaction_type FROM transactions", (err, types) => {
        if (err) {
          console.error('❌ خطأ في فحص أنواع المعاملات:', err.message);
        } else {
          console.log('\n📋 أنواع المعاملات الموجودة:');
          types.forEach((type, index) => {
            console.log(`   ${index + 1}. ${type.transaction_type}`);
          });
        }
        
        // إحصائيات بسيطة
        db.all(`
          SELECT 
            transaction_type,
            COUNT(*) as count,
            SUM(total_price) as total_amount,
            SUM(profit) as total_profit
          FROM transactions 
          GROUP BY transaction_type
        `, (err, summary) => {
          if (err) {
            console.error('❌ خطأ في حساب الإحصائيات:', err.message);
          } else {
            console.log('\n📊 إحصائيات المعاملات:');
            summary.forEach(row => {
              console.log(`   ${row.transaction_type}:`);
              console.log(`     العدد: ${row.count}`);
              console.log(`     إجمالي المبلغ: ${row.total_amount || 0} د.ل`);
              console.log(`     إجمالي الأرباح: ${row.total_profit || 0} د.ل`);
            });
          }
          
          db.close((err) => {
            if (err) {
              console.error('❌ خطأ في إغلاق قاعدة البيانات:', err.message);
            } else {
              console.log('\n🔒 تم إغلاق الاتصال بقاعدة البيانات');
            }
          });
        });
      });
    });
  });
});
