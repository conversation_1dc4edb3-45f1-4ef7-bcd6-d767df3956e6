/**
 * وحدة إصلاح نظام الاسترجاع
 * تقوم بإصلاح وتحسين نظام استرجاع المعاملات
 */

const { logError, logSystem } = require('./error-handler');
const DatabaseManager = require('./database-singleton');
const returnTransactionsManager = require('./return-transactions-manager');

/**
 * إصلاح نظام الاسترجاع
 * @returns {Object} نتيجة عملية الإصلاح
 */
function fixRetrievalSystem() {
  try {
    console.log('بدء إصلاح نظام الاسترجاع...');
    
    // الحصول على اتصال قاعدة البيانات
    const dbManager = DatabaseManager.getInstance();
    const db = dbManager.getConnection();
    
    if (!db) {
      throw new Error('قاعدة البيانات غير متصلة');
    }
    
    // التحقق من حالة نظام الاسترجاع
    const status = returnTransactionsManager.checkReturnSystemStatus();
    
    if (!status.isReady) {
      console.log('نظام الاسترجاع غير جاهز، جاري إصلاحه...');
      
      // إصلاح نظام الاسترجاع
      const repairResult = returnTransactionsManager.repairReturnSystem();
      
      if (repairResult.success) {
        console.log('تم إصلاح نظام الاسترجاع بنجاح');
        logSystem('تم إصلاح نظام الاسترجاع بنجاح', 'info');
        
        return {
          success: true,
          message: 'تم إصلاح نظام الاسترجاع بنجاح',
          details: repairResult
        };
      } else {
        console.log('فشل في إصلاح نظام الاسترجاع');
        logSystem('فشل في إصلاح نظام الاسترجاع', 'warning');
        
        return {
          success: false,
          message: 'فشل في إصلاح نظام الاسترجاع',
          details: repairResult
        };
      }
    } else {
      console.log('نظام الاسترجاع جاهز بالفعل');
      
      return {
        success: true,
        message: 'نظام الاسترجاع جاهز بالفعل',
        details: status
      };
    }
  } catch (error) {
    console.error('خطأ في إصلاح نظام الاسترجاع:', error);
    logError(error, 'fixRetrievalSystem');
    
    return {
      success: false,
      message: `خطأ في إصلاح نظام الاسترجاع: ${error.message}`,
      error: error
    };
  }
}

module.exports = {
  fixRetrievalSystem
};