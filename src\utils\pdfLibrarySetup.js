/**
 * ملف إعداد مكتبات PDF
 * يقوم بتسجيل المكتبات المطلوبة في النافذة العالمية
 */

import { jsPDF } from 'jspdf';
// استيراد autoTable بشكل منفصل
import autoTable from 'jspdf-autotable';
import { setupArabicSupport } from './arabicPdfSupport';

/**
 * تسجيل مكتبات PDF في النافذة العالمية
 */
export function setupPDFLibraries() {
  try {
    // تسجيل jsPDF في النافذة العالمية
    if (typeof window !== 'undefined') {
      // تسجيل jsPDF في النافذة العالمية
      window.jsPDF = jsPDF;

      // تسجيل autoTable كامتداد لـ jsPDF
      if (typeof jsPDF.prototype.autoTable !== 'function') {
        console.log('تسجيل autoTable كامتداد لـ jsPDF...');

        // تسجيل autoTable يدويًا
        jsPDF.prototype.autoTable = autoTable;

        if (typeof jsPDF.prototype.autoTable === 'function') {
          console.log('تم تسجيل autoTable بنجاح');
        } else {
          console.error('فشل في تسجيل autoTable');
        }
      } else {
        console.log('autoTable مسجل بالفعل');
      }

      // تسجيل autoTable في النافذة العالمية للاستخدام المباشر
      window.autoTable = autoTable;

      // إعداد دعم اللغة العربية
      const testDoc = new jsPDF();
      setupArabicSupport(testDoc);

      console.log('تم تسجيل مكتبات PDF في النافذة العالمية ودعم اللغة العربية');
    }
  } catch (error) {
    console.error('خطأ في إعداد مكتبات PDF:', error);
  }
}

// تنفيذ الإعداد عند استيراد الملف
setupPDFLibraries();

export default setupPDFLibraries;
