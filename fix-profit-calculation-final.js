/**
 * إصلاح نهائي لحساب الأرباح مع مصاريف النقل
 * يضمن أن مصاريف النقل في المشتريات لا تؤثر على الأرباح
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// مسار قاعدة البيانات
const dbPath = 'C:\\Users\\<USER>\\AppData\\Roaming\\warehouse-management-system\\wms-database.db';

console.log('🔧 إصلاح نهائي لحساب الأرباح مع مصاريف النقل');
console.log('=' .repeat(60));

async function fixProfitCalculation() {
  return new Promise((resolve, reject) => {
    // الاتصال بقاعدة البيانات
    const db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        console.error('❌ خطأ في الاتصال بقاعدة البيانات:', err.message);
        reject(err);
        return;
      }
      console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

      // 1. عرض حالة الخزينة الحالية
      console.log('\n📊 حالة الخزينة الحالية:');
      db.get('SELECT * FROM cashbox WHERE id = 1', (err, currentCashbox) => {
        if (err) {
          console.error('❌ خطأ في الحصول على بيانات الخزينة:', err.message);
          db.close();
          reject(err);
          return;
        }

        if (currentCashbox) {
          console.log(`   الرصيد الحالي: ${currentCashbox.current_balance} د.ل`);
          console.log(`   إجمالي المبيعات: ${currentCashbox.sales_total} د.ل`);
          console.log(`   إجمالي المشتريات: ${currentCashbox.purchases_total} د.ل`);
          console.log(`   إجمالي مصاريف النقل: ${currentCashbox.transport_total || 0} د.ل`);
          console.log(`   إجمالي الأرباح الحالي: ${currentCashbox.profit_total} د.ل`);
        }

        // الانتقال إلى الخطوة التالية
        calculateProfits(db, currentCashbox, resolve, reject);
      });
    });
  });
}

function calculateProfits(db, currentCashbox, resolve, reject) {
  try {

    // 2. إعادة حساب الأرباح بالطريقة الصحيحة
    console.log('\n🔄 إعادة حساب الأرباح بالطريقة الصحيحة...');

    // الحصول على جميع معاملات البيع
    const salesQuery = `
      SELECT
        t.id,
        t.item_id,
        t.quantity,
        t.selling_price,
        t.profit as stored_profit,
        i.avg_price
      FROM transactions t
      LEFT JOIN inventory i ON t.item_id = i.item_id
      WHERE t.transaction_type = 'sale'
        AND t.selling_price > 0
    `;

    db.all(salesQuery, (err, salesTransactions) => {
      if (err) {
        console.error('❌ خطأ في الحصول على معاملات البيع:', err.message);
        db.close();
        reject(err);
        return;
      }

      let totalCalculatedProfit = 0;
      console.log(`   معالجة ${salesTransactions.length} معاملة بيع...`);

      // معالجة كل معاملة بيع
      processSalesTransactions(db, salesTransactions, 0, totalCalculatedProfit, currentCashbox, resolve, reject);
    });
  } catch (error) {
    console.error('❌ خطأ في حساب الأرباح:', error);
    db.close();
    reject(error);
  }
}

function processSalesTransactions(db, salesTransactions, index, totalCalculatedProfit, currentCashbox, resolve, reject) {
  if (index >= salesTransactions.length) {
    // انتهينا من معالجة جميع المعاملات، الآن نحسب المرتجعات
    calculateReturns(db, totalCalculatedProfit, currentCashbox, resolve, reject);
    return;
  }

  const transaction = salesTransactions[index];
  const { item_id, quantity, selling_price, avg_price, stored_profit } = transaction;

  // التحقق من وجود متوسط سعر الشراء
  if (!avg_price || avg_price <= 0) {
    console.warn(`     متوسط سعر الشراء غير متوفر للصنف ${item_id}, استخدام الربح المحفوظ: ${stored_profit || 0}`);
    totalCalculatedProfit += Number(stored_profit || 0);
    // الانتقال للمعاملة التالية
    processSalesTransactions(db, salesTransactions, index + 1, totalCalculatedProfit, currentCashbox, resolve, reject);
    return;
  }

  // حساب مصاريف النقل المخصصة لهذا الصنف من عمليات الشراء
  const purchaseQuery = `
    SELECT quantity, transport_cost
    FROM transactions
    WHERE item_id = ?
      AND transaction_type = 'purchase'
      AND transport_cost > 0
  `;

  db.all(purchaseQuery, [item_id], (err, purchases) => {
    if (err) {
      console.error(`     خطأ في حساب مصاريف النقل للصنف ${item_id}:`, err);
      // استخدام الربح المحفوظ في حالة الخطأ
      totalCalculatedProfit += Number(stored_profit || 0);
    } else {
      let transportCostPerUnit = 0;

      if (purchases && purchases.length > 0) {
        let totalPurchasedQuantity = 0;
        let totalTransportCost = 0;

        purchases.forEach(purchase => {
          totalPurchasedQuantity += purchase.quantity || 0;
          totalTransportCost += purchase.transport_cost || 0;
        });

        if (totalPurchasedQuantity > 0) {
          transportCostPerUnit = totalTransportCost / totalPurchasedQuantity;
        }
      }

      // حساب الربح الصحيح: (سعر البيع - متوسط سعر الشراء - مصاريف النقل لكل وحدة) × الكمية
      const calculatedProfit = (selling_price - avg_price - transportCostPerUnit) * quantity;

      if (transportCostPerUnit > 0) {
        console.log(`     الصنف ${item_id}: (${selling_price} - ${avg_price} - ${transportCostPerUnit}) × ${quantity} = ${calculatedProfit.toFixed(2)}`);
      }

      totalCalculatedProfit += calculatedProfit;
    }

    // الانتقال للمعاملة التالية
    processSalesTransactions(db, salesTransactions, index + 1, totalCalculatedProfit, currentCashbox, resolve, reject);
  });
}

function calculateReturns(db, totalCalculatedProfit, currentCashbox, resolve, reject) {
  // خصم أرباح المرتجعات
  const returnsQuery = `
    SELECT COALESCE(SUM(ABS(profit)), 0) as total_return_profit
    FROM transactions
    WHERE transaction_type = 'return'
  `;

  db.get(returnsQuery, (err, returnsResult) => {
    if (err) {
      console.error('❌ خطأ في حساب أرباح المرتجعات:', err.message);
      db.close();
      reject(err);
      return;
    }

    const totalReturnProfit = Number(returnsResult ? returnsResult.total_return_profit : 0);

    // الربح النهائي = أرباح المبيعات - أرباح المرتجعات
    const finalTotalProfit = totalCalculatedProfit - totalReturnProfit;

    console.log('\n📈 نتائج إعادة الحساب:');
    console.log(`   إجمالي أرباح المبيعات: ${totalCalculatedProfit.toFixed(2)} د.ل`);
    console.log(`   إجمالي أرباح المرتجعات: ${totalReturnProfit.toFixed(2)} د.ل`);
    console.log(`   إجمالي الأرباح النهائي: ${finalTotalProfit.toFixed(2)} د.ل`);
    console.log(`   الأرباح السابقة: ${currentCashbox.profit_total} د.ل`);
    console.log(`   الفرق: ${(finalTotalProfit - currentCashbox.profit_total).toFixed(2)} د.ل`);

    // 3. تحديث الأرباح في قاعدة البيانات
    console.log('\n💾 تحديث الأرباح في قاعدة البيانات...');

    const roundedProfit = Math.round(finalTotalProfit * 100) / 100;

    const updateQuery = `
      UPDATE cashbox
      SET profit_total = ?,
          updated_at = ?
      WHERE id = 1
    `;

    db.run(updateQuery, [roundedProfit, new Date().toISOString()], function(err) {
      if (err) {
        console.error('❌ خطأ في تحديث الأرباح:', err.message);
        db.close();
        reject(err);
        return;
      }

      if (this.changes > 0) {
        console.log(`   ✅ تم تحديث الأرباح بنجاح إلى: ${roundedProfit} د.ل`);

        // التحقق من التحديث
        db.get('SELECT profit_total FROM cashbox WHERE id = 1', (err, verifyResult) => {
          if (err) {
            console.error('❌ خطأ في التحقق من التحديث:', err.message);
          } else {
            console.log(`   التحقق: الأرباح المحفوظة الآن: ${verifyResult.profit_total} د.ل`);
          }

          // 4. عرض ملخص الإصلاح
          console.log('\n📋 ملخص الإصلاح:');
          console.log('   ✅ تم إصلاح حساب الأرباح ليراعي مصاريف النقل بشكل صحيح');
          console.log('   ✅ مصاريف النقل في المشتريات تخصم من الرصيد الحالي فقط');
          console.log('   ✅ مصاريف النقل في البيع تخصم من الأرباح');
          console.log('   ✅ تم تحديث إجمالي الأرباح في الخزينة');

          console.log('\n🎉 تم الإصلاح بنجاح!');
          console.log('=' .repeat(60));

          // إغلاق قاعدة البيانات والانتهاء
          db.close((err) => {
            if (err) {
              console.error('❌ خطأ في إغلاق قاعدة البيانات:', err.message);
            } else {
              console.log('🔒 تم إغلاق الاتصال بقاعدة البيانات');
            }
            resolve();
          });
        });
      } else {
        console.log('   ❌ فشل في تحديث الأرباح');
        db.close();
        reject(new Error('فشل في تحديث الأرباح'));
      }
    });
  });
}

// تشغيل الإصلاح
fixProfitCalculation().catch(console.error);
