/**
 * سكريبت لإصلاح الخزينة بناءً على المعاملات الموجودة
 */

const DatabaseManager = require('./database-singleton');

/**
 * إصلاح الخزينة بناءً على المعاملات الموجودة
 */
async function fixCashboxFromTransactions() {
  try {
    console.log('[CASHBOX-FIX] بدء إصلاح الخزينة بناءً على المعاملات الموجودة...');

    // تهيئة قاعدة البيانات
    const dbManager = DatabaseManager.getInstance();
    if (!dbManager) {
      throw new Error('فشل في الحصول على مدير قاعدة البيانات');
    }

    // تهيئة قاعدة البيانات
    const path = require('path');
    const dbPath = path.join(__dirname, 'warehouse.db');
    await dbManager.initialize(dbPath);

    const db = dbManager.getConnection();
    if (!db) {
      throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
    }

    // الحصول على الخزينة الحالية
    const getCashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
    const currentCashbox = getCashboxStmt.get();

    if (!currentCashbox) {
      throw new Error('لا توجد خزينة في قاعدة البيانات');
    }

    console.log('[CASHBOX-FIX] الخزينة الحالية:', {
      id: currentCashbox.id,
      initial_balance: currentCashbox.initial_balance,
      current_balance: currentCashbox.current_balance,
      sales_total: currentCashbox.sales_total,
      purchases_total: currentCashbox.purchases_total,
      profit_total: currentCashbox.profit_total
    });

    // الحصول على جميع المعاملات
    const getTransactionsStmt = db.prepare(`
      SELECT t.*, inv.avg_price
      FROM transactions t
      LEFT JOIN inventory inv ON t.item_id = inv.item_id
      ORDER BY t.transaction_date ASC
    `);
    const transactions = getTransactionsStmt.all();

    console.log(`[CASHBOX-FIX] تم العثور على ${transactions.length} معاملة`);

    // حساب الإجماليات من المعاملات
    let totalSales = 0;
    let totalPurchases = 0;
    let totalReturns = 0;
    let totalProfit = 0;
    let totalTransportCost = 0;

    for (const transaction of transactions) {
      const amount = parseFloat(transaction.total_price) || 0;
      const transportCost = parseFloat(transaction.transport_cost) || 0;

      if (transaction.transaction_type === 'sale') {
        totalSales += amount;

        // حساب الربح
        let profit = 0;
        if (transaction.profit && transaction.profit > 0) {
          profit = parseFloat(transaction.profit);
        } else if (transaction.selling_price > 0 && transaction.avg_price > 0) {
          // حساب الربح الأساسي
          const basicProfit = (transaction.selling_price - transaction.avg_price) * transaction.quantity;
          // خصم مصاريف النقل
          profit = Math.max(0, basicProfit - transportCost);
        } else {
          // تقدير 20% من سعر البيع
          profit = Math.max(0, (amount * 0.2) - transportCost);
        }

        totalProfit += profit;

      } else if (transaction.transaction_type === 'purchase') {
        totalPurchases += amount;
        totalTransportCost += transportCost;

      } else if (transaction.transaction_type === 'return') {
        totalReturns += amount;

        // خصم الربح المرتبط بالإرجاع
        let returnProfit = 0;
        if (transaction.profit && transaction.profit > 0) {
          returnProfit = parseFloat(transaction.profit);
        } else {
          returnProfit = amount * 0.2; // تقدير 20%
        }
        totalProfit -= returnProfit;
      }
    }

    // التأكد من أن الأرباح لا تكون سالبة
    totalProfit = Math.max(0, totalProfit);

    console.log('[CASHBOX-FIX] الإجماليات المحسوبة من المعاملات:', {
      totalSales,
      totalPurchases,
      totalReturns,
      totalProfit,
      totalTransportCost
    });

    // حساب الرصيد الحالي الجديد
    const newCurrentBalance = currentCashbox.initial_balance + totalSales - totalPurchases - totalReturns - totalTransportCost;

    console.log('[CASHBOX-FIX] الرصيد الحالي الجديد المحسوب:', newCurrentBalance);

    // تحديث الخزينة
    const updateCashboxStmt = db.prepare(`
      UPDATE cashbox
      SET current_balance = ?,
          sales_total = ?,
          purchases_total = ?,
          returns_total = ?,
          profit_total = ?,
          transport_total = ?,
          updated_at = ?
      WHERE id = ?
    `);

    const updateResult = updateCashboxStmt.run(
      newCurrentBalance,
      totalSales,
      totalPurchases,
      totalReturns,
      totalProfit,
      totalTransportCost,
      new Date().toISOString(),
      currentCashbox.id
    );

    console.log('[CASHBOX-FIX] نتيجة تحديث الخزينة:', updateResult);

    // التحقق من التحديث
    const updatedCashbox = getCashboxStmt.get();
    console.log('[CASHBOX-FIX] الخزينة بعد التحديث:', {
      id: updatedCashbox.id,
      initial_balance: updatedCashbox.initial_balance,
      current_balance: updatedCashbox.current_balance,
      sales_total: updatedCashbox.sales_total,
      purchases_total: updatedCashbox.purchases_total,
      returns_total: updatedCashbox.returns_total,
      profit_total: updatedCashbox.profit_total,
      transport_total: updatedCashbox.transport_total
    });

    console.log('[CASHBOX-FIX] تم إصلاح الخزينة بنجاح');
    return {
      success: true,
      message: 'تم إصلاح الخزينة بنجاح',
      before: {
        current_balance: currentCashbox.current_balance,
        sales_total: currentCashbox.sales_total,
        purchases_total: currentCashbox.purchases_total,
        profit_total: currentCashbox.profit_total
      },
      after: {
        current_balance: updatedCashbox.current_balance,
        sales_total: updatedCashbox.sales_total,
        purchases_total: updatedCashbox.purchases_total,
        profit_total: updatedCashbox.profit_total
      }
    };

  } catch (error) {
    console.error('[CASHBOX-FIX] خطأ في إصلاح الخزينة:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// تشغيل السكريبت إذا تم استدعاؤه مباشرة
if (require.main === module) {
  fixCashboxFromTransactions().then(result => {
    console.log('نتيجة إصلاح الخزينة:', result);
    process.exit(result.success ? 0 : 1);
  }).catch(error => {
    console.error('خطأ في تشغيل السكريبت:', error);
    process.exit(1);
  });
}

module.exports = {
  fixCashboxFromTransactions
};
