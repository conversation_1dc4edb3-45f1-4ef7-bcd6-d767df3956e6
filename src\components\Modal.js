import React, { useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import { FaTimes } from 'react-icons/fa';
import './Modal.css';

/**
 * مكون النافذة المنبثقة
 * يوفر تصميمًا موحدًا للنوافذ المنبثقة في التطبيق
 */
const Modal = ({
  isOpen,
  onClose,
  title,
  children,
  footer,
  size = 'md',
  closeOnClickOutside = true,
  closeOnEsc = true,
  showCloseButton = true,
  className = '',
  contentClassName = '',
  headerClassName = '',
  bodyClassName = '',
  footerClassName = '',
  ...props
}) => {
  const modalRef = useRef(null);

  // تنفيذ إغلاق النافذة بشكل فوري
  const handleClose = () => {
    console.log('إغلاق النافذة المنبثقة...');

    // إغلاق النافذة فوراً بدون تأخير
    onClose();
  };

  // إغلاق النافذة عند الضغط على زر Escape
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (closeOnEsc && event.key === 'Escape' && isOpen) {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      // منع التمرير في الصفحة الخلفية
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      // استعادة التمرير عند إغلاق النافذة
      document.body.style.overflow = '';
    };
  }, [isOpen, closeOnEsc]);

  // معالج النقر خارج النافذة
  const handleClickOutside = (event) => {
    if (closeOnClickOutside && modalRef.current && !modalRef.current.contains(event.target)) {
      handleClose();
    }
  };

  // إذا كانت النافذة مغلقة، لا تعرض شيئًا
  if (!isOpen) return null;

  // تحديد فئات CSS
  const modalClasses = [
    'app-modal-container',
    `app-modal-${size}`,
    className
  ].filter(Boolean).join(' ');

  // إنشاء النافذة في جسم المستند
  return createPortal(
    <div className="app-modal-backdrop" onClick={handleClickOutside}>
      <div
        className={modalClasses}
        ref={modalRef}
        {...props}
      >
        {/* رأس النافذة */}
        {(title || showCloseButton) && (
          <div className={`app-modal-header ${headerClassName}`}>
            {title && <h3 className="app-modal-title">{title}</h3>}
            {showCloseButton && (
              <button
                className="app-modal-close"
                onClick={handleClose}
                aria-label="إغلاق"
              >
                <FaTimes />
              </button>
            )}
          </div>
        )}

        {/* محتوى النافذة */}
        <div className={`app-modal-body ${bodyClassName}`}>
          {children}
        </div>

        {/* تذييل النافذة */}
        {footer && (
          <div className={`app-modal-footer ${footerClassName}`}>
            {footer}
          </div>
        )}
      </div>
    </div>,
    document.body
  );
};

export default Modal;
