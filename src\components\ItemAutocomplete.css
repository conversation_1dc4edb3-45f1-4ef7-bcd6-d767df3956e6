/* تنسيق مكون الإكمال التلقائي للأصناف */
.item-autocomplete-wrapper {
  position: relative;
  width: 100%;
}

.item-autocomplete-icon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-light, #777);
  z-index: 1;
}

.item-autocomplete-container {
  position: relative;
  width: 100%;
}

.item-autocomplete-input {
  width: 100%;
  padding: 10px 35px 10px 10px;
  border: 1px solid var(--border-color, #ddd);
  border-radius: 4px;
  font-size: 14px;
  color: var(--text-color, #333);
  background-color: var(--bg-color, #fff);
  transition: border-color 0.3s;
  text-align: right;
  direction: rtl;
}

.item-autocomplete-input:focus {
  outline: none;
  border-color: var(--primary-color, #3498db);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.item-autocomplete-suggestions-container {
  display: none;
}

.item-autocomplete-suggestions-container-open {
  display: block;
  position: absolute;
  top: 100%;
  width: 100%;
  margin-top: 5px;
  background-color: var(--bg-color, #fff);
  border: 1px solid var(--border-color, #ddd);
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  max-height: 300px;
  overflow-y: auto;
  z-index: 100;
}

.item-autocomplete-suggestions-list {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

.item-autocomplete-suggestion {
  cursor: pointer;
  padding: 10px;
  border-bottom: 1px solid var(--border-color, #ddd);
}

.item-autocomplete-suggestion:last-child {
  border-bottom: none;
}

.item-autocomplete-suggestion-highlighted {
  background-color: var(--highlight-color, #f5f5f5);
}

/* تنسيق اقتراح الصنف */
.item-suggestion {
  display: flex;
  flex-direction: column;
  gap: 5px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.item-suggestion:hover,
.item-suggestion.selected {
  background-color: var(--hover-color, #f5f5f5);
}

.item-suggestion-name {
  font-weight: bold;
  color: var(--text-color, #333);
}

.item-suggestion-details {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  font-size: 12px;
  color: var(--text-light, #777);
}

.item-suggestion-detail {
  display: flex;
  align-items: center;
  gap: 5px;
}

.item-suggestion-label {
  display: flex;
  align-items: center;
  gap: 3px;
}

.item-suggestion-value {
  font-weight: bold;
}

/* تنسيق الكمية */
.item-quantity {
  font-weight: bold;
}

.item-quantity.out {
  color: var(--danger-color, #e74c3c);
}

.item-quantity.low {
  color: var(--warning-color, #f39c12);
}

/* تنسيق حالة التحميل والخطأ */
.item-autocomplete-loading,
.item-autocomplete-error {
  padding: 5px 10px;
  font-size: 12px;
  text-align: center;
  margin-top: 5px;
}

.item-autocomplete-loading {
  color: var(--text-light, #777);
}

.item-autocomplete-error {
  color: var(--danger-color, #e74c3c);
}

/* تخصيص المكون في سياقات مختلفة */

/* تخصيص في صفحة المخزون */
.inventory-search .item-autocomplete-input {
  height: 40px;
  border-radius: 20px;
  padding-right: 40px;
  background-color: var(--bg-light, #f8f9fa);
  border-color: var(--border-color, #ddd);
}

.inventory-search .item-autocomplete-icon {
  right: 15px;
}

.inventory-search .item-autocomplete-suggestions-container-open {
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* تخصيص في صفحة البيع للعملاء */
.item-search-in-sales .item-autocomplete-input {
  height: 42px;
  border-radius: 6px;
  border-color: var(--primary-color-light, #5dade2);
  box-shadow: 0 0 0 1px rgba(52, 152, 219, 0.1);
}

.item-search-in-sales .item-autocomplete-input:focus {
  border-color: var(--primary-color, #3498db);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.item-search-in-sales .item-autocomplete-suggestions-container-open {
  border-radius: 6px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  border-color: var(--primary-color-light, #5dade2);
}

.item-search-in-sales .item-autocomplete-suggestion-highlighted {
  background-color: rgba(52, 152, 219, 0.1);
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
  .item-autocomplete-suggestions-container-open {
    max-height: 250px;
  }

  .item-suggestion-details {
    flex-direction: column;
    gap: 5px;
  }
}
