.item-search-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  direction: rtl;
}

.item-search-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.item-search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
  background-color: #f8f9fa;
}

.item-search-header h3 {
  margin: 0;
  font-size: 1.2rem;
  color: #333;
}

.item-search-close {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: #777;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s;
}

.item-search-close:hover {
  color: #e74c3c;
}

.item-search-body {
  padding: 15px 20px;
  overflow-y: auto;
  flex: 1;
  max-height: calc(90vh - 130px);
}

.item-search-input {
  position: relative;
  margin-bottom: 15px;
}

.item-search-input input {
  width: 100%;
  padding: 10px 40px 10px 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.item-search-input input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.search-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #777;
}

.item-search-results {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
  margin-top: 10px;
}

.item-card {
  border: 1px solid #eee;
  border-radius: 6px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.2s;
  background-color: #fff;
}

.item-card:hover {
  border-color: #3498db;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.item-card.selected {
  border-color: #3498db;
  background-color: #ebf5fb;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.2);
}

.item-name {
  font-weight: bold;
  margin-bottom: 8px;
  font-size: 1rem;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-details {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.item-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
}

.item-detail-label {
  color: #777;
  display: flex;
  align-items: center;
}

.item-detail-value {
  font-weight: 500;
  color: #333;
}

.item-detail-value.item-quantity {
  color: #27ae60;
}

.item-detail-value.item-quantity.low {
  color: #f39c12;
}

.item-detail-value.item-quantity.out {
  color: #e74c3c;
}

.item-search-footer {
  padding: 15px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  background-color: #f8f9fa;
}

.item-search-btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.item-search-btn-primary {
  background-color: #3498db;
  color: white;
}

.item-search-btn-primary:hover {
  background-color: #2980b9;
}

.item-search-btn-primary:disabled {
  background-color: #95a5a6;
  cursor: not-allowed;
}

.item-search-btn-secondary {
  background-color: #ecf0f1;
  color: #333;
}

.item-search-btn-secondary:hover {
  background-color: #dfe6e9;
}

.no-items-message {
  text-align: center;
  padding: 20px;
  color: #777;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

@media (max-width: 768px) {
  .item-search-results {
    grid-template-columns: 1fr;
  }

  .item-search-container {
    width: 95%;
    max-height: 95vh;
  }

  .item-search-body {
    max-height: calc(95vh - 130px);
  }
}
