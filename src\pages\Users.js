import React, { useState, useEffect, useRef } from 'react';
import { FaPlus, FaEdit, FaTrash, FaSearch, FaUserShield, FaUser, FaUserTie } from 'react-icons/fa';
import { useAuth } from '../context/AuthContext';
import * as database from '../utils/database';
import { useApp } from '../context/hooks/useApp';
import useFormReset from '../hooks/useFormReset';

/**
 * صفحة إدارة المستخدمين
 *
 * تتيح هذه الصفحة للمدراء إدارة المستخدمين في النظام (إضافة، تعديل، حذف)
 * تستخدم الصفحة عدة طرق للحصول على البيانات وإدارتها:
 * 1. محاولة استخدام سياق التطبيق (AppContext) عبر هوك useApp
 * 2. استخدام وظائف قاعدة البيانات مباشرة عبر وحدة database
 * 3. استخدام واجهة window.api إذا كانت متاحة
 *
 * تم تصميم الصفحة لتعمل حتى في حالة عدم توفر بعض هذه الطرق
 */
const Users = () => {
  // استخدام سياق المصادقة للحصول على المستخدم الحالي
  const { currentUser } = useAuth();

  // محاولة استخدام هوك useApp للحصول على وظائف إدارة المستخدمين
  let appContext = null;
  try {
    appContext = useApp();
  } catch (error) {
    console.error('خطأ في استخدام هوك useApp:', error);
    // استمر في التنفيذ حتى لو فشل استخدام الهوك
  }

  // وظيفة بديلة للتحقق من صلاحيات المستخدم في حالة عدم توفر سياق التطبيق
  const fallbackCheckPermission = (action) => {
    const currentUserRole = localStorage.getItem('currentUserRole');

    // المدير لديه جميع الصلاحيات
    if (currentUserRole === 'admin' || currentUserRole === 'manager') {
      return true;
    }

    // تعريف الصلاحيات حسب دور المستخدم
    const permissions = {
      employee: [
        'add_item', 'update_item', 'delete_item',
        'add_purchase', 'add_sale', 'delete_transaction',
        'add_customer', 'update_customer', 'delete_customer',
        'view_reports', 'view_inventory', 'view_items', 'view_customers', 'view_transactions'
      ],
      viewer: [
        'view_reports', 'view_inventory', 'view_items', 'view_customers', 'view_transactions'
      ]
    };

    // التحقق من وجود الصلاحية في قائمة صلاحيات دور المستخدم
    return permissions[currentUserRole] && permissions[currentUserRole].includes(action);
  };

  // تعريف متغيرات الحالة
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [modalMode, setModalMode] = useState('add');
  const [currentUserEdit, setCurrentUserEdit] = useState(null);
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    full_name: '',
    role: 'employee'
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [alert, setAlert] = useState({ show: false, type: '', message: '' });

  // مرجع للنموذج
  const formRef = useRef(null);

  // مرجع لمؤقت التنبيهات
  const alertTimeoutRef = useRef(null);

  // مرجع لتتبع ما إذا كان المكون لا يزال مثبتًا
  const isMounted = useRef(true);

  // تنظيف المؤقتات عند إلغاء تحميل المكون
  useEffect(() => {
    // تعيين isMounted إلى true عند تحميل المكون
    isMounted.current = true;

    return () => {
      isMounted.current = false;
      if (alertTimeoutRef.current) {
        clearTimeout(alertTimeoutRef.current);
        alertTimeoutRef.current = null;
      }
    };
  }, []);

  // تحميل البيانات عند تحميل المكون
  useEffect(() => {
    console.log('Users component mounted');

    // محاولة تحميل المستخدمين من سياق التطبيق أولاً إذا كان متاحًا
    if (appContext && appContext.users && appContext.users.length > 0) {
      console.log('استخدام المستخدمين من سياق التطبيق:', appContext.users.length);
      setUsers(appContext.users);
      setLoading(false);
    } else {
      // إذا لم يكن سياق التطبيق متاحًا أو لا يحتوي على مستخدمين، استخدم وظيفة loadUsers
      console.log('تحميل المستخدمين باستخدام loadUsers');
      loadUsers();
    }
  }, []);

  // تصفية المستخدمين عند تغيير البحث
  useEffect(() => {
    if (users && users.length > 0) {
      setFilteredUsers(
        users.filter(user =>
          user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (user.full_name && user.full_name.toLowerCase().includes(searchTerm.toLowerCase()))
        )
      );
    } else {
      setFilteredUsers([]);
    }
  }, [users, searchTerm]);

  // تحميل المستخدمين من قاعدة البيانات
  const loadUsers = async () => {
    setLoading(true);
    setError(null);

    try {
      // التحقق من توفر window.api
      if (!window.api) {
        console.error('window.api غير متوفر');
        setError('حدث خطأ في الاتصال بقاعدة البيانات. يرجى إعادة تشغيل التطبيق.');
        setUsers([]);
        setLoading(false);
        return;
      }

      // استدعاء وظيفة الحصول على المستخدمين من قاعدة البيانات
      console.log('جاري استدعاء window.api.users.getAll()...');
      let usersData;

      try {
        // محاولة استخدام واجهة المستخدمين المباشرة
        if (window.api.users && typeof window.api.users.getAll === 'function') {
          usersData = await window.api.users.getAll();
          console.log('تم استخدام window.api.users.getAll() بنجاح');
        } else {
          // استخدام واجهة invoke العامة
          usersData = await window.api.invoke('get-users');
          console.log('تم استخدام window.api.invoke("get-users") بنجاح');
        }
      } catch (apiError) {
        console.error('خطأ في استدعاء API:', apiError);
        // محاولة استخدام database.getUsers كخيار أخير
        console.log('محاولة استخدام database.getUsers()...');
        usersData = await database.getUsers();
      }

      console.log('نتيجة استدعاء الحصول على المستخدمين:', usersData);

      if (Array.isArray(usersData)) {
        // إصلاح تلقائي: تأكد أن كل مستخدم لديه id وrole
        const safeUsers = usersData.map(user => ({
          ...user,
          id: user.id || user._id || '',
          role: user.role || 'employee'
        }));
        console.log('تم تحميل المستخدمين بنجاح:', safeUsers.length);
        setUsers(safeUsers);
      } else {
        console.error('البيانات المستلمة ليست مصفوفة:', usersData);
        setError('حدث خطأ أثناء تحميل بيانات المستخدمين');
        setUsers([]);
      }
    } catch (error) {
      console.error('خطأ في تحميل المستخدمين:', error);
      setError('حدث خطأ أثناء تحميل بيانات المستخدمين: ' + (error.message || 'خطأ غير معروف'));
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // استخدام هوك إعادة تعيين النموذج
  const { enableFormFields, enableFormFieldsAfterDelete } = useFormReset(formRef);

  const handleAddUser = () => {
    setModalMode('add');
    setFormData({
      username: '',
      password: '',
      full_name: '',
      role: 'employee'
    });
    setShowModal(true);

    // إعادة تمكين حقول النموذج
    setTimeout(() => {
      enableFormFields();
    }, 100);
  };

  const handleEditUser = (user) => {
    setModalMode('edit');
    setCurrentUserEdit(user);
    setFormData({
      username: user.username,
      password: '',
      full_name: user.full_name || '',
      role: user.role
    });
    setShowModal(true);

    // إعادة تمكين حقول النموذج
    setTimeout(() => {
      enableFormFields();
    }, 100);
  };

  const handleDeleteUser = async (id) => {
    // الحصول على المستخدم الحالي من localStorage إذا كان currentUser فارغًا
    let currentUserId = currentUser ? currentUser.id : null;
    if (!currentUserId && localStorage.getItem('currentUserId')) {
      currentUserId = localStorage.getItem('currentUserId');
      console.log('استخدام معرف المستخدم الحالي من localStorage:', currentUserId);
    }

    // منع حذف المستخدم الحالي
    if (id === currentUserId || id.toString() === currentUserId.toString()) {
      showAlert('danger', 'لا يمكن حذف المستخدم الحالي');
      return;
    }

    // منع حذف المدير الوحيد
    const admins = users.filter(user => user.role === 'admin');
    if (admins.length === 1 && admins[0].id === id) {
      showAlert('danger', 'لا يمكن حذف المستخدم الإداري الوحيد');
      return;
    }

    if (window.confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
      try {
        setLoading(true);
        // استدعاء وظيفة حذف المستخدم من قاعدة البيانات
        try {
          // محاولة استخدام واجهة المستخدمين المباشرة
          if (window.api.users && typeof window.api.users.delete === 'function') {
            await window.api.users.delete(id);
            console.log('تم استخدام window.api.users.delete بنجاح');
          } else {
            // استخدام واجهة invoke العامة
            await window.api.invoke('delete-user', id);
            console.log('تم استخدام window.api.invoke("delete-user") بنجاح');
          }
        } catch (apiError) {
          console.error('خطأ في استدعاء API لحذف المستخدم:', apiError);
          // محاولة استخدام database.deleteUser كخيار أخير
          await database.deleteUser(id);
        }

        // تحديث قائمة المستخدمين
        setUsers(prevUsers => prevUsers.filter(user => user.id !== id));
        showAlert('success', 'تم حذف المستخدم بنجاح');

        // استخدام الوظيفة المحسنة لإعادة تمكين حقول الإدخال بعد الحذف
        console.log('استدعاء enableFormFieldsAfterDelete بعد عملية حذف المستخدم...');
        enableFormFieldsAfterDelete();
      } catch (error) {
        console.error('خطأ في حذف المستخدم:', error);
        showAlert('danger', 'فشل في حذف المستخدم: ' + (error.message || 'خطأ غير معروف'));
      } finally {
        setLoading(false);
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    console.log('Form submitted', formData);

    if (!formData.username) {
      showAlert('danger', 'الرجاء إدخال اسم المستخدم');
      return;
    }

    if (modalMode === 'add' && !formData.password) {
      showAlert('danger', 'الرجاء إدخال كلمة المرور');
      return;
    }

    try {
      setLoading(true);

      if (modalMode === 'add') {
        console.log('Adding new user');

        // التحقق من عدم تكرار اسم المستخدم
        if (users.some(user => user.username === formData.username)) {
          console.log('Username already exists');
          showAlert('danger', 'اسم المستخدم موجود بالفعل');
          setLoading(false);
          return;
        }

        // إضافة مستخدم جديد
        const newUser = {
          username: formData.username,
          password: formData.password,
          full_name: formData.full_name || '',
          role: formData.role
        };

        console.log('New user object:', newUser);

        // استدعاء وظيفة إضافة المستخدم إلى قاعدة البيانات
        let addedUser;
        try {
          // محاولة استخدام واجهة المستخدمين المباشرة
          if (window.api.users && typeof window.api.users.add === 'function') {
            addedUser = await window.api.users.add(newUser);
            console.log('تم استخدام window.api.users.add بنجاح');
          } else {
            // استخدام واجهة invoke العامة
            addedUser = await window.api.invoke('add-user', newUser);
            console.log('تم استخدام window.api.invoke("add-user") بنجاح');
          }
        } catch (apiError) {
          console.error('خطأ في استدعاء API لإضافة المستخدم:', apiError);
          // محاولة استخدام database.addUser كخيار أخير
          addedUser = await database.addUser(newUser);
        }
        console.log('User added successfully:', addedUser);

        // إضافة المستخدم الجديد إلى القائمة
        setUsers(prevUsers => [...prevUsers, addedUser]);

        // إغلاق النافذة المنبثقة
        if (isMounted.current) {
          setShowModal(false);

          // عرض رسالة نجاح
          showAlert('success', 'تم إضافة المستخدم بنجاح');
        }
      } else {
        console.log('Updating user', currentUserEdit);

        // تحديث مستخدم موجود
        const updatedUser = {
          ...currentUserEdit,
          full_name: formData.full_name,
          role: formData.role
        };

        // إضافة كلمة المرور إذا تم توفيرها
        if (formData.password) {
          updatedUser.password = formData.password;
        }

        console.log('Updated user object:', updatedUser);

        // استدعاء وظيفة تحديث المستخدم في قاعدة البيانات
        let result;
        try {
          // محاولة استخدام واجهة المستخدمين المباشرة
          if (window.api.users && typeof window.api.users.update === 'function') {
            result = await window.api.users.update(updatedUser);
            console.log('تم استخدام window.api.users.update بنجاح');
          } else {
            // استخدام واجهة invoke العامة
            result = await window.api.invoke('update-user', updatedUser);
            console.log('تم استخدام window.api.invoke("update-user") بنجاح');
          }
        } catch (apiError) {
          console.error('خطأ في استدعاء API لتحديث المستخدم:', apiError);
          // محاولة استخدام database.updateUser كخيار أخير
          result = await database.updateUser(updatedUser);
        }
        console.log('User updated successfully:', result);

        // تحديث قائمة المستخدمين
        setUsers(prevUsers =>
          prevUsers.map(user =>
            user.id === updatedUser.id ? result : user
          )
        );

        // إغلاق النافذة المنبثقة
        if (isMounted.current) {
          setShowModal(false);

          // عرض رسالة نجاح
          showAlert('success', 'تم تحديث المستخدم بنجاح');
        }
      }
    } catch (err) {
      console.error('Error saving user:', err);
      showAlert('danger', 'فشل في حفظ المستخدم: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const showAlert = (type, message) => {
    if (isMounted.current) {
      setAlert({ show: true, type, message });

      // تنظيف أي مؤقت سابق
      if (alertTimeoutRef.current) {
        clearTimeout(alertTimeoutRef.current);
      }

      // إخفاء التنبيه بعد 3 ثوان
      alertTimeoutRef.current = setTimeout(() => {
        if (isMounted.current) {
          setAlert({ show: false, type: '', message: '' });
          alertTimeoutRef.current = null;
        }
      }, 3000);
    }
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="spinner"></div>
      </div>
    );
  }

  // التحقق من صلاحيات المستخدم - فقط المدير أو المدير العام يمكنه الوصول إلى صفحة المستخدمين
  const currentUserRole = localStorage.getItem('currentUserRole');
  console.log('Current user role in Users page:', currentUserRole);
  console.log('Current user from context:', currentUser);

  // تحديد ما إذا كان المستخدم لديه صلاحية الوصول
  const hasAccess = () => {
    // التحقق من المستخدم الحالي في السياق
    if (currentUser && (currentUser.role === 'admin' || currentUser.role === 'manager')) {
      return true;
    }

    // التحقق من دور المستخدم في التخزين المحلي
    if (currentUserRole === 'admin' || currentUserRole === 'manager') {
      return true;
    }

    // التحقق من المستخدم المخزن في التخزين المحلي
    try {
      const storedUserStr = localStorage.getItem('wms_user');
      if (storedUserStr) {
        const storedUser = JSON.parse(storedUserStr);
        if (storedUser && (storedUser.role === 'admin' || storedUser.role === 'manager')) {
          return true;
        }
      }
    } catch (error) {
      console.error('Error parsing stored user:', error);
    }

    // محاولة استخدام checkCurrentUserPermission من سياق المستخدمين إذا كان متاحًا
    if (appContext && appContext.checkCurrentUserPermission) {
      return appContext.checkCurrentUserPermission('manage_users');
    }

    // استخدام الوظيفة البديلة إذا لم يكن سياق التطبيق متاحًا
    return fallbackCheckPermission('manage_users');
  };

  // إذا لم يكن لدى المستخدم صلاحية الوصول، عرض رسالة خطأ
  if (!hasAccess()) {
    return (
      <div className="alert alert-danger">
        ليس لديك صلاحية للوصول إلى هذه الصفحة. هذه الصفحة متاحة فقط للمدراء.
      </div>
    );
  }

  return (
    <div>
      <h1 className="mb-4">إدارة المستخدمين</h1>

      {alert.show && (
        <div className={`alert alert-${alert.type}`}>
          {alert.message}
        </div>
      )}

      {error && (
        <div className="alert alert-danger">
          {error}
        </div>
      )}

      <div className="mb-4 flex justify-between items-center">
        <button className="btn btn-primary" onClick={handleAddUser}>
          <FaPlus className="ml-1" />
          إضافة مستخدم جديد
        </button>

        <div className="flex items-center">
          <div className="relative">
            <input
              type="text"
              className="form-control"
              placeholder="بحث..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <FaSearch className="absolute left-3 top-3 text-gray-400" />
          </div>
        </div>
      </div>

      <div className="table-container">
        <table className="table">
          <thead>
            <tr>
              <th>#</th>
              <th>اسم المستخدم</th>
              <th>الاسم الكامل</th>
              <th>الصلاحية</th>
              <th>تاريخ الإنشاء</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            {filteredUsers.length > 0 ? (
              filteredUsers.map((user, index) => (
                <tr key={user.id}>
                  <td>{index + 1}</td>
                  <td>{user.username}</td>
                  <td>{user.full_name || '-'}</td>
                  <td>
                    {user.role === 'admin' ? (
                      <span className="badge badge-primary">
                        <FaUserShield className="ml-1" />
                        مدير
                      </span>
                    ) : user.role === 'employee' ? (
                      <span className="badge badge-info">
                        <FaUserTie className="ml-1" />
                        موظف
                      </span>
                    ) : (
                      <span className="badge badge-secondary">
                        <FaUser className="ml-1" />
                        مشاهد
                      </span>
                    )}
                  </td>
                  <td>{(() => {
                    const date = new Date(user.created_at);
                    const day = date.getDate().toString().padStart(2, '0');
                    const month = (date.getMonth() + 1).toString().padStart(2, '0');
                    const year = date.getFullYear();
                    return `${day}/${month}/${year}`;
                  })()}</td>
                  <td>
                    <button
                      className="btn btn-sm btn-secondary ml-1"
                      onClick={() => handleEditUser(user)}
                    >
                      <FaEdit />
                    </button>
                    <button
                      className="btn btn-sm btn-danger"
                      onClick={() => handleDeleteUser(user.id)}
                      disabled={
                        user.id === (currentUser ? currentUser.id : localStorage.getItem('currentUserId')) ||
                        user.id.toString() === (currentUser ? currentUser.id : localStorage.getItem('currentUserId')).toString()
                      }
                    >
                      <FaTrash />
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="6" className="text-center">
                  {searchTerm ? 'لا توجد نتائج للبحث' : 'لا يوجد مستخدمين مسجلين'}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* نافذة إضافة/تعديل المستخدم */}
      {showModal && (
        <div className="modal-backdrop">
          <div className="modal-container">
            <div className="modal-header">
              <h3>
                {modalMode === 'add' ? 'إضافة مستخدم جديد' : 'تعديل المستخدم'}
              </h3>
              <button
                className="close-btn"
                onClick={() => setShowModal(false)}
              >
                &times;
              </button>
            </div>

            <div className="modal-body">
              <form ref={formRef} onSubmit={handleSubmit}>
                <div className="form-group">
                  <label className="form-label">اسم المستخدم *</label>
                  <input
                    type="text"
                    name="username"
                    className="form-control"
                    value={formData.username}
                    onChange={handleInputChange}
                    disabled={modalMode === 'edit'}
                    required
                  />
                </div>

                <div className="form-group">
                  <label className="form-label">
                    كلمة المرور {modalMode === 'edit' && '(اتركها فارغة إذا لم ترغب في تغييرها)'}
                  </label>
                  <input
                    type="password"
                    name="password"
                    className="form-control"
                    value={formData.password}
                    onChange={handleInputChange}
                    required={modalMode === 'add'}
                  />
                </div>

                <div className="form-group">
                  <label className="form-label">الاسم الكامل</label>
                  <input
                    type="text"
                    name="full_name"
                    className="form-control"
                    value={formData.full_name}
                    onChange={handleInputChange}
                  />
                </div>

                <div className="form-group">
                  <label className="form-label">الصلاحية</label>
                  <select
                    name="role"
                    className="form-control"
                    value={formData.role}
                    onChange={handleInputChange}
                  >
                    <option value="viewer">مشاهد</option>
                    <option value="employee">موظف</option>
                    <option value="manager">مدير</option>
                    <option value="admin">مسؤول النظام</option>
                  </select>
                </div>

                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn btn-secondary"
                    onClick={() => setShowModal(false)}
                  >
                    إلغاء
                  </button>
                  <button type="submit" className="btn btn-primary">
                    {modalMode === 'add' ? 'إضافة' : 'تحديث'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Users;
