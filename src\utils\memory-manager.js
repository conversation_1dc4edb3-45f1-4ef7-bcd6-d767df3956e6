/**
 * مدير الذاكرة
 * يوفر وظائف لتحسين استخدام الذاكرة وتنظيفها
 */

const logger = require('./logger');

// حجم الذاكرة المؤقتة الافتراضي
const DEFAULT_CACHE_SIZE = 100;

// الوقت الافتراضي لانتهاء صلاحية الذاكرة المؤقتة (بالمللي ثانية)
const DEFAULT_CACHE_EXPIRY = 5 * 60 * 1000; // 5 دقائق

/**
 * إنشاء ذاكرة مؤقتة مع إدارة الحجم وانتهاء الصلاحية
 * @param {Object} options - خيارات الذاكرة المؤقتة
 * @returns {Object} - كائن الذاكرة المؤقتة
 */
function createCache(options = {}) {
  const maxSize = options.maxSize || DEFAULT_CACHE_SIZE;
  const expiry = options.expiry || DEFAULT_CACHE_EXPIRY;
  
  // بيانات الذاكرة المؤقتة
  const cache = new Map();
  // سجل الوصول للعناصر (للخوارزمية LRU)
  const accessLog = [];
  
  // إحصائيات الذاكرة المؤقتة
  const stats = {
    hits: 0,
    misses: 0,
    evictions: 0,
    expirations: 0
  };
  
  /**
   * الحصول على عنصر من الذاكرة المؤقتة
   * @param {string} key - مفتاح العنصر
   * @returns {*} - قيمة العنصر أو undefined إذا لم يكن موجودًا
   */
  function get(key) {
    // التحقق من وجود العنصر
    if (!cache.has(key)) {
      stats.misses++;
      return undefined;
    }
    
    const item = cache.get(key);
    
    // التحقق من انتهاء الصلاحية
    if (item.expiry && item.expiry < Date.now()) {
      // إزالة العنصر منتهي الصلاحية
      cache.delete(key);
      removeFromAccessLog(key);
      stats.expirations++;
      stats.misses++;
      return undefined;
    }
    
    // تحديث سجل الوصول
    updateAccessLog(key);
    
    stats.hits++;
    return item.value;
  }
  
  /**
   * تخزين عنصر في الذاكرة المؤقتة
   * @param {string} key - مفتاح العنصر
   * @param {*} value - قيمة العنصر
   * @param {number} [customExpiry] - وقت انتهاء الصلاحية المخصص (بالمللي ثانية)
   */
  function set(key, value, customExpiry) {
    // التحقق من الحجم وإزالة العناصر القديمة إذا لزم الأمر
    if (cache.size >= maxSize && !cache.has(key)) {
      evictOldest();
    }
    
    // حساب وقت انتهاء الصلاحية
    const itemExpiry = customExpiry ? Date.now() + customExpiry : (expiry ? Date.now() + expiry : null);
    
    // تخزين العنصر
    cache.set(key, {
      value,
      expiry: itemExpiry,
      created: Date.now()
    });
    
    // تحديث سجل الوصول
    updateAccessLog(key);
  }
  
  /**
   * إزالة عنصر من الذاكرة المؤقتة
   * @param {string} key - مفتاح العنصر
   * @returns {boolean} - نجاح العملية
   */
  function remove(key) {
    const result = cache.delete(key);
    if (result) {
      removeFromAccessLog(key);
    }
    return result;
  }
  
  /**
   * مسح الذاكرة المؤقتة بالكامل
   */
  function clear() {
    cache.clear();
    accessLog.length = 0;
  }
  
  /**
   * إزالة العناصر منتهية الصلاحية
   * @returns {number} - عدد العناصر التي تمت إزالتها
   */
  function clearExpired() {
    let count = 0;
    const now = Date.now();
    
    for (const [key, item] of cache.entries()) {
      if (item.expiry && item.expiry < now) {
        cache.delete(key);
        removeFromAccessLog(key);
        count++;
        stats.expirations++;
      }
    }
    
    return count;
  }
  
  /**
   * تحديث سجل الوصول للعناصر
   * @param {string} key - مفتاح العنصر
   */
  function updateAccessLog(key) {
    // إزالة المفتاح من السجل إذا كان موجودًا
    removeFromAccessLog(key);
    // إضافة المفتاح إلى نهاية السجل (آخر استخدام)
    accessLog.push(key);
  }
  
  /**
   * إزالة مفتاح من سجل الوصول
   * @param {string} key - مفتاح العنصر
   */
  function removeFromAccessLog(key) {
    const index = accessLog.indexOf(key);
    if (index !== -1) {
      accessLog.splice(index, 1);
    }
  }
  
  /**
   * إزالة العنصر الأقدم استخدامًا
   */
  function evictOldest() {
    if (accessLog.length > 0) {
      const oldestKey = accessLog.shift();
      cache.delete(oldestKey);
      stats.evictions++;
    }
  }
  
  /**
   * الحصول على إحصائيات الذاكرة المؤقتة
   * @returns {Object} - إحصائيات الذاكرة المؤقتة
   */
  function getStats() {
    return {
      ...stats,
      size: cache.size,
      maxSize,
      hitRate: stats.hits + stats.misses > 0 ? stats.hits / (stats.hits + stats.misses) : 0
    };
  }
  
  // تشغيل مهمة دورية لتنظيف العناصر منتهية الصلاحية
  const cleanupInterval = setInterval(() => {
    const removed = clearExpired();
    if (removed > 0) {
      logger.debug(`تم تنظيف ${removed} عنصر منتهي الصلاحية من الذاكرة المؤقتة`);
    }
  }, Math.min(expiry / 2, 60000)); // نصف وقت انتهاء الصلاحية أو دقيقة واحدة كحد أقصى
  
  // وظيفة لإيقاف المهمة الدورية
  function dispose() {
    clearInterval(cleanupInterval);
  }
  
  return {
    get,
    set,
    remove,
    clear,
    clearExpired,
    getStats,
    dispose
  };
}

/**
 * تنظيف الذاكرة وتحسين الأداء
 */
function optimizeMemory() {
  try {
    logger.info('بدء تحسين استخدام الذاكرة...');
    
    // تشغيل جامع القمامة يدويًا
    if (global.gc) {
      logger.info('تشغيل جامع القمامة يدويًا...');
      global.gc();
    } else {
      logger.warn('جامع القمامة اليدوي غير متاح. قم بتشغيل Node.js مع خيار --expose-gc');
    }
    
    // تسجيل استخدام الذاكرة الحالي
    const memoryUsage = process.memoryUsage();
    logger.info('استخدام الذاكرة الحالي:', {
      rss: formatMemorySize(memoryUsage.rss),
      heapTotal: formatMemorySize(memoryUsage.heapTotal),
      heapUsed: formatMemorySize(memoryUsage.heapUsed),
      external: formatMemorySize(memoryUsage.external)
    });
    
    return true;
  } catch (error) {
    logger.error('خطأ أثناء تحسين الذاكرة:', error);
    return false;
  }
}

/**
 * تنسيق حجم الذاكرة بشكل مقروء
 * @param {number} bytes - حجم الذاكرة بالبايت
 * @returns {string} - حجم الذاكرة المنسق
 */
function formatMemorySize(bytes) {
  if (bytes < 1024) {
    return bytes + ' bytes';
  } else if (bytes < 1024 * 1024) {
    return (bytes / 1024).toFixed(2) + ' KB';
  } else if (bytes < 1024 * 1024 * 1024) {
    return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
  } else {
    return (bytes / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
  }
}

module.exports = {
  createCache,
  optimizeMemory,
  formatMemorySize
};
