import React, { createContext, useState, useEffect, useContext } from 'react';
import * as database from '../../utils/database';
import { SettingsContext } from './SettingsProvider';

// إنشاء سياق المستخدمين
export const UsersContext = createContext();

/**
 * مزود سياق المستخدمين
 *
 * يوفر هذا المكون وظائف إدارة المستخدمين
 *
 * @param {Object} props - خصائص المكون
 * @param {React.ReactNode} props.children - المكونات الفرعية
 * @returns {React.ReactElement} مزود سياق المستخدمين
 */
export const UsersProvider = ({ children }) => {
  // حالة المستخدمين
  const [users, setUsers] = useState([]);

  // استخدام سياق الإعدادات
  const { settings } = useContext(SettingsContext);

  // تحميل المستخدمين عند بدء التطبيق
  useEffect(() => {
    const loadUsers = async () => {
      try {
        const usersData = await database.getUsers();
        setUsers(usersData);
        console.log('تم تحميل المستخدمين بنجاح:', usersData.length);
      } catch (error) {
        console.error('خطأ في تحميل المستخدمين:', error);
        // تحميل بيانات افتراضية للمستخدمين (مدير النظام فقط)
        const defaultUsers = [
          {
            id: 1,
            username: 'admin',
            full_name: 'مدير النظام',
            role: 'admin',
            created_at: new Date().toISOString()
          }
        ];
        setUsers(defaultUsers);
      }
    };

    loadUsers();
  }, []);

  // إضافة مستخدم جديد
  const addUser = async (newUser) => {
    try {
      console.log('بدء إضافة مستخدم جديد:', newUser);

      // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
      const existingUser = users.find(user => user.username === newUser.username);
      if (existingUser) {
        console.error('اسم المستخدم موجود بالفعل:', existingUser);
        throw new Error('اسم المستخدم موجود بالفعل');
      }

      // التحقق من صحة الدور
      if (!['admin', 'employee', 'viewer'].includes(newUser.role)) {
        console.error('دور المستخدم غير صالح:', newUser.role);
        throw new Error('دور المستخدم غير صالح. الأدوار المتاحة: مدير (admin)، موظف (employee)، مشاهد (viewer)');
      }

      // التحقق من وجود كلمة مرور
      if (!newUser.password) {
        console.error('كلمة المرور مطلوبة');
        throw new Error('كلمة المرور مطلوبة');
      }

      // إضافة المستخدم باستخدام قاعدة البيانات
      console.log('جاري إضافة المستخدم إلى قاعدة البيانات...');
      const userData = {
        username: newUser.username,
        password: newUser.password, // سيتم تشفيرها في الخلفية
        full_name: newUser.full_name || '',
        role: newUser.role,
        created_at: new Date().toISOString()
      };

      // استدعاء دالة إضافة المستخدم في قاعدة البيانات
      const addedUser = await database.addUser(userData);
      console.log('تمت إضافة المستخدم بنجاح:', addedUser);

      // إضافة حقل id للتوافق مع الواجهة الأمامية
      const userWithId = {
        ...addedUser,
        id: addedUser._id || addedUser.id
      };

      // تحديث حالة المستخدمين
      setUsers(prevUsers => [...prevUsers, userWithId]);
      console.log('تم تحديث حالة المستخدمين');

      return userWithId;
    } catch (error) {
      console.error('خطأ في إضافة المستخدم:', error);
      throw error;
    }
  };

  // تحديث مستخدم موجود
  const updateUser = async (updatedUser) => {
    try {
      console.log('بدء تحديث المستخدم:', updatedUser);

      // التحقق من عدم وجود مستخدم آخر بنفس اسم المستخدم
      const existingUser = users.find(user =>
        user.username === updatedUser.username &&
        user.id !== updatedUser.id &&
        user._id !== updatedUser._id
      );

      if (existingUser) {
        console.error('اسم المستخدم موجود بالفعل:', existingUser);
        throw new Error('اسم المستخدم موجود بالفعل');
      }

      // التحقق من صحة الدور
      if (!['admin', 'employee', 'viewer'].includes(updatedUser.role)) {
        console.error('دور المستخدم غير صالح:', updatedUser.role);
        throw new Error('دور المستخدم غير صالح. الأدوار المتاحة: مدير (admin)، موظف (employee)، مشاهد (viewer)');
      }

      // إعداد بيانات المستخدم للتحديث
      const userData = {
        _id: updatedUser._id || updatedUser.id,
        username: updatedUser.username,
        full_name: updatedUser.full_name || '',
        role: updatedUser.role
      };

      // إضافة كلمة المرور إذا تم توفيرها
      if (updatedUser.password) {
        userData.password = updatedUser.password;
      }

      // استدعاء دالة تحديث المستخدم في قاعدة البيانات
      console.log('جاري تحديث المستخدم في قاعدة البيانات...');
      const updatedUserFromDB = await database.updateUser(userData);
      console.log('تم تحديث المستخدم بنجاح:', updatedUserFromDB);

      // إضافة حقل id للتوافق مع الواجهة الأمامية
      const userWithId = {
        ...updatedUserFromDB,
        id: updatedUserFromDB._id || updatedUserFromDB.id
      };

      // تحديث حالة المستخدمين
      setUsers(prevUsers =>
        prevUsers.map(user =>
          (user.id === userWithId.id || user._id === userWithId._id) ? userWithId : user
        )
      );
      console.log('تم تحديث حالة المستخدمين');

      return userWithId;
    } catch (error) {
      console.error('خطأ في تحديث المستخدم:', error);
      throw error;
    }
  };

  // حذف مستخدم
  const deleteUser = async (userId) => {
    try {
      console.log('بدء حذف المستخدم بالمعرف:', userId);

      // التحقق من عدم حذف المستخدم الوحيد بدور مدير
      const adminUsers = users.filter(user => user.role === 'admin');
      const userToDelete = users.find(user => user.id === userId || user._id === userId);

      if (!userToDelete) {
        console.error('المستخدم غير موجود:', userId);
        throw new Error('المستخدم غير موجود');
      }

      if (adminUsers.length === 1 && (adminUsers[0].id === userId || adminUsers[0]._id === userId)) {
        console.error('محاولة حذف المستخدم المدير الوحيد');
        throw new Error('لا يمكن حذف المستخدم المدير الوحيد');
      }

      // استدعاء دالة حذف المستخدم في قاعدة البيانات
      console.log('جاري حذف المستخدم من قاعدة البيانات...');
      const result = await database.deleteUser(userToDelete._id || userToDelete.id);
      console.log('تم حذف المستخدم بنجاح:', result);

      // تحديث حالة المستخدمين
      setUsers(prevUsers => prevUsers.filter(user =>
        user.id !== userId && user._id !== userId
      ));
      console.log('تم تحديث حالة المستخدمين');

      return true;
    } catch (error) {
      console.error('خطأ في حذف المستخدم:', error);
      throw error;
    }
  };

  // التحقق من صلاحيات المستخدم
  const checkUserPermission = (userId, action) => {
    // البحث عن المستخدم في قائمة المستخدمين
    const user = users.find(user => user.id === userId || user._id === userId);
    if (!user) {
      console.warn('المستخدم غير موجود للتحقق من الصلاحيات:', userId);
      return false;
    }

    console.log(`التحقق من صلاحية "${action}" للمستخدم ${user.username} (${user.role})`);

    // المدير لديه جميع الصلاحيات دائمًا
    if (user.role === 'admin' || user.role === 'manager') {
      console.log('المستخدم مدير ولديه جميع الصلاحيات');
      return true;
    }

    // تعريف الصلاحيات حسب دور المستخدم
    const permissions = {
      admin: [
        // إدارة المستخدمين
        'manage_users', 'add_user', 'update_user', 'delete_user',
        // إدارة الأصناف
        'add_item', 'update_item', 'update_item_with_sales', 'delete_item', 'delete_item_with_sales',
        // إدارة المعاملات
        'add_purchase', 'add_sale', 'delete_transaction',
        // إدارة العملاء
        'add_customer', 'update_customer', 'delete_customer',
        // إدارة الآلات
        'manage_machines', 'add_machine', 'update_machine', 'delete_machine',
        // إدارة النظام
        'manage_settings', 'backup', 'restore',
        // عرض التقارير
        'view_reports', 'view_inventory', 'view_items', 'view_customers', 'view_transactions',
        // الزكاة
        'manage_zakat', 'calculate_zakat',
        // الطباعة
        'print_reports', 'print_invoices',
        // إدارة الخزينة
        'manage_cashbox', 'add_cashbox_transaction'
      ],
      manager: [
        // إدارة المستخدمين
        'manage_users', 'add_user', 'update_user', 'delete_user',
        // إدارة الأصناف
        'add_item', 'update_item', 'update_item_with_sales',
        // إدارة المعاملات
        'add_purchase', 'add_sale', 'delete_transaction',
        // إدارة العملاء
        'add_customer', 'update_customer',
        // إدارة الآلات
        'manage_machines', 'add_machine', 'update_machine', 'delete_machine',
        // إدارة النظام
        'manage_settings', 'backup', 'restore',
        // عرض التقارير
        'view_reports', 'view_inventory', 'view_items', 'view_customers', 'view_transactions',
        // الزكاة
        'manage_zakat', 'calculate_zakat',
        // الطباعة
        'print_reports', 'print_invoices',
        // إدارة الخزينة
        'manage_cashbox', 'add_cashbox_transaction'
      ],
      employee: [
        // إدارة الأصناف (محدودة)
        'add_item', 'update_item', 'update_item_with_sales',
        // إدارة المعاملات
        'add_purchase', 'add_sale', 'delete_transaction',
        // إدارة العملاء
        'add_customer', 'update_customer',
        // إدارة الآلات
        'manage_machines', 'add_machine', 'update_machine', 'delete_machine',
        // عرض التقارير
        'view_reports', 'view_inventory', 'view_items', 'view_customers', 'view_transactions',
        // الزكاة
        'manage_zakat', 'calculate_zakat',
        // الطباعة
        'print_reports', 'print_invoices',
        // إدارة النظام (محدودة)
        'backup', 'restore',
        // إدارة الخزينة (محدودة)
        'view_cashbox', 'add_cashbox_transaction'
      ],
      viewer: [
        // عرض التقارير فقط
        'view_reports', 'view_inventory', 'view_items', 'view_customers', 'view_transactions',
        'view_cashbox', 'print_reports'
      ]
    };

    // التحقق من وجود الصلاحية في قائمة صلاحيات دور المستخدم
    if (permissions[user.role] && permissions[user.role].includes(action)) {
      console.log(`المستخدم لديه صلاحية "${action}"`);
      return true;
    }

    console.log(`المستخدم ليس لديه صلاحية "${action}"`);
    return false;
  };

  // التحقق من صلاحيات المستخدم الحالي
  const checkCurrentUserPermission = (action) => {
    // الحصول على معرف المستخدم الحالي من التخزين المحلي
    const currentUserId = localStorage.getItem('currentUserId');
    const currentUserRole = localStorage.getItem('currentUserRole');
    const currentUserName = localStorage.getItem('currentUserName');

    if (!currentUserId) {
      console.warn('لا يوجد مستخدم حالي');
      return false;
    }

    console.log(`التحقق من صلاحية "${action}" للمستخدم الحالي:`, {
      id: currentUserId,
      username: currentUserName,
      role: currentUserRole
    });

    // تحسين الأداء: إذا كان المستخدم الحالي مديرًا، فلديه جميع الصلاحيات
    if (currentUserRole === 'admin' || currentUserRole === 'manager') {
      console.log('المستخدم الحالي مدير ولديه جميع الصلاحيات');
      return true;
    }

    // محاولة تحويل المعرف إلى رقم إذا كان ممكنًا
    try {
      // التحقق مما إذا كان المعرف رقمًا أو نصًا
      if (!isNaN(currentUserId) && !currentUserId.includes('-')) {
        return checkUserPermission(parseInt(currentUserId), action);
      } else {
        // إذا كان المعرف نصًا (مثل معرف MongoDB)، استخدمه كما هو
        return checkUserPermission(currentUserId, action);
      }
    } catch (error) {
      console.error('خطأ في التحقق من صلاحيات المستخدم الحالي:', error);
      return false;
    }
  };

  // القيمة التي سيتم توفيرها للمكونات
  const value = {
    users,
    addUser,
    updateUser,
    deleteUser,
    checkUserPermission,
    checkCurrentUserPermission
  };

  return (
    <UsersContext.Provider value={value}>
      {children}
    </UsersContext.Provider>
  );
};

export default UsersProvider;
