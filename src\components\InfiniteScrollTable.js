import React, { useState, useEffect, useRef, useCallback } from 'react';
import PropTypes from 'prop-types';
import './InfiniteScrollTable.css';

/**
 * مكون جدول مع تمرير لانهائي
 * يقوم بتحميل البيانات تدريجياً عند التمرير لأسفل
 */
const InfiniteScrollTable = ({
  columns,
  data,
  fetchMore,
  hasMore,
  loading,
  loadingMessage = 'جاري التحميل...',
  emptyMessage = 'لا توجد بيانات',
  rowsPerPage = 20,
  onRowClick,
  keyField = 'id',
  className = '',
  tableClassName = '',
  loadMoreThreshold = 200
}) => {
  // Variable no utilizada - considere eliminarla o usarla
  const [displayedData, setDisplayedData] = useState([]);
  // Variable no utilizada - considere eliminarla o usarla
  const [page, setPage] = useState(1);
  // Variable no utilizada - considere eliminarla o usarla
  const [localLoading, setLocalLoading] = useState(false);
  const containerRef = useRef(null);

  // تحميل البيانات الأولية
  useEffect(() => {
    if (data && data.length > 0) {
      setDisplayedData(data.slice(0, rowsPerPage));
    } else {
      setDisplayedData([]);
    }
    setPage(1);
  }, [data, rowsPerPage]);

  // وظيفة التحميل عند التمرير
  const loadMore = useCallback(async () => {
    if (localLoading || !hasMore) return;

    setLocalLoading(true);
    try {
      const nextPage = page + 1;
      
      // إذا كانت البيانات متوفرة محلياً
      if (data && data.length > displayedData.length) {
        const newData = data.slice(0, nextPage * rowsPerPage);
        setDisplayedData(newData);
        setPage(nextPage);
      } 
      // إذا كان هناك حاجة لتحميل المزيد من البيانات من الخادم
      else if (fetchMore) {
        await fetchMore(nextPage, rowsPerPage);
        setPage(nextPage);
      }
    } catch (error) {
      console.error('خطأ في تحميل المزيد من البيانات:', error);
    } finally {
      setLocalLoading(false);
    }
  }, [localLoading, hasMore, page, data, displayedData.length, rowsPerPage, fetchMore]);

  // مراقبة التمرير
  const handleScroll = useCallback(() => {
    if (!containerRef.current) return;
    
    const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
    const scrollBottom = scrollHeight - scrollTop - clientHeight;
    
    if (scrollBottom < loadMoreThreshold && !localLoading && hasMore) {
      loadMore();
    }
  }, [loadMore, localLoading, hasMore, loadMoreThreshold]);

  // إضافة مستمع التمرير
  useEffect(() => {
    const currentContainer = containerRef.current;
    if (currentContainer) {
      currentContainer.addEventListener('scroll', handleScroll);
      return () => {
        currentContainer.removeEventListener('scroll', handleScroll);
      };
    }
  }, [handleScroll]);

  return (
    <div 
      className={`infinite-scroll-container ${className}`} 
      ref={containerRef}
    >
      <table className={`infinite-scroll-table ${tableClassName}`}>
        <thead>
          <tr>
            {columns.map((column) => (
              <th 
                key={column.key} 
                className={column.className || ''}
                style={column.style || {}}
              >
                {column.title}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {displayedData.length > 0 ? (
            displayedData.map((row) => (
              <tr 
                key={row[keyField]} 
                onClick={() => onRowClick && onRowClick(row)}
                className={onRowClick ? 'clickable' : ''}
              >
                {columns.map((column) => (
                  <td 
                    key={`${row[keyField]}-${column.key}`}
                    className={column.className || ''}
                    style={column.style || {}}
                  >
                    {column.render ? column.render(row) : row[column.key]}
                  </td>
                ))}
              </tr>
            ))
          ) : !loading ? (
            <tr>
              <td colSpan={columns.length} className="empty-message">
                {emptyMessage}
              </td>
            </tr>
          ) : null}
        </tbody>
      </table>
      
      {(loading || localLoading) && (
        <div className="loading-indicator">
          <div className="spinner"></div>
          <p>{loadingMessage}</p>
        </div>
      )}
      
      {!hasMore && displayedData.length > 0 && (
        <div className="end-message">
          تم عرض جميع النتائج
        </div>
      )}
    </div>
  );
};

InfiniteScrollTable.propTypes = {
  columns: PropTypes.arrayOf(
    PropTypes.shape({
      key: PropTypes.string.isRequired,
      title: PropTypes.node.isRequired,
      render: PropTypes.func,
      className: PropTypes.string,
      style: PropTypes.object
    })
  ).isRequired,
  data: PropTypes.array.isRequired,
  fetchMore: PropTypes.func,
  hasMore: PropTypes.bool,
  loading: PropTypes.bool,
  loadingMessage: PropTypes.string,
  emptyMessage: PropTypes.string,
  rowsPerPage: PropTypes.number,
  onRowClick: PropTypes.func,
  keyField: PropTypes.string,
  className: PropTypes.string,
  tableClassName: PropTypes.string,
  loadMoreThreshold: PropTypes.number
};

export default InfiniteScrollTable;
