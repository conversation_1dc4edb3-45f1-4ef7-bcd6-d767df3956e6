/**
 * اختبار تأثير عملية الشراء على الأرباح
 * هذا الاختبار يحاكي عملية شراء ويتحقق من أن الأرباح لا تتأثر
 */

const Database = require('better-sqlite3');
const path = require('path');

async function testPurchaseImpact() {
  let db;
  
  try {
    console.log('🧪 اختبار تأثير عملية الشراء على الأرباح...\n');

    // الاتصال بقاعدة البيانات الحقيقية
    const dbPath = 'C:\\Users\\<USER>\\AppData\\Roaming\\warehouse-management-system\\wms-database\\warehouse.db';
    db = new Database(dbPath);
    
    console.log('✅ تم الاتصال بقاعدة البيانات الحقيقية');

    // 1. الحصول على حالة الخزينة قبل الاختبار
    console.log('\n📊 حالة الخزينة قبل الاختبار:');
    const initialCashbox = db.prepare('SELECT * FROM cashbox WHERE id = 1').get();
    if (initialCashbox) {
      console.log(`   الرصيد الحالي: ${initialCashbox.current_balance} د.ل`);
      console.log(`   إجمالي الأرباح: ${initialCashbox.profit_total} د.ل`);
      console.log(`   إجمالي المبيعات: ${initialCashbox.sales_total} د.ل`);
      console.log(`   إجمالي المشتريات: ${initialCashbox.purchases_total} د.ل`);
      console.log(`   إجمالي مصاريف النقل: ${initialCashbox.transport_total || 0} د.ل`);
    } else {
      console.log('   ⚠️ لا توجد خزينة في قاعدة البيانات');
      return;
    }

    // حفظ القيم الأصلية
    const originalValues = {
      current_balance: initialCashbox.current_balance,
      profit_total: initialCashbox.profit_total,
      sales_total: initialCashbox.sales_total,
      purchases_total: initialCashbox.purchases_total,
      transport_total: initialCashbox.transport_total || 0
    };

    // 2. محاكاة عملية شراء مع مصاريف نقل
    console.log('\n🛒 محاكاة عملية شراء مع مصاريف نقل...');
    
    const testPurchase = {
      total_price: 500,      // مبلغ الشراء
      transport_cost: 25     // مصاريف النقل
    };

    console.log(`   مبلغ الشراء: ${testPurchase.total_price} د.ل`);
    console.log(`   مصاريف النقل: ${testPurchase.transport_cost} د.ل`);
    console.log(`   إجمالي المبلغ المخصوم: ${testPurchase.total_price + testPurchase.transport_cost} د.ل`);

    // 3. تطبيق التحديث على الخزينة (محاكاة ما يحدث في unified-transaction-manager)
    const updateStmt = db.prepare(`
      UPDATE cashbox 
      SET current_balance = current_balance - ?,
          purchases_total = purchases_total + ?,
          transport_total = COALESCE(transport_total, 0) + ?,
          updated_at = ?
      WHERE id = 1
    `);

    updateStmt.run(
      testPurchase.total_price + testPurchase.transport_cost, // خصم المبلغ الإجمالي من الرصيد
      testPurchase.total_price,                               // إضافة مبلغ الشراء فقط
      testPurchase.transport_cost,                            // إضافة مصاريف النقل
      new Date().toISOString()
    );

    console.log('   ✅ تم تطبيق التحديث على الخزينة');

    // 4. فحص النتيجة
    console.log('\n📊 حالة الخزينة بعد عملية الشراء:');
    const updatedCashbox = db.prepare('SELECT * FROM cashbox WHERE id = 1').get();
    
    if (updatedCashbox) {
      console.log(`   الرصيد الحالي: ${updatedCashbox.current_balance} د.ل`);
      console.log(`   إجمالي الأرباح: ${updatedCashbox.profit_total} د.ل`);
      console.log(`   إجمالي المبيعات: ${updatedCashbox.sales_total} د.ل`);
      console.log(`   إجمالي المشتريات: ${updatedCashbox.purchases_total} د.ل`);
      console.log(`   إجمالي مصاريف النقل: ${updatedCashbox.transport_total || 0} د.ل`);

      // 5. التحقق من التغييرات
      console.log('\n🔍 تحليل التغييرات:');
      
      const balanceChange = updatedCashbox.current_balance - originalValues.current_balance;
      const profitChange = updatedCashbox.profit_total - originalValues.profit_total;
      const salesChange = updatedCashbox.sales_total - originalValues.sales_total;
      const purchasesChange = updatedCashbox.purchases_total - originalValues.purchases_total;
      const transportChange = (updatedCashbox.transport_total || 0) - originalValues.transport_total;

      console.log(`   تغيير الرصيد الحالي: ${balanceChange} د.ل (متوقع: ${-(testPurchase.total_price + testPurchase.transport_cost)})`);
      console.log(`   تغيير الأرباح: ${profitChange} د.ل (متوقع: 0)`);
      console.log(`   تغيير المبيعات: ${salesChange} د.ل (متوقع: 0)`);
      console.log(`   تغيير المشتريات: ${purchasesChange} د.ل (متوقع: ${testPurchase.total_price})`);
      console.log(`   تغيير مصاريف النقل: ${transportChange} د.ل (متوقع: ${testPurchase.transport_cost})`);

      // 6. تقييم النتائج
      console.log('\n✅ تقييم النتائج:');
      
      let allTestsPassed = true;

      // فحص الرصيد الحالي
      if (Math.abs(balanceChange - (-(testPurchase.total_price + testPurchase.transport_cost))) < 0.01) {
        console.log('   ✅ الرصيد الحالي: تم خصم المبلغ الصحيح');
      } else {
        console.log('   ❌ الرصيد الحالي: خطأ في المبلغ المخصوم');
        allTestsPassed = false;
      }

      // فحص الأرباح (الأهم!)
      if (Math.abs(profitChange) < 0.01) {
        console.log('   ✅ الأرباح: لم تتأثر بعملية الشراء (هذا صحيح!)');
      } else {
        console.log('   ❌ الأرباح: تأثرت بعملية الشراء (هذا خطأ!)');
        allTestsPassed = false;
      }

      // فحص المبيعات
      if (Math.abs(salesChange) < 0.01) {
        console.log('   ✅ المبيعات: لم تتأثر بعملية الشراء');
      } else {
        console.log('   ❌ المبيعات: تأثرت بعملية الشراء');
        allTestsPassed = false;
      }

      // فحص المشتريات
      if (Math.abs(purchasesChange - testPurchase.total_price) < 0.01) {
        console.log('   ✅ المشتريات: تم إضافة المبلغ الصحيح');
      } else {
        console.log('   ❌ المشتريات: خطأ في المبلغ المضاف');
        allTestsPassed = false;
      }

      // فحص مصاريف النقل
      if (Math.abs(transportChange - testPurchase.transport_cost) < 0.01) {
        console.log('   ✅ مصاريف النقل: تم إضافة المبلغ الصحيح');
      } else {
        console.log('   ❌ مصاريف النقل: خطأ في المبلغ المضاف');
        allTestsPassed = false;
      }

      // النتيجة النهائية
      if (allTestsPassed) {
        console.log('\n🎉 ممتاز! جميع الاختبارات نجحت. الإصلاح يعمل بشكل صحيح!');
        console.log('   ✅ عمليات الشراء لا تؤثر على الأرباح');
        console.log('   ✅ مصاريف النقل تخصم من الرصيد وتسجل منفصلة');
        console.log('   ✅ إجمالي المشتريات يتم تحديثه بشكل صحيح');
      } else {
        console.log('\n❌ هناك مشاكل في النظام تحتاج إلى إصلاح');
      }
    }

    // 7. إعادة القيم الأصلية
    console.log('\n🔄 إعادة القيم الأصلية...');
    
    const restoreStmt = db.prepare(`
      UPDATE cashbox 
      SET current_balance = ?,
          profit_total = ?,
          sales_total = ?,
          purchases_total = ?,
          transport_total = ?,
          updated_at = ?
      WHERE id = 1
    `);

    restoreStmt.run(
      originalValues.current_balance,
      originalValues.profit_total,
      originalValues.sales_total,
      originalValues.purchases_total,
      originalValues.transport_total,
      new Date().toISOString()
    );

    console.log('   ✅ تم إعادة جميع القيم إلى حالتها الأصلية');

    // التحقق من الإعادة
    const restoredCashbox = db.prepare('SELECT * FROM cashbox WHERE id = 1').get();
    if (restoredCashbox && 
        Math.abs(restoredCashbox.current_balance - originalValues.current_balance) < 0.01 &&
        Math.abs(restoredCashbox.profit_total - originalValues.profit_total) < 0.01) {
      console.log('   ✅ تم التحقق من إعادة القيم بنجاح');
    } else {
      console.log('   ⚠️ قد تكون هناك مشكلة في إعادة القيم');
    }

  } catch (error) {
    console.error('❌ فشل في اختبار تأثير الشراء:', error.message);
  } finally {
    if (db) {
      db.close();
      console.log('\n🔒 تم إغلاق اتصال قاعدة البيانات');
    }
  }
}

// تشغيل الاختبار إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  testPurchaseImpact();
}

module.exports = { testPurchaseImpact };
