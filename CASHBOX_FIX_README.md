# إصلاح نظام الخزينة - دليل المستخدم

## المشكلة
توقفت الخزينة عن تحديث خانة المشتريات (لا يتم تسجيل عمليات الشراء في رصيد الخزينة)

## الحل السريع ⚡

### تشغيل الإصلاح التلقائي
```bash
node quick-cashbox-fix.js
```

هذا الأمر سيقوم بـ:
- ✅ تشخيص المشكلة
- ✅ إصلاح هيكل قاعدة البيانات
- ✅ اختبار النظام
- ✅ التحقق من عمل الإصلاحات

## الحل المفصل 🔧

### 1. التشخيص
```bash
node diagnose-cashbox-issue.js
```
يفحص النظام ويحدد المشاكل بدقة

### 2. إصلاح هيكل قاعدة البيانات
```bash
node fix-cashbox-transport-column.js
```
يضيف الأعمدة المفقودة ويصلح هيكل الجدول

### 3. اختبار النظام
```bash
node test-cashbox-system.js
```
يختبر جميع وظائف الخزينة

## الملفات المصلحة

### unified-transaction-manager.js
- **المشكلة**: متغير `updateResult` غير معرف في السطر 1321
- **الإصلاح**: إضافة `const updateResult =` قبل استدعاء `updateStmt.run()`

## التحقق من نجاح الإصلاح

### 1. تشغيل التطبيق
```bash
npm start
```

### 2. اختبار عملية شراء
1. اذهب إلى قسم "المشتريات"
2. اختر صنف واضغط "شراء"
3. أدخل البيانات المطلوبة
4. اضغط "تسجيل عملية الشراء"

### 3. التحقق من الخزينة
1. اذهب إلى قسم "الخزينة"
2. تأكد من تحديث:
   - ✅ الرصيد الحالي (نقص)
   - ✅ إجمالي المشتريات (زيادة)
   - ✅ مصاريف النقل (إذا كانت موجودة)

### 4. فحص سجل المعاملات
- تأكد من ظهور معاملة الشراء في سجل الخزينة
- تحقق من صحة المبالغ والتواريخ

## علامات نجاح الإصلاح ✅

### في واجهة المستخدم:
- تحديث فوري لبطاقات الخزينة
- ظهور معاملات الشراء في السجل
- عدم ظهور رسائل خطأ

### في سجلات النظام:
```
[CASHBOX-FIX] تحديث الخزينة في حالة الشراء
[CASHBOX-FIX] نتيجة تحديث الخزينة: {"changes":1}
تم تحديث الخزينة بعد عملية الشراء
```

## استكشاف الأخطاء 🔍

### إذا لم يعمل الإصلاح:

#### 1. تحقق من رسائل الخطأ
```bash
# فحص سجلات النظام
tail -f logs/app.log
```

#### 2. تحقق من قاعدة البيانات
```sql
-- فحص هيكل جدول الخزينة
PRAGMA table_info(cashbox);

-- فحص البيانات
SELECT * FROM cashbox;
```

#### 3. إعادة تشغيل الإصلاح
```bash
# إعادة تشغيل الإصلاح الكامل
node quick-cashbox-fix.js
```

### مشاكل شائعة وحلولها:

#### ❌ "قاعدة البيانات غير متصلة"
**الحل**: تأكد من تشغيل التطبيق أولاً

#### ❌ "عمود transport_total غير موجود"
**الحل**: 
```bash
node fix-cashbox-transport-column.js
```

#### ❌ "لا توجد بيانات خزينة"
**الحل**: إنشاء خزينة جديدة من واجهة التطبيق

#### ❌ "فشل في تحديث الخزينة"
**الحل**: 
1. تحقق من صلاحيات قاعدة البيانات
2. أعد تشغيل التطبيق
3. جرب الإصلاح مرة أخرى

## النسخ الاحتياطي 💾

### قبل تطبيق الإصلاح:
```bash
# إنشاء نسخة احتياطية من قاعدة البيانات
cp "C:\Users\<USER>\AppData\Roaming\warehouse-management-system\wms-database\warehouse.db" "warehouse_backup_$(date +%Y%m%d_%H%M%S).db"
```

### استعادة النسخة الاحتياطية:
```bash
# في حالة الحاجة للتراجع
cp warehouse_backup_YYYYMMDD_HHMMSS.db "C:\Users\<USER>\AppData\Roaming\warehouse-management-system\wms-database\warehouse.db"
```

## الدعم التقني 📞

### معلومات مفيدة للدعم:
- **إصدار النظام**: نسخة التطوير
- **قاعدة البيانات**: SQLite
- **مسار قاعدة البيانات**: `C:\Users\<USER>\AppData\Roaming\warehouse-management-system\wms-database`
- **الملفات المصلحة**: `unified-transaction-manager.js`

### عند طلب الدعم، أرفق:
1. تقرير التشخيص: `diagnosis-report-*.json`
2. تقرير الإصلاح: `quick-fix-report-*.json`
3. سجلات النظام الأخيرة
4. لقطة شاشة من رسالة الخطأ (إن وجدت)

## الملاحظات المهمة ⚠️

1. **لا تحذف** ملفات الإصلاح بعد نجاح العملية
2. **احتفظ** بالنسخ الاحتياطية لمدة أسبوع على الأقل
3. **راقب** النظام لمدة يومين بعد الإصلاح
4. **أبلغ** عن أي مشاكل جديدة فوراً

## التحديثات المستقبلية 🔄

هذا الإصلاح يحل المشكلة الحالية، لكن للوقاية من مشاكل مستقبلية:

1. **تحديث النظام** بانتظام
2. **إجراء نسخ احتياطية** دورية
3. **مراقبة سجلات النظام** أسبوعياً
4. **اختبار الوظائف** بعد كل تحديث

---

**تاريخ الإصلاح**: ديسمبر 2024  
**الإصدار**: 1.0  
**المطور**: نظام إدارة المخزون
