/**
 * إصلاح مشاكل قسم تقارير فواتير العملاء
 * يعالج المشاكل المكتشفة في الفحص الشامل
 */

const { logError, logSystem } = require('./error-handler');
const DatabaseManager = require('./database-singleton');

/**
 * فحص وإصلاح تكرار معالجات IPC
 */
function fixDuplicateIPCHandlers() {
  console.log('🔧 فحص وإصلاح تكرار معالجات IPC...');
  
  try {
    const { ipcMain } = require('electron');
    
    // فحص المعالجات المسجلة
    const registeredHandlers = ipcMain.eventNames();
    console.log('المعالجات المسجلة:', registeredHandlers);
    
    // فحص تكرار get-customer-invoices
    const customerInvoicesHandlers = registeredHandlers.filter(name => 
      name === 'get-customer-invoices'
    );
    
    if (customerInvoicesHandlers.length > 1) {
      console.log(`⚠️ تم العثور على ${customerInvoicesHandlers.length} معالج لـ get-customer-invoices`);
      
      // إزالة جميع المعالجات المكررة
      ipcMain.removeAllListeners('get-customer-invoices');
      console.log('✅ تم إزالة المعالجات المكررة');
      
      // إعادة تسجيل معالج واحد فقط
      registerSingleCustomerInvoicesHandler();
      
      return {
        success: true,
        duplicatesFound: customerInvoicesHandlers.length,
        message: 'تم إصلاح تكرار المعالجات'
      };
    } else {
      console.log('✅ لا توجد معالجات مكررة');
      return {
        success: true,
        duplicatesFound: 0,
        message: 'لا توجد معالجات مكررة'
      };
    }
    
  } catch (error) {
    console.error('❌ خطأ في إصلاح تكرار المعالجات:', error);
    logError(error, 'fixDuplicateIPCHandlers');
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * تسجيل معالج واحد محسن لفواتير العملاء
 */
function registerSingleCustomerInvoicesHandler() {
  const { ipcMain } = require('electron');
  
  ipcMain.handle('get-customer-invoices', async (event, filters) => {
    try {
      logSystem(`استدعاء get-customer-invoices مع الفلاتر: ${JSON.stringify(filters || {})}`, 'info');

      // التحقق المحسن من الفلاتر
      if (!filters || !filters.customerId) {
        throw new Error('معرف العميل مطلوب للحصول على الفواتير');
      }

      // التحقق من صحة معرف العميل
      const customerId = parseInt(filters.customerId);
      if (isNaN(customerId) || customerId <= 0) {
        throw new Error('معرف العميل غير صالح');
      }

      // الحصول على اتصال قاعدة البيانات
      const dbManager = DatabaseManager.getInstance();
      const db = dbManager.getConnection();

      if (!db) {
        throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
      }

      // بناء شروط التصفية
      let whereConditions = ['t.customer_id = ?'];
      let queryParams = [customerId];

      // إضافة فلتر التاريخ إذا كان متوفراً
      if (filters.startDate) {
        whereConditions.push('DATE(t.transaction_date) >= DATE(?)');
        queryParams.push(filters.startDate);
      }

      if (filters.endDate) {
        whereConditions.push('DATE(t.transaction_date) <= DATE(?)');
        queryParams.push(filters.endDate);
      }

      // استعلام محسن للحصول على الفواتير مع الأصناف في استعلام واحد
      const optimizedQuery = `
        SELECT 
          t.invoice_number,
          t.transaction_date as invoice_date,
          SUM(t.total_price) as total_amount,
          SUM(t.profit) as total_profit,
          COUNT(DISTINCT t.id) as items_count,
          'paid' as payment_status,
          GROUP_CONCAT(
            json_object(
              'id', t.id,
              'item_id', t.item_id,
              'item_name', COALESCE(i.name, 'صنف غير معروف'),
              'quantity', t.quantity,
              'price', t.price,
              'total_price', t.total_price,
              'unit', i.unit,
              'description', i.name
            )
          ) as items_json
        FROM transactions t
        LEFT JOIN items i ON t.item_id = i.id
        WHERE t.transaction_type = 'sale' 
        AND ${whereConditions.join(' AND ')}
        AND t.invoice_number IS NOT NULL
        AND t.invoice_number != ''
        GROUP BY t.invoice_number, t.transaction_date
        ORDER BY t.transaction_date DESC
      `;

      const stmt = db.prepare(optimizedQuery);
      const invoicesData = stmt.all(...queryParams);

      // معالجة البيانات وتحويل JSON
      const invoicesWithItems = invoicesData.map(invoice => {
        let items = [];
        
        if (invoice.items_json) {
          try {
            // تحويل البيانات المجمعة إلى مصفوفة
            const itemsArray = invoice.items_json.split(',');
            items = itemsArray.map(itemJson => {
              try {
                return JSON.parse(itemJson);
              } catch (parseError) {
                console.warn('خطأ في تحويل JSON للصنف:', parseError);
                return null;
              }
            }).filter(item => item !== null);
          } catch (error) {
            console.warn('خطأ في معالجة أصناف الفاتورة:', error);
            items = [];
          }
        }

        return {
          id: invoice.invoice_number,
          invoice_number: invoice.invoice_number,
          invoice_date: invoice.invoice_date,
          total_amount: Number(invoice.total_amount) || 0,
          total_profit: Number(invoice.total_profit) || 0,
          items_count: Number(invoice.items_count) || 0,
          payment_status: invoice.payment_status,
          items: items
        };
      });

      // حساب إحصائيات محسنة
      const totalAmount = invoicesWithItems.reduce((sum, invoice) => sum + (invoice.total_amount || 0), 0);
      const totalProfit = invoicesWithItems.reduce((sum, invoice) => sum + (invoice.total_profit || 0), 0);
      const paidInvoices = invoicesWithItems.filter(invoice => invoice.payment_status === 'paid').length;
      const unpaidInvoices = invoicesWithItems.length - paidInvoices;

      logSystem(`تم الحصول على فواتير العميل: ${invoicesWithItems.length} فاتورة للعميل ${customerId}`, 'info');

      return {
        success: true,
        invoices: invoicesWithItems,
        stats: {
          totalInvoices: invoicesWithItems.length,
          totalAmount,
          totalProfit,
          profitMargin: totalAmount > 0 ? (totalProfit / totalAmount) * 100 : 0,
          paidInvoices,
          unpaidInvoices
        }
      };

    } catch (error) {
      logError(error, 'get-customer-invoices-enhanced');
      logSystem(`خطأ في الحصول على فواتير العميل: ${error.message}`, 'error');

      return {
        success: false,
        invoices: [],
        stats: {
          totalInvoices: 0,
          totalAmount: 0,
          totalProfit: 0,
          profitMargin: 0,
          paidInvoices: 0,
          unpaidInvoices: 0
        },
        error: error.message
      };
    }
  });

  console.log('✅ تم تسجيل معالج محسن لفواتير العملاء');
}

/**
 * فحص وإصلاح قاعدة البيانات
 */
function validateAndFixDatabase() {
  console.log('🔧 فحص وإصلاح قاعدة البيانات...');
  
  try {
    const dbManager = DatabaseManager.getInstance();
    const db = dbManager.getConnection();

    if (!db) {
      throw new Error('قاعدة البيانات غير متصلة');
    }

    // فحص وجود الجداول المطلوبة
    const requiredTables = ['transactions', 'customers', 'items'];
    const issues = [];

    requiredTables.forEach(tableName => {
      try {
        const checkStmt = db.prepare(`SELECT name FROM sqlite_master WHERE type='table' AND name=?`);
        const tableExists = checkStmt.get(tableName);
        
        if (!tableExists) {
          issues.push(`جدول ${tableName} غير موجود`);
        } else {
          console.log(`✅ جدول ${tableName} موجود`);
        }
      } catch (error) {
        issues.push(`خطأ في فحص جدول ${tableName}: ${error.message}`);
      }
    });

    // فحص الفهارس المطلوبة
    try {
      const indexQuery = `
        CREATE INDEX IF NOT EXISTS idx_transactions_customer_invoice 
        ON transactions(customer_id, invoice_number, transaction_date)
      `;
      db.exec(indexQuery);
      console.log('✅ تم إنشاء/التحقق من الفهارس المطلوبة');
    } catch (error) {
      issues.push(`خطأ في إنشاء الفهارس: ${error.message}`);
    }

    // فحص سلامة البيانات
    try {
      const dataIntegrityQuery = `
        SELECT COUNT(*) as invalid_invoices
        FROM transactions 
        WHERE transaction_type = 'sale' 
        AND (customer_id IS NULL OR customer_id = 0)
      `;
      
      const result = db.prepare(dataIntegrityQuery).get();
      
      if (result.invalid_invoices > 0) {
        console.log(`⚠️ تم العثور على ${result.invalid_invoices} معاملة بيع بدون عميل`);
        issues.push(`${result.invalid_invoices} معاملة بيع بدون عميل`);
      } else {
        console.log('✅ سلامة البيانات جيدة');
      }
    } catch (error) {
      issues.push(`خطأ في فحص سلامة البيانات: ${error.message}`);
    }

    return {
      success: issues.length === 0,
      issues: issues,
      message: issues.length === 0 ? 'قاعدة البيانات سليمة' : `تم العثور على ${issues.length} مشكلة`
    };

  } catch (error) {
    console.error('❌ خطأ في فحص قاعدة البيانات:', error);
    logError(error, 'validateAndFixDatabase');
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * تشغيل جميع الإصلاحات
 */
function runAllFixes() {
  console.log('🚀 بدء إصلاح مشاكل تقارير فواتير العملاء...');
  console.log('='.repeat(60));

  const results = {
    duplicateHandlers: null,
    databaseValidation: null,
    overall: false
  };

  try {
    // 1. إصلاح تكرار المعالجات
    console.log('\n1️⃣ إصلاح تكرار معالجات IPC...');
    results.duplicateHandlers = fixDuplicateIPCHandlers();
    
    if (results.duplicateHandlers.success) {
      console.log(`✅ ${results.duplicateHandlers.message}`);
    } else {
      console.log(`❌ فشل: ${results.duplicateHandlers.error}`);
    }

    // 2. فحص وإصلاح قاعدة البيانات
    console.log('\n2️⃣ فحص وإصلاح قاعدة البيانات...');
    results.databaseValidation = validateAndFixDatabase();
    
    if (results.databaseValidation.success) {
      console.log(`✅ ${results.databaseValidation.message}`);
    } else {
      console.log(`❌ مشاكل في قاعدة البيانات:`);
      results.databaseValidation.issues?.forEach(issue => {
        console.log(`   - ${issue}`);
      });
    }

    // تقييم النتيجة الإجمالية
    results.overall = results.duplicateHandlers.success && results.databaseValidation.success;

    console.log('\n' + '='.repeat(60));
    console.log('📋 ملخص الإصلاحات:');
    console.log(`   - إصلاح المعالجات: ${results.duplicateHandlers.success ? 'نجح' : 'فشل'}`);
    console.log(`   - فحص قاعدة البيانات: ${results.databaseValidation.success ? 'نجح' : 'فشل'}`);
    console.log(`   - النتيجة الإجمالية: ${results.overall ? 'نجح' : 'فشل'}`);

    if (results.overall) {
      console.log('\n🎉 تم إكمال جميع الإصلاحات بنجاح!');
      console.log('💡 يمكنك الآن استخدام تقارير فواتير العملاء بثقة');
    } else {
      console.log('\n⚠️ بعض الإصلاحات لم تكتمل بنجاح');
      console.log('💡 راجع الأخطاء أعلاه وحاول مرة أخرى');
    }

    console.log('='.repeat(60));
    return results;

  } catch (error) {
    console.error('❌ خطأ في تشغيل الإصلاحات:', error);
    logError(error, 'runAllFixes');
    
    results.overall = false;
    results.error = error.message;
    
    return results;
  }
}

// تصدير الدوال
module.exports = {
  fixDuplicateIPCHandlers,
  registerSingleCustomerInvoicesHandler,
  validateAndFixDatabase,
  runAllFixes
};

// تشغيل الإصلاحات إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  console.log('🚀 بدء إصلاح مشاكل تقارير فواتير العملاء...');
  
  const result = runAllFixes();
  
  console.log('\n🏁 انتهى الإصلاح');
  process.exit(result.overall ? 0 : 1);
}
