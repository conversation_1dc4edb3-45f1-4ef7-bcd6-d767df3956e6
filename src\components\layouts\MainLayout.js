import React from 'react';
import { Outlet, NavLink, useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import { 
  FaHome, 
  FaBoxes, 
  FaWarehouse, 
  FaArrowCircleDown, 
  FaArrowCircleUp, 
  FaChartBar, 
  FaUsers, 
  FaDatabase, 
  FaSignOutAlt, 
  FaMoon, 
  FaSun 
} from 'react-icons/fa';

const MainLayout = () => {
  const { currentUser, logout } = useAuth();
  const { darkMode, toggleDarkMode } = useTheme();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  return (
    <div className="container">
      <aside className="sidebar">
        <div className="sidebar-header">
          <h3>نظام إدارة المخازن</h3>
        </div>
        
        <div className="sidebar-menu">
          <NavLink to="/" className={({ isActive }) => 
            isActive ? "sidebar-menu-item active" : "sidebar-menu-item"
          } end>
            <FaHome />
            <span>الرئيسية</span>
          </NavLink>
          
          <NavLink to="/items" className={({ isActive }) => 
            isActive ? "sidebar-menu-item active" : "sidebar-menu-item"
          }>
            <FaBoxes />
            <span>الأصناف</span>
          </NavLink>
          
          <NavLink to="/inventory" className={({ isActive }) => 
            isActive ? "sidebar-menu-item active" : "sidebar-menu-item"
          }>
            <FaWarehouse />
            <span>المخزون</span>
          </NavLink>
          
          <NavLink to="/receiving" className={({ isActive }) => 
            isActive ? "sidebar-menu-item active" : "sidebar-menu-item"
          }>
            <FaArrowCircleDown />
            <span>الاستلام</span>
          </NavLink>
          
          <NavLink to="/withdrawal" className={({ isActive }) => 
            isActive ? "sidebar-menu-item active" : "sidebar-menu-item"
          }>
            <FaArrowCircleUp />
            <span>الصرف</span>
          </NavLink>
          
          <NavLink to="/reports" className={({ isActive }) => 
            isActive ? "sidebar-menu-item active" : "sidebar-menu-item"
          }>
            <FaChartBar />
            <span>التقارير</span>
          </NavLink>
          
          {currentUser && currentUser.role === 'admin' && (
            <NavLink to="/users" className={({ isActive }) => 
              isActive ? "sidebar-menu-item active" : "sidebar-menu-item"
            }>
              <FaUsers />
              <span>المستخدمين</span>
            </NavLink>
          )}
          
          <NavLink to="/backup" className={({ isActive }) => 
            isActive ? "sidebar-menu-item active" : "sidebar-menu-item"
          }>
            <FaDatabase />
            <span>النسخ الاحتياطي</span>
          </NavLink>
        </div>
        
        <div className="sidebar-footer" style={{ marginTop: 'auto', padding: '1rem', borderTop: '1px solid rgba(255, 255, 255, 0.1)' }}>
          <div className="sidebar-menu-item" onClick={toggleDarkMode}>
            {darkMode ? <FaSun /> : <FaMoon />}
            <span>{darkMode ? 'الوضع الفاتح' : 'الوضع الداكن'}</span>
          </div>
          
          <div className="sidebar-menu-item" onClick={handleLogout}>
            <FaSignOutAlt />
            <span>تسجيل الخروج</span>
          </div>
        </div>
      </aside>
      
      <main className="main-content">
        <Outlet />
      </main>
    </div>
  );
};

export default MainLayout;
