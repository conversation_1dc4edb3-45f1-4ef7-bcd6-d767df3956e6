import React, { useState, useEffect, useCallback } from 'react';
import { FaCloudDownloadAlt, FaDatabase, FaSync, FaCheck, FaExclamationTriangle, FaInfoCircle, FaUpload, FaTrash, FaHistory, FaUndo } from 'react-icons/fa';
import { useApp } from '../../context/AppContext';
import './UpdateManager.css';

// تعريف الرسوم المتحركة للتحميل
const spinAnimation = `
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  .spin-icon {
    animation: spin 1.5s linear infinite;
  }
`;

/**
 * مكون إدارة التحديثات
 * يوفر واجهة للتحقق من وجود تحديثات للتطبيق وقاعدة البيانات وتطبيقها
 */
const UpdateManager = () => {
  // حالة التحديثات
  const [appUpdateStatus, setAppUpdateStatus] = useState({
    checking: false,
    available: false,
    version: null,
    releaseNotes: null,
    downloading: false,
    downloadProgress: 0,
    error: null
  });

  // حالة تحديثات قاعدة البيانات
  const [dbUpdateStatus, setDbUpdateStatus] = useState({
    checking: false,
    available: false,
    version: null,
    changes: [],
    applying: false,
    progress: 0,
    error: null
  });

  // حالة النسخ الاحتياطية
  const [backupsStatus, setBackupsStatus] = useState({
    loading: false,
    backups: [],
    error: null,
    restoring: false
  });

  // استخدام سياق التطبيق
  const { settings, updateSettings } = useApp();

  // التحقق من وجود تحديثات للتطبيق
  const checkAppUpdates = useCallback(async () => {
    try {
      setAppUpdateStatus(prev => ({ ...prev, checking: true, error: null }));

      // استدعاء API للتحقق من وجود تحديثات
      if (window.api && window.api.updates && typeof window.api.updates.checkAppUpdates === 'function') {
        try {
          const result = await window.api.updates.checkAppUpdates();

          if (result && typeof result === 'object') {
            setAppUpdateStatus(prev => ({
              ...prev,
              checking: false,
              available: result.available || false,
              version: result.latestVersion || prev.version,
              releaseNotes: result.releaseNotes || '',
              updateFilePath: result.updateFilePath || null
            }));
          } else {
            throw new Error('نتيجة التحقق من التحديثات غير صالحة');
          }
        } catch (apiError) {
          console.error('خطأ في استدعاء API للتحقق من التحديثات:', apiError);
          setAppUpdateStatus(prev => ({
            ...prev,
            checking: false,
            error: apiError.message || 'حدث خطأ أثناء التحقق من التحديثات'
          }));
        }
      } else {
        // محاكاة التحقق من التحديثات (للاختبار)
        console.warn('واجهة التحديثات غير متوفرة، سيتم استخدام محاكاة التحقق من التحديثات');
        setTimeout(() => {
          setAppUpdateStatus(prev => ({
            ...prev,
            checking: false,
            available: false,
            version: '1.1.0',
            releaseNotes: 'لا توجد تحديثات متاحة حاليًا.',
            updateFilePath: null
          }));
        }, 1500);
      }
    } catch (error) {
      console.error('خطأ في التحقق من تحديثات التطبيق:', error);
      setAppUpdateStatus(prev => ({
        ...prev,
        checking: false,
        error: error.message || 'حدث خطأ أثناء التحقق من التحديثات'
      }));
    }
  }, []);

  // اختيار ملف تحديث للتطبيق
  const selectAppUpdateFile = async () => {
    try {
      setAppUpdateStatus(prev => ({ ...prev, checking: true, error: null }));

      // استدعاء API لاختيار ملف تحديث
      if (window.api && window.api.updates) {
        const result = await window.api.updates.selectAppUpdateFile();

        if (result.canceled) {
          setAppUpdateStatus(prev => ({
            ...prev,
            checking: false
          }));
          return;
        }

        setAppUpdateStatus(prev => ({
          ...prev,
          checking: false,
          available: true,
          version: result.version,
          releaseNotes: `تم اختيار ملف التحديث: ${result.fileName}`,
          updateFilePath: result.filePath
        }));
      } else {
        // محاكاة اختيار ملف تحديث (للاختبار)
        setTimeout(() => {
          setAppUpdateStatus(prev => ({
            ...prev,
            checking: false,
            available: true,
            version: '1.2.0',
            releaseNotes: 'تم اختيار ملف التحديث: app-update-1.2.0.zip',
            updateFilePath: 'C:\\path\\to\\app-update-1.2.0.zip'
          }));
        }, 1000);
      }
    } catch (error) {
      console.error('خطأ في اختيار ملف تحديث للتطبيق:', error);
      setAppUpdateStatus(prev => ({
        ...prev,
        checking: false,
        error: error.message || 'حدث خطأ أثناء اختيار ملف التحديث'
      }));
    }
  };

  // تنزيل وتثبيت تحديث التطبيق
  const downloadAndInstallUpdate = async () => {
    try {
      setAppUpdateStatus(prev => ({ ...prev, downloading: true, downloadProgress: 0, error: null }));

      // استدعاء API لتنزيل وتثبيت التحديث
      if (window.api && window.api.updates) {
        // إعداد مستمع لتقدم التنزيل
        try {
          // التحقق من أن onDownloadProgress قابل للتعديل
          if (Object.getOwnPropertyDescriptor(window.api.updates, 'onDownloadProgress').writable) {
            window.api.updates.onDownloadProgress = (progress) => {
              setAppUpdateStatus(prev => ({ ...prev, downloadProgress: progress }));
            };
          } else {
            console.warn('خاصية onDownloadProgress غير قابلة للتعديل، سيتم استخدام طريقة بديلة');
            // استخدام طريقة بديلة لتتبع التقدم
            window.addEventListener('update-download-progress', (event) => {
              if (event.detail && typeof event.detail.progress === 'number') {
                setAppUpdateStatus(prev => ({ ...prev, downloadProgress: event.detail.progress }));
              }
            });
          }
        } catch (error) {
          console.warn('خطأ في إعداد مستمع تقدم التنزيل:', error);
        }

        // تنزيل وتثبيت التحديث
        const result = await window.api.updates.downloadAndInstallUpdate();

        if (result.success) {
          setAppUpdateStatus(prev => ({
            ...prev,
            downloading: false,
            downloadProgress: 100
          }));

          // إظهار رسالة نجاح
          alert(result.message || 'تم تثبيت التحديث بنجاح. قد يتطلب الأمر إعادة تشغيل التطبيق لتطبيق التحديثات.');
        } else {
          throw new Error(result.message || 'فشل تثبيت التحديث');
        }
      } else {
        // محاكاة تنزيل التحديث (للاختبار)
        let progress = 0;
        const interval = setInterval(() => {
          progress += 10;
          setAppUpdateStatus(prev => ({ ...prev, downloadProgress: progress }));

          if (progress >= 100) {
            clearInterval(interval);
            setAppUpdateStatus(prev => ({ ...prev, downloading: false }));
            alert('تمت محاكاة تنزيل التحديث بنجاح. في البيئة الفعلية، سيتم إعادة تشغيل التطبيق لتثبيت التحديث.');
          }
        }, 500);
      }
    } catch (error) {
      console.error('خطأ في تنزيل وتثبيت التحديث:', error);
      setAppUpdateStatus(prev => ({
        ...prev,
        downloading: false,
        error: error.message || 'حدث خطأ أثناء تنزيل وتثبيت التحديث'
      }));
    }
  };

  // التحقق من وجود تحديثات لقاعدة البيانات
  const checkDatabaseUpdates = useCallback(async () => {
    try {
      setDbUpdateStatus(prev => ({ ...prev, checking: true, error: null }));

      // استدعاء API للتحقق من وجود تحديثات لقاعدة البيانات
      if (window.api && window.api.updates && typeof window.api.updates.checkDatabaseUpdates === 'function') {
        try {
          const result = await window.api.updates.checkDatabaseUpdates();

          if (result && typeof result === 'object') {
            setDbUpdateStatus(prev => ({
              ...prev,
              checking: false,
              available: result.available || false,
              version: result.latestVersion || prev.version,
              changes: Array.isArray(result.changes) ? result.changes : [],
              updateFilePath: result.updateFilePath || null
            }));
          } else {
            throw new Error('نتيجة التحقق من تحديثات قاعدة البيانات غير صالحة');
          }
        } catch (apiError) {
          console.error('خطأ في استدعاء API للتحقق من تحديثات قاعدة البيانات:', apiError);
          setDbUpdateStatus(prev => ({
            ...prev,
            checking: false,
            error: apiError.message || 'حدث خطأ أثناء التحقق من تحديثات قاعدة البيانات'
          }));
        }
      } else {
        // محاكاة التحقق من تحديثات قاعدة البيانات (للاختبار)
        console.warn('واجهة التحديثات غير متوفرة، سيتم استخدام محاكاة التحقق من تحديثات قاعدة البيانات');
        setTimeout(() => {
          setDbUpdateStatus(prev => ({
            ...prev,
            checking: false,
            available: false,
            version: '1.0',
            changes: [],
            updateFilePath: null
          }));
        }, 1500);
      }
    } catch (error) {
      console.error('خطأ في التحقق من تحديثات قاعدة البيانات:', error);
      setDbUpdateStatus(prev => ({
        ...prev,
        checking: false,
        error: error.message || 'حدث خطأ أثناء التحقق من تحديثات قاعدة البيانات'
      }));
    }
  }, []);

  // اختيار ملف تحديث لقاعدة البيانات
  const selectDatabaseUpdateFile = async () => {
    try {
      setDbUpdateStatus(prev => ({ ...prev, checking: true, error: null }));

      // استدعاء API لاختيار ملف تحديث
      if (window.api && window.api.updates) {
        const result = await window.api.updates.selectDatabaseUpdateFile();

        if (result.canceled) {
          setDbUpdateStatus(prev => ({
            ...prev,
            checking: false
          }));
          return;
        }

        setDbUpdateStatus(prev => ({
          ...prev,
          checking: false,
          available: true,
          version: result.version,
          changes: result.changes || [],
          updateFilePath: result.filePath
        }));
      } else {
        // محاكاة اختيار ملف تحديث (للاختبار)
        setTimeout(() => {
          setDbUpdateStatus(prev => ({
            ...prev,
            checking: false,
            available: true,
            version: '1.1',
            changes: ['إضافة جدول جديد للإشعارات', 'تحسين أداء الاستعلامات'],
            updateFilePath: 'C:\\path\\to\\db-update-1.1.sql'
          }));
        }, 1000);
      }
    } catch (error) {
      console.error('خطأ في اختيار ملف تحديث لقاعدة البيانات:', error);
      setDbUpdateStatus(prev => ({
        ...prev,
        checking: false,
        error: error.message || 'حدث خطأ أثناء اختيار ملف التحديث'
      }));
    }
  };

  // تطبيق تحديثات قاعدة البيانات
  const applyDatabaseUpdates = async () => {
    try {
      setDbUpdateStatus(prev => ({ ...prev, applying: true, progress: 0, error: null }));

      // استدعاء API لتطبيق تحديثات قاعدة البيانات
      if (window.api && window.api.updates) {
        // إعداد مستمع لتقدم التطبيق
        try {
          // التحقق من أن onDatabaseUpdateProgress قابل للتعديل
          if (Object.getOwnPropertyDescriptor(window.api.updates, 'onDatabaseUpdateProgress').writable) {
            window.api.updates.onDatabaseUpdateProgress = (progress) => {
              setDbUpdateStatus(prev => ({ ...prev, progress }));
            };
          } else {
            console.warn('خاصية onDatabaseUpdateProgress غير قابلة للتعديل، سيتم استخدام طريقة بديلة');
            // استخدام طريقة بديلة لتتبع التقدم
            window.addEventListener('database-update-progress', (event) => {
              if (event.detail && typeof event.detail.progress === 'number') {
                setDbUpdateStatus(prev => ({ ...prev, progress: event.detail.progress }));
              }
            });
          }
        } catch (error) {
          console.warn('خطأ في إعداد مستمع تقدم تحديث قاعدة البيانات:', error);
        }

        // تطبيق التحديثات
        const result = await window.api.updates.applyDatabaseUpdates();

        if (result.success) {
          // تحديث حالة التحديث
          setDbUpdateStatus(prev => ({
            ...prev,
            applying: false,
            available: false,
            progress: 100,
            version: result.newVersion
          }));

          // إظهار رسالة نجاح
          alert(result.message || 'تم تطبيق تحديثات قاعدة البيانات بنجاح.');

          // تحديث قائمة النسخ الاحتياطية
          loadBackupsList();
        } else {
          throw new Error(result.message || 'فشل تطبيق تحديثات قاعدة البيانات');
        }
      } else {
        // محاكاة تطبيق تحديثات قاعدة البيانات (للاختبار)
        let progress = 0;
        const interval = setInterval(() => {
          progress += 10;
          setDbUpdateStatus(prev => ({ ...prev, progress }));

          if (progress >= 100) {
            clearInterval(interval);
            setDbUpdateStatus(prev => ({
              ...prev,
              applying: false,
              available: false
            }));

            alert('تمت محاكاة تطبيق تحديثات قاعدة البيانات بنجاح.');
          }
        }, 300);
      }
    } catch (error) {
      console.error('خطأ في تطبيق تحديثات قاعدة البيانات:', error);
      setDbUpdateStatus(prev => ({
        ...prev,
        applying: false,
        error: error.message || 'حدث خطأ أثناء تطبيق تحديثات قاعدة البيانات'
      }));
    }
  };

  // تحميل قائمة النسخ الاحتياطية
  const loadBackupsList = useCallback(async () => {
    try {
      setBackupsStatus(prev => ({ ...prev, loading: true, error: null }));

      // استدعاء API للحصول على قائمة النسخ الاحتياطية
      if (window.api && window.api.updates && typeof window.api.updates.getBackupsList === 'function') {
        try {
          const backups = await window.api.updates.getBackupsList();

          if (Array.isArray(backups)) {
            setBackupsStatus(prev => ({
              ...prev,
              loading: false,
              backups: backups
            }));
          } else {
            console.warn('قائمة النسخ الاحتياطية غير صالحة:', backups);
            setBackupsStatus(prev => ({
              ...prev,
              loading: false,
              backups: [],
              error: 'تنسيق قائمة النسخ الاحتياطية غير صالح'
            }));
          }
        } catch (apiError) {
          console.error('خطأ في استدعاء API للحصول على قائمة النسخ الاحتياطية:', apiError);
          setBackupsStatus(prev => ({
            ...prev,
            loading: false,
            error: apiError.message || 'حدث خطأ أثناء تحميل قائمة النسخ الاحتياطية'
          }));
        }
      } else {
        // محاكاة الحصول على قائمة النسخ الاحتياطية (للاختبار)
        console.warn('واجهة التحديثات غير متوفرة، سيتم استخدام محاكاة قائمة النسخ الاحتياطية');
        setTimeout(() => {
          const mockBackups = [
            {
              name: 'db_backup_2023-05-15T12-30-45.db',
              path: 'C:\\path\\to\\backups\\db_backup_2023-05-15T12-30-45.db',
              size: 1024 * 1024 * 2, // 2 MB
              date: new Date('2023-05-15T12:30:45'),
              type: 'database'
            },
            {
              name: 'db_backup_2023-05-10T09-15-20.db',
              path: 'C:\\path\\to\\backups\\db_backup_2023-05-10T09-15-20.db',
              size: 1024 * 1024 * 1.5, // 1.5 MB
              date: new Date('2023-05-10T09:15:20'),
              type: 'database'
            }
          ];

          setBackupsStatus(prev => ({
            ...prev,
            loading: false,
            backups: mockBackups
          }));
        }, 1000);
      }
    } catch (error) {
      console.error('خطأ في تحميل قائمة النسخ الاحتياطية:', error);
      setBackupsStatus(prev => ({
        ...prev,
        loading: false,
        backups: [],
        error: error.message || 'حدث خطأ أثناء تحميل قائمة النسخ الاحتياطية'
      }));
    }
  }, []);

  // استعادة نسخة احتياطية
  const restoreBackup = async (backupPath) => {
    try {
      if (!window.confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال قاعدة البيانات الحالية.')) {
        return;
      }

      setBackupsStatus(prev => ({ ...prev, restoring: true, error: null }));

      // استدعاء API لاستعادة النسخة الاحتياطية
      if (window.api && window.api.updates) {
        const result = await window.api.updates.restoreDatabaseBackup(backupPath);

        if (result.success) {
          setBackupsStatus(prev => ({
            ...prev,
            restoring: false
          }));

          // إظهار رسالة نجاح
          alert(result.message || 'تم استعادة النسخة الاحتياطية بنجاح.');

          // إعادة تحميل قائمة النسخ الاحتياطية
          loadBackupsList();
        } else {
          throw new Error(result.message || 'فشل استعادة النسخة الاحتياطية');
        }
      } else {
        // محاكاة استعادة النسخة الاحتياطية (للاختبار)
        setTimeout(() => {
          setBackupsStatus(prev => ({
            ...prev,
            restoring: false
          }));

          alert('تمت محاكاة استعادة النسخة الاحتياطية بنجاح.');
        }, 1500);
      }
    } catch (error) {
      console.error('خطأ في استعادة النسخة الاحتياطية:', error);
      setBackupsStatus(prev => ({
        ...prev,
        restoring: false,
        error: error.message || 'حدث خطأ أثناء استعادة النسخة الاحتياطية'
      }));
    }
  };

  // حذف نسخة احتياطية
  const deleteBackup = async (backupPath) => {
    try {
      if (!window.confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟ لا يمكن التراجع عن هذا الإجراء.')) {
        return;
      }

      setBackupsStatus(prev => ({ ...prev, loading: true, error: null }));

      // استدعاء API لحذف النسخة الاحتياطية
      if (window.api && window.api.updates) {
        const result = await window.api.updates.deleteBackup(backupPath);

        if (result.success) {
          // إعادة تحميل قائمة النسخ الاحتياطية
          loadBackupsList();
        } else {
          throw new Error(result.message || 'فشل حذف النسخة الاحتياطية');
        }
      } else {
        // محاكاة حذف النسخة الاحتياطية (للاختبار)
        setTimeout(() => {
          // إعادة تحميل قائمة النسخ الاحتياطية
          loadBackupsList();
        }, 1000);
      }
    } catch (error) {
      console.error('خطأ في حذف النسخة الاحتياطية:', error);
      setBackupsStatus(prev => ({
        ...prev,
        loading: false,
        error: error.message || 'حدث خطأ أثناء حذف النسخة الاحتياطية'
      }));
    }
  };

  // تنسيق حجم الملف
  const formatFileSize = (bytes) => {
    try {
      // التحقق من صحة القيمة
      if (bytes === undefined || bytes === null) return 'غير محدد';
      if (isNaN(bytes) || bytes < 0) return 'حجم غير صالح';
      if (bytes === 0) return '0 Bytes';

      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

      // حساب المؤشر بطريقة آمنة
      let i = 0;
      try {
        i = Math.floor(Math.log(bytes) / Math.log(k));
        // التأكد من أن المؤشر ضمن نطاق المصفوفة
        i = Math.min(i, sizes.length - 1);
      } catch (mathError) {
        console.warn('خطأ في حساب مؤشر حجم الملف:', mathError);
        i = 0;
      }

      // حساب الحجم المنسق
      const formattedSize = parseFloat((bytes / Math.pow(k, i)).toFixed(2));
      return formattedSize + ' ' + sizes[i];
    } catch (error) {
      console.warn('خطأ في تنسيق حجم الملف:', error);
      return 'خطأ في الحجم';
    }
  };

  // تنسيق التاريخ
  const formatDate = (date) => {
    if (!date) return 'غير محدد';

    try {
      const d = new Date(date);

      // التحقق من صحة التاريخ
      if (isNaN(d.getTime())) {
        return 'تاريخ غير صالح';
      }

      // محاولة استخدام تنسيق ar-LY
      try {
        return d.toLocaleDateString('ar-LY') + ' ' + d.toLocaleTimeString('ar-LY');
      } catch (localeError) {
        // استخدام تنسيق بديل في حالة عدم دعم ar-LY
        try {
          return d.toLocaleDateString('ar') + ' ' + d.toLocaleTimeString('ar');
        } catch (error) {
          // استخدام تنسيق بديل في حالة عدم دعم ar
          return d.toLocaleDateString() + ' ' + d.toLocaleTimeString();
        }
      }
    } catch (error) {
      console.warn('خطأ في تنسيق التاريخ:', error);
      return 'خطأ في التاريخ';
    }
  };

  // تحديث إعدادات التحديث التلقائي
  const updateAutoUpdateSettings = (enabled) => {
    try {
      // تحديث الإعدادات في السياق
      updateSettings({
        ...settings,
        autoUpdateEnabled: enabled
      });

      // تحديث الإعدادات في قاعدة البيانات
      if (window.api && window.api.updates) {
        window.api.updates.setAutoUpdateSettings({ enabled });
      }
    } catch (error) {
      console.error('خطأ في تحديث إعدادات التحديث التلقائي:', error);
    }
  };

  // الحصول على إعدادات التحديث التلقائي
  const getAutoUpdateSettings = useCallback(async () => {
    try {
      if (window.api && window.api.updates && typeof window.api.updates.getAutoUpdateSettings === 'function') {
        const settings = await window.api.updates.getAutoUpdateSettings();

        if (settings && typeof settings === 'object') {
          // تحديث الإعدادات في السياق
          updateSettings(prev => ({
            ...prev,
            autoUpdateEnabled: settings.enabled
          }));
        } else {
          console.warn('إعدادات التحديث التلقائي غير صالحة:', settings);
        }
      } else {
        console.warn('واجهة التحديثات غير متوفرة أو وظيفة getAutoUpdateSettings غير موجودة');
      }
    } catch (error) {
      console.error('خطأ في الحصول على إعدادات التحديث التلقائي:', error);
    }
  }, [updateSettings]);

  // تحميل إعدادات التحديث التلقائي
  const loadAutoUpdateSettings = useCallback(async () => {
    try {
      if (window.api && window.api.updates) {
        const settings = await window.api.updates.getAutoUpdateSettings();

        // تحديث الإعدادات في السياق
        updateSettings(prev => ({
          ...prev,
          autoUpdateEnabled: settings.enabled
        }));
      }
    } catch (error) {
      console.error('خطأ في الحصول على إعدادات التحديث التلقائي:', error);
    }
  }, [updateSettings]);

  // التحقق من وجود تحديثات عند تحميل المكون
  useEffect(() => {
    // تأخير قصير لضمان تحميل واجهة المستخدم أولاً
    const initTimeout = setTimeout(() => {
      try {
        // التحقق من وجود تحديثات للتطبيق وقاعدة البيانات عند تحميل المكون
        checkAppUpdates();
        checkDatabaseUpdates();
        loadBackupsList();
        loadAutoUpdateSettings();
      } catch (error) {
        console.error('خطأ في تهيئة مكون إدارة التحديثات:', error);
      }
    }, 500);

    // تنظيف المستمعين عند إزالة المكون
    return () => {
      clearTimeout(initTimeout);

      try {
        // إزالة مستمعي الأحداث
        window.removeEventListener('update-download-progress', () => {});
        window.removeEventListener('database-update-progress', () => {});

        // محاولة تنظيف المستمعين في window.api.updates إذا كان ذلك ممكنًا
        if (window.api && window.api.updates) {
          try {
            // التحقق من أن الخصائص قابلة للتعديل
            if (Object.getOwnPropertyDescriptor(window.api.updates, 'onDownloadProgress')?.writable) {
              window.api.updates.onDownloadProgress = null;
            }

            if (Object.getOwnPropertyDescriptor(window.api.updates, 'onDatabaseUpdateProgress')?.writable) {
              window.api.updates.onDatabaseUpdateProgress = null;
            }
          } catch (error) {
            console.warn('خطأ في تنظيف مستمعي window.api.updates:', error);
          }
        }
      } catch (error) {
        console.warn('خطأ في تنظيف المستمعين:', error);
      }
    };
  }, [checkAppUpdates, checkDatabaseUpdates, loadBackupsList, loadAutoUpdateSettings]);

  // التعامل مع الأخطاء الحرجة
  const [criticalError, setCriticalError] = useState(null);

  // محاولة عرض المكون مع معالجة الأخطاء
  try {
    return (
      <div className="update-manager">
        <style>{spinAnimation}</style>

        {/* عرض رسالة الخطأ الحرجة إذا وجدت */}
        {criticalError && (
          <div className="critical-error">
            <FaExclamationTriangle />
            <div>
              <h3>حدث خطأ غير متوقع</h3>
              <p>{criticalError}</p>
              <button
                className="update-button check"
                onClick={() => window.location.reload()}
              >
                <FaSync /> إعادة تحميل الصفحة
              </button>
            </div>
          </div>
        )}

        {!criticalError && (
          <>
            {/* قسم تحديثات التطبيق */}
      <div className="update-section">
        <h3 className="update-section-title">
          <FaCloudDownloadAlt className="update-icon" />
          تحديثات التطبيق
        </h3>

        <div className="update-content">
          <div className="update-info">
            <p>
              <strong>الإصدار الحالي:</strong> {settings.appVersion || '1.1.0'}
            </p>

            {appUpdateStatus.checking ? (
              <div className="update-status">
                <FaSync className="spin-icon" />
                جاري التحقق من وجود تحديثات...
              </div>
            ) : appUpdateStatus.error ? (
              <div className="update-status error">
                <FaExclamationTriangle />
                {appUpdateStatus.error}
              </div>
            ) : appUpdateStatus.available ? (
              <div className="update-status available">
                <FaExclamationTriangle />
                يوجد تحديث جديد متاح: الإصدار {appUpdateStatus.version}
              </div>
            ) : (
              <div className="update-status up-to-date">
                <FaCheck />
                التطبيق محدث إلى أحدث إصدار
              </div>
            )}

            {appUpdateStatus.releaseNotes && (
              <div className="release-notes">
                <h4>ملاحظات الإصدار:</h4>
                <p>{appUpdateStatus.releaseNotes}</p>
              </div>
            )}
          </div>

          <div className="update-actions">
            <button
              className="update-button check"
              onClick={checkAppUpdates}
              disabled={appUpdateStatus.checking || appUpdateStatus.downloading}
            >
              <FaSync className={appUpdateStatus.checking ? 'spin-icon' : ''} />
              التحقق من وجود تحديثات
            </button>

            <button
              className="update-button upload"
              onClick={selectAppUpdateFile}
              disabled={appUpdateStatus.checking || appUpdateStatus.downloading}
            >
              <FaUpload />
              اختيار ملف تحديث
            </button>

            {appUpdateStatus.available && (
              <button
                className="update-button download"
                onClick={downloadAndInstallUpdate}
                disabled={appUpdateStatus.downloading}
              >
                <FaCloudDownloadAlt />
                {appUpdateStatus.downloading ? 'جاري التثبيت...' : 'تثبيت التحديث'}
              </button>
            )}
          </div>

          {appUpdateStatus.downloading && (
            <div className="progress-container">
              <div className="progress-label">جاري تثبيت التحديث: {appUpdateStatus.downloadProgress}%</div>
              <div className="progress-bar">
                <div
                  className="progress-fill"
                  style={{ width: `${appUpdateStatus.downloadProgress}%` }}
                ></div>
              </div>
            </div>
          )}

          <div className="auto-update-settings">
            <label className="auto-update-label">
              <input
                type="checkbox"
                checked={settings.autoUpdateEnabled || false}
                onChange={(e) => updateAutoUpdateSettings(e.target.checked)}
              />
              تفعيل التحديث التلقائي
            </label>
            <small className="auto-update-note">
              <FaInfoCircle />
              عند تفعيل هذا الخيار، سيقوم التطبيق بالتحقق من وجود تحديثات وتثبيتها تلقائيًا عند بدء التشغيل.
            </small>
          </div>
        </div>
      </div>

      {/* قسم تحديثات قاعدة البيانات */}
      <div className="update-section">
        <h3 className="update-section-title">
          <FaDatabase className="update-icon" />
          تحديثات قاعدة البيانات
        </h3>

        <div className="update-content">
          <div className="update-info">
            <p>
              <strong>إصدار قاعدة البيانات الحالي:</strong> {dbUpdateStatus.version || '1.0'}
            </p>

            {dbUpdateStatus.checking ? (
              <div className="update-status">
                <FaSync className="spin-icon" />
                جاري التحقق من وجود تحديثات لقاعدة البيانات...
              </div>
            ) : dbUpdateStatus.error ? (
              <div className="update-status error">
                <FaExclamationTriangle />
                {dbUpdateStatus.error}
              </div>
            ) : dbUpdateStatus.available ? (
              <div className="update-status available">
                <FaExclamationTriangle />
                يوجد تحديث جديد متاح لقاعدة البيانات: الإصدار {dbUpdateStatus.version}
              </div>
            ) : (
              <div className="update-status up-to-date">
                <FaCheck />
                قاعدة البيانات محدثة إلى أحدث إصدار
              </div>
            )}

            {dbUpdateStatus.available && dbUpdateStatus.changes.length > 0 && (
              <div className="db-changes">
                <h4>التغييرات:</h4>
                <ul>
                  {dbUpdateStatus.changes.map((change, index) => (
                    <li key={index}>{change}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>

          <div className="update-actions">
            <button
              className="update-button check"
              onClick={checkDatabaseUpdates}
              disabled={dbUpdateStatus.checking || dbUpdateStatus.applying}
            >
              <FaSync className={dbUpdateStatus.checking ? 'spin-icon' : ''} />
              التحقق من وجود تحديثات
            </button>

            <button
              className="update-button upload"
              onClick={selectDatabaseUpdateFile}
              disabled={dbUpdateStatus.checking || dbUpdateStatus.applying}
            >
              <FaUpload />
              اختيار ملف تحديث
            </button>

            {dbUpdateStatus.available && (
              <button
                className="update-button apply"
                onClick={applyDatabaseUpdates}
                disabled={dbUpdateStatus.applying}
              >
                <FaDatabase />
                {dbUpdateStatus.applying ? 'جاري التطبيق...' : 'تطبيق التحديثات'}
              </button>
            )}
          </div>

          {dbUpdateStatus.applying && (
            <div className="progress-container">
              <div className="progress-label">جاري تطبيق التحديثات: {dbUpdateStatus.progress}%</div>
              <div className="progress-bar">
                <div
                  className="progress-fill"
                  style={{ width: `${dbUpdateStatus.progress}%` }}
                ></div>
              </div>
            </div>
          )}

          <div className="db-update-note">
            <FaInfoCircle />
            <span>
              ملاحظة: سيتم إنشاء نسخة احتياطية من قاعدة البيانات قبل تطبيق أي تحديثات.
              يمكنك استعادة النسخة الاحتياطية في حالة حدوث أي مشاكل.
            </span>
          </div>
        </div>
      </div>

      {/* قسم النسخ الاحتياطية */}
      <div className="update-section">
        <h3 className="update-section-title">
          <FaHistory className="update-icon" />
          النسخ الاحتياطية
        </h3>

        <div className="update-content">
          <div className="backups-header">
            <div className="backups-title">
              <h4>قائمة النسخ الاحتياطية</h4>
              {backupsStatus.loading && <FaSync className="spin-icon" />}
            </div>
            <button
              className="update-button refresh"
              onClick={loadBackupsList}
              disabled={backupsStatus.loading || backupsStatus.restoring}
            >
              <FaSync className={backupsStatus.loading ? 'spin-icon' : ''} />
              تحديث القائمة
            </button>
          </div>

          {backupsStatus.error && (
            <div className="error-message">
              <FaExclamationTriangle />
              <span>{backupsStatus.error}</span>
            </div>
          )}

          {!Array.isArray(backupsStatus.backups) || backupsStatus.backups.length === 0 ? (
            <div className="empty-backups">
              <p>لا توجد نسخ احتياطية متاحة.</p>
            </div>
          ) : (
            <div className="backups-table-container">
              <table className="backups-table">
                <thead>
                  <tr>
                    <th>اسم الملف</th>
                    <th>النوع</th>
                    <th>الحجم</th>
                    <th>التاريخ</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {backupsStatus.backups.map((backup, index) => (
                    <tr key={index}>
                      <td>{backup.name || 'بدون اسم'}</td>
                      <td>{backup.type === 'database' ? 'قاعدة بيانات' : 'تطبيق'}</td>
                      <td>{formatFileSize(backup.size || 0)}</td>
                      <td>{formatDate(backup.date)}</td>
                      <td className="backup-actions">
                        {backup.type === 'database' && (
                          <button
                            className="backup-button restore"
                            onClick={() => restoreBackup(backup.path)}
                            disabled={backupsStatus.restoring}
                            title="استعادة النسخة الاحتياطية"
                          >
                            <FaUndo />
                          </button>
                        )}
                        <button
                          className="backup-button delete"
                          onClick={() => deleteBackup(backup.path)}
                          disabled={backupsStatus.loading || backupsStatus.restoring}
                          title="حذف النسخة الاحتياطية"
                        >
                          <FaTrash />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {backupsStatus.restoring && (
            <div className="restoring-message">
              <FaSync className="spin-icon" />
              <span>جاري استعادة النسخة الاحتياطية...</span>
            </div>
          )}

          <div className="backups-note">
            <FaInfoCircle />
            <span>
              يتم إنشاء نسخة احتياطية تلقائيًا قبل تطبيق أي تحديثات. يمكنك استعادة أي نسخة احتياطية في حالة حدوث مشكلة.
            </span>
          </div>
        </div>
      </div>
          </>
        )}
      </div>
    );
  } catch (error) {
    console.error('خطأ حرج في مكون UpdateManager:', error);

    // عرض واجهة الخطأ البسيطة
    return (
      <div className="update-manager error-state">
        <style>{spinAnimation}</style>
        <div className="critical-error">
          <FaExclamationTriangle />
          <div>
            <h3>حدث خطأ غير متوقع في صفحة الإعدادات</h3>
            <p>{error?.message || 'خطأ غير معروف'}</p>
            <button
              className="update-button check"
              onClick={() => window.location.reload()}
            >
              <FaSync /> إعادة تحميل الصفحة
            </button>
          </div>
        </div>
      </div>
    );
  }
};

export default UpdateManager;
