import React, { createContext, useState, useEffect, useContext } from 'react';
import { NotificationsContext } from './NotificationsProvider';

// إنشاء سياق الخزينة
export const CashboxContext = createContext();

/**
 * مزود سياق الخزينة
 *
 * يوفر هذا المكون وظائف إدارة الخزينة
 *
 * @param {Object} props - خصائص المكون
 * @param {React.ReactNode} props.children - المكونات الفرعية
 * @returns {React.ReactElement} مزود سياق الخزينة
 */
export const CashboxProvider = ({ children }) => {
  // استخدام سياق الإشعارات
  const { showNotification } = useContext(NotificationsContext);

  // حالة الخزينة
  const [cashbox, setCashbox] = useState(null);

  // حالة معاملات الخزينة
  const [cashboxTransactions, setCashboxTransactions] = useState([]);

  // تحميل بيانات الخزينة عند بدء التطبيق
  useEffect(() => {
    const loadCashboxData = async () => {
      try {
        console.log('جاري تحميل بيانات الخزينة...');

        // التحقق من وجود window.api قبل استدعاء وظائف الخزينة
        if (typeof window === 'undefined' || !window.api || !window.api.cashbox) {
          console.error('window.api.cashbox غير متوفر. لن يتم تحميل بيانات الخزينة.');

          // إعداد بيانات افتراضية للخزينة
          setCashbox({ exists: false, balance: 0, initial_balance: 0 });
          setCashboxTransactions([]);

          // تسجيل الخطأ فقط دون محاولة إعادة التحميل
          localStorage.setItem('lastFailedLoadTime', new Date().getTime().toString());
          return;
        }

        // محاولة تحميل بيانات الخزينة مع محاولات متعددة
        let attempts = 0;
        const maxAttempts = 3;
        let cashboxData = null;

        while (attempts < maxAttempts && !cashboxData) {
          attempts++;
          try {
            console.log(`محاولة تحميل بيانات الخزينة (${attempts}/${maxAttempts})...`);
            cashboxData = await window.api.cashbox.get();
            console.log('تم تحميل بيانات الخزينة بنجاح:', cashboxData);
            setCashbox(cashboxData);

            if (cashboxData && cashboxData.exists) {
              // تحميل معاملات الخزينة
              const transactionsData = await window.api.cashbox.getTransactions();
              setCashboxTransactions(transactionsData);
              console.log('تم تحميل معاملات الخزينة بنجاح:', transactionsData.length);
            } else {
              setCashboxTransactions([]);
            }
          } catch (attemptError) {
            console.error(`خطأ في محاولة تحميل بيانات الخزينة ${attempts}/${maxAttempts}:`, attemptError);

            if (attempts >= maxAttempts) {
              console.error('فشلت جميع محاولات تحميل بيانات الخزينة');
              setCashbox({ exists: false, balance: 0, initial_balance: 0 });
              setCashboxTransactions([]);
            } else {
              // انتظار قبل المحاولة التالية
              await new Promise(resolve => setTimeout(resolve, 1000));
            }
          }
        }
      } catch (error) {
        console.error('خطأ عام في تحميل بيانات الخزينة:', error);
        setCashbox({ exists: false, balance: 0, initial_balance: 0 });
        setCashboxTransactions([]);
      }
    };

    loadCashboxData();
  }, []);

  // إنشاء خزينة جديدة
  const createCashbox = async (initialBalance) => {
    try {
      console.log('بدء إنشاء خزينة جديدة بالرصيد الابتدائي:', initialBalance);

      // التحقق من وجود window.api قبل استدعاء وظائف الخزينة
      if (typeof window === 'undefined' || !window.api || !window.api.cashbox) {
        console.error('window.api.cashbox غير متوفر. لا يمكن إنشاء خزينة جديدة.');
        throw new Error('واجهة API للخزينة غير متوفرة');
      }

      // إنشاء خزينة جديدة
      const result = await window.api.cashbox.create(initialBalance);
      console.log('تم إنشاء الخزينة بنجاح:', result);

      // تحديث حالة الخزينة
      setCashbox(result);
      setCashboxTransactions([]);

      // إظهار إشعار بنجاح إنشاء الخزينة
      showNotification('تم إنشاء الخزينة بنجاح', 'success');

      return result;
    } catch (error) {
      console.error('خطأ في إنشاء الخزينة:', error);

      // إظهار إشعار بفشل إنشاء الخزينة
      showNotification(`فشل في إنشاء الخزينة: ${error.message}`, 'error');

      throw error;
    }
  };

  // تحديث الرصيد الابتدائي للخزينة
  const updateCashboxInitialBalance = async (newInitialBalance) => {
    try {
      console.log('بدء تحديث الرصيد الابتدائي للخزينة:', newInitialBalance);

      // التحقق من وجود window.api قبل استدعاء وظائف الخزينة
      if (typeof window === 'undefined' || !window.api || !window.api.cashbox) {
        console.error('window.api.cashbox غير متوفر. لا يمكن تحديث الرصيد الابتدائي للخزينة.');
        throw new Error('واجهة API للخزينة غير متوفرة');
      }

      // التحقق من وجود الخزينة
      if (!cashbox || !cashbox.exists) {
        console.log('الخزينة غير موجودة. سيتم إنشاء خزينة جديدة بالرصيد الابتدائي المحدد.');

        // إنشاء خزينة جديدة بالرصيد الابتدائي المحدد
        const createResult = await window.api.cashbox.create(newInitialBalance);
        console.log('تم إنشاء الخزينة بنجاح:', createResult);

        // تحديث حالة الخزينة
        setCashbox(createResult.cashbox);

        // إظهار إشعار بنجاح إنشاء الخزينة
        showNotification('تم إنشاء الخزينة بنجاح بالرصيد الابتدائي المحدد', 'success');

        return createResult.cashbox;
      }

      // تحديث الرصيد الابتدائي للخزينة
      const result = await window.api.cashbox.updateInitialBalance(newInitialBalance);
      console.log('تم تحديث الرصيد الابتدائي للخزينة بنجاح:', result);

      // تحديث حالة الخزينة - إصلاح: استخدام result.cashbox بدلاً من result
      if (result && result.success && result.cashbox) {
        // التحقق من وجود جميع الحقول المطلوبة في بيانات الخزينة
        const requiredFields = ['id', 'initial_balance', 'current_balance', 'profit_total', 'sales_total', 'purchases_total'];
        const missingFields = requiredFields.filter(field => result.cashbox[field] === undefined);

        if (missingFields.length > 0) {
          console.warn('حقول مفقودة في بيانات الخزينة:', missingFields);
          // إضافة القيم الافتراضية للحقول المفقودة
          const cashboxWithDefaults = {
            ...result.cashbox,
            returns_total: result.cashbox.returns_total || 0,
            transport_total: result.cashbox.transport_total || 0
          };
          setCashbox(cashboxWithDefaults);
          console.log('تم تحديث حالة الخزينة مع القيم الافتراضية:', cashboxWithDefaults);
        } else {
          setCashbox(result.cashbox);
          console.log('تم تحديث حالة الخزينة في CashboxProvider:', result.cashbox);
        }
      } else {
        console.error('خطأ في بنية النتيجة المستلمة:', result);
        console.error('تفاصيل النتيجة:', {
          hasResult: !!result,
          hasSuccess: result && result.success,
          hasCashbox: result && result.cashbox,
          resultKeys: result ? Object.keys(result) : 'لا توجد نتيجة'
        });
        throw new Error(`تم استلام بيانات غير صالحة من الخادم: ${result ? JSON.stringify(result) : 'لا توجد نتيجة'}`);
      }

      // إظهار إشعار بنجاح تحديث الرصيد الابتدائي
      showNotification('تم تحديث الرصيد الابتدائي للخزينة بنجاح', 'success');

      return result;
    } catch (error) {
      console.error('خطأ في تحديث الرصيد الابتدائي للخزينة:', error);

      // إظهار إشعار بفشل تحديث الرصيد الابتدائي
      showNotification(`فشل في تحديث الرصيد الابتدائي للخزينة: ${error.message}`, 'error');

      throw error;
    }
  };

  // إضافة معاملة للخزينة
  const addCashboxTransaction = async (transaction) => {
    try {
      console.log('بدء إضافة معاملة للخزينة:', transaction);

      // التحقق من وجود window.api قبل استدعاء وظائف الخزينة
      if (typeof window === 'undefined' || !window.api || !window.api.cashbox) {
        console.error('window.api.cashbox غير متوفر. لا يمكن إضافة معاملة للخزينة.');
        throw new Error('واجهة API للخزينة غير متوفرة');
      }

      // التحقق من وجود الخزينة
      if (!cashbox || !cashbox.exists) {
        console.log('الخزينة غير موجودة. سيتم إنشاء خزينة جديدة قبل إضافة المعاملة.');

        // إنشاء خزينة جديدة برصيد ابتدائي 0
        const initialBalance = transaction.type === 'income' ? transaction.amount : 0;
        const createResult = await window.api.cashbox.create(initialBalance);
        console.log('تم إنشاء الخزينة بنجاح:', createResult);

        // تحديث حالة الخزينة
        setCashbox(createResult.cashbox);

        // إظهار إشعار بنجاح إنشاء الخزينة
        showNotification('تم إنشاء الخزينة بنجاح', 'success');

        // إذا كانت المعاملة من نوع دخل، فقد تم إضافتها بالفعل عند إنشاء الخزينة
        if (transaction.type === 'income') {
          // إنشاء معاملة وهمية للعرض في واجهة المستخدم
          const dummyTransaction = {
            id: 1,
            type: 'income',
            amount: transaction.amount,
            source: transaction.source || 'إنشاء الخزينة',
            notes: transaction.notes || 'معاملة إنشاء الخزينة',
            created_at: new Date().toISOString()
          };

          // تحديث معاملات الخزينة
          setCashboxTransactions(prevTransactions => [dummyTransaction, ...prevTransactions]);

          return {
            success: true,
            cashbox: createResult.cashbox,
            transaction: dummyTransaction
          };
        }
      }

      // إضافة معاملة للخزينة
      const result = await window.api.cashbox.addTransaction(transaction);
      console.log('تم إضافة المعاملة للخزينة بنجاح:', result);

      // تحديث حالة الخزينة - إصلاح: التأكد من بنية النتيجة
      if (result && result.success && result.cashbox) {
        setCashbox(result.cashbox);
        console.log('تم تحديث حالة الخزينة بعد إضافة المعاملة:', result.cashbox);
      } else {
        console.error('خطأ في بنية النتيجة المستلمة من إضافة المعاملة:', result);
        throw new Error('تم استلام بيانات غير صالحة من الخادم');
      }

      // تحديث معاملات الخزينة
      setCashboxTransactions(prevTransactions => [result.transaction, ...prevTransactions]);

      // إظهار إشعار بنجاح إضافة المعاملة
      showNotification('تم إضافة المعاملة للخزينة بنجاح', 'success');

      return result;
    } catch (error) {
      console.error('خطأ في إضافة معاملة للخزينة:', error);

      // إظهار إشعار بفشل إضافة المعاملة
      showNotification(`فشل في إضافة معاملة للخزينة: ${error.message}`, 'error');

      throw error;
    }
  };

  // الحصول على معاملات الخزينة
  const getCashboxTransactions = async () => {
    try {
      console.log('بدء الحصول على معاملات الخزينة');

      // التحقق من وجود window.api قبل استدعاء وظائف الخزينة
      if (typeof window === 'undefined' || !window.api || !window.api.cashbox) {
        console.error('window.api.cashbox غير متوفر. لا يمكن الحصول على معاملات الخزينة.');
        throw new Error('واجهة API للخزينة غير متوفرة');
      }

      // التحقق من وجود الخزينة
      if (!cashbox || !cashbox.exists) {
        console.log('الخزينة غير موجودة. سيتم إرجاع قائمة فارغة من المعاملات.');
        setCashboxTransactions([]);
        return [];
      }

      // الحصول على معاملات الخزينة
      const transactions = await window.api.cashbox.getTransactions();
      console.log('تم الحصول على معاملات الخزينة بنجاح:', transactions.length);

      // تحديث معاملات الخزينة
      setCashboxTransactions(transactions);

      return transactions;
    } catch (error) {
      console.error('خطأ في الحصول على معاملات الخزينة:', error);
      // في حالة الخطأ، نعيد قائمة فارغة بدلاً من رمي الخطأ
      setCashboxTransactions([]);
      return [];
    }
  };

  // إصلاح قيم الخزينة السالبة
  const fixNegativeCashboxValues = async () => {
    try {
      console.log('بدء إصلاح قيم الخزينة السالبة');

      // التحقق من وجود window.api قبل استدعاء وظائف الخزينة
      if (typeof window === 'undefined' || !window.api) {
        console.error('window.api غير متوفر. لا يمكن إصلاح قيم الخزينة السالبة.');
        throw new Error('واجهة API غير متوفرة');
      }

      // التحقق من وجود الخزينة
      if (!cashbox || !cashbox.exists) {
        console.log('الخزينة غير موجودة. سيتم إنشاء خزينة جديدة برصيد ابتدائي 0.');

        // إنشاء خزينة جديدة برصيد ابتدائي 0
        const createResult = await window.api.cashbox.create(0);
        console.log('تم إنشاء الخزينة بنجاح:', createResult);

        // تحديث حالة الخزينة
        setCashbox(createResult.cashbox);

        // إظهار إشعار بنجاح إنشاء الخزينة
        showNotification('تم إنشاء الخزينة بنجاح برصيد ابتدائي 0', 'success');

        return { success: true, message: 'تم إنشاء خزينة جديدة بدلاً من إصلاح القيم السالبة' };
      }

      // إصلاح قيم الخزينة السالبة
      const result = await window.api.invoke('fix-negative-cashbox-values');
      console.log('نتيجة إصلاح قيم الخزينة السالبة:', result);

      if (result && result.success) {
        // تحديث حالة الخزينة
        const updatedCashbox = await window.api.cashbox.get();
        setCashbox(updatedCashbox);

        // إظهار إشعار بنجاح إصلاح قيم الخزينة السالبة
        showNotification('تم إصلاح قيم الخزينة السالبة بنجاح', 'success');

        return result;
      } else {
        throw new Error(result.error || 'فشل في إصلاح قيم الخزينة السالبة');
      }
    } catch (error) {
      console.error('خطأ في إصلاح قيم الخزينة السالبة:', error);

      // إظهار إشعار بفشل إصلاح قيم الخزينة السالبة
      showNotification(`فشل في إصلاح قيم الخزينة السالبة: ${error.message}`, 'error');

      throw error;
    }
  };

  // القيمة التي سيتم توفيرها للمكونات
  const value = {
    cashbox,
    cashboxTransactions,
    createCashbox,
    updateCashboxInitialBalance,
    addCashboxTransaction,
    getCashboxTransactions,
    fixNegativeCashboxValues
  };

  return (
    <CashboxContext.Provider value={value}>
      {children}
    </CashboxContext.Provider>
  );
};

export default CashboxProvider;
