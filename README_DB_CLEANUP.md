# تعليمات إزالة إعدادات Google Drive من قاعدة البيانات

بعد إزالة خاصية رفع النسخ الاحتياطي على جوجل درايف من النظام، يجب إزالة الإعدادات المتعلقة بها من قاعدة البيانات. يمكن القيام بذلك بإحدى الطريقتين التاليتين:

## الطريقة الأولى: استخدام سكريبت SQL

1. افتح أداة إدارة قاعدة البيانات SQLite (مثل SQLite Browser).
2. افتح ملف قاعدة البيانات الموجود في المسار:
   ```
   %APPDATA%\wms-dev\wms-database\warehouse.db
   ```
3. انتقل إلى تبويب "تنفيذ SQL".
4. قم بنسخ محتوى ملف `remove_google_drive_settings.sql` وتنفيذه.

## الطريقة الثانية: استخدام سكريبت Node.js

1. تأكد من إغلاق التطبيق تمامًا.
2. افتح موجه الأوامر (Command Prompt) أو PowerShell.
3. انتقل إلى مجلد التطبيق.
4. قم بتنفيذ السكريبت باستخدام الأمر التالي:
   ```
   node apply_db_changes.js
   ```

## التحقق من نجاح العملية

بعد تنفيذ أي من الطريقتين، يمكنك التحقق من نجاح العملية بالطرق التالية:

1. فتح قاعدة البيانات باستخدام أداة إدارة قاعدة البيانات SQLite.
2. تنفيذ الاستعلام التالي:
   ```sql
   SELECT * FROM settings WHERE key LIKE 'googleDrive%';
   ```
3. يجب ألا يظهر أي نتائج، مما يعني أن جميع إعدادات Google Drive قد تمت إزالتها بنجاح.

## ملاحظات هامة

- قم بعمل نسخة احتياطية من قاعدة البيانات قبل تنفيذ أي من هذه التغييرات.
- تأكد من إغلاق التطبيق تمامًا قبل تعديل قاعدة البيانات.
- بعد تنفيذ التغييرات، قم بإعادة تشغيل التطبيق للتأكد من أنه يعمل بشكل صحيح.

## استكشاف الأخطاء وإصلاحها

إذا واجهت أي مشاكل أثناء تنفيذ هذه التغييرات، يمكنك:

1. التحقق من سجلات الأخطاء في التطبيق.
2. استعادة النسخة الاحتياطية من قاعدة البيانات.
3. التواصل مع فريق الدعم الفني للحصول على المساعدة.
