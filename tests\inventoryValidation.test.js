/**
 * اختبارات وحدة التحقق الشامل من توفر المخزون
 * تتحقق هذه الاختبارات من صحة عمل آلية التحقق من توفر الأصناف قبل عمليات البيع
 */

// محاكاة window.api للاختبار
global.window = {
  api: {
    customers: {
      getItemInfoForSale: jest.fn()
    },
    inventory: {
      getItem: jest.fn()
    }
  }
};

const { 
  validateInventoryAvailability, 
  generateDetailedErrorMessage, 
  generateShortErrorMessage 
} = require('../src/utils/inventoryValidator');

describe('Inventory Validation Tests', () => {
  beforeEach(() => {
    // إعادة تعيين جميع المحاكيات قبل كل اختبار
    jest.clearAllMocks();
  });

  describe('validateInventoryAvailability', () => {
    test('should validate available items successfully', async () => {
      // محاكاة استجابة API للصنف المتوفر
      window.api.customers.getItemInfoForSale.mockResolvedValue({
        id: 1,
        name: 'صنف اختبار',
        current_quantity: 100,
        available_for_sale: true
      });

      const items = [
        { item_id: 1, item_name: 'صنف اختبار', quantity: 50 }
      ];

      const result = await validateInventoryAvailability(items);

      expect(result.isValid).toBe(true);
      expect(result.validItems).toHaveLength(1);
      expect(result.unavailableItems).toHaveLength(0);
      expect(result.insufficientItems).toHaveLength(0);
      expect(result.errors).toHaveLength(0);
    });

    test('should detect insufficient quantity', async () => {
      // محاكاة استجابة API للصنف بكمية غير كافية
      window.api.customers.getItemInfoForSale.mockResolvedValue({
        id: 1,
        name: 'صنف اختبار',
        current_quantity: 10,
        available_for_sale: true
      });

      const items = [
        { item_id: 1, item_name: 'صنف اختبار', quantity: 50 }
      ];

      const result = await validateInventoryAvailability(items);

      expect(result.isValid).toBe(false);
      expect(result.validItems).toHaveLength(0);
      expect(result.unavailableItems).toHaveLength(0);
      expect(result.insufficientItems).toHaveLength(1);
      expect(result.insufficientItems[0].shortage).toBe(40);
    });

    test('should detect unavailable items', async () => {
      // محاكاة استجابة API للصنف غير المتاح للبيع
      window.api.customers.getItemInfoForSale.mockResolvedValue({
        id: 1,
        name: 'صنف اختبار',
        current_quantity: 100,
        available_for_sale: false,
        message: 'الصنف غير متاح للبيع'
      });

      const items = [
        { item_id: 1, item_name: 'صنف اختبار', quantity: 50 }
      ];

      const result = await validateInventoryAvailability(items);

      expect(result.isValid).toBe(false);
      expect(result.validItems).toHaveLength(0);
      expect(result.unavailableItems).toHaveLength(1);
      expect(result.insufficientItems).toHaveLength(0);
      expect(result.unavailableItems[0].error).toBe('الصنف غير متاح للبيع');
    });

    test('should handle missing items', async () => {
      // محاكاة عدم وجود الصنف
      window.api.customers.getItemInfoForSale.mockResolvedValue(null);

      const items = [
        { item_id: 999, item_name: 'صنف غير موجود', quantity: 50 }
      ];

      const result = await validateInventoryAvailability(items);

      expect(result.isValid).toBe(false);
      expect(result.validItems).toHaveLength(0);
      expect(result.unavailableItems).toHaveLength(1);
      expect(result.insufficientItems).toHaveLength(0);
      expect(result.unavailableItems[0].error).toBe('الصنف غير موجود في المخزون');
    });

    test('should validate multiple items with mixed results', async () => {
      // محاكاة استجابات مختلفة للأصناف المختلفة
      window.api.customers.getItemInfoForSale
        .mockResolvedValueOnce({
          id: 1,
          name: 'صنف متوفر',
          current_quantity: 100,
          available_for_sale: true
        })
        .mockResolvedValueOnce({
          id: 2,
          name: 'صنف بكمية قليلة',
          current_quantity: 5,
          available_for_sale: true
        })
        .mockResolvedValueOnce(null);

      const items = [
        { item_id: 1, item_name: 'صنف متوفر', quantity: 50 },
        { item_id: 2, item_name: 'صنف بكمية قليلة', quantity: 10 },
        { item_id: 3, item_name: 'صنف غير موجود', quantity: 20 }
      ];

      const result = await validateInventoryAvailability(items);

      expect(result.isValid).toBe(false);
      expect(result.validItems).toHaveLength(1);
      expect(result.unavailableItems).toHaveLength(1);
      expect(result.insufficientItems).toHaveLength(1);
    });

    test('should handle invalid input', async () => {
      const result = await validateInventoryAvailability([]);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('لا توجد أصناف للتحقق من توفرها');
    });

    test('should handle API errors gracefully', async () => {
      // محاكاة خطأ في API
      window.api.customers.getItemInfoForSale.mockRejectedValue(new Error('خطأ في الشبكة'));

      const items = [
        { item_id: 1, item_name: 'صنف اختبار', quantity: 50 }
      ];

      const result = await validateInventoryAvailability(items);

      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('generateDetailedErrorMessage', () => {
    test('should generate detailed error message for unavailable items', () => {
      const validationResult = {
        isValid: false,
        unavailableItems: [
          {
            item_name: 'صنف غير متوفر',
            error: 'الصنف غير موجود في المخزون'
          }
        ],
        insufficientItems: [],
        errors: []
      };

      const message = generateDetailedErrorMessage(validationResult);

      expect(message).toContain('🚫 أصناف غير متوفرة:');
      expect(message).toContain('صنف غير متوفر');
      expect(message).toContain('الصنف غير موجود في المخزون');
    });

    test('should generate detailed error message for insufficient items', () => {
      const validationResult = {
        isValid: false,
        unavailableItems: [],
        insufficientItems: [
          {
            item_name: 'صنف بكمية قليلة',
            requested_quantity: 100,
            available_quantity: 10,
            shortage: 90
          }
        ],
        errors: []
      };

      const message = generateDetailedErrorMessage(validationResult);

      expect(message).toContain('⚠️ أصناف بكميات غير كافية:');
      expect(message).toContain('صنف بكمية قليلة');
      expect(message).toContain('المطلوب: 100');
      expect(message).toContain('المتوفر: 10');
      expect(message).toContain('النقص: 90');
    });

    test('should return empty string for valid results', () => {
      const validationResult = {
        isValid: true,
        unavailableItems: [],
        insufficientItems: [],
        errors: []
      };

      const message = generateDetailedErrorMessage(validationResult);

      expect(message).toBe('');
    });
  });

  describe('generateShortErrorMessage', () => {
    test('should generate short error message', () => {
      const validationResult = {
        isValid: false,
        unavailableItems: [{ item_name: 'صنف 1' }],
        insufficientItems: [{ item_name: 'صنف 2' }],
        errors: []
      };

      const message = generateShortErrorMessage(validationResult);

      expect(message).toContain('1 صنف غير متوفر');
      expect(message).toContain('1 صنف بكمية غير كافية');
    });

    test('should return empty string for valid results', () => {
      const validationResult = {
        isValid: true,
        unavailableItems: [],
        insufficientItems: [],
        errors: []
      };

      const message = generateShortErrorMessage(validationResult);

      expect(message).toBe('');
    });
  });
});

// اختبارات التكامل
describe('Integration Tests', () => {
  test('should prevent partial sales when some items are unavailable', async () => {
    // محاكاة سيناريو حقيقي: عملية بيع متعددة الأصناف مع بعض الأصناف غير المتوفرة
    window.api.customers.getItemInfoForSale
      .mockResolvedValueOnce({
        id: 1,
        name: 'صنف متوفر',
        current_quantity: 100,
        available_for_sale: true
      })
      .mockResolvedValueOnce(null); // صنف غير موجود

    const saleItems = [
      { item_id: 1, item_name: 'صنف متوفر', quantity: 50 },
      { item_id: 2, item_name: 'صنف غير موجود', quantity: 20 }
    ];

    const result = await validateInventoryAvailability(saleItems);

    // يجب أن تفشل العملية بالكامل
    expect(result.isValid).toBe(false);
    expect(result.validItems).toHaveLength(1);
    expect(result.unavailableItems).toHaveLength(1);

    // يجب أن تحتوي رسالة الخطأ على تفاصيل واضحة
    const errorMessage = generateDetailedErrorMessage(result);
    expect(errorMessage).toContain('لا يمكن إتمام عملية البيع');
    expect(errorMessage).toContain('صنف غير موجود');
  });
});
