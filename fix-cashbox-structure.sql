
-- إصلا<PERSON> بنية جدول الخزينة
-- إضافة الحقول المفقودة إذا لم تكن موجودة

-- التحقق من وجود الحقول وإضافتها إذا لم تكن موجودة
-- ملاحظة: SQLite لا يدعم IF NOT EXISTS للأعمدة، لذا نستخدم معالجة الأخطاء

-- إضافة حقل profit_total
ALTER TABLE cashbox ADD COLUMN profit_total REAL DEFAULT 0;

-- إضافة حقل sales_total
ALTER TABLE cashbox ADD COLUMN sales_total REAL DEFAULT 0;

-- إضافة حقل purchases_total
ALTER TABLE cashbox ADD COLUMN purchases_total REAL DEFAULT 0;

-- إضافة حقل returns_total
ALTER TABLE cashbox ADD COLUMN returns_total REAL DEFAULT 0;

-- إضا<PERSON>ة حقل transport_total
ALTER TABLE cashbox ADD COLUMN transport_total REAL DEFAULT 0;

-- تحديث القيم الافتراضية للحقول الجديدة
UPDATE cashbox
SET
  profit_total = COALESCE(profit_total, 0),
  sales_total = COALESCE(sales_total, 0),
  purchases_total = COALESCE(purchases_total, 0),
  returns_total = COALESCE(returns_total, 0),
  transport_total = COALESCE(transport_total, 0),
  updated_at = CURRENT_TIMESTAMP
WHERE id IS NOT NULL;

-- عرض بنية الجدول النهائية
PRAGMA table_info(cashbox);
