/**
 * إصلاح سريع لتصحيح قيمة الأرباح إلى 5450
 */

console.log('🔧 إصلاح سريع لتصحيح قيمة الأرباح إلى 5450...');
console.log('='.repeat(50));

try {
  // 1. الحصول على قاعدة البيانات
  const DatabaseManager = require('./database-singleton');
  const db = DatabaseManager.getDatabase();
  
  if (!db) {
    throw new Error('قاعدة البيانات غير متصلة');
  }
  console.log('✅ تم الاتصال بقاعدة البيانات');

  // 2. فحص القيمة الحالية
  const currentQuery = db.prepare('SELECT profit_total FROM cashbox WHERE id = 1');
  const currentResult = currentQuery.get();
  const currentProfit = currentResult ? Number(currentResult.profit_total) : 0;
  
  console.log(`💾 الأرباح الحالية: ${currentProfit}`);
  console.log(`🎯 الأرباح المطلوبة: 5450`);

  // 3. تصحيح القيمة إلى 5450
  if (Math.abs(currentProfit - 5450) > 0.01) {
    console.log('\n🔧 تصحيح قيمة الأرباح...');
    
    const correctProfit = 5450;
    
    // استخدام معاملة قاعدة البيانات لضمان الحفظ
    const fixTransaction = db.transaction(() => {
      const updateStmt = db.prepare(`
        UPDATE cashbox 
        SET profit_total = ?, 
            updated_at = ? 
        WHERE id = 1
      `);
      
      const updateResult = updateStmt.run(correctProfit, new Date().toISOString());
      
      if (updateResult.changes === 0) {
        throw new Error('لم يتم تحديث أي صف في جدول cashbox');
      }
      
      console.log(`✅ تم تحديث الخزينة، الصفوف المتأثرة: ${updateResult.changes}`);
      
      // التحقق الفوري من الحفظ
      const verifyStmt = db.prepare('SELECT profit_total FROM cashbox WHERE id = 1');
      const verifyResult = verifyStmt.get();
      
      if (!verifyResult) {
        throw new Error('فشل في استرجاع البيانات للتحقق');
      }
      
      const savedValue = Number(verifyResult.profit_total);
      console.log(`💾 القيمة المحفوظة: ${savedValue}`);
      
      if (Math.abs(savedValue - correctProfit) > 0.01) {
        throw new Error(`فشل في حفظ القيمة: متوقع ${correctProfit} لكن تم حفظ ${savedValue}`);
      }
      
      return {
        success: true,
        oldValue: currentProfit,
        newValue: savedValue
      };
    });
    
    const result = fixTransaction();
    
    if (result.success) {
      console.log('🎉 تم تصحيح قيمة الأرباح إلى 5450 بنجاح!');
      console.log(`📊 القيمة القديمة: ${result.oldValue}`);
      console.log(`📊 القيمة الجديدة: ${result.newValue}`);
      console.log(`📊 الفرق: ${result.newValue - result.oldValue}`);
      
      // 4. تحديث النظام ليستخدم القيمة الصحيحة
      console.log('\n🔄 تحديث مدير المعاملات...');
      const transactionManager = require('./unified-transaction-manager');
      
      // التأكد من أن النظام يحفظ القيمة الصحيحة
      const saveResult = transactionManager.updateProfitTotalInDatabase(5450);
      
      if (saveResult) {
        console.log('✅ تم تحديث مدير المعاملات بالقيمة الصحيحة');
      } else {
        console.log('⚠️ تحذير: قد تحتاج لإعادة تشغيل التطبيق');
      }
      
      console.log('\n🎯 النتيجة النهائية:');
      console.log('   ✅ تم تصحيح قيمة الأرباح إلى 5450');
      console.log('   ✅ تم حفظ القيمة في قاعدة البيانات');
      console.log('   ✅ تم تحديث مدير المعاملات');
      console.log('\n💡 يمكنك الآن التحقق من عرض الأرباح 5450 في واجهة النظام');
      
    } else {
      throw new Error('فشل في تصحيح قيمة الأرباح');
    }
  } else {
    console.log('✅ قيمة الأرباح صحيحة بالفعل (5450)');
  }

} catch (error) {
  console.error('❌ خطأ في الإصلاح السريع:', error.message);
  console.log('\n💡 جرب تشغيل الإصلاح الشامل: node fix-profit-5450-correct.js');
}

console.log('\n' + '='.repeat(50));
console.log('🏁 انتهى الإصلاح السريع');
