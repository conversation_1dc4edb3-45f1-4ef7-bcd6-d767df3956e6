/**
 * Web Worker لتنفيذ العمليات الحسابية الثقيلة
 * يتم استخدامه لتنفيذ العمليات الحسابية المعقدة في خلفية التطبيق
 * دون التأثير على واجهة المستخدم
 */

// استقبال الرسائل من النافذة الرئيسية
self.addEventListener('message', (event) => {
  const { type, data, id } = event.data;

  try {
    let result;

    // تنفيذ العملية المطلوبة بناءً على النوع
    switch (type) {
      case 'calculateProfits':
        result = calculateProfits(data);
        break;
      case 'calculateInventoryStats':
        result = calculateInventoryStats(data);
        break;
      case 'calculateTransactionStats':
        result = calculateTransactionStats(data);
        break;
      case 'calculateTopSellingItems':
        result = calculateTopSellingItems(data);
        break;
      case 'calculateMostProfitableItems':
        result = calculateMostProfitableItems(data);
        break;
      case 'filterTransactions':
        result = filterTransactions(data);
        break;
      default:
        throw new Error(`نوع العملية غير معروف: ${type}`);
    }

    // إرسال النتيجة إلى النافذة الرئيسية
    self.postMessage({
      id,
      type,
      result,
      error: null
    });
  } catch (error) {
    // إرسال الخطأ إلى النافذة الرئيسية
    self.postMessage({
      id,
      type,
      result: null,
      error: error.message
    });
  }
});

/**
 * حساب الأرباح
 * @param {Object} data - بيانات المعاملات
 * @returns {Object} - إحصائيات الأرباح
 */
function calculateProfits(data) {
  const { transactions } = data;

  // التحقق من وجود المعاملات
  if (!transactions || !Array.isArray(transactions) || transactions.length === 0) {
    return {
      yearly: 0,
      halfYearly: 0,
      quarterly: 0,
      threeQuarters: 0
    };
  }

  // الحصول على التاريخ الحالي
  const now = new Date();
  const currentYear = now.getFullYear();
  const currentMonth = now.getMonth();

  // تصفية المعاملات حسب النوع والتاريخ
  const salesTransactions = transactions.filter(t => t.transaction_type === 'sale');

  // حساب الأرباح الربع سنوية
  const quarterlyTransactions = salesTransactions.filter(t => {
    const transactionDate = new Date(t.transaction_date);
    return (
      transactionDate.getFullYear() === currentYear &&
      transactionDate.getMonth() >= currentMonth - 3 &&
      transactionDate.getMonth() <= currentMonth
    );
  });

  const quarterlyProfit = quarterlyTransactions.reduce((sum, t) => sum + (t.profit || 0), 0);

  // حساب الأرباح نصف السنوية
  const halfYearlyTransactions = salesTransactions.filter(t => {
    const transactionDate = new Date(t.transaction_date);
    return (
      transactionDate.getFullYear() === currentYear &&
      transactionDate.getMonth() >= currentMonth - 6 &&
      transactionDate.getMonth() <= currentMonth
    );
  });

  const halfYearlyProfit = halfYearlyTransactions.reduce((sum, t) => sum + (t.profit || 0), 0);

  // حساب الأرباح ثلاثة أرباع السنوية
  const threeQuartersTransactions = salesTransactions.filter(t => {
    const transactionDate = new Date(t.transaction_date);
    return (
      transactionDate.getFullYear() === currentYear &&
      transactionDate.getMonth() >= currentMonth - 9 &&
      transactionDate.getMonth() <= currentMonth
    );
  });

  const threeQuartersProfit = threeQuartersTransactions.reduce((sum, t) => sum + (t.profit || 0), 0);

  // حساب الأرباح السنوية
  const yearlyTransactions = salesTransactions.filter(t => {
    const transactionDate = new Date(t.transaction_date);
    return transactionDate.getFullYear() === currentYear;
  });

  const yearlyProfit = yearlyTransactions.reduce((sum, t) => sum + (t.profit || 0), 0);

  return {
    quarterly: quarterlyProfit,
    halfYearly: halfYearlyProfit,
    threeQuarters: threeQuartersProfit,
    yearly: yearlyProfit
  };
}

/**
 * حساب إحصائيات المخزون
 * @param {Object} data - بيانات المخزون
 * @returns {Object} - إحصائيات المخزون
 */
function calculateInventoryStats(data) {
  const { inventory } = data;

  // التحقق من وجود المخزون
  if (!inventory || !Array.isArray(inventory) || inventory.length === 0) {
    return {
      totalItems: 0,
      totalValue: 0,
      lowStockItems: 0,
      outOfStockItems: 0
    };
  }

  // حساب إجمالي قيمة المخزون
  const totalValue = inventory.reduce((sum, item) => {
    return sum + (item.current_quantity * item.avg_price);
  }, 0);

  // حساب عدد الأصناف منخفضة المخزون
  const lowStockItems = inventory.filter(item => {
    return (
      item.current_quantity > 0 &&
      item.current_quantity <= (item.minimum_quantity || 5)
    );
  }).length;

  // حساب عدد الأصناف التي نفدت من المخزون
  const outOfStockItems = inventory.filter(item => {
    return item.current_quantity <= 0;
  }).length;

  return {
    totalItems: inventory.length,
    totalValue,
    lowStockItems,
    outOfStockItems
  };
}

/**
 * حساب إحصائيات المعاملات
 * @param {Object} data - بيانات المعاملات
 * @returns {Object} - إحصائيات المعاملات
 */
function calculateTransactionStats(data) {
  const { transactions, dateRange, startDate, endDate } = data;

  // التحقق من وجود المعاملات
  if (!transactions || !Array.isArray(transactions) || transactions.length === 0) {
    return {
      purchases: { count: 0, value: 0 },
      sales: { count: 0, value: 0 },
      receiving: { count: 0, value: 0 },
      dispensing: { count: 0, value: 0 }
    };
  }

  // تصفية المعاملات حسب النطاق الزمني
  const filteredTransactions = filterTransactionsByDateRange(transactions, dateRange, startDate, endDate);

  // تصنيف المعاملات حسب النوع
  const purchaseTransactions = filteredTransactions.filter(t => t.transaction_type === 'purchase');
  const salesTransactions = filteredTransactions.filter(t => t.transaction_type === 'sale');
  const receivingTransactions = filteredTransactions.filter(t => t.transaction_type === 'receiving');
  const dispensingTransactions = filteredTransactions.filter(t => t.transaction_type === 'dispensing');

  // حساب إحصائيات المشتريات
  const purchasesCount = purchaseTransactions.length;
  const purchasesValue = purchaseTransactions.reduce((sum, t) => sum + (t.total_price || 0), 0);

  // حساب إحصائيات المبيعات
  const salesCount = salesTransactions.length;
  const salesValue = salesTransactions.reduce((sum, t) => sum + (t.total_price || 0), 0);

  // حساب إحصائيات الاستلام
  const receivingCount = receivingTransactions.length;
  const receivingValue = receivingTransactions.reduce((sum, t) => sum + (t.total_price || 0), 0);

  // حساب إحصائيات الصرف
  const dispensingCount = dispensingTransactions.length;
  const dispensingValue = dispensingTransactions.reduce((sum, t) => sum + (t.total_price || 0), 0);

  return {
    purchases: { count: purchasesCount, value: purchasesValue },
    sales: { count: salesCount, value: salesValue },
    receiving: { count: receivingCount, value: receivingValue },
    dispensing: { count: dispensingCount, value: dispensingValue }
  };
}

/**
 * حساب الأصناف الأكثر مبيعًا
 * @param {Object} data - بيانات المعاملات والأصناف
 * @returns {Array} - قائمة الأصناف الأكثر مبيعًا
 */
function calculateTopSellingItems(data) {
  const { transactions, items, limit = 5 } = data;

  // التحقق من وجود المعاملات والأصناف
  if (!transactions || !Array.isArray(transactions) || transactions.length === 0 ||
      !items || !Array.isArray(items) || items.length === 0) {
    return [];
  }

  // تصفية معاملات البيع
  const salesTransactions = transactions.filter(t => t.transaction_type === 'sale');

  // تجميع المبيعات حسب الصنف
  const itemSales = {};

  salesTransactions.forEach(transaction => {
    const itemId = transaction.item_id;

    if (!itemSales[itemId]) {
      itemSales[itemId] = {
        item_id: itemId,
        quantity: 0,
        total_sales: 0
      };
    }

    itemSales[itemId].quantity += transaction.quantity || 0;
    itemSales[itemId].total_sales += transaction.total_price || 0;
  });

  // تحويل البيانات إلى مصفوفة
  const itemSalesArray = Object.values(itemSales);

  // إضافة معلومات الصنف
  itemSalesArray.forEach(itemSale => {
    const item = items.find(i => i.id === itemSale.item_id || i._id === itemSale.item_id);

    if (item) {
      itemSale.name = item.name;
      itemSale.unit = item.unit;
    }
  });

  // ترتيب الأصناف حسب الكمية المباعة
  itemSalesArray.sort((a, b) => b.quantity - a.quantity);

  // إرجاع الأصناف الأكثر مبيعًا
  return itemSalesArray.slice(0, limit);
}

/**
 * حساب الأصناف الأكثر ربحًا
 * @param {Object} data - بيانات المعاملات والأصناف
 * @returns {Array} - قائمة الأصناف الأكثر ربحًا
 */
function calculateMostProfitableItems(data) {
  const { transactions, items, limit = 5 } = data;

  // التحقق من وجود المعاملات والأصناف
  if (!transactions || !Array.isArray(transactions) || transactions.length === 0 ||
      !items || !Array.isArray(items) || items.length === 0) {
    return [];
  }

  // تصفية معاملات البيع
  const salesTransactions = transactions.filter(t => t.transaction_type === 'sale');

  // تجميع الأرباح حسب الصنف
  const itemProfits = {};

  salesTransactions.forEach(transaction => {
    const itemId = transaction.item_id;

    if (!itemProfits[itemId]) {
      itemProfits[itemId] = {
        item_id: itemId,
        total_profit: 0,
        total_sales: 0
      };
    }

    itemProfits[itemId].total_profit += transaction.profit || 0;
    itemProfits[itemId].total_sales += transaction.total_price || 0;
  });

  // تحويل البيانات إلى مصفوفة
  const itemProfitsArray = Object.values(itemProfits);

  // إضافة معلومات الصنف
  itemProfitsArray.forEach(itemProfit => {
    const item = items.find(i => i.id === itemProfit.item_id || i._id === itemProfit.item_id);

    if (item) {
      itemProfit.name = item.name;
      itemProfit.unit = item.unit;
    }
  });

  // ترتيب الأصناف حسب الربح
  itemProfitsArray.sort((a, b) => b.total_profit - a.total_profit);

  // إرجاع الأصناف الأكثر ربحًا
  return itemProfitsArray.slice(0, limit);
}

/**
 * تصفية المعاملات
 * @param {Object} data - بيانات المعاملات والفلاتر
 * @returns {Array} - قائمة المعاملات المصفاة
 */
function filterTransactions(data) {
  const { transactions, filters } = data;

  // التحقق من وجود المعاملات
  if (!transactions || !Array.isArray(transactions) || transactions.length === 0) {
    return [];
  }

  // التحقق من وجود الفلاتر
  if (!filters) {
    return transactions;
  }

  // تصفية المعاملات حسب الفلاتر
  return transactions.filter(transaction => {
    // تصفية حسب نوع المعاملة
    if (filters.transactionType && filters.transactionType !== 'all' &&
        transaction.transaction_type !== filters.transactionType) {
      return false;
    }

    // تصفية حسب الصنف
    if (filters.itemId && transaction.item_id !== filters.itemId) {
      return false;
    }

    // تصفية حسب العميل
    if (filters.customerId && transaction.customer !== filters.customerId) {
      return false;
    }

    // تصفية حسب المورد
    // تم إزالة فلتر الموردين

    // تصفية حسب النطاق الزمني
    if (filters.dateRange) {
      const transactionDate = new Date(transaction.transaction_date);

      if (filters.dateRange === 'custom') {
        // نطاق زمني مخصص
        if (filters.startDate && new Date(filters.startDate) > transactionDate) {
          return false;
        }

        if (filters.endDate) {
          const endDate = new Date(filters.endDate);
          endDate.setHours(23, 59, 59, 999); // نهاية اليوم

          if (endDate < transactionDate) {
            return false;
          }
        }
      } else {
        // نطاق زمني محدد مسبقًا
        const now = new Date();
        let startDate;

        switch (filters.dateRange) {
          case 'today':
            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            break;
          case 'yesterday':
            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1);
            const endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            return transactionDate >= startDate && transactionDate < endDate;
          case 'thisWeek':
            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay());
            break;
          case 'thisMonth':
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
            break;
          case 'lastMonth':
            startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
            const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);
            return transactionDate >= startDate && transactionDate <= endOfLastMonth;
          case 'thisYear':
            startDate = new Date(now.getFullYear(), 0, 1);
            break;
          default:
            startDate = null;
        }

        if (startDate && transactionDate < startDate) {
          return false;
        }
      }
    }

    // تصفية حسب البحث
    if (filters.searchTerm && filters.searchTerm.trim() !== '') {
      const searchTerm = filters.searchTerm.trim().toLowerCase();
      const itemName = (transaction.item_name || '').toLowerCase();
      const notes = (transaction.notes || '').toLowerCase();
      const invoiceNumber = (transaction.invoice_number || '').toLowerCase();

      if (!itemName.includes(searchTerm) &&
          !notes.includes(searchTerm) &&
          !invoiceNumber.includes(searchTerm)) {
        return false;
      }
    }

    return true;
  });
}

/**
 * تصفية المعاملات حسب النطاق الزمني
 * @param {Array} transactions - قائمة المعاملات
 * @param {String} dateRange - النطاق الزمني
 * @param {String} startDate - تاريخ البداية
 * @param {String} endDate - تاريخ النهاية
 * @returns {Array} - قائمة المعاملات المصفاة
 */
function filterTransactionsByDateRange(transactions, dateRange, startDate, endDate) {
  if (!dateRange || dateRange === 'all') {
    return transactions;
  }

  return transactions.filter(transaction => {
    const transactionDate = new Date(transaction.transaction_date);

    if (dateRange === 'custom') {
      // نطاق زمني مخصص
      if (startDate && new Date(startDate) > transactionDate) {
        return false;
      }

      if (endDate) {
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999); // نهاية اليوم

        if (endDateTime < transactionDate) {
          return false;
        }
      }
    } else {
      // نطاق زمني محدد مسبقًا
      const now = new Date();
      let startDateTime;

      switch (dateRange) {
        case 'today':
          startDateTime = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case 'yesterday':
          startDateTime = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1);
          const endDateTime = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          return transactionDate >= startDateTime && transactionDate < endDateTime;
        case 'thisWeek':
          startDateTime = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay());
          break;
        case 'thisMonth':
          startDateTime = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        case 'lastMonth':
          startDateTime = new Date(now.getFullYear(), now.getMonth() - 1, 1);
          const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);
          return transactionDate >= startDateTime && transactionDate <= endOfLastMonth;
        case 'thisYear':
          startDateTime = new Date(now.getFullYear(), 0, 1);
          break;
        default:
          startDateTime = null;
      }

      if (startDateTime && transactionDate < startDateTime) {
        return false;
      }
    }

    return true;
  });
}
