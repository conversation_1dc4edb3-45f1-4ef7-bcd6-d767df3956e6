import React, { createContext, useState, useContext, useEffect } from 'react';

const AuthContext = createContext();

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }) => {
  // تعريف متغيرات الحالة
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if user is already logged in (from localStorage)
    const storedUser = localStorage.getItem('wms_user');
    if (storedUser) {
      try {
        const user = JSON.parse(storedUser);
        setCurrentUser(user);

        // تخزين معرف المستخدم ودوره في التخزين المحلي للاستخدام في التحقق من الصلاحيات
        // تحديث القيم دائمًا للتأكد من تطابقها
        localStorage.setItem('currentUserId', user.id);
        localStorage.setItem('currentUserRole', user.role);
        localStorage.setItem('currentUserName', user.username);

        console.log('User loaded from localStorage:', user);
        console.log('User role set in localStorage:', user.role);
      } catch (error) {
        console.error('Error parsing stored user:', error);
        localStorage.removeItem('wms_user');
        localStorage.removeItem('currentUserId');
        localStorage.removeItem('currentUserRole');
        localStorage.removeItem('currentUserName');
      }
    }
    setLoading(false);
  }, []);

  const login = async (username, password) => {
    try {
      let result;

      try {
        // محاولة استخدام واجهة المستخدمين المباشرة
        if (window.api.users && typeof window.api.users.login === 'function') {
          result = await window.api.users.login({ username, password });
          console.log('تم استخدام window.api.users.login بنجاح');
        } else {
          // استخدام واجهة invoke العامة
          result = await window.api.invoke('login', { username, password });
          console.log('تم استخدام window.api.invoke("login") بنجاح');
        }
      } catch (apiError) {
        console.error('خطأ في استدعاء API لتسجيل الدخول:', apiError);
        throw apiError;
      }

      if (result && result.success) {
        setCurrentUser(result.user);

        // تخزين معلومات المستخدم في التخزين المحلي
        localStorage.setItem('wms_user', JSON.stringify(result.user));

        // تخزين معرف المستخدم ودوره في التخزين المحلي للاستخدام في التحقق من الصلاحيات
        localStorage.setItem('currentUserId', result.user.id);
        localStorage.setItem('currentUserRole', result.user.role);
        localStorage.setItem('currentUserName', result.user.username);

        console.log('تم تسجيل الدخول بنجاح:', {
          id: result.user.id,
          username: result.user.username,
          role: result.user.role
        });

        return { success: true };
      } else {
        return { success: false, message: result ? result.message : 'فشل تسجيل الدخول' };
      }
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, message: 'حدث خطأ أثناء تسجيل الدخول: ' + (error.message || 'خطأ غير معروف') };
    }
  };

  const logout = () => {
    setCurrentUser(null);

    // إزالة جميع معلومات المستخدم من التخزين المحلي
    localStorage.removeItem('wms_user');
    localStorage.removeItem('currentUserId');
    localStorage.removeItem('currentUserRole');
    localStorage.removeItem('currentUserName');

    console.log('تم تسجيل الخروج بنجاح');
  };

  const value = {
    currentUser,
    isAuthenticated: !!currentUser,
    login,
    logout,
    loading
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
