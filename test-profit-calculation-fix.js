/**
 * اختبار إصلاح حساب الأرباح مع مصاريف النقل
 * هذا السكريبت يختبر أن مصاريف النقل في المشتريات لا تؤثر على الأرباح
 */

const Database = require('better-sqlite3');
const path = require('path');

// مسار قاعدة البيانات
const dbPath = path.join(process.env.APPDATA || process.env.HOME, 'warehouse-management-system', 'wms-database.db');

console.log('🧪 اختبار إصلاح حساب الأرباح مع مصاريف النقل');
console.log('=' .repeat(60));

async function testProfitCalculationFix() {
  let db;
  
  try {
    // الاتصال بقاعدة البيانات
    db = new Database(dbPath);
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // 1. فحص حالة الخزينة قبل الاختبار
    console.log('\n📊 حالة الخزينة قبل الاختبار:');
    const cashboxBefore = db.prepare('SELECT * FROM cashbox WHERE id = 1').get();
    if (cashboxBefore) {
      console.log(`   الرصيد الحالي: ${cashboxBefore.current_balance} د.ل`);
      console.log(`   إجمالي المبيعات: ${cashboxBefore.sales_total} د.ل`);
      console.log(`   إجمالي المشتريات: ${cashboxBefore.purchases_total} د.ل`);
      console.log(`   إجمالي مصاريف النقل: ${cashboxBefore.transport_total || 0} د.ل`);
      console.log(`   إجمالي الأرباح: ${cashboxBefore.profit_total} د.ل`);
    }

    // 2. اختبار دالة إعادة حساب الأرباح الجديدة
    console.log('\n🔄 اختبار دالة إعادة حساب الأرباح الجديدة...');
    
    // استيراد مدير المعاملات
    const transactionManager = require('./unified-transaction-manager');
    
    // تهيئة مدير المعاملات
    const initResult = transactionManager.initialize();
    if (!initResult) {
      throw new Error('فشل في تهيئة مدير المعاملات');
    }
    
    // حساب الأرباح باستخدام الدالة الجديدة
    const calculatedProfit = transactionManager.recalculateTotalProfits();
    console.log(`   الأرباح المحسوبة بالدالة الجديدة: ${calculatedProfit} د.ل`);

    // 3. مقارنة مع الأرباح المحفوظة في قاعدة البيانات
    console.log('\n📈 مقارنة النتائج:');
    const currentStoredProfit = cashboxBefore ? cashboxBefore.profit_total : 0;
    console.log(`   الأرباح المحفوظة حالياً: ${currentStoredProfit} د.ل`);
    console.log(`   الأرباح المحسوبة الجديدة: ${calculatedProfit} د.ل`);
    console.log(`   الفرق: ${calculatedProfit - currentStoredProfit} د.ل`);

    // 4. تحديث الأرباح في قاعدة البيانات
    console.log('\n💾 تحديث الأرباح في قاعدة البيانات...');
    const updateSuccess = transactionManager.updateProfitTotalInDatabase(calculatedProfit);
    
    if (updateSuccess) {
      console.log('   ✅ تم تحديث الأرباح بنجاح');
      
      // التحقق من التحديث
      const cashboxAfter = db.prepare('SELECT * FROM cashbox WHERE id = 1').get();
      console.log(`   الأرباح بعد التحديث: ${cashboxAfter.profit_total} د.ل`);
    } else {
      console.log('   ❌ فشل في تحديث الأرباح');
    }

    // 5. اختبار سيناريو شراء مع مصاريف نقل
    console.log('\n🛒 اختبار سيناريو شراء مع مصاريف نقل...');
    
    // الحصول على صنف للاختبار
    const testItem = db.prepare('SELECT * FROM items LIMIT 1').get();
    if (!testItem) {
      console.log('   ⚠️  لا توجد أصناف للاختبار');
      return;
    }

    console.log(`   اختبار مع الصنف: ${testItem.name} (ID: ${testItem.id})`);

    // محاكاة عملية شراء مع مصاريف نقل
    const testPurchase = {
      item_id: testItem.id,
      transaction_type: 'purchase',
      quantity: 10,
      price: 100,
      transport_cost: 50,
      notes: 'اختبار مصاريف النقل',
      user_id: 1,
      skip_inventory_update: true // لتجنب تأثير على المخزون الفعلي
    };

    console.log('   محاكاة عملية شراء:');
    console.log(`     الكمية: ${testPurchase.quantity}`);
    console.log(`     سعر الوحدة: ${testPurchase.price} د.ل`);
    console.log(`     مصاريف النقل: ${testPurchase.transport_cost} د.ل`);
    console.log(`     إجمالي التكلفة: ${testPurchase.quantity * testPurchase.price + testPurchase.transport_cost} د.ل`);

    // حفظ حالة الأرباح قبل العملية
    const profitBeforePurchase = db.prepare('SELECT profit_total FROM cashbox WHERE id = 1').get().profit_total;

    // تنفيذ عملية الشراء
    try {
      const purchaseResult = await transactionManager.createTransaction(testPurchase);
      
      if (purchaseResult.success) {
        console.log('   ✅ تم تنفيذ عملية الشراء بنجاح');
        
        // فحص تأثير العملية على الأرباح
        const profitAfterPurchase = db.prepare('SELECT profit_total FROM cashbox WHERE id = 1').get().profit_total;
        
        console.log(`   الأرباح قبل الشراء: ${profitBeforePurchase} د.ل`);
        console.log(`   الأرباح بعد الشراء: ${profitAfterPurchase} د.ل`);
        console.log(`   التغيير في الأرباح: ${profitAfterPurchase - profitBeforePurchase} د.ل`);
        
        if (Math.abs(profitAfterPurchase - profitBeforePurchase) < 0.01) {
          console.log('   ✅ ممتاز! مصاريف النقل في الشراء لم تؤثر على الأرباح');
        } else {
          console.log('   ❌ خطأ! مصاريف النقل في الشراء أثرت على الأرباح');
        }
        
        // حذف المعاملة التجريبية
        if (purchaseResult.transaction && purchaseResult.transaction.id) {
          db.prepare('DELETE FROM transactions WHERE id = ?').run(purchaseResult.transaction.id);
          console.log('   🗑️  تم حذف المعاملة التجريبية');
        }
        
      } else {
        console.log('   ❌ فشل في تنفيذ عملية الشراء');
      }
    } catch (purchaseError) {
      console.log(`   ❌ خطأ في تنفيذ عملية الشراء: ${purchaseError.message}`);
    }

    console.log('\n🎉 انتهى الاختبار بنجاح!');
    console.log('=' .repeat(60));

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error);
  } finally {
    if (db) {
      db.close();
      console.log('🔒 تم إغلاق الاتصال بقاعدة البيانات');
    }
  }
}

// تشغيل الاختبار
testProfitCalculationFix().catch(console.error);
