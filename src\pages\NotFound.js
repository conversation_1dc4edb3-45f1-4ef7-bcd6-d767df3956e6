import React from 'react';
import { Link } from 'react-router-dom';
import { FaHome, FaExclamationTriangle } from 'react-icons/fa';

const NotFound = () => {
  return (
    <div className="text-center p-5">
      <div className="mb-4" style={{ fontSize: '5rem', color: '#e74c3c' }}>
        <FaExclamationTriangle />
      </div>
      
      <h1 className="mb-4" style={{ fontSize: '2.5rem' }}>404</h1>
      <h2 className="mb-4">الصفحة غير موجودة</h2>
      
      <p className="mb-4">
        عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها.
      </p>
      
      <Link to="/" className="btn btn-primary">
        <FaHome className="ml-1" />
        العودة إلى الصفحة الرئيسية
      </Link>
    </div>
  );
};

export default NotFound;
