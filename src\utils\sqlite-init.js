/**
 * سكريبت تهيئة قاعدة بيانات SQLite
 *
 * هذا السكريبت يقوم بتهيئة قاعدة بيانات SQLite وإنشاء الجداول والفهارس اللازمة.
 * يجب تشغيل هذا السكريبت قبل استخدام قاعدة بيانات SQLite.
 */

const path = require('path');
const fs = require('fs');
const { app } = require('electron');

// محاولة استيراد مكتبة better-sqlite3
let Database;
try {
  Database = require('better-sqlite3');
} catch (error) {
  console.error('خطأ في استيراد مكتبة better-sqlite3:', error.message);
  console.error('يرجى تثبيت المكتبة باستخدام الأمر: npm install better-sqlite3');
  process.exit(1);
}

// تحديد مسار قاعدة البيانات
const dbDir = path.join(app.getPath('userData'), 'wms-database');
const dbPath = path.join(dbDir, 'warehouse.db');

// التأكد من وجود المجلد
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

console.log('مسار قاعدة البيانات:', dbPath);

// إنشاء اتصال بقاعدة البيانات
const db = new Database(dbPath, {
  verbose: console.log
});

// تمكين الدعم للمفاتيح الأجنبية
db.pragma('foreign_keys = ON');

// إنشاء جدول المستخدمين
db.exec(`
  CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    full_name TEXT,
    role TEXT NOT NULL,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
  )
`);

// إنشاء جدول الأصناف
db.exec(`
  CREATE TABLE IF NOT EXISTS items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    unit TEXT,
    minimum_quantity INTEGER DEFAULT 0,
    avg_price REAL DEFAULT 0,
    selling_price REAL DEFAULT 0,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
  )
`);

// إنشاء جدول المخزون
db.exec(`
  CREATE TABLE IF NOT EXISTS inventory (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    item_id INTEGER NOT NULL,
    current_quantity INTEGER DEFAULT 0,
    last_updated TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (item_id) REFERENCES items (id)
  )
`);

// إنشاء جدول المعاملات
db.exec(`
  CREATE TABLE IF NOT EXISTS transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    transaction_id TEXT UNIQUE NOT NULL,
    item_id INTEGER,
    transaction_type TEXT NOT NULL,
    quantity INTEGER NOT NULL,
    price REAL DEFAULT 0,
    total_price REAL DEFAULT 0,
    profit REAL DEFAULT 0,
    customer TEXT,
    invoice_number TEXT,
    notes TEXT,
    transaction_date TEXT DEFAULT CURRENT_TIMESTAMP,
    user_id INTEGER,
    FOREIGN KEY (item_id) REFERENCES items (id),
    FOREIGN KEY (user_id) REFERENCES users (id)
  )
`);

// إنشاء جدول العملاء
db.exec(`
  CREATE TABLE IF NOT EXISTS customers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    contact_person TEXT,
    phone TEXT,
    email TEXT,
    address TEXT,
    customer_type TEXT DEFAULT 'normal',
    parent_id INTEGER,
    credit_limit REAL DEFAULT 0,
    balance REAL DEFAULT 0,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
  )
`);

// إنشاء جدول الآلات
db.exec(`
  CREATE TABLE IF NOT EXISTS machines (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    purchase_date TEXT NOT NULL,
    purchase_price REAL NOT NULL,
    current_value REAL NOT NULL,
    notes TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
  )
`);

// إنشاء جدول الإعدادات
db.exec(`
  CREATE TABLE IF NOT EXISTS settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    key TEXT UNIQUE NOT NULL,
    value TEXT,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
  )
`);

// إنشاء جدول الخزينة
db.exec(`
  CREATE TABLE IF NOT EXISTS cashbox (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    initial_balance REAL NOT NULL,
    current_balance REAL NOT NULL,
    profit_total REAL DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )
`);

// إنشاء جدول معاملات الخزينة
db.exec(`
  CREATE TABLE IF NOT EXISTS cashbox_transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    type TEXT NOT NULL,
    amount REAL NOT NULL,
    source TEXT NOT NULL,
    notes TEXT,
    user_id INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id)
  )
`);

// إنشاء فهارس للأصناف
db.exec('CREATE INDEX IF NOT EXISTS idx_items_name ON items(name)');

// إنشاء فهارس للمخزون
db.exec('CREATE INDEX IF NOT EXISTS idx_inventory_item_id ON inventory(item_id)');

// إنشاء فهارس للمعاملات
db.exec('CREATE INDEX IF NOT EXISTS idx_transactions_transaction_id ON transactions(transaction_id)');
db.exec('CREATE INDEX IF NOT EXISTS idx_transactions_item_id ON transactions(item_id)');
db.exec('CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(transaction_type)');
db.exec('CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(transaction_date)');
db.exec('CREATE INDEX IF NOT EXISTS idx_transactions_customer ON transactions(customer)');
db.exec('CREATE INDEX IF NOT EXISTS idx_transactions_invoice ON transactions(invoice_number)');

// إنشاء فهارس للعملاء
db.exec('CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(name)');
db.exec('CREATE INDEX IF NOT EXISTS idx_customers_type ON customers(customer_type)');
db.exec('CREATE INDEX IF NOT EXISTS idx_customers_parent ON customers(parent_id)');

// إنشاء فهارس للآلات
db.exec('CREATE INDEX IF NOT EXISTS idx_machines_name ON machines(name)');

// إنشاء فهارس للإعدادات
db.exec('CREATE INDEX IF NOT EXISTS idx_settings_key ON settings(key)');

// إنشاء فهارس للخزينة
db.exec('CREATE INDEX IF NOT EXISTS idx_cashbox_transactions_type ON cashbox_transactions(type)');
db.exec('CREATE INDEX IF NOT EXISTS idx_cashbox_transactions_created_at ON cashbox_transactions(created_at)');

// إغلاق اتصال قاعدة البيانات
db.close();

console.log('تم تهيئة قاعدة بيانات SQLite بنجاح');
