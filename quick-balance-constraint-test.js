/**
 * اختبار سريع لقيد الرصيد الحالي في عمليات البيع
 * 
 * هذا السكريبت يقوم باختبار سريع للتأكد من أن الرصيد الحالي
 * لا يتجاوز الرصيد الافتتاحي أثناء عمليات البيع
 */

console.log('⚡ اختبار سريع لقيد الرصيد الحالي في عمليات البيع...');

// التحقق من وجود window.api
if (typeof window !== 'undefined' && window.api) {
  
  // اختبار سريع
  async function quickBalanceConstraintTest() {
    try {
      console.log('🚀 بدء الاختبار السريع...');
      
      // 1. تعيين رصيد ابتدائي
      console.log('\n1️⃣ تعيين رصيد ابتدائي (3000)...');
      const initialResult = await window.api.cashbox.updateInitialBalance(3000);
      if (initialResult.success) {
        console.log(`✅ الرصيد الابتدائي: ${initialResult.cashbox.initial_balance}`);
        console.log(`✅ الرصيد الحالي: ${initialResult.cashbox.current_balance}`);
      } else {
        console.error('❌ فشل في تعيين الرصيد الابتدائي');
        return;
      }
      
      // 2. اختبار عملية بيع تتجاوز الرصيد الافتتاحي
      console.log('\n2️⃣ اختبار عملية بيع تتجاوز الرصيد الافتتاحي (مبلغ 5000)...');
      const beforeSale = await window.api.cashbox.getCashbox();
      console.log(`📊 الرصيد قبل البيع: ${beforeSale.current_balance}`);
      console.log(`📊 الرصيد الافتتاحي: ${beforeSale.initial_balance}`);
      
      const saleResult = await window.api.cashbox.addTransaction({
        type: 'sale',
        amount: 5000,
        source: 'test',
        notes: 'اختبار بيع يتجاوز الرصيد الافتتاحي'
      });
      
      if (saleResult.success) {
        console.log(`📊 الرصيد بعد البيع: ${saleResult.cashbox.current_balance}`);
        console.log(`📊 الرصيد الافتتاحي: ${saleResult.cashbox.initial_balance}`);
        console.log(`📊 إجمالي المبيعات: ${saleResult.cashbox.sales_total}`);
        console.log(`📊 إجمالي الأرباح: ${saleResult.cashbox.profit_total}`);
        
        // التحقق من أن الرصيد الحالي لا يتجاوز الرصيد الافتتاحي
        if (saleResult.cashbox.current_balance <= saleResult.cashbox.initial_balance) {
          console.log('✅ الرصيد الحالي لا يتجاوز الرصيد الافتتاحي (القيد يعمل)!');
        } else {
          console.error(`❌ الرصيد الحالي (${saleResult.cashbox.current_balance}) يتجاوز الرصيد الافتتاحي (${saleResult.cashbox.initial_balance})!`);
          console.error('❌ القيد لا يعمل في الوقت الفعلي!');
        }
        
        // التحقق من أن إجمالي المبيعات تم تسجيله بشكل صحيح
        if (saleResult.cashbox.sales_total === 5000) {
          console.log('✅ إجمالي المبيعات تم تسجيله بشكل صحيح');
        } else {
          console.error(`❌ خطأ في إجمالي المبيعات! المتوقع: 5000، الفعلي: ${saleResult.cashbox.sales_total}`);
        }
        
        // التحقق من أن الأرباح تم تحديثها (المبلغ الزائد ذهب للأرباح)
        if (saleResult.cashbox.profit_total === 5000) {
          console.log('✅ الأرباح تم تحديثها بشكل صحيح (المبلغ الزائد ذهب للأرباح)');
        } else {
          console.error(`❌ خطأ في حساب الأرباح! المتوقع: 5000، الفعلي: ${saleResult.cashbox.profit_total}`);
        }
      } else {
        console.error('❌ فشل في عملية البيع');
        return;
      }
      
      // 3. اختبار عملية بيع أخرى
      console.log('\n3️⃣ اختبار عملية بيع أخرى (مبلغ 2000)...');
      const beforeSecondSale = await window.api.cashbox.getCashbox();
      console.log(`📊 الرصيد قبل البيع الثاني: ${beforeSecondSale.current_balance}`);
      
      const secondSaleResult = await window.api.cashbox.addTransaction({
        type: 'sale',
        amount: 2000,
        source: 'test',
        notes: 'اختبار بيع ثاني'
      });
      
      if (secondSaleResult.success) {
        console.log(`📊 الرصيد بعد البيع الثاني: ${secondSaleResult.cashbox.current_balance}`);
        console.log(`📊 الرصيد الافتتاحي: ${secondSaleResult.cashbox.initial_balance}`);
        console.log(`📊 إجمالي المبيعات: ${secondSaleResult.cashbox.sales_total}`);
        console.log(`📊 إجمالي الأرباح: ${secondSaleResult.cashbox.profit_total}`);
        
        // التحقق من أن الرصيد الحالي لا يزال لا يتجاوز الرصيد الافتتاحي
        if (secondSaleResult.cashbox.current_balance <= secondSaleResult.cashbox.initial_balance) {
          console.log('✅ الرصيد الحالي لا يزال لا يتجاوز الرصيد الافتتاحي (القيد يعمل باستمرار)!');
        } else {
          console.error(`❌ الرصيد الحالي (${secondSaleResult.cashbox.current_balance}) يتجاوز الرصيد الافتتاحي (${secondSaleResult.cashbox.initial_balance})!`);
        }
        
        // التحقق من أن إجمالي المبيعات تم تحديثه
        const expectedSalesTotal = 5000 + 2000; // 7000
        if (secondSaleResult.cashbox.sales_total === expectedSalesTotal) {
          console.log('✅ إجمالي المبيعات تم تحديثه بشكل صحيح');
        } else {
          console.error(`❌ خطأ في إجمالي المبيعات! المتوقع: ${expectedSalesTotal}، الفعلي: ${secondSaleResult.cashbox.sales_total}`);
        }
        
        // التحقق من أن الأرباح تم تحديثها
        const expectedProfitTotal = 7000; // إجمالي المبيعات - إجمالي المشتريات = 7000 - 0 = 7000
        if (secondSaleResult.cashbox.profit_total === expectedProfitTotal) {
          console.log('✅ الأرباح تم تحديثها بشكل صحيح');
        } else {
          console.error(`❌ خطأ في حساب الأرباح! المتوقع: ${expectedProfitTotal}، الفعلي: ${secondSaleResult.cashbox.profit_total}`);
        }
      } else {
        console.error('❌ فشل في عملية البيع الثانية');
        return;
      }
      
      // 4. النتيجة النهائية
      console.log('\n📊 النتيجة النهائية:');
      const finalCashbox = await window.api.cashbox.getCashbox();
      console.log(`الرصيد الابتدائي: ${finalCashbox.initial_balance}`);
      console.log(`الرصيد الحالي: ${finalCashbox.current_balance}`);
      console.log(`إجمالي المبيعات: ${finalCashbox.sales_total}`);
      console.log(`إجمالي الأرباح: ${finalCashbox.profit_total}`);
      
      // التحقق من صحة الحسابات النهائية
      const expectedFinalValues = {
        initial_balance: 3000,
        current_balance: 3000, // يجب أن يبقى مساوياً للرصيد الافتتاحي
        sales_total: 7000,
        profit_total: 7000
      };
      
      let allCorrect = true;
      for (const [key, expectedValue] of Object.entries(expectedFinalValues)) {
        const actualValue = finalCashbox[key];
        if (actualValue !== expectedValue) {
          allCorrect = false;
          console.error(`❌ خطأ في ${key}! المتوقع: ${expectedValue}، الفعلي: ${actualValue}`);
        }
      }
      
      // التحقق من القيد الأساسي
      const constraintWorking = finalCashbox.current_balance <= finalCashbox.initial_balance;
      
      if (allCorrect && constraintWorking) {
        console.log('\n🎉 الاختبار السريع نجح! قيد الرصيد الحالي يعمل بشكل صحيح');
        console.log('✅ الرصيد الحالي لا يتجاوز الرصيد الافتتاحي أثناء عمليات البيع');
        console.log('✅ المبلغ الزائد يذهب تلقائياً إلى الأرباح');
        console.log('✅ القيد يعمل في الوقت الفعلي');
      } else {
        console.log('\n⚠️ هناك مشكلة في القيد. يرجى مراجعة الإصلاحات');
      }
      
    } catch (error) {
      console.error('❌ خطأ في الاختبار السريع:', error);
    }
  }
  
  // تشغيل الاختبار
  quickBalanceConstraintTest();
  
} else {
  console.error('❌ window.api غير متوفر');
  console.log('💡 يجب تشغيل هذا السكريبت من داخل التطبيق');
  console.log('');
  console.log('📋 لتشغيل الاختبار السريع:');
  console.log('1. افتح التطبيق');
  console.log('2. افتح وحدة التحكم (F12)');
  console.log('3. انسخ والصق الكود التالي:');
  console.log('');
  console.log('// اختبار سريع لقيد الرصيد الحالي');
  console.log('(async () => {');
  console.log('  const initial = await window.api.cashbox.updateInitialBalance(3000);');
  console.log('  console.log("رصيد ابتدائي:", initial.cashbox.current_balance);');
  console.log('  ');
  console.log('  const sale = await window.api.cashbox.addTransaction({type: "sale", amount: 5000, source: "test"});');
  console.log('  console.log("بعد البيع:", "رصيد حالي:", sale.cashbox.current_balance, "رصيد ابتدائي:", sale.cashbox.initial_balance);');
  console.log('  console.log("القيد يعمل:", sale.cashbox.current_balance <= sale.cashbox.initial_balance ? "✅" : "❌");');
  console.log('})();');
}
