/* أنماط محسنة لصفحة التقارير */
:root {
  --primary-color: #1a3a5f;
  --primary-light: #2c5282;
  --primary-dark: #0f2942;
  --primary-rgb: 26, 58, 95;
  --accent-color: #3498db;
  --accent-light: #5dade2;
  --accent-dark: #2980b9;
  --accent-rgb: 52, 152, 219;
  --success-color: #27ae60;
  --success-rgb: 39, 174, 96;
  --warning-color: #f39c12;
  --warning-rgb: 243, 156, 18;
  --danger-color: #e74c3c;
  --danger-rgb: 231, 76, 60;
  --info-color: #3498db;
  --info-rgb: 52, 152, 219;
  --text-dark: #2d3748;
  --text-light: #718096;
  --bg-light: #f8f9fa;
  --bg-dark: #2d3748;
  --border-color: #e2e8f0;
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.08);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* تحسينات عامة للصفحة */
.reports-page {
  padding: 30px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* تحسين العنوان الرئيسي */
.reports-header {
  margin-bottom: 40px;
  text-align: center;
  position: relative;
  padding-bottom: 20px;
}

.reports-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 4px;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
  border-radius: 2px;
}

.reports-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 15px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.reports-header p {
  font-size: 1.1rem;
  color: var(--text-light);
  max-width: 800px;
  margin: 0 auto;
}

/* تحسين أقسام التقارير */
.reports-sections {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 40px;
}

.report-section {
  background-color: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  padding: 25px;
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
  position: relative;
  overflow: hidden;
}

/* قسم التقرير بعرض كامل */
.report-section.full-width {
  grid-column: 1 / -1;
  background-color: rgba(var(--primary-rgb), 0.03);
  border: 2px dashed rgba(var(--primary-rgb), 0.2);
}

.report-section:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.report-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
}

.report-section h2 {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
  position: relative;
}

/* تحسين أزرار التقارير */
.reports-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.report-button {
  display: flex;
  align-items: center;
  padding: 20px;
  background-color: white;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
  border: 1px solid var(--border-color);
  cursor: pointer;
  text-decoration: none;
  color: var(--text-dark);
  position: relative;
  overflow: hidden;
}

.report-button:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-light);
}

/* أنماط تقرير المخزون والأصناف المفصل */
.detailed-report-section {
  background-color: white;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  padding: 20px;
  margin-bottom: 30px;
  border: 1px solid var(--border-color);
  position: relative;
}

.detailed-report-section h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
}

.detailed-report-section h3 svg {
  margin-left: 8px;
  color: var(--primary-color);
}

.detailed-report-actions {
  display: flex;
  gap: 15px;
  margin-top: 30px;
  justify-content: center;
}

.detailed-report-actions button {
  min-width: 180px;
}

/* أنماط شريط التنقل بين أقسام التقرير */
.inventory-report-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
  justify-content: center;
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
}

.inventory-tab-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 15px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  background-color: white;
  color: var(--text-color);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.inventory-tab-btn:hover {
  background-color: var(--primary-light);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.inventory-tab-btn svg {
  font-size: 1.2rem;
  color: var(--primary-color);
}

.inventory-tab-btn:hover svg {
  color: white;
}

/* أنماط مؤشرات أداء المخزون */
.inventory-kpi-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}

.inventory-kpi-section h4 {
  font-size: 1.2rem;
  margin-bottom: 15px;
  color: var(--primary-color);
}

.kpi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.kpi-card {
  background-color: #f8f9fa;
  border-radius: var(--border-radius-sm);
  padding: 15px;
  box-shadow: var(--shadow-xs);
}

.kpi-title {
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--text-color);
}

.kpi-value {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 10px;
}

.kpi-progress {
  height: 8px;
  background-color: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.kpi-progress-bar {
  height: 100%;
  border-radius: 4px;
}

/* أنماط ملخص الأصناف التي تحتاج إعادة طلب */
.low-stock-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
}

.low-stock-summary-item {
  flex: 1;
  min-width: 200px;
  display: flex;
  align-items: center;
  gap: 15px;
  background-color: white;
  padding: 15px;
  border-radius: var(--border-radius-sm);
  box-shadow: var(--shadow-sm);
}

.summary-icon {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-light);
  color: white;
  border-radius: 50%;
  font-size: 1.5rem;
}

.summary-icon.danger {
  background-color: var(--danger-color);
}

.summary-icon.warning {
  background-color: var(--warning-color);
}

.summary-details {
  flex: 1;
}

.summary-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-color);
}

.summary-label {
  font-size: 0.9rem;
  color: var(--text-muted);
}

/* أنماط أزرار الإجراءات */
.action-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

/* أنماط تصفية المخزون */
.inventory-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
  align-items: center;
  justify-content: space-between;
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: var(--border-radius-sm);
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-group label {
  font-weight: 600;
  color: var(--text-color);
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  background-color: white;
  color: var(--text-color);
}

/* أنماط تحليل المخزون */
.inventory-analysis-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.analysis-card {
  background-color: white;
  border-radius: var(--border-radius-sm);
  padding: 20px;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

.analysis-card h4 {
  font-size: 1.2rem;
  margin-bottom: 15px;
  color: var(--primary-color);
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
}

/* أنماط الرسم البياني الدائري */
.pie-chart-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.pie-chart-placeholder {
  width: 200px;
  height: 200px;
  margin: 0 auto;
  position: relative;
}

.pie-chart-segments {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
  background-color: #f8f9fa;
}

.pie-segment {
  position: absolute;
  width: 100%;
  height: 100%;
  transform-origin: 50% 100%;
}

.pie-segment.high-value {
  background-color: #4caf50;
}

.pie-segment.medium-value {
  background-color: #2196f3;
}

.pie-segment.low-value {
  background-color: #ff9800;
}

.pie-chart-legend {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.legend-color {
  width: 20px;
  height: 20px;
  border-radius: 4px;
}

.legend-color.high-value {
  background-color: #4caf50;
}

.legend-color.medium-value {
  background-color: #2196f3;
}

.legend-color.low-value {
  background-color: #ff9800;
}

.legend-text {
  flex: 1;
}

.legend-label {
  font-size: 0.9rem;
  color: var(--text-color);
}

.legend-value {
  font-weight: 600;
  color: var(--text-color);
}

/* أنماط تحليل حالة المخزون */
.inventory-status-chart {
  margin-top: 15px;
}

.status-bars {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.status-bar-group {
  display: flex;
  align-items: center;
}

.status-label {
  width: 120px;
  font-weight: 600;
  color: var(--text-color);
}

.status-bar-container {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-bar {
  height: 20px;
  border-radius: 10px;
  min-width: 5px;
  transition: width 0.5s ease;
}

.status-bar.out-of-stock {
  background-color: var(--danger-color);
}

.status-bar.low-stock {
  background-color: var(--warning-color);
}

.status-bar.in-stock {
  background-color: var(--success-color);
}

.status-value {
  font-weight: 600;
  color: var(--text-color);
  min-width: 80px;
}

/* أنماط تحليل هوامش الربح */
.profit-margin-analysis {
  margin-top: 15px;
}

.margin-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.margin-stat {
  text-align: center;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: var(--border-radius-sm);
}

.margin-value {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--primary-color);
}

.margin-label {
  font-size: 0.8rem;
  color: var(--text-muted);
  margin-top: 5px;
}

.top-margin-items h5 {
  font-size: 1rem;
  margin-bottom: 10px;
  color: var(--text-color);
}

.top-margin-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.top-margin-item {
  display: flex;
  justify-content: space-between;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: var(--border-radius-sm);
}

.item-name {
  font-weight: 600;
  color: var(--text-color);
}

.item-margin {
  font-weight: 700;
  color: var(--success-color);
}

/* أنماط توصيات المخزون */
.inventory-recommendations {
  margin-top: 15px;
}

.recommendation-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.recommendation-item {
  display: flex;
  gap: 15px;
  padding: 15px;
  border-radius: var(--border-radius-sm);
  background-color: #f8f9fa;
}

.recommendation-item.urgent {
  border-right: 4px solid var(--danger-color);
}

.recommendation-item.warning {
  border-right: 4px solid var(--warning-color);
}

.recommendation-item.info {
  border-right: 4px solid var(--info-color);
}

.recommendation-item.success {
  border-right: 4px solid var(--success-color);
}

.recommendation-icon {
  font-size: 1.5rem;
  color: var(--text-muted);
}

.recommendation-item.urgent .recommendation-icon {
  color: var(--danger-color);
}

.recommendation-item.warning .recommendation-icon {
  color: var(--warning-color);
}

.recommendation-item.info .recommendation-icon {
  color: var(--info-color);
}

.recommendation-item.success .recommendation-icon {
  color: var(--success-color);
}

.recommendation-content {
  flex: 1;
}

.recommendation-title {
  font-weight: 600;
  margin-bottom: 5px;
  color: var(--text-color);
}

.recommendation-description {
  color: var(--text-muted);
  font-size: 0.9rem;
}

/* أنماط الرسم البياني للأصناف الأكثر مبيعًا */
.top-selling-summary {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 20px;
}

.top-selling-chart {
  margin-top: 20px;
}

.top-selling-chart h4 {
  font-size: 1.1rem;
  margin-bottom: 15px;
  color: var(--text-color);
}

.chart-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.chart-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.chart-label {
  width: 150px;
  font-weight: 600;
  color: var(--text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chart-bar-container {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10px;
}

.chart-bar {
  height: 25px;
  border-radius: 5px;
  min-width: 5px;
  transition: width 0.5s ease;
}

.chart-value {
  font-weight: 600;
  color: var(--text-color);
  white-space: nowrap;
}

.chart-value .percentage {
  font-size: 0.8rem;
  color: var(--text-muted);
  margin-right: 5px;
}

.report-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 5px;
  height: 100%;
  background-color: var(--primary-color);
  transition: width var(--transition-normal);
}

.report-button:hover::before {
  width: 10px;
}

.report-button.active {
  background-color: rgba(var(--primary-rgb), 0.05);
  border-color: var(--primary-color);
}

.report-button.active::before {
  width: 10px;
}

.report-button-icon {
  font-size: 2rem;
  color: var(--primary-color);
  margin-left: 15px;
  transition: transform var(--transition-normal);
}

.report-button:hover .report-button-icon {
  transform: scale(1.2);
}

.report-button-content {
  flex: 1;
}

.report-button-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 5px;
}

.report-button-description {
  font-size: 0.9rem;
  color: var(--text-light);
}

/* تحسين التبويبات */
.tabs-container {
  margin-bottom: 30px;
  border-bottom: none;
  background-color: white;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  padding: 5px;
}

.tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.tab-btn {
  padding: 12px 20px;
  border: none;
  background-color: transparent;
  color: var(--text-dark);
  font-weight: 600;
  cursor: pointer;
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  overflow: hidden;
}

.tab-btn::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 3px;
  background-color: var(--primary-color);
  transition: width var(--transition-normal);
}

.tab-btn:hover {
  color: var(--primary-color);
  background-color: rgba(var(--primary-rgb), 0.05);
}

.tab-btn:hover::before {
  width: 80%;
}

.tab-btn.active {
  color: var(--primary-color);
  background-color: rgba(var(--primary-rgb), 0.1);
}

.tab-btn.active::before {
  width: 80%;
}

.tab-btn svg {
  font-size: 1.3rem;
  transition: transform var(--transition-normal);
}

.tab-btn:hover svg {
  transform: scale(1.2);
}

/* تحسين الإحصائيات */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 25px;
  margin-bottom: 40px;
}

.stat-card {
  background-color: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  padding: 25px;
  text-align: center;
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
  position: relative;
  overflow: hidden;
  border: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.stat-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-md);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
}

.stat-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 30%;
  height: 30%;
  background: radial-gradient(circle, rgba(var(--primary-rgb), 0.05) 0%, rgba(var(--primary-rgb), 0) 70%);
  border-radius: 50%;
  z-index: 0;
}

.stat-card-icon {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 20px;
  opacity: 0.9;
  transition: transform var(--transition-normal);
  background-color: rgba(var(--primary-rgb), 0.1);
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  position: relative;
  z-index: 1;
}

.stat-card:hover .stat-card-icon {
  transform: scale(1.1) rotate(5deg);
  background-color: rgba(var(--primary-rgb), 0.15);
}

.stat-card-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 10px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 1;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: inline-block;
}

.stat-card-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-light);
  position: relative;
  padding-bottom: 10px;
  z-index: 1;
  text-align: center;
  width: 100%;
}

.stat-card-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
  opacity: 0.7;
  border-radius: 1.5px;
  transition: width var(--transition-normal);
}

.stat-card:hover .stat-card-title::after {
  width: 80px;
}

.stat-card-subtitle {
  font-size: 0.95rem;
  color: var(--text-light);
  margin-top: 10px;
  background-color: rgba(var(--primary-rgb), 0.05);
  padding: 5px 10px;
  border-radius: 20px;
  display: inline-block;
  transition: all var(--transition-normal);
  z-index: 1;
  position: relative;
}

.stat-card:hover .stat-card-subtitle {
  background-color: rgba(var(--primary-rgb), 0.1);
  transform: scale(1.05);
}

/* تنسيق قسم فواتير العملاء */
.customer-select-label {
  display: flex;
  align-items: center;
  font-weight: bold;
  margin-bottom: 10px;
  color: var(--text-dark);
}

.customer-select-label svg {
  margin-left: 8px;
  color: var(--primary-color);
}

.customer-select {
  width: 100%;
  max-width: 400px;
  padding: 10px 15px;
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color);
  background-color: white;
  font-size: 1rem;
  margin-bottom: 20px;
  transition: border-color var(--transition-normal);
}

.customer-select:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2);
}

.invoice-details {
  margin-bottom: 30px;
  animation: fadeIn 0.5s ease-in-out;
}

.invoice-details .card-header {
  background-color: rgba(var(--primary-rgb), 0.05);
  border-bottom: 1px solid rgba(var(--primary-rgb), 0.1);
}

.invoice-details .card-header h5 {
  display: flex;
  align-items: center;
  margin: 0;
  color: var(--primary-color);
}

.invoice-summary {
  background-color: rgba(var(--primary-rgb), 0.03);
  padding: 15px;
  border-radius: var(--border-radius-md);
  margin-top: 20px;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .reports-page {
    padding: 20px;
  }

  .reports-header h1 {
    font-size: 2rem;
  }

  .reports-sections {
    grid-template-columns: 1fr;
  }

  .tabs {
    flex-direction: column;
  }

  .tab-btn {
    width: 100%;
    justify-content: flex-start;
  }

  .stat-card {
    padding: 20px;
  }

  .stat-card-icon {
    font-size: 2rem;
  }

  .stat-card-value {
    font-size: 1.8rem;
  }
}
