# إصلاح مشكلة تحديث الرصيد الافتتاحي للخزينة

## المشكلة

كانت هناك مشكلة في نظام الخزينة تحدث عند القيام بالخطوات التالية:

1. إجراء عمليات بيع وشراء في النظام بينما الرصيد الافتتاحي للخزينة يساوي صفر أو غير محدد
2. محاولة تحديث/تعديل الرصيد الافتتاحي للخزينة بعد إجراء هذه العمليات

**النتيجة**: لا يمكن تحديث الرصيد الافتتاحي ويظهر خطأ أو لا يتم حفظ التحديث.

## سبب المشكلة

1. **بنية جدول الخزينة غير متسقة**: في بعض الملفات يتم إنشاء جدول الخزينة بأعمدة أساسية فقط، بينما في ملفات أخرى يتم إنشاؤه بأعمدة إضافية مثل `sales_total` و `purchases_total` و `returns_total` و `transport_total`.

2. **دالة `updateInitialBalance` تحاول الوصول لأعمدة قد لا تكون موجودة**: الدالة تستخدم أعمدة مثل `sales_total` و `purchases_total` التي قد لا تكون موجودة في بعض قواعد البيانات.

3. **عدم وجود آلية للتحقق من بنية الجدول قبل التحديث**.

## الحل المطبق

### 1. إضافة دالة للتحقق من بنية الجدول

تم إضافة دالة `ensureCashboxTableStructure()` في `cashbox-manager.js` التي:

- تتحقق من وجود الأعمدة المطلوبة في جدول الخزينة
- تضيف الأعمدة المفقودة تلقائياً
- تسجل العمليات في وحدة التحكم للمتابعة

```javascript
function ensureCashboxTableStructure() {
  // التحقق من معلومات الجدول الحالي
  const tableInfo = db.prepare("PRAGMA table_info(cashbox)").all();
  const existingColumns = tableInfo.map(col => col.name);

  // قائمة الأعمدة المطلوبة
  const requiredColumns = [
    { name: 'sales_total', type: 'REAL', default: '0' },
    { name: 'purchases_total', type: 'REAL', default: '0' },
    { name: 'returns_total', type: 'REAL', default: '0' },
    { name: 'transport_total', type: 'REAL', default: '0' }
  ];

  // إضافة الأعمدة المفقودة
  for (const column of requiredColumns) {
    if (!existingColumns.includes(column.name)) {
      const alterQuery = `ALTER TABLE cashbox ADD COLUMN ${column.name} ${column.type} DEFAULT ${column.default}`;
      db.prepare(alterQuery).run();
    }
  }
}
```

### 2. تحديث جميع دوال الخزينة

تم تحديث الدوال التالية لاستدعاء `ensureCashboxTableStructure()` قبل أي عملية:

- `getCashbox()`
- `createCashbox()`
- `updateInitialBalance()`
- `addTransaction()`

### 3. إضافة معالجة آمنة للقيم

تم تحديث الكود للتعامل مع القيم المفقودة باستخدام القيم الافتراضية:

```javascript
// بدلاً من
cashbox.sales_total

// أصبح
cashbox.sales_total || 0
```

### 4. تحديث عمليات إنشاء الخزينة

تم تحديث جميع عمليات إنشاء الخزينة لتتضمن جميع الأعمدة المطلوبة:

```sql
INSERT INTO cashbox (
  initial_balance, current_balance, profit_total, sales_total, 
  purchases_total, returns_total, transport_total,
  created_at, updated_at
)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
```

### 5. أداة إصلاح شاملة

تم إنشاء ملف `fix-cashbox-structure.js` الذي:

- يتحقق من بنية جدول الخزينة الحالية
- يضيف الأعمدة المفقودة
- يحدث القيم الافتراضية
- يختبر تحديث الرصيد الافتتاحي
- يعرض تقريراً مفصلاً عن العملية

## كيفية تطبيق الإصلاح

### للمطورين:

1. **تشغيل أداة الإصلاح**:
   ```bash
   node fix-cashbox-structure.js
   ```

2. **التحقق من النتائج**: ستعرض الأداة تقريراً مفصلاً عن العمليات المنفذة

### للمستخدمين:

1. **إعادة تشغيل التطبيق**: الإصلاح سيتم تلقائياً عند أول استدعاء لأي دالة خزينة

2. **اختبار تحديث الرصيد الافتتاحي**:
   - اذهب إلى قسم الخزينة
   - اضغط على "تعديل" بجانب الرصيد الافتتاحي
   - أدخل قيمة جديدة واضغط "حفظ التغييرات"

## الميزات الجديدة

1. **إصلاح تلقائي**: لا حاجة لتدخل يدوي، الإصلاح يحدث تلقائياً
2. **أمان البيانات**: لا يتم حذف أو تعديل البيانات الموجودة
3. **تسجيل مفصل**: جميع العمليات مسجلة في وحدة التحكم
4. **اختبار تلقائي**: التحقق من نجاح الإصلاح

## الملفات المحدثة

- `cashbox-manager.js`: إضافة دالة التحقق من البنية وتحديث جميع الدوال
- `fix-cashbox-structure.js`: أداة الإصلاح الشاملة (جديد)
- `CASHBOX_INITIAL_BALANCE_FIX.md`: هذا الملف (جديد)

## اختبار الإصلاح

بعد تطبيق الإصلاح، يمكن اختبار النظام كالتالي:

1. إنشاء خزينة جديدة برصيد افتتاحي = 0
2. إجراء عمليات بيع وشراء
3. محاولة تحديث الرصيد الافتتاحي إلى قيمة جديدة
4. التحقق من نجاح التحديث وإعادة حساب الأرباح

## ملاحظات مهمة

- الإصلاح متوافق مع جميع إصدارات قاعدة البيانات الموجودة
- لا يؤثر على البيانات الموجودة
- يعمل تلقائياً دون تدخل المستخدم
- يمكن تشغيله عدة مرات دون مشاكل
