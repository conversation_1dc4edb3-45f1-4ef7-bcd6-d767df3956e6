/**
 * اختبار مباشر لمعالج تشخيص الأرباح
 */

const DatabaseManager = require('./database-singleton');
const { logError, logSystem } = require('./error-handler');

async function directTestProfitDiagnosis() {
  console.log('🔍 بدء الاختبار المباشر لتشخيص الأرباح...');
  
  try {
    // الحصول على قاعدة البيانات
    const dbManager = DatabaseManager.getInstance();
    const db = dbManager.getConnection();
    
    if (!db) {
      console.error('❌ قاعدة البيانات غير متوفرة');
      return;
    }

    console.log('✅ تم الحصول على اتصال قاعدة البيانات');

    // فحص الخزينة
    console.log('\n📊 1. فحص الخزينة الحالية:');
    const cashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
    const cashbox = cashboxStmt.get();
    
    if (cashbox) {
      console.log('✅ بيانات الخزينة:');
      console.log(`   - الرصيد الافتتاحي: ${cashbox.initial_balance}`);
      console.log(`   - الرصيد الحالي: ${cashbox.current_balance}`);
      console.log(`   - إجمالي الأرباح: ${cashbox.profit_total}`);
      console.log(`   - إجمالي المبيعات: ${cashbox.sales_total}`);
      console.log(`   - إجمالي المشتريات: ${cashbox.purchases_total}`);
    } else {
      console.log('❌ لا توجد بيانات في جدول cashbox');
      return;
    }

    // فحص معاملات البيع
    console.log('\n💰 2. فحص معاملات البيع:');
    const salesStmt = db.prepare(`
      SELECT COUNT(*) as count, SUM(profit) as total_profit, SUM(total_price) as total_sales
      FROM transactions 
      WHERE transaction_type = 'sale'
    `);
    const salesData = salesStmt.get();
    
    console.log(`✅ معاملات البيع:`);
    console.log(`   - عدد معاملات البيع: ${salesData.count}`);
    console.log(`   - إجمالي الأرباح من المعاملات: ${salesData.total_profit || 0}`);
    console.log(`   - إجمالي قيمة المبيعات: ${salesData.total_sales || 0}`);

    // فحص تفاصيل معاملات البيع
    console.log('\n🔍 3. تفاصيل معاملات البيع:');
    const salesDetailsStmt = db.prepare(`
      SELECT id, item_id, quantity, selling_price, total_price, profit, transaction_date
      FROM transactions 
      WHERE transaction_type = 'sale'
      ORDER BY transaction_date DESC
      LIMIT 10
    `);
    const salesDetails = salesDetailsStmt.all();
    
    if (salesDetails.length > 0) {
      console.log(`✅ تفاصيل آخر ${salesDetails.length} معاملة بيع:`);
      salesDetails.forEach((sale, index) => {
        console.log(`   ${index + 1}. معاملة ${sale.id}:`);
        console.log(`      - الصنف: ${sale.item_id}`);
        console.log(`      - الكمية: ${sale.quantity}`);
        console.log(`      - سعر البيع: ${sale.selling_price}`);
        console.log(`      - إجمالي السعر: ${sale.total_price}`);
        console.log(`      - الربح: ${sale.profit || 0}`);
        console.log(`      - التاريخ: ${sale.transaction_date}`);
      });
    } else {
      console.log('❌ لا توجد معاملات بيع في النظام');
    }

    // فحص معاملات الشراء
    console.log('\n🛒 4. فحص معاملات الشراء:');
    const purchaseStmt = db.prepare(`
      SELECT COUNT(*) as count, SUM(total_price) as total_purchases
      FROM transactions 
      WHERE transaction_type = 'purchase'
    `);
    const purchaseData = purchaseStmt.get();
    
    console.log(`✅ معاملات الشراء:`);
    console.log(`   - عدد معاملات الشراء: ${purchaseData.count}`);
    console.log(`   - إجمالي قيمة المشتريات: ${purchaseData.total_purchases || 0}`);

    // اختبار إعادة حساب الأرباح
    console.log('\n🧮 5. اختبار إعادة حساب الأرباح:');
    const recalculatedProfit = salesData.total_profit || 0;
    console.log(`✅ الأرباح المحسوبة من المعاملات: ${recalculatedProfit}`);
    console.log(`✅ الأرباح المحفوظة في الخزينة: ${cashbox.profit_total}`);
    
    const difference = Math.abs(recalculatedProfit - cashbox.profit_total);
    if (difference > 0.01) {
      console.log(`⚠️ هناك فرق بين الأرباح المحسوبة والمحفوظة: ${difference}`);
    } else {
      console.log(`✅ الأرباح متطابقة`);
    }

    // فحص المخزون
    console.log('\n📦 6. فحص المخزون:');
    const inventoryStmt = db.prepare(`
      SELECT item_id, avg_price, selling_price, current_quantity
      FROM inventory
      LIMIT 5
    `);
    const inventory = inventoryStmt.all();
    
    if (inventory.length > 0) {
      console.log(`✅ عينة من المخزون:`);
      inventory.forEach((item, index) => {
        console.log(`   ${index + 1}. الصنف ${item.item_id}:`);
        console.log(`      - متوسط سعر الشراء: ${item.avg_price}`);
        console.log(`      - سعر البيع: ${item.selling_price}`);
        console.log(`      - الكمية الحالية: ${item.current_quantity}`);
      });
    } else {
      console.log('❌ لا توجد أصناف في المخزون');
    }

    // النتيجة والتوصيات
    console.log('\n📋 7. النتيجة والتوصيات:');
    
    if (salesData.count === 0) {
      console.log('❌ المشكلة: لا توجد معاملات بيع في النظام');
      console.log('💡 الحل: تأكد من وجود معاملات بيع مسجلة');
    } else if (salesData.total_profit === 0 || salesData.total_profit === null) {
      console.log('❌ المشكلة: معاملات البيع موجودة لكن الأرباح = 0');
      console.log('💡 الحل: يجب إعادة حساب الأرباح لمعاملات البيع');
    } else if (difference > 0.01) {
      console.log('❌ المشكلة: عدم تطابق الأرباح المحسوبة مع المحفوظة');
      console.log('💡 الحل: تحديث إجمالي الأرباح في الخزينة');
    } else {
      console.log('✅ النظام يبدو سليماً');
    }

    // إرجاع البيانات للمراجعة
    return {
      cashbox,
      salesSummary: salesData,
      salesDetails,
      purchaseSummary: purchaseData,
      inventory,
      analysis: {
        profitDifference: difference,
        isHealthy: difference <= 0.01 && salesData.count > 0
      }
    };

  } catch (error) {
    console.error('❌ خطأ في التشخيص:', error);
    logError(error, 'directTestProfitDiagnosis');
    return null;
  }
}

// تشغيل التشخيص إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  directTestProfitDiagnosis()
    .then((result) => {
      console.log('\n✅ انتهى التشخيص المباشر');
      if (result) {
        console.log('\n📊 ملخص النتائج:');
        console.log(`- عدد معاملات البيع: ${result.salesSummary.count}`);
        console.log(`- إجمالي الأرباح: ${result.salesSummary.total_profit || 0}`);
        console.log(`- فرق الأرباح: ${result.analysis.profitDifference}`);
        console.log(`- حالة النظام: ${result.analysis.isHealthy ? 'سليم' : 'يحتاج إصلاح'}`);
      }
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ خطأ في تشغيل التشخيص:', error);
      process.exit(1);
    });
}

module.exports = {
  directTestProfitDiagnosis
};
