/**
 * ملف reactPolyfill.js
 * 
 * يوفر هذا الملف بدائل لمكتبات React و ReactDOM في بيئة المتصفح.
 * يتم استيراد هذا الملف في بداية التطبيق لضمان توفر هذه المكتبات.
 */

// تعريف React إذا لم يكن موجودًا
if (typeof React === 'undefined') {
  window.React = {
    createElement: function() { return {}; },
    useState: function() { return [null, function() {}]; },
    useEffect: function() {},
    useRef: function() { return { current: null }; },
    createContext: function() { return { Provider: function() {}, Consumer: function() {} }; },
    Fragment: 'div',
    __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED: {
      ReactCurrentDispatcher: { current: {} },
      ReactCurrentOwner: { current: {} }
    }
  };
  console.log('تم تعريف React بنجاح');
}

// تعريف ReactDOM إذا لم يكن موجودًا
if (typeof ReactDOM === 'undefined') {
  window.ReactDOM = {
    render: function() {},
    createPortal: function() {},
    findDOMNode: function() {},
    __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED: {
      Events: []
    }
  };
  console.log('تم تعريف ReactDOM بنجاح');
}

// تعريف scheduler إذا لم يكن موجودًا
if (typeof scheduler === 'undefined') {
  window.scheduler = {
    unstable_now: function() { return Date.now(); },
    unstable_scheduleCallback: function(priority, callback) { setTimeout(callback, 0); return {}; },
    unstable_cancelCallback: function() {}
  };
  console.log('تم تعريف scheduler بنجاح');
}

// تصدير كائن فارغ للتوافق مع ES modules
export default {};
