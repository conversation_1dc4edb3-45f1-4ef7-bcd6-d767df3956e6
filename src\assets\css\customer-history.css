/* تنسيقات نافذة سجل مبيعات العميل */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  overflow-y: auto;
  padding: 20px;
  backdrop-filter: blur(3px);
}

.modal-container {
  background-color: #fff;
  border-radius: 12px;
  width: 95%;
  max-width: 1300px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  animation: modalFadeIn 0.4s ease-out;
}

.sales-history-container {
  background-color: #f8f9fa;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 25px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark-color, #0056b3));
  color: white;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.modal-header h3 {
  margin: 0;
  font-size: 22px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.header-icon {
  margin-left: 10px;
  font-size: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.header-text {
  display: inline-block;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  transition: all 0.3s;
}

.close-btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.modal-body {
  padding: 25px;
  overflow-y: auto;
}

/* تنسيقات ملخص المبيعات */
.sales-summary {
  margin-bottom: 30px;
}

.summary-card {
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  height: 100%;
  position: relative;
  overflow: hidden;
  background-color: white;
}

.summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color, #6c757d));
  opacity: 0.8;
}

.items-card::before {
  background: linear-gradient(to right, #3498db, #2980b9);
}

.sales-card::before {
  background: linear-gradient(to right, #2ecc71, #27ae60);
}

.profit-card::before {
  background: linear-gradient(to right, #f39c12, #e67e22);
}

.summary-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.card-body {
  text-align: center;
}

.card-icon {
  font-size: 28px;
  margin-bottom: 15px;
  color: var(--primary-color);
  background-color: rgba(var(--primary-rgb, 0, 123, 255), 0.1);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
}

.items-card .card-icon {
  color: #3498db;
  background-color: rgba(52, 152, 219, 0.1);
}

.sales-card .card-icon {
  color: #2ecc71;
  background-color: rgba(46, 204, 113, 0.1);
}

.profit-card .card-icon {
  color: #f39c12;
  background-color: rgba(243, 156, 18, 0.1);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #555;
}

.card-text {
  font-size: 32px;
  font-weight: 700;
  margin: 0;
  color: #333;
}

/* تنسيقات أزرار التحكم */
.control-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  color: #333;
  display: flex;
  align-items: center;
}

.section-icon {
  margin-left: 8px;
  color: var(--primary-color);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.btn-print, .btn-close {
  padding: 10px 15px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  border: none;
}

.btn-print {
  background-color: var(--primary-color);
  color: white;
}

.btn-print:hover {
  background-color: var(--primary-dark-color, #0056b3);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-close {
  background-color: #6c757d;
  color: white;
}

.btn-close:hover {
  background-color: #5a6268;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* تنسيقات أدوات الفلترة والبحث */
.filter-tools {
  margin-bottom: 25px;
  background-color: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.search-box, .filter-box {
  position: relative;
  margin-bottom: 10px;
}

.search-icon, .filter-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #aaa;
  font-size: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.search-input, .filter-select {
  width: 100%;
  padding: 12px 15px 12px 40px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s;
  background-color: #fff;
  color: #333;
  height: 45px;
}

.search-input:focus, .filter-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb, 0, 123, 255), 0.1);
  outline: none;
}

/* تنسيقات إضافية للقوائم المنسدلة */
select.filter-select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23aaa' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: left 15px center;
  padding-left: 40px;
  cursor: pointer;
}

/* تنسيقات جدول البيانات */
.table-container {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
  margin-top: 20px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 0;
  direction: rtl;
}

.data-table thead {
  background: linear-gradient(to right, #343a40, #495057);
}

.data-table th {
  color: white;
  font-weight: 600;
  padding: 15px;
  text-align: right;
  border: none;
  font-size: 14px;
  position: relative;
}

.data-table th:after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 2px;
  background: rgba(255, 255, 255, 0.1);
}

.data-table td {
  padding: 15px;
  border-top: 1px solid #eee;
  vertical-align: middle;
  font-size: 14px;
}

.data-table tbody tr {
  transition: all 0.3s;
}

.data-table tbody tr:nth-child(odd) {
  background-color: rgba(0, 0, 0, 0.02);
}

.data-table tbody tr:hover {
  background-color: rgba(var(--primary-rgb, 0, 123, 255), 0.05);
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.text-center {
  text-align: center;
}

.profit-value {
  color: #2ecc71;
}

.action-button {
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  border: none;
  width: 100%;
  gap: 5px;
}

.button-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 5px;
}

.button-text {
  display: inline-block;
}

.th-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: 5px;
  font-size: 14px;
}

.print-button {
  background-color: var(--primary-color);
  color: white;
}

.print-button:hover {
  background-color: var(--primary-dark-color, #0056b3);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* تنسيقات حالة عدم وجود بيانات */
.empty-state {
  text-align: center;
  padding: 50px 20px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
  margin-top: 20px;
  animation: fadeIn 0.5s ease-in-out;
}

.empty-icon {
  font-size: 50px;
  color: #b0bec5;
  margin-bottom: 20px;
  background-color: #f5f5f5;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  animation: pulse 2s infinite;
}

.empty-icon svg {
  font-size: 40px;
}

.empty-title {
  font-size: 22px;
  font-weight: 600;
  color: #455a64;
  margin-bottom: 10px;
}

.empty-description {
  font-size: 16px;
  color: #78909c;
  max-width: 400px;
  margin: 0 auto;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* تنسيقات حالة التحميل */
.loading-state {
  text-align: center;
  padding: 50px 20px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 5px solid rgba(var(--primary-rgb, 0, 123, 255), 0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  margin: 0 auto 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-title {
  font-size: 22px;
  font-weight: 600;
  color: #455a64;
  margin-bottom: 10px;
}

.loading-description {
  font-size: 16px;
  color: #78909c;
  max-width: 400px;
  margin: 0 auto;
}

/* تنسيقات حالة الخطأ */
.error-state {
  text-align: center;
  padding: 50px 20px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
}

.error-icon {
  font-size: 50px;
  color: #e74c3c;
  margin-bottom: 20px;
  background-color: rgba(231, 76, 60, 0.1);
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
}

.error-title {
  font-size: 22px;
  font-weight: 600;
  color: #c0392b;
  margin-bottom: 10px;
}

.error-description {
  font-size: 16px;
  color: #7f8c8d;
  max-width: 400px;
  margin: 0 auto 20px;
}

.retry-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.retry-button:hover {
  background-color: var(--primary-dark-color, #0056b3);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* تأثيرات الحركة */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تنسيقات للشاشات الصغيرة */
@media (max-width: 768px) {
  .modal-container {
    width: 95%;
    max-height: 95vh;
  }

  .modal-header {
    padding: 15px;
  }

  .modal-header h3 {
    font-size: 18px;
  }

  .modal-body {
    padding: 15px;
  }

  .sales-summary .col-md-4 {
    margin-bottom: 15px;
  }

  .control-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .action-buttons {
    width: 100%;
  }

  .btn-print, .btn-close {
    flex: 1;
  }

  .table-container {
    overflow-x: auto;
  }

  .data-table th, .data-table td {
    padding: 10px;
    font-size: 13px;
  }

  .search-input, .filter-select {
    padding: 10px 15px 10px 40px;
  }
}
