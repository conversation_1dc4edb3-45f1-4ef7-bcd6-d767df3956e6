/**
 * سكريبت لإصلاح الخزينة عبر IPC
 */

// إنشاء نافذة مخفية لاستدعاء IPC
const { app, BrowserWindow, ipcMain } = require('electron');

async function fixCashboxViaIPC() {
  try {
    console.log('بدء إصلاح الخزينة عبر IPC...');
    
    // تهيئة Electron
    await app.whenReady();
    
    // إنشاء نافذة مخفية
    const window = new BrowserWindow({
      show: false,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false
      }
    });
    
    // تحميل صفحة فارغة
    await window.loadURL('data:text/html,<html><body>Fixing cashbox...</body></html>');
    
    // استيراد معالجات IPC
    require('./ipc-handlers').registerAllHandlers();
    
    // استدعاء دالة إصلاح الخزينة
    const result = await new Promise((resolve, reject) => {
      ipcMain.handle('fix-cashbox-test', async () => {
        try {
          // استيراد الدالة مباشرة
          const dbManager = require('./database-singleton').getInstance();
          const db = dbManager.getConnection();
          
          if (!db) {
            throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
          }
          
          console.log('تم الحصول على اتصال قاعدة البيانات بنجاح');
          
          // الحصول على الخزينة الحالية
          const getCashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
          const currentCashbox = getCashboxStmt.get();
          
          if (!currentCashbox) {
            throw new Error('لا توجد خزينة في قاعدة البيانات');
          }
          
          console.log('الخزينة الحالية:', {
            current_balance: currentCashbox.current_balance,
            sales_total: currentCashbox.sales_total,
            purchases_total: currentCashbox.purchases_total,
            profit_total: currentCashbox.profit_total
          });
          
          // الحصول على جميع المعاملات
          const getTransactionsStmt = db.prepare(`
            SELECT t.*, inv.avg_price, inv.selling_price as inventory_selling_price
            FROM transactions t
            LEFT JOIN inventory inv ON t.item_id = inv.item_id
            ORDER BY t.transaction_date ASC
          `);
          const transactions = getTransactionsStmt.all();
          
          console.log(`تم العثور على ${transactions.length} معاملة`);
          
          // حساب الإجماليات
          let totalSales = 0;
          let totalPurchases = 0;
          let totalReturns = 0;
          let totalProfit = 0;
          let totalTransportCost = 0;
          
          for (const transaction of transactions) {
            const amount = parseFloat(transaction.total_price) || 0;
            const transportCost = parseFloat(transaction.transport_cost) || 0;
            
            if (transaction.transaction_type === 'sale') {
              totalSales += amount;
              
              // حساب الربح
              let profit = 0;
              if (transaction.profit && transaction.profit !== 0) {
                profit = parseFloat(transaction.profit);
              } else {
                // الحصول على سعر البيع
                let sellingPrice = transaction.selling_price;
                if (!sellingPrice || sellingPrice === 0) {
                  sellingPrice = transaction.inventory_selling_price || 0;
                }
                
                if (sellingPrice > 0 && transaction.avg_price > 0) {
                  // حساب الربح الأساسي
                  profit = (sellingPrice - transaction.avg_price) * transaction.quantity;
                  
                  // خصم مصاريف النقل إذا كانت موجودة
                  if (transportCost > 0) {
                    profit -= transportCost;
                  }
                } else {
                  // تقدير 20% من سعر البيع
                  profit = amount * 0.2;
                }
              }
              
              totalProfit += profit;
              
            } else if (transaction.transaction_type === 'purchase') {
              totalPurchases += amount;
              totalTransportCost += transportCost;
              
            } else if (transaction.transaction_type === 'return') {
              totalReturns += amount;
              
              // خصم الربح المرتبط بالإرجاع
              let returnProfit = transaction.profit || (amount * 0.2);
              totalProfit -= returnProfit;
            }
          }
          
          // التأكد من أن الأرباح لا تكون سالبة
          totalProfit = Math.max(0, totalProfit);
          
          console.log('الإجماليات المحسوبة:', {
            totalSales,
            totalPurchases,
            totalReturns,
            totalProfit,
            totalTransportCost
          });
          
          // حساب الرصيد الحالي الجديد
          const newCurrentBalance = currentCashbox.initial_balance + totalSales - totalPurchases - totalReturns - totalTransportCost;
          
          // تحديث الخزينة
          const updateCashboxStmt = db.prepare(`
            UPDATE cashbox
            SET current_balance = ?,
                sales_total = ?,
                purchases_total = ?,
                returns_total = ?,
                profit_total = ?,
                transport_total = ?,
                updated_at = ?
            WHERE id = ?
          `);
          
          const updateResult = updateCashboxStmt.run(
            newCurrentBalance,
            totalSales,
            totalPurchases,
            totalReturns,
            totalProfit,
            totalTransportCost,
            new Date().toISOString(),
            currentCashbox.id
          );
          
          console.log(`تم تحديث الخزينة بنجاح. عدد الصفوف المتأثرة: ${updateResult.changes}`);
          
          return {
            success: true,
            message: 'تم إصلاح الخزينة بنجاح',
            before: {
              current_balance: currentCashbox.current_balance,
              sales_total: currentCashbox.sales_total,
              purchases_total: currentCashbox.purchases_total,
              profit_total: currentCashbox.profit_total
            },
            after: {
              current_balance: newCurrentBalance,
              sales_total: totalSales,
              purchases_total: totalPurchases,
              profit_total: totalProfit
            }
          };
          
        } catch (error) {
          console.error('خطأ في إصلاح الخزينة:', error);
          return {
            success: false,
            error: error.message
          };
        }
      });
      
      // استدعاء الدالة
      window.webContents.executeJavaScript(`
        require('electron').ipcRenderer.invoke('fix-cashbox-test')
      `).then(resolve).catch(reject);
    });
    
    console.log('نتيجة إصلاح الخزينة:', result);
    
    // إغلاق النافذة والتطبيق
    window.close();
    app.quit();
    
    return result;
    
  } catch (error) {
    console.error('خطأ في تشغيل السكريبت:', error);
    app.quit();
    return { success: false, error: error.message };
  }
}

// تشغيل السكريبت
if (require.main === module) {
  fixCashboxViaIPC().then(result => {
    console.log('النتيجة النهائية:', result);
    process.exit(result.success ? 0 : 1);
  }).catch(error => {
    console.error('خطأ في تشغيل السكريبت:', error);
    process.exit(1);
  });
}

module.exports = { fixCashboxViaIPC };
