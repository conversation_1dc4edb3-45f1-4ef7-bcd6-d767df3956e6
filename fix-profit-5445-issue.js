/**
 * إصلاح مشكلة حفظ الأرباح 5445
 * المشكلة: النظام يحسب الأرباح بشكل صحيح (5445) لكن يحفظ 0 في قاعدة البيانات
 */

const { logError, logSystem } = require('./error-handler');
const DatabaseManager = require('./database-singleton');

/**
 * إصلاح مباشر لمشكلة حفظ الأرباح
 */
function fixProfit5445Issue() {
  console.log('🔧 إصلاح مشكلة حفظ الأرباح 5445...');
  console.log('='.repeat(50));
  
  try {
    // 1. الحصول على قاعدة البيانات
    const db = DatabaseManager.getDatabase();
    if (!db) {
      throw new Error('قاعدة البيانات غير متصلة');
    }
    console.log('✅ تم الاتصال بقاعدة البيانات');

    // 2. حساب الأرباح الصحيحة من معاملات البيع
    console.log('\n📊 حساب الأرباح من معاملات البيع...');
    const profitQuery = db.prepare(`
      SELECT 
        id,
        item_id,
        quantity,
        selling_price,
        profit,
        transaction_date
      FROM transactions 
      WHERE transaction_type = 'sale' 
      AND profit > 0
      ORDER BY transaction_date DESC
    `);
    
    const salesTransactions = profitQuery.all();
    console.log(`📋 عدد معاملات البيع: ${salesTransactions.length}`);
    
    let totalCalculatedProfit = 0;
    salesTransactions.forEach((transaction, index) => {
      const profit = Number(transaction.profit) || 0;
      totalCalculatedProfit += profit;
      console.log(`   ${index + 1}. المعاملة ${transaction.id}: الربح = ${profit}`);
    });
    
    console.log(`💰 إجمالي الأرباح المحسوب: ${totalCalculatedProfit}`);

    // 3. التحقق من القيمة الحالية في الخزينة
    console.log('\n🔍 فحص القيمة الحالية في الخزينة...');
    const currentQuery = db.prepare('SELECT profit_total FROM cashbox WHERE id = 1');
    const currentResult = currentQuery.get();
    const currentProfit = currentResult ? Number(currentResult.profit_total) : 0;
    
    console.log(`💾 الأرباح الحالية في الخزينة: ${currentProfit}`);

    // 4. إصلاح القيمة إذا كانت خاطئة
    if (Math.abs(currentProfit - totalCalculatedProfit) > 0.01) {
      console.log('\n🔧 إصلاح قيمة الأرباح في الخزينة...');
      
      // استخدام معاملة قاعدة البيانات لضمان الحفظ
      const fixTransaction = db.transaction(() => {
        // تحديث القيمة
        const updateStmt = db.prepare(`
          UPDATE cashbox 
          SET profit_total = ?, 
              updated_at = ? 
          WHERE id = 1
        `);
        
        const updateResult = updateStmt.run(totalCalculatedProfit, new Date().toISOString());
        
        if (updateResult.changes === 0) {
          throw new Error('لم يتم تحديث أي صف في جدول cashbox');
        }
        
        console.log(`✅ تم تحديث الخزينة، الصفوف المتأثرة: ${updateResult.changes}`);
        
        // التحقق الفوري من الحفظ
        const verifyStmt = db.prepare('SELECT profit_total FROM cashbox WHERE id = 1');
        const verifyResult = verifyStmt.get();
        
        if (!verifyResult) {
          throw new Error('فشل في استرجاع البيانات للتحقق');
        }
        
        const savedValue = Number(verifyResult.profit_total);
        console.log(`💾 القيمة المحفوظة: ${savedValue}`);
        
        if (Math.abs(savedValue - totalCalculatedProfit) > 0.01) {
          throw new Error(`فشل في حفظ القيمة: متوقع ${totalCalculatedProfit} لكن تم حفظ ${savedValue}`);
        }
        
        return {
          success: true,
          oldValue: currentProfit,
          newValue: savedValue,
          difference: savedValue - currentProfit
        };
      });
      
      const fixResult = fixTransaction();
      
      if (fixResult.success) {
        console.log('🎉 تم إصلاح مشكلة حفظ الأرباح بنجاح!');
        console.log(`📊 القيمة القديمة: ${fixResult.oldValue}`);
        console.log(`📊 القيمة الجديدة: ${fixResult.newValue}`);
        console.log(`📊 الفرق: ${fixResult.difference}`);
        
        logSystem(`تم إصلاح مشكلة حفظ الأرباح: من ${fixResult.oldValue} إلى ${fixResult.newValue}`, 'info');
        
        return {
          success: true,
          fixed: true,
          oldValue: fixResult.oldValue,
          newValue: fixResult.newValue,
          calculatedProfit: totalCalculatedProfit
        };
      } else {
        throw new Error('فشل في إصلاح قيمة الأرباح');
      }
    } else {
      console.log('✅ قيمة الأرباح في الخزينة صحيحة');
      return {
        success: true,
        fixed: false,
        currentValue: currentProfit,
        calculatedProfit: totalCalculatedProfit
      };
    }

  } catch (error) {
    console.error('❌ خطأ في إصلاح مشكلة الأرباح:', error);
    logError(error, 'fixProfit5445Issue');
    
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * اختبار الإصلاح
 */
function testProfitFix() {
  console.log('\n🧪 اختبار الإصلاح...');
  
  try {
    const db = DatabaseManager.getDatabase();
    if (!db) {
      throw new Error('قاعدة البيانات غير متصلة');
    }

    // الحصول على القيمة الحالية
    const currentQuery = db.prepare('SELECT profit_total FROM cashbox WHERE id = 1');
    const currentResult = currentQuery.get();
    const currentValue = currentResult ? Number(currentResult.profit_total) : 0;
    
    console.log(`📊 القيمة الحالية: ${currentValue}`);

    // اختبار تحديث قيمة تجريبية
    const testValue = currentValue + 0.01;
    
    const testTransaction = db.transaction(() => {
      const updateStmt = db.prepare(`
        UPDATE cashbox 
        SET profit_total = ?, 
            updated_at = ? 
        WHERE id = 1
      `);
      
      const updateResult = updateStmt.run(testValue, new Date().toISOString());
      
      if (updateResult.changes === 0) {
        throw new Error('لم يتم تحديث أي صف');
      }
      
      // التحقق من الحفظ
      const verifyStmt = db.prepare('SELECT profit_total FROM cashbox WHERE id = 1');
      const verifyResult = verifyStmt.get();
      const savedValue = Number(verifyResult.profit_total);
      
      if (Math.abs(savedValue - testValue) > 0.01) {
        throw new Error(`فشل في حفظ القيمة التجريبية: متوقع ${testValue} لكن تم حفظ ${savedValue}`);
      }
      
      return { success: true, savedValue };
    });
    
    const testResult = testTransaction();
    
    if (testResult.success) {
      console.log(`✅ تم حفظ القيمة التجريبية بنجاح: ${testResult.savedValue}`);
      
      // إعادة القيمة الأصلية
      const restoreTransaction = db.transaction(() => {
        const restoreStmt = db.prepare(`
          UPDATE cashbox 
          SET profit_total = ?, 
              updated_at = ? 
          WHERE id = 1
        `);
        
        restoreStmt.run(currentValue, new Date().toISOString());
        return { success: true };
      });
      
      restoreTransaction();
      console.log(`🔄 تم إعادة القيمة الأصلية: ${currentValue}`);
      
      return { success: true, message: 'اختبار الحفظ نجح' };
    } else {
      throw new Error('فشل اختبار الحفظ');
    }

  } catch (error) {
    console.error('❌ فشل اختبار الإصلاح:', error);
    return { success: false, error: error.message };
  }
}

/**
 * تشغيل الإصلاح الكامل
 */
function runCompleteFix() {
  console.log('🚀 بدء الإصلاح الكامل لمشكلة الأرباح 5445...');
  console.log('='.repeat(60));
  
  const results = {
    fix: null,
    test: null,
    success: false
  };

  try {
    // 1. تشغيل الإصلاح
    results.fix = fixProfit5445Issue();
    
    if (results.fix.success) {
      // 2. اختبار الإصلاح
      results.test = testProfitFix();
      
      if (results.test.success) {
        results.success = true;
        console.log('\n🎉 تم إكمال الإصلاح الكامل بنجاح!');
        console.log('💡 يمكنك الآن التحقق من عرض الأرباح في واجهة النظام');
        
        if (results.fix.fixed) {
          console.log(`📊 تم تصحيح قيمة الأرباح من ${results.fix.oldValue} إلى ${results.fix.newValue}`);
        } else {
          console.log('📊 قيمة الأرباح كانت صحيحة بالفعل');
        }
      } else {
        console.log('\n⚠️ الإصلاح نجح لكن الاختبار فشل');
      }
    } else {
      console.log('\n❌ فشل في تشغيل الإصلاح');
    }

    console.log('\n📋 ملخص النتائج:');
    console.log(`   - الإصلاح: ${results.fix ? (results.fix.success ? 'نجح' : 'فشل') : 'لم يتم'}`);
    console.log(`   - الاختبار: ${results.test ? (results.test.success ? 'نجح' : 'فشل') : 'لم يتم'}`);
    console.log(`   - النتيجة الإجمالية: ${results.success ? 'نجح' : 'فشل'}`);
    
    console.log('='.repeat(60));
    
    return results;

  } catch (error) {
    console.error('❌ خطأ في الإصلاح الكامل:', error);
    results.success = false;
    results.error = error.message;
    return results;
  }
}

module.exports = {
  fixProfit5445Issue,
  testProfitFix,
  runCompleteFix
};

// تشغيل الإصلاح إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  console.log('🚀 بدء إصلاح مشكلة الأرباح 5445...');
  
  const results = runCompleteFix();
  
  console.log('\n🏁 انتهى الإصلاح');
  process.exit(results.success ? 0 : 1);
}
