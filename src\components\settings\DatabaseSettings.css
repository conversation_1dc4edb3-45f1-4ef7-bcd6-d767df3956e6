/* إعدادات قاعدة البيانات */
.database-settings {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.settings-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  color: #333;
  font-size: 1.5rem;
}

.settings-icon {
  margin-left: 10px;
  color: #4a6da7;
}

.settings-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.settings-section h3 {
  margin-bottom: 15px;
  color: #333;
  font-size: 1.2rem;
}

.settings-section p {
  margin-bottom: 15px;
  color: #666;
  line-height: 1.5;
}

/* محدد نوع قاعدة البيانات */
.database-type-selector {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.database-type-button {
  padding: 10px 15px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.database-type-button:hover {
  background-color: #e9e9e9;
}

.database-type-button.active {
  background-color: #4a6da7;
  color: white;
  border-color: #4a6da7;
}

.database-type-info {
  font-size: 0.9rem;
  color: #777;
  font-style: italic;
}

/* أزرار الإجراءات */
.database-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 15px;
  background-color: #4a6da7;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.action-button:hover {
  background-color: #3a5d97;
}

.action-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.action-button.danger {
  background-color: #dc3545;
}

.action-button.danger:hover {
  background-color: #c82333;
}

/* رسائل الحالة */
.status-message {
  margin-top: 15px;
  padding: 15px;
  border-radius: 4px;
  background-color: #f8f9fa;
  border-right: 4px solid #6c757d;
}

.status-message.success {
  background-color: #d4edda;
  border-color: #28a745;
  color: #155724;
}

.status-message.error {
  background-color: #f8d7da;
  border-color: #dc3545;
  color: #721c24;
}

.status-message.info {
  background-color: #cce5ff;
  border-color: #0d6efd;
  color: #004085;
}

.status-message pre {
  margin-top: 10px;
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  overflow: auto;
  max-height: 200px;
  font-family: monospace;
  font-size: 0.9rem;
  white-space: pre-wrap;
}

/* منطقة الخطر */
.danger-zone {
  border-bottom: none;
  border-radius: 4px;
  background-color: #fff8f8;
  padding: 20px;
  border: 1px solid #ffcccc;
}

.danger-zone h3 {
  color: #dc3545;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
  .database-type-selector {
    flex-direction: column;
  }
  
  .database-actions {
    flex-direction: column;
  }
  
  .action-button {
    width: 100%;
  }
}
