/**
 * وظيفة مستقلة لطباعة الفاتورة
 * هذه الوظيفة لا تعتمد على React أو أي مكتبات أخرى
 */

/**
 * طباعة فاتورة المبيعات
 * @param {Object} sale - بيانات عملية البيع
 * @param {Object} customer - بيانات العميل
 * @param {Object} settings - إعدادات النظام
 */
export default function printInvoice(sale, customer, settings = {}) {
  try {
    console.log('بدء طباعة الفاتورة:', { sale, customer, settings });

    // التحقق من البيانات
    if (!sale) {
      console.error('بيانات البيع غير موجودة');
      alert('بيانات البيع غير موجودة');
      return;
    }

    // التحقق من نوع الفاتورة (إذا كانت تبدأ بـ F فهي فاتورة فرعية)
    const invoiceNumber = sale?.invoice_number || '';
    const isSubInvoice = invoiceNumber.startsWith('F');

    console.log(`نوع الفاتورة: ${isSubInvoice ? 'فاتورة فرعية' : 'فاتورة رئيسية'}, رقم الفاتورة: ${invoiceNumber}`);

    // استخدام النموذج البسيط للفواتير الفرعية
    if (isSubInvoice) {
      return printSimpleInvoice(sale, customer, settings);
    }

    // فتح نافذة جديدة للطباعة
    const printWindow = window.open('', '_blank', 'width=800,height=600,toolbar=0,scrollbars=1,status=0,menubar=0');

    if (!printWindow) {
      alert('يرجى السماح بالنوافذ المنبثقة لطباعة الفاتورة');
      return;
    }

    // تسجيل معلومات التصحيح
    console.log('تم فتح نافذة الطباعة بنجاح');

    // تنسيق التاريخ (بالتنسيق الميلادي)
    const formatDate = (dateString) => {
      const date = new Date(dateString);
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      return `${year}/${month}/${day}`;
    };

    // الحصول على معلومات الشركة
    const companyName = settings.systemName || 'شركة اتش قروب';
    const companyAddress = settings.address || 'للتصميم و تصنيع الأثاث والديكورات';
    const companyPhone = settings.phone || '';
    const companyLogo = settings.logoUrl || '';

    // تنسيق التاريخ
    const invoiceDate = formatDate(sale?.transaction_date || new Date().toISOString());

    // بيانات العميل
    const customerName = customer?.name || sale?.customer_name || 'عميل';
    const customerPhone = customer?.phone || '';

    // إجمالي المبيعات
    const totalAmount = sale?.total_price || (sale?.quantity * sale?.price) || 0;

    // كتابة محتوى الفاتورة في النافذة الجديدة
    try {
      // تحضير محتوى HTML للفاتورة
      const invoiceContent = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>فاتورة رقم ${invoiceNumber}</title>
          <style>
            @page {
              size: A4 portrait;
              margin: 0.5cm;
            }
            body {
              font-family: 'Arial', sans-serif;
              direction: rtl;
              margin: 0;
              padding: 0;
              color: #333;
              background-color: white;
            }
            .invoice-container {
              width: 100%;
              max-width: 800px;
              margin: 0 auto;
              padding: 20px;
              box-sizing: border-box;
            }
            .header {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              margin-bottom: 20px;
              padding-bottom: 10px;
              border-bottom: 1px solid #ddd;
            }
            .company-info {
              text-align: right;
            }
            .company-name {
              font-size: 22px;
              font-weight: bold;
              color: #333;
              margin: 0 0 5px 0;
            }
            .company-address {
              font-size: 14px;
              color: #666;
              margin: 0;
            }
            .invoice-info {
              text-align: left;
            }
            .invoice-title {
              font-size: 18px;
              font-weight: bold;
              margin: 0 0 5px 0;
            }
            .invoice-number {
              font-size: 14px;
              margin: 0 0 5px 0;
            }
            .invoice-date {
              font-size: 14px;
              margin: 0;
            }
            .customer-info {
              margin-bottom: 20px;
            }
            .customer-title {
              font-size: 16px;
              font-weight: bold;
              margin: 0 0 10px 0;
            }
            .customer-details {
              display: flex;
              justify-content: space-between;
            }
            .customer-phone {
              font-size: 14px;
            }
            .invoice-table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 20px;
            }
            .invoice-table th, .invoice-table td {
              border: 1px solid #ddd;
              padding: 8px;
              text-align: center;
            }
            .invoice-table th {
              background-color: #f2f2f2;
              font-weight: bold;
            }
            .invoice-table tr:nth-child(even) {
              background-color: #f9f9f9;
            }
            .totals {
              margin-top: 20px;
              display: flex;
              flex-direction: column;
              align-items: flex-end;
            }
            .total-row {
              display: flex;
              justify-content: space-between;
              width: 300px;
              margin-bottom: 5px;
            }
            .total-label {
              font-weight: bold;
            }
            .total-value {
              font-weight: bold;
            }
            .terms {
              margin-top: 30px;
              border-top: 1px solid #ddd;
              padding-top: 10px;
            }
            .terms-title {
              font-weight: bold;
              margin-bottom: 5px;
            }
            .terms-item {
              margin-bottom: 5px;
              font-size: 14px;
            }
            .signature {
              margin-top: 40px;
              text-align: left;
            }
            .signature-line {
              display: inline-block;
              width: 200px;
              border-bottom: 1px solid #333;
              margin-bottom: 5px;
            }
            .signature-label {
              font-size: 14px;
            }
            .footer {
              margin-top: 30px;
              text-align: center;
              font-size: 12px;
              color: #666;
            }
            .highlight {
              color: #ff6600;
              font-weight: bold;
            }
            .logo {
              max-width: 100px;
              max-height: 100px;
            }
            @media print {
              body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
              }
              .no-print {
                display: none;
              }
            }
          </style>
        </head>
        <body>
          <div class="invoice-container">
            <!-- Header -->
            <div class="header">
              <div class="company-info">
                ${companyLogo ? `<img src="${companyLogo}" class="logo" alt="شعار الشركة" />` : ''}
                <h1 class="company-name">${companyName}</h1>
                <p class="company-address">${companyAddress}</p>
                ${companyPhone ? `<p class="company-address">${companyPhone}</p>` : ''}
              </div>
              <div class="invoice-info">
                <p class="invoice-title">فاتورة رقم: ${invoiceNumber}</p>
                <p class="invoice-date">التاريخ: ${invoiceDate}</p>
              </div>
            </div>

            <!-- Customer Info -->
            <div class="customer-info">
              <p class="customer-title">صاحب الطلب: ${customerName}</p>
              <div class="customer-details">
                <p class="customer-phone">رقم الهاتف: ${customerPhone || ''}</p>
              </div>
            </div>

            <!-- Invoice Items -->
            <table class="invoice-table">
              <thead>
                <tr>
                  <th>وصف المنتج</th>
                  <th>السعر</th>
                  <th>الكمية</th>
                  <th>المجموع</th>
                </tr>
              </thead>
              <tbody>
                ${sale.items && Array.isArray(sale.items) && sale.items.length > 0 ?
                  sale.items.map((item, index) => `
                    <tr>
                      <td>${item.item_name || 'منتج'}</td>
                      <td>${item.price ? item.price.toFixed(3) : '0.000'}</td>
                      <td>${item.quantity || '1'}</td>
                      <td>${item.total_price ? item.total_price.toFixed(3) : '0.000'}</td>
                    </tr>
                  `).join('')
                  :
                  `<tr>
                    <td>${sale.item_name || 'منتج'}</td>
                    <td>${(sale.price || 0).toFixed(3)}</td>
                    <td>${sale.quantity || 1}</td>
                    <td>${totalAmount.toFixed(3)}</td>
                  </tr>`
                }
              </tbody>
            </table>

            <!-- Totals -->
            <div class="totals">
              <div class="total-row">
                <span class="total-label">المبلغ المستلم:</span>
                <span class="total-value">0.000</span>
              </div>
              <div class="total-row">
                <span class="total-label">المبلغ الباقي:</span>
                <span class="total-value">0.000</span>
              </div>
              <div class="total-row">
                <span class="total-label">المبلغ الإجمالي:</span>
                <span class="total-value">${totalAmount.toFixed(3)}</span>
              </div>
            </div>

            <!-- Terms and Conditions -->
            <div class="terms">
              <p class="terms-title">بنود الاتفاق:</p>
              <p class="terms-item">مدة العرض 3 أيام من تاريخ اصدار الفاتورة.</p>
              <p class="terms-item">دفعة متقدمة بنسبة 50% عند الموافقة على العرض.</p>
              <p class="terms-item">دفعــة ثانية بنسبة 30% قبل 3 أيام من موعد التركيب.</p>
              <p class="terms-item">مدة التسليم 45 يوم من تاريخ استلام الدفعة الأولى.</p>
              <p class="terms-item highlight">هذا العرض شامل النقل والتنزيل الطابق الأرضي فقط.</p>
            </div>

            <!-- Signature -->
            <div class="signature">
              <div class="signature-line"></div>
              <p class="signature-label">التوقيع</p>
            </div>

            <!-- Footer -->
            <div class="footer">
              <p>نشكرك على ثقتك بنا ونتمنى لك تجربة ممتعة مع خدماتنا، شكراً لكم.</p>
            </div>
          </div>

          <script>
            window.onload = function() {
              setTimeout(function() {
                window.print();
                setTimeout(function() {
                  window.close();
                }, 500);
              }, 1000);
            };
          </script>
        </body>
      </html>
      `;

    // كتابة المحتوى إلى النافذة بطريقة آمنة
    try {
      // إنشاء مستند HTML جديد
      printWindow.document.open();

      // إنشاء عناصر HTML بشكل برمجي
      const htmlDoc = printWindow.document.implementation.createHTMLDocument('فاتورة مبيعات');

      // تعيين محتوى المستند
      htmlDoc.documentElement.innerHTML = invoiceContent;

      // نسخ المحتوى إلى نافذة الطباعة بطريقة آمنة
      try {
        printWindow.document.replaceChild(
          printWindow.document.importNode(htmlDoc.documentElement, true),
          printWindow.document.documentElement
        );
      } catch (replaceError) {
        console.error('خطأ في استبدال عنصر المستند:', replaceError);

        // طريقة بديلة: استخدام innerHTML
        printWindow.document.documentElement.innerHTML = htmlDoc.documentElement.innerHTML;
      }

      // إغلاق الكتابة في المستند
      printWindow.document.close();
    } catch (writeError) {
      console.error('خطأ في كتابة محتوى الفاتورة بالطريقة المتقدمة:', writeError);

      // استخدام الطريقة البديلة في حالة فشل الطريقة الأولى
      try {
        // إعادة فتح المستند
        printWindow.document.open();

        // كتابة المحتوى مباشرة إلى نافذة الطباعة
        printWindow.document.write('<!DOCTYPE html>' + invoiceContent);

        // إغلاق المستند
        printWindow.document.close();
      } catch (fallbackError) {
        console.error('خطأ في كتابة محتوى الفاتورة بالطريقة البديلة:', fallbackError);

        // الطريقة الأخيرة: استخدام document.write مباشرة
        printWindow.document.open();
        // @ts-ignore - تجاهل تحذير استخدام document.write
        printWindow.document.write('<!DOCTYPE html>' + invoiceContent);
        printWindow.document.close();
      }
    }
  } catch (error) {
    console.error('خطأ في كتابة محتوى الفاتورة:', error);
    alert('حدث خطأ أثناء إنشاء الفاتورة. يرجى المحاولة مرة أخرى.');
  }
  } catch (error) {
    console.error('خطأ في طباعة الفاتورة:', error);
    alert('حدث خطأ أثناء محاولة طباعة الفاتورة. يرجى المحاولة مرة أخرى.');
  }
}

/**
 * طباعة فاتورة المبيعات بتنسيق بسيط (للفواتير الفرعية)
 * @param {Object} sale - بيانات عملية البيع
 * @param {Object} customer - بيانات العميل
 * @param {Object} settings - إعدادات النظام
 */
function printSimpleInvoice(sale, customer, settings = {}) {
  try {
    console.log('بدء طباعة الفاتورة البسيطة:', { sale, customer, settings });

    // فتح نافذة جديدة للطباعة
    const printWindow = window.open('', '_blank', 'width=800,height=600,toolbar=0,scrollbars=1,status=0,menubar=0');

    if (!printWindow) {
      alert('يرجى السماح بالنوافذ المنبثقة لطباعة الفاتورة');
      return;
    }

    // تنسيق التاريخ (بالتنسيق الميلادي)
    const formatDate = (dateString) => {
      const date = new Date(dateString);
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      return `${year}/${month}/${day}`;
    };

    // الحصول على معلومات الشركة
    const companyName = settings.systemName || 'شركة اتش قروب';
    const companyAddress = settings.address || 'للتصميم و تصنيع الأثاث والديكورات';
    const companyPhone = settings.phone || '';
    const companyLogo = settings.logoUrl || '';

    // تنسيق التاريخ
    const invoiceDate = formatDate(sale?.transaction_date || new Date().toISOString());

    // استخدام رقم الفاتورة الموجود
    const invoiceNumber = sale?.invoice_number || '';

    // بيانات العميل
    const customerName = customer?.name || sale?.customer_name || 'عميل';
    const customerPhone = customer?.phone || '';

    // إعداد الأصناف
    let items = [];
    if (sale.items && Array.isArray(sale.items)) {
      items = sale.items;
    } else {
      // إذا كان هناك صنف واحد فقط
      items = [{
        item_name: sale.item_name || 'منتج',
        quantity: sale.quantity || 1,
        price: sale.price || 0,
        total_price: sale.total_price || (sale.quantity * sale.price) || 0
      }];
    }

    // إجمالي المبيعات
    const totalAmount = sale?.total_price || items.reduce((sum, item) => sum + (item.total_price || 0), 0) || 0;

    // تحضير محتوى HTML للفاتورة البسيطة
    const invoiceContent = `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>فاتورة رقم ${invoiceNumber}</title>
        <style>
          @page {
            size: A4 portrait;
            margin: 0.5cm;
          }
          body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            margin: 0;
            padding: 0;
            color: #333;
            background-color: white;
          }
          .invoice-container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            box-sizing: border-box;
          }
          .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
          }
          .company-info {
            text-align: right;
          }
          .company-name {
            font-size: 22px;
            font-weight: bold;
            color: #333;
            margin: 0 0 5px 0;
          }
          .company-address {
            font-size: 14px;
            color: #666;
            margin: 0;
          }
          .invoice-info {
            text-align: left;
          }
          .invoice-title {
            font-size: 18px;
            font-weight: bold;
            margin: 0 0 5px 0;
          }
          .invoice-number {
            font-size: 14px;
            margin: 0 0 5px 0;
          }
          .invoice-date {
            font-size: 14px;
            margin: 0;
          }
          .customer-info {
            margin-bottom: 20px;
          }
          .customer-title {
            font-size: 16px;
            font-weight: bold;
            margin: 0 0 10px 0;
          }
          .customer-details {
            display: flex;
            justify-content: space-between;
          }
          .customer-phone {
            font-size: 14px;
          }
          .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
          }
          .invoice-table th, .invoice-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
          }
          .invoice-table th {
            background-color: #f2f2f2;
            font-weight: bold;
          }
          .invoice-table tr:nth-child(even) {
            background-color: #f9f9f9;
          }
          .totals {
            margin-top: 20px;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
          }
          .total-row {
            display: flex;
            justify-content: space-between;
            width: 300px;
            margin-bottom: 5px;
          }
          .total-label {
            font-weight: bold;
          }
          .total-value {
            font-weight: bold;
          }
          .terms {
            margin-top: 30px;
            border-top: 1px solid #ddd;
            padding-top: 10px;
          }
          .terms-title {
            font-weight: bold;
            margin-bottom: 5px;
          }
          .terms-item {
            margin-bottom: 5px;
            font-size: 14px;
          }
          .signature {
            margin-top: 40px;
            text-align: left;
          }
          .signature-line {
            display: inline-block;
            width: 200px;
            border-bottom: 1px solid #333;
            margin-bottom: 5px;
          }
          .signature-label {
            font-size: 14px;
          }
          .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 12px;
            color: #666;
          }
          .highlight {
            color: #ff6600;
            font-weight: bold;
          }
          .logo {
            max-width: 100px;
            max-height: 100px;
          }
          @media print {
            body {
              -webkit-print-color-adjust: exact;
              print-color-adjust: exact;
            }
            .no-print {
              display: none;
            }
          }
        </style>
      </head>
      <body>
        <div class="invoice-container">
          <!-- Header -->
          <div class="header">
            <div class="company-info">
              ${companyLogo ? `<img src="${companyLogo}" class="logo" alt="شعار الشركة" />` : ''}
              <h1 class="company-name">${companyName}</h1>
              <p class="company-address">${companyAddress}</p>
              ${companyPhone ? `<p class="company-address">${companyPhone}</p>` : ''}
            </div>
            <div class="invoice-info">
              <p class="invoice-title">فاتورة رقم: ${invoiceNumber}</p>
              <p class="invoice-date">التاريخ: ${invoiceDate}</p>
            </div>
          </div>

          <!-- Customer Info -->
          <div class="customer-info">
            <p class="customer-title">صاحب الطلب: ${customerName}</p>
            <div class="customer-details">
              <p class="customer-phone">رقم الهاتف: ${customerPhone || ''}</p>
            </div>
          </div>

          <!-- Invoice Items -->
          <table class="invoice-table">
            <thead>
              <tr>
                <th>وصف المنتج</th>
                <th>السعر</th>
                <th>الكمية</th>
                <th>المجموع</th>
              </tr>
            </thead>
            <tbody>
              ${items.map((item, index) => `
                <tr>
                  <td>${item.item_name || 'منتج'}</td>
                  <td>${item.price ? item.price.toFixed(3) : '0.000'}</td>
                  <td>${item.quantity || '1'}</td>
                  <td>${item.total_price ? item.total_price.toFixed(3) : '0.000'}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>

          <!-- Totals -->
          <div class="totals">
            <div class="total-row">
              <span class="total-label">المبلغ المستلم:</span>
              <span class="total-value">0.000</span>
            </div>
            <div class="total-row">
              <span class="total-label">المبلغ الباقي:</span>
              <span class="total-value">0.000</span>
            </div>
            <div class="total-row">
              <span class="total-label">المبلغ الإجمالي:</span>
              <span class="total-value">${totalAmount.toFixed(3)}</span>
            </div>
          </div>

          <!-- Terms and Conditions -->
          <div class="terms">
            <p class="terms-title">بنود الاتفاق:</p>
            <p class="terms-item">مدة العرض 3 أيام من تاريخ اصدار الفاتورة.</p>
            <p class="terms-item">دفعة متقدمة بنسبة 50% عند الموافقة على العرض.</p>
            <p class="terms-item">دفعــة ثانية بنسبة 30% قبل 3 أيام من موعد التركيب.</p>
            <p class="terms-item">مدة التسليم 45 يوم من تاريخ استلام الدفعة الأولى.</p>
            <p class="terms-item highlight">هذا العرض شامل النقل والتنزيل الطابق الأرضي فقط.</p>
          </div>

          <!-- Signature -->
          <div class="signature">
            <div class="signature-line"></div>
            <p class="signature-label">التوقيع</p>
          </div>

          <!-- Footer -->
          <div class="footer">
            <p>نشكرك على ثقتك بنا ونتمنى لك تجربة ممتعة مع خدماتنا، شكراً لكم.</p>
          </div>
        </div>

        <script>
          window.onload = function() {
            // تأكد من تحميل الصفحة بشكل كامل قبل الطباعة
            if (document.readyState === 'complete') {
              setTimeout(printAndClose, 1000);
            } else {
              window.addEventListener('load', function() {
                setTimeout(printAndClose, 1000);
              });
            }

            function printAndClose() {
              try {
                window.print();
                // إغلاق النافذة بعد الطباعة
                setTimeout(function() {
                  window.close();
                }, 500);
              } catch (e) {
                console.error('خطأ في الطباعة:', e);
                alert('حدث خطأ أثناء الطباعة. يرجى المحاولة مرة أخرى.');
              }
            }
          };
        </script>
      </body>
    </html>
    `;

    // كتابة المحتوى إلى النافذة بطريقة آمنة
    try {
      // إنشاء مستند HTML جديد
      printWindow.document.open();

      // إنشاء عناصر HTML بشكل برمجي
      const htmlDoc = printWindow.document.implementation.createHTMLDocument('فاتورة مبيعات');

      // تعيين محتوى المستند
      htmlDoc.documentElement.innerHTML = invoiceContent;

      // نسخ المحتوى إلى نافذة الطباعة بطريقة آمنة
      try {
        printWindow.document.replaceChild(
          printWindow.document.importNode(htmlDoc.documentElement, true),
          printWindow.document.documentElement
        );
      } catch (replaceError) {
        console.error('خطأ في استبدال عنصر المستند:', replaceError);

        // طريقة بديلة: استخدام innerHTML
        printWindow.document.documentElement.innerHTML = htmlDoc.documentElement.innerHTML;
      }

      // إغلاق الكتابة في المستند
      printWindow.document.close();
    } catch (error) {
      console.error('خطأ في كتابة محتوى الفاتورة البسيطة:', error);

      // استخدام الطريقة البديلة في حالة فشل الطريقة الأولى
      try {
        printWindow.document.open();
        // @ts-ignore - تجاهل تحذير استخدام document.write
        printWindow.document.write(invoiceContent);
        printWindow.document.close();
      } catch (fallbackError) {
        console.error('خطأ في الطريقة البديلة:', fallbackError);
        alert('حدث خطأ أثناء إنشاء الفاتورة. يرجى المحاولة مرة أخرى.');
      }
    }
  } catch (error) {
    console.error('خطأ في طباعة الفاتورة البسيطة:', error);
    alert('حدث خطأ أثناء محاولة طباعة الفاتورة. يرجى المحاولة مرة أخرى.');
  }
}

export function exportToPDF(sale, customer, settings = {}) {
  try {
    // التحقق من وجود jsPDF
    if (typeof window.jspdf === 'undefined') {
      console.error('مكتبة jsPDF غير متوفرة');
      alert('لا يمكن تصدير PDF. يرجى استخدام وظيفة الطباعة واختيار "حفظ كملف PDF" من خيارات الطباعة.');
      return;
    }

    // تنسيق التاريخ (بالتنسيق الميلادي)
    const formatDate = (dateString) => {
      const date = new Date(dateString);
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      return `${year}/${month}/${day}`;
    };

    // الحصول على معلومات الشركة
    const companyName = settings.systemName || 'شركة اتش قروب';
    const companyAddress = settings.address || 'للتصميم و تصنيع الأثاث والديكورات';
    const companyLogo = settings.logoUrl || (window.appSettings && window.appSettings.logoUrl) || '';

    // تنسيق التاريخ
    const invoiceDate = formatDate(sale?.transaction_date || new Date().toISOString());

    // إنشاء رقم الفاتورة
    const invoiceNumber = sale?.invoice_number || `H${Math.floor(Math.random() * 100000).toString().padStart(5, '0')}`;

    // بيانات العميل
    const customerName = customer?.name || sale?.customer_name || 'عميل';
    const customerPhone = customer?.phone || '';

    // إجمالي المبيعات
    const totalAmount = sale?.total_price || (sale?.quantity * sale?.price) || 0;

    // إنشاء مستند PDF جديد
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4',
      putOnlyUsedFonts: true,
      compress: true
    });

    // الحصول على أبعاد الصفحة
    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();

    // تعيين الألوان
    const primaryColor = [26, 58, 95]; // #1a3a5f - اللون الأزرق الداكن
    const accentColor = [26, 58, 95]; // استخدام نفس اللون الأزرق بدلاً من البرتقالي

    // إضافة الشعار إذا كان متوفراً
    if (companyLogo) {
      try {
        doc.addImage(companyLogo, 'PNG', 15, 10, 25, 25);
      } catch (logoError) {
        console.error('خطأ في إضافة الشعار:', logoError);
      }
    }

    // إضافة اسم الشركة
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2]);
    doc.setFontSize(18);
    doc.text(companyName, 100, 20, { align: 'right' });

    // إضافة شعار الشركة
    doc.setTextColor(accentColor[0], accentColor[1], accentColor[2]);
    doc.setFontSize(12);
    doc.text(companyAddress, 100, 28, { align: 'right' });

    // إضافة معلومات الفاتورة
    doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2]);
    doc.setFontSize(12);
    doc.text(`فاتورة رقم: ${invoiceNumber}`, pageWidth - 15, 20, { align: 'right' });
    doc.text(`التاريخ: ${invoiceDate}`, pageWidth - 15, 28, { align: 'right' });

    // إضافة خط فاصل
    doc.setDrawColor(accentColor[0], accentColor[1], accentColor[2]);
    doc.setLineWidth(0.5);
    doc.line(15, 40, pageWidth - 15, 40);

    // إضافة معلومات العميل
    doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2]);
    doc.setFontSize(11);
    doc.text(`صاحب الطلب: ${customerName}`, 15, 50);
    doc.text(`رقم الهاتف: ${customerPhone || '-'}`, pageWidth - 15, 50, { align: 'right' });

    // إضافة جدول المنتجات
    const tableColumn = ['المجموع', 'السعر', 'الكمية', 'وصف المنتج', '#'];
    const tableRows = [[
      `${totalAmount.toFixed(3)}`,
      `${(sale.price || 0).toFixed(3)}`,
      `${sale.quantity || 1}`,
      `${sale.item_name || 'منتج'}`,
      '1'
    ]];

    doc.autoTable({
      head: [tableColumn],
      body: tableRows,
      startY: 60,
      styles: {
        halign: 'right',
        font: 'helvetica',
        fontSize: 10,
        textColor: [50, 50, 50]
      },
      headStyles: {
        fillColor: [247, 249, 252],
        textColor: primaryColor,
        fontStyle: 'bold',
        lineWidth: 0.1,
        lineColor: accentColor
      },
      columnStyles: {
        0: { fontStyle: 'bold' }
      },
      theme: 'grid',
      tableLineColor: [220, 220, 220]
    });

    // إضافة ملخص الفاتورة
    const finalY = doc.lastAutoTable ? doc.lastAutoTable.finalY + 10 : 100;

    doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2]);
    doc.setFontSize(11);
    doc.text('المبلغ المستلم:', 15, finalY);
    doc.text('0.000', pageWidth - 15, finalY, { align: 'right' });

    doc.text('المبلغ الباقي:', 15, finalY + 8);
    doc.text('0.000', pageWidth - 15, finalY + 8, { align: 'right' });

    doc.setFontSize(13);
    doc.setFont('helvetica', 'bold');
    doc.text('المبلغ الإجمالي:', 15, finalY + 18);
    doc.text(`${totalAmount.toFixed(3)}`, pageWidth - 15, finalY + 18, { align: 'right' });



    // إضافة مكان للتوقيع
    doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2]);
    doc.setFont('helvetica', 'normal');
    doc.text('التوقيع:', pageWidth - 50, finalY + 35);
    doc.line(pageWidth - 50, finalY + 40, pageWidth - 15, finalY + 40);

    // إضافة تذييل الصفحة
    doc.setFontSize(9);
    doc.setTextColor(100, 100, 100);
    doc.text('شكراً لكم.', pageWidth / 2, pageHeight - 20, { align: 'center' });

    // حفظ الملف
    doc.save(`فاتورة_${invoiceNumber}.pdf`);
  } catch (error) {
    console.error('خطأ في تصدير الفاتورة كملف PDF:', error);
    alert('حدث خطأ أثناء تصدير الفاتورة كملف PDF. يرجى استخدام وظيفة الطباعة واختيار "حفظ كملف PDF" من خيارات الطباعة.');
  }
}

// تم تصدير الوظيفة كـ default في الأعلى
