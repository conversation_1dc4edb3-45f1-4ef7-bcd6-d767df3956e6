# إصلاح خطأ تحديث الرصيد الابتدائي للخزينة

## وصف المشكلة

كان هناك خطأ في نظام الخزينة يظهر الرسائل التالية:

```
CashboxProvider.js:159 خطأ في بنية النتيجة المستلمة: Object
CashboxProvider.js:168 خطأ في تحديث الرصيد الابتدائي للخزينة: Error: تم استلام بيانات غير صالحة من الخادم
```

## سبب المشكلة

المشكلة كانت بسبب:

1. **عدم تطابق بنية جدول الخزينة**: ملف `src\db\schema\cashbox.js` لم يكن يحتوي على حقل `transport_total`
2. **حقول مفقودة في البيانات المرجعة**: دالة `updateInitialBalance` لم تكن ترجع حقل `transport_total`
3. **معالجة أخطاء غير كافية**: لم تكن هناك معلومات كافية لتشخيص المشكلة

## الإصلاحات المطبقة

### 1. تحديث بنية جدول الخزينة

**الملف**: `src\db\schema\cashbox.js`

```javascript
const createCashboxTable = `
CREATE TABLE IF NOT EXISTS cashbox (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  initial_balance REAL NOT NULL,
  current_balance REAL NOT NULL,
  profit_total REAL DEFAULT 0,
  sales_total REAL DEFAULT 0,
  purchases_total REAL DEFAULT 0,
  returns_total REAL DEFAULT 0,
  transport_total REAL DEFAULT 0,  // ← تم إضافة هذا الحقل
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
`;
```

### 2. تحسين معالجة الأخطاء

**الملف**: `src\context\providers\CashboxProvider.js`

- إضافة التحقق من وجود جميع الحقول المطلوبة
- إضافة القيم الافتراضية للحقول المفقودة
- تحسين رسائل الخطأ لتوضيح سبب المشكلة

### 3. إضافة حقل transport_total

**الملف**: `cashbox-manager.js`

- إضافة `transport_total` إلى بيانات الخزينة المرجعة
- إضافة `transport_total` إلى إشعارات تحديث الخزينة
- إصلاح المتغيرات غير المعرفة في رسائل السجل

## كيفية تطبيق الإصلاح

### الخطوة 1: إصلاح بنية قاعدة البيانات

```bash
node fix-cashbox-database-structure.js
```

هذا السكريبت سيقوم بـ:
- التحقق من بنية جدول الخزينة
- إضافة الحقول المفقودة (مثل `transport_total`)
- تحديث القيم الافتراضية
- اختبار التحديثات

### الخطوة 2: اختبار التحديث

```bash
node test-cashbox-update.js
```

هذا السكريبت سيقوم بـ:
- اختبار دالة تحديث الرصيد الابتدائي
- التحقق من صحة البيانات المرجعة
- التأكد من وجود جميع الحقول المطلوبة

### الخطوة 3: إعادة تشغيل التطبيق

بعد تطبيق الإصلاحات، أعد تشغيل التطبيق واختبر تحديث الرصيد الابتدائي.

## التحقق من نجاح الإصلاح

1. **افتح واجهة الخزينة**
2. **جرب تحديث الرصيد الابتدائي**
3. **تأكد من عدم ظهور رسائل خطأ في وحدة التحكم**
4. **تأكد من تحديث البيانات بشكل صحيح**

## الملفات المعدلة

- `src\db\schema\cashbox.js` - إضافة حقل `transport_total`
- `src\context\providers\CashboxProvider.js` - تحسين معالجة الأخطاء
- `cashbox-manager.js` - إضافة `transport_total` وإصلاح المتغيرات

## الملفات الجديدة

- `fix-cashbox-database-structure.js` - سكريبت إصلاح بنية قاعدة البيانات
- `test-cashbox-update.js` - سكريبت اختبار تحديث الرصيد الابتدائي
- `CASHBOX_ERROR_FIX.md` - هذا الملف

## ملاحظات مهمة

1. **النسخ الاحتياطية**: تأكد من عمل نسخة احتياطية من قاعدة البيانات قبل تطبيق الإصلاحات
2. **اختبار شامل**: اختبر جميع وظائف الخزينة بعد تطبيق الإصلاح
3. **مراقبة الأداء**: راقب أداء النظام للتأكد من عدم وجود مشاكل جديدة

## في حالة استمرار المشكلة

إذا استمرت المشكلة بعد تطبيق الإصلاحات:

1. تحقق من سجلات وحدة التحكم للحصول على تفاصيل إضافية
2. تأكد من تطبيق جميع الإصلاحات بشكل صحيح
3. جرب إعادة إنشاء قاعدة البيانات من البداية
4. تواصل مع فريق التطوير مع تفاصيل الخطأ الجديد
