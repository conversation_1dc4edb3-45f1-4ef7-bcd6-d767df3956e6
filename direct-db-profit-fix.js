/**
 * إصلاح مباشر لقاعدة البيانات لتصحيح قيمة الأرباح إلى 5450
 * يعمل مباشرة مع قاعدة البيانات بدون الحاجة لتشغيل التطبيق
 */

console.log('🔧 إصلاح مباشر لقاعدة البيانات - تصحيح الأرباح إلى 5450...');
console.log('='.repeat(60));

try {
  // 1. استيراد better-sqlite3 مباشرة
  const Database = require('better-sqlite3');
  const path = require('path');
  const os = require('os');
  
  // 2. تحديد مسار قاعدة البيانات
  const dbPath = path.join(
    os.homedir(),
    'AppData',
    'Roaming',
    'warehouse-management-system',
    'wms-database',
    'warehouse.db'
  );
  
  console.log(`📂 مسار قاعدة البيانات: ${dbPath}`);
  
  // 3. الاتصال بقاعدة البيانات
  const db = new Database(dbPath);
  console.log('✅ تم الاتصال بقاعدة البيانات مباشرة');

  // 4. فحص القيمة الحالية
  const currentQuery = db.prepare('SELECT profit_total FROM cashbox WHERE id = 1');
  const currentResult = currentQuery.get();
  const currentProfit = currentResult ? Number(currentResult.profit_total) : 0;
  
  console.log(`💾 الأرباح الحالية: ${currentProfit}`);
  console.log(`🎯 الأرباح المطلوبة: 5450`);

  // 5. حساب الأرباح من معاملات البيع للتحقق
  console.log('\n📊 حساب الأرباح من معاملات البيع...');
  const profitQuery = db.prepare(`
    SELECT 
      id,
      quantity,
      profit,
      transaction_date
    FROM transactions 
    WHERE transaction_type = 'sale' 
    AND profit > 0
    ORDER BY transaction_date DESC
  `);
  
  const salesTransactions = profitQuery.all();
  console.log(`📋 عدد معاملات البيع: ${salesTransactions.length}`);
  
  let calculatedProfit = 0;
  salesTransactions.forEach((transaction, index) => {
    const profit = Number(transaction.profit) || 0;
    calculatedProfit += profit;
    if (index < 5) { // عرض أول 5 معاملات فقط
      console.log(`   ${index + 1}. المعاملة ${transaction.id}: الربح = ${profit}`);
    }
  });
  
  if (salesTransactions.length > 5) {
    console.log(`   ... و ${salesTransactions.length - 5} معاملة أخرى`);
  }
  
  console.log(`💰 إجمالي الأرباح المحسوب: ${calculatedProfit}`);

  // 6. تصحيح القيمة إلى 5450
  const correctProfit = 5450;
  console.log(`\n🔧 تصحيح قيمة الأرباح إلى ${correctProfit}...`);
  
  // استخدام معاملة قاعدة البيانات لضمان الحفظ
  const fixTransaction = db.transaction(() => {
    // تحديث القيمة
    const updateStmt = db.prepare(`
      UPDATE cashbox 
      SET profit_total = ?, 
          updated_at = ? 
      WHERE id = 1
    `);
    
    const updateResult = updateStmt.run(correctProfit, new Date().toISOString());
    
    if (updateResult.changes === 0) {
      throw new Error('لم يتم تحديث أي صف في جدول cashbox');
    }
    
    console.log(`✅ تم تحديث الخزينة، الصفوف المتأثرة: ${updateResult.changes}`);
    
    // التحقق الفوري من الحفظ
    const verifyStmt = db.prepare('SELECT profit_total FROM cashbox WHERE id = 1');
    const verifyResult = verifyStmt.get();
    
    if (!verifyResult) {
      throw new Error('فشل في استرجاع البيانات للتحقق');
    }
    
    const savedValue = Number(verifyResult.profit_total);
    console.log(`💾 القيمة المحفوظة: ${savedValue}`);
    
    if (Math.abs(savedValue - correctProfit) > 0.01) {
      throw new Error(`فشل في حفظ القيمة: متوقع ${correctProfit} لكن تم حفظ ${savedValue}`);
    }
    
    return {
      success: true,
      oldValue: currentProfit,
      newValue: savedValue
    };
  });
  
  const result = fixTransaction();
  
  if (result.success) {
    console.log('🎉 تم تصحيح قيمة الأرباح إلى 5450 بنجاح!');
    console.log(`📊 القيمة القديمة: ${result.oldValue}`);
    console.log(`📊 القيمة الجديدة: ${result.newValue}`);
    console.log(`📊 الفرق: ${result.newValue - result.oldValue}`);
    
    // 7. التحقق النهائي
    console.log('\n🔍 التحقق النهائي...');
    const finalQuery = db.prepare('SELECT profit_total FROM cashbox WHERE id = 1');
    const finalResult = finalQuery.get();
    const finalProfit = finalResult ? Number(finalResult.profit_total) : 0;
    
    console.log(`💾 القيمة النهائية في قاعدة البيانات: ${finalProfit}`);
    
    if (Math.abs(finalProfit - 5450) < 0.01) {
      console.log('\n🎯 النتيجة النهائية:');
      console.log('   ✅ تم تصحيح قيمة الأرباح إلى 5450');
      console.log('   ✅ تم حفظ القيمة في قاعدة البيانات');
      console.log('   ✅ تم التحقق من صحة الحفظ');
      console.log('\n💡 الخطوات التالية:');
      console.log('   1. تشغيل التطبيق: npm start');
      console.log('   2. الذهاب إلى قسم الخزينة');
      console.log('   3. التحقق من عرض الأرباح 5450');
      console.log('   4. إجراء عملية بيع تجريبية للتأكد من التحديث');
    } else {
      console.log(`❌ خطأ: القيمة النهائية ${finalProfit} لا تطابق المطلوب 5450`);
    }
    
    // 8. إضافة سجل في قاعدة البيانات للتوثيق
    try {
      const logStmt = db.prepare(`
        INSERT INTO cashbox_transactions (type, amount, source, notes, created_at)
        VALUES (?, ?, ?, ?, ?)
      `);
      
      logStmt.run(
        'adjustment',
        result.newValue - result.oldValue,
        'system_correction',
        `تصحيح قيمة الأرباح من ${result.oldValue} إلى ${result.newValue}`,
        new Date().toISOString()
      );
      
      console.log('📝 تم تسجيل عملية التصحيح في سجل الخزينة');
    } catch (logError) {
      console.log('⚠️ تحذير: لم يتم تسجيل عملية التصحيح في السجل');
    }
    
  } else {
    throw new Error('فشل في تصحيح قيمة الأرباح');
  }

  // 9. إغلاق قاعدة البيانات
  db.close();
  console.log('🔒 تم إغلاق اتصال قاعدة البيانات');

} catch (error) {
  console.error('❌ خطأ في الإصلاح المباشر:', error.message);
  
  if (error.message.includes('SQLITE_CANTOPEN')) {
    console.log('\n💡 نصائح لحل المشكلة:');
    console.log('   1. تأكد من أن التطبيق غير يعمل');
    console.log('   2. تحقق من مسار قاعدة البيانات');
    console.log('   3. تأكد من صلاحيات الوصول للملف');
  } else if (error.message.includes('no such table')) {
    console.log('\n💡 المشكلة: جدول الخزينة غير موجود');
    console.log('   1. تشغيل التطبيق مرة واحدة لإنشاء الجداول');
    console.log('   2. ثم تشغيل هذا الإصلاح مرة أخرى');
  }
}

console.log('\n' + '='.repeat(60));
console.log('🏁 انتهى الإصلاح المباشر');
