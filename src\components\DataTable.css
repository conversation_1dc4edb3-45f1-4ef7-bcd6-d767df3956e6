/* أنماط مكون جدول البيانات */
.app-datatable {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  margin-bottom: var(--spacing-lg);
}

/* شريط الأدوات */
.app-datatable-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: rgba(0, 0, 0, 0.02);
  border-bottom: 1px solid var(--border-color);
}

/* حقل البحث */
.app-datatable-search {
  position: relative;
  flex: 1;
  max-width: 300px;
}

.app-datatable-search input {
  width: 100%;
  padding: 8px 12px 8px 35px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: 0.9rem;
  transition: border-color 0.2s;
}

.app-datatable-search input:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.app-datatable-search-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-light);
  font-size: 0.9rem;
}

/* زر التصفية */
.app-datatable-filter-toggle {
  background-color: transparent;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  padding: 6px 12px;
  font-size: 0.9rem;
  color: var(--text-dark);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.2s;
}

.app-datatable-filter-toggle:hover:not(:disabled) {
  background-color: rgba(0, 0, 0, 0.05);
}

.app-datatable-filter-toggle:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* حقول التصفية */
.app-datatable-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: rgba(0, 0, 0, 0.01);
  border-bottom: 1px solid var(--border-color);
}

.app-datatable-filter {
  flex: 1;
  min-width: 150px;
}

.app-datatable-filter input {
  width: 100%;
  padding: 6px 10px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: 0.85rem;
}

.app-datatable-filter input:focus {
  border-color: var(--primary-color);
  outline: none;
}

/* حاوية الجدول */
.app-datatable-table-container {
  overflow-x: auto;
}

/* الجدول */
.app-datatable-table {
  width: 100%;
  border-collapse: collapse;
}

.app-datatable-table th,
.app-datatable-table td {
  padding: var(--spacing-md);
  text-align: right;
  border-bottom: 1px solid var(--border-color);
}

.app-datatable-table th {
  background-color: rgba(0, 0, 0, 0.02);
  font-weight: 600;
  color: var(--text-dark);
  position: sticky;
  top: 0;
  z-index: 10;
}

.app-datatable-table th.sortable {
  cursor: pointer;
  user-select: none;
}

.app-datatable-table th.sortable:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.app-datatable-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.app-datatable-sort-icon {
  margin-right: 5px;
  color: var(--text-light);
  font-size: 0.8rem;
}

.app-datatable-table tbody tr {
  transition: background-color 0.2s;
}

.app-datatable-table tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.app-datatable-table tbody tr.clickable {
  cursor: pointer;
}

/* حالة التحميل */
.app-datatable-loading {
  text-align: center;
  padding: var(--spacing-xl) !important;
  color: var(--text-light);
}

.app-datatable-spinner {
  display: inline-block;
  width: 24px;
  height: 24px;
  border: 2px solid rgba(var(--primary-rgb), 0.2);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s linear infinite;
  margin-left: 10px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* حالة فارغة */
.app-datatable-empty {
  text-align: center;
  padding: var(--spacing-xl) !important;
  color: var(--text-light);
  font-style: italic;
}

/* الصفحات */
.app-datatable-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: rgba(0, 0, 0, 0.02);
  border-top: 1px solid var(--border-color);
}

.app-datatable-pagination-info {
  font-size: 0.9rem;
  color: var(--text-light);
}

.app-datatable-pagination-buttons {
  display: flex;
  gap: 5px;
  align-items: center;
}

.app-datatable-pagination-button {
  min-width: 32px;
  height: 32px;
  padding: 0 8px;
  border: 1px solid var(--border-color);
  background-color: white;
  border-radius: var(--border-radius-sm);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.app-datatable-pagination-button:hover:not(:disabled) {
  background-color: rgba(0, 0, 0, 0.05);
}

.app-datatable-pagination-button.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.app-datatable-pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.app-datatable-pagination-ellipsis {
  margin: 0 5px;
}

/* تنسيقات للشاشات الصغيرة */
@media (max-width: 768px) {
  .app-datatable-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .app-datatable-search {
    max-width: none;
  }
  
  .app-datatable-pagination {
    flex-direction: column;
    gap: 10px;
  }
  
  .app-datatable-pagination-buttons {
    width: 100%;
    justify-content: center;
  }
}
