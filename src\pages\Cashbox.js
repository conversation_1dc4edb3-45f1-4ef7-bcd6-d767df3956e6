import React, { useState, useEffect, useRef } from 'react';
// تم إزالة استيراد ProfitFixButton - الأرباح تتحدث تلقائياً الآن
import {
  FaWallet,
  FaMoneyBillWave,
  FaPlus,
  FaArrowUp,
  FaArrowDown,
  FaFilter,
  FaExchangeAlt,
  FaTools,
  FaTrash,
  FaTruck,
  FaChartLine
} from 'react-icons/fa';
import Card from '../components/Card';
import Button from '../components/Button';
import Modal from '../components/Modal';
import FormattedCurrency from '../components/FormattedCurrency';
import { useApp } from '../context/AppContext';
import './Cashbox.css';

const Cashbox = () => {
  // State variables
  const [cashbox, setCashbox] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [transactions, setTransactions] = useState([]);
  const [calculatedProfit, setCalculatedProfit] = useState(0);

  // Modal states
  const [showInitialBalanceModal, setShowInitialBalanceModal] = useState(false);
  const [showEditInitialBalanceModal, setShowEditInitialBalanceModal] = useState(false);
  const [showAddTransactionModal, setShowAddTransactionModal] = useState(false);
  const [initialBalance, setInitialBalance] = useState(0);
  const [transactionForm, setTransactionForm] = useState({
    type: 'income',
    amount: '',
    source: '',
    notes: ''
  });
  // تم إزالة حالة fixingProfits - الأرباح تتحدث تلقائياً الآن

  // Filter states - تم تعديل الفلاتر لعرض جميع المعاملات افتراضيًا
  const [filters, setFilters] = useState({
    type: '',
    source: '', // تم تعيين قيمة فارغة لعرض جميع المعاملات
    startDate: '',
    endDate: ''
  });
  const [showFilters, setShowFilters] = useState(false);

  // App context
  const { showNotification, updateCashboxInitialBalance } = useApp();

  // Refs
  const formRef = useRef(null);
  const isMounted = useRef(true);

  // دالة حساب الأرباح الصحيحة (نفس منطق تحليل الأرباح الشهرية)
  const calculateCorrectProfit = async () => {
    try {
      console.log('[PROFIT-CALC] بدء حساب الأرباح الصحيحة...');

      if (!window.api || !window.api.invoke) {
        console.error('[PROFIT-CALC] واجهة API غير متوفرة');
        return 0;
      }

      // استعلام مبسط للحصول على إجمالي الأرباح من التقارير
      try {
        const profitsReport = await window.api.invoke('get-profits-report', { forceRefresh: true });

        if (profitsReport && profitsReport.success && profitsReport.data) {
          const totalProfit = profitsReport.data.totalProfit || 0;
          console.log(`[PROFIT-CALC] إجمالي الأرباح من التقارير: ${totalProfit}`);
          return Math.round(totalProfit * 100) / 100;
        }
      } catch (reportError) {
        console.warn('[PROFIT-CALC] فشل في الحصول على الأرباح من التقارير، سنحسبها يدوياً:', reportError);
      }

      // إذا فشل الحصول على التقارير، نحسب الأرباح يدوياً
      const query = `
        SELECT
          t.transaction_type,
          t.item_id,
          t.quantity,
          t.selling_price,
          t.profit,
          i.avg_price
        FROM transactions t
        LEFT JOIN inventory i ON t.item_id = i.item_id
        WHERE t.transaction_type IN ('sale', 'return')
      `;

      const result = await window.api.invoke('execute-direct-query', { query });

      if (!result || !result.success || !result.data) {
        console.error('[PROFIT-CALC] فشل في الحصول على بيانات المعاملات');
        return 0;
      }

      const transactions = result.data;
      let totalProfit = 0;

      console.log(`[PROFIT-CALC] معالجة ${transactions.length} معاملة...`);

      for (const transaction of transactions) {
        try {
          const { transaction_type, item_id, quantity, selling_price, profit, avg_price } = transaction;

          if (transaction_type === 'sale' && selling_price > 0) {
            // استخدام الربح المحفوظ إذا كان متوفراً، وإلا نحسبه
            let transactionProfit = profit || 0;

            if (transactionProfit <= 0 && avg_price > 0) {
              // حساب الربح: (سعر البيع - متوسط سعر الشراء) × الكمية
              transactionProfit = (selling_price - avg_price) * quantity;
            }

            totalProfit += transactionProfit;

          } else if (transaction_type === 'return') {
            // خصم أرباح المرتجعات
            totalProfit -= Math.abs(profit || 0);
          }

        } catch (transactionError) {
          console.error(`[PROFIT-CALC] خطأ في معالجة المعاملة ${transaction.id}:`, transactionError);
        }
      }

      console.log(`[PROFIT-CALC] إجمالي الأرباح المحسوبة: ${totalProfit}`);
      return Math.round(totalProfit * 100) / 100; // تقريب إلى رقمين عشريين

    } catch (error) {
      console.error('[PROFIT-CALC] خطأ في حساب الأرباح:', error);
      return 0;
    }
  };

  // دالة لفرض تحديث قيمة الأرباح مباشرة من قاعدة البيانات
  const forceUpdateProfitFromDatabase = async () => {
    try {
      console.log('[FORCE-PROFIT-UPDATE] فرض تحديث الأرباح من قاعدة البيانات...');

      const query = `SELECT profit_total FROM cashbox WHERE id = 1 LIMIT 1`;
      const result = await window.api.invoke('execute-direct-query', { query });

      if (result && result.success && result.data && result.data.length > 0) {
        const profitValue = result.data[0].profit_total;
        console.log('[FORCE-PROFIT-UPDATE] قيمة الأرباح من قاعدة البيانات:', profitValue);

        // تحديث قيمة الأرباح فقط
        setCashbox(prev => ({
          ...prev,
          profit_total: Number(profitValue || 0),
          _forceUpdate: Date.now() + Math.random() * 10000
        }));

        console.log('[FORCE-PROFIT-UPDATE] تم فرض تحديث قيمة الأرباح:', Number(profitValue || 0));
        return Number(profitValue || 0);
      }
    } catch (error) {
      console.error('[FORCE-PROFIT-UPDATE] خطأ في فرض تحديث الأرباح:', error);
    }
    return null;
  };

  // دالة تحديث الخزينة فورياً مع فرض إعادة الرسم
  const updateCashboxDirectly = async () => {
    try {
      console.log('[INSTANT-CASHBOX] بدء التحديث الفوري للخزينة...');

      // استعلام مباشر للحصول على بيانات الخزينة
      const query = `
        SELECT * FROM cashbox WHERE id = 1 LIMIT 1
      `;

      // تنفيذ الاستعلام باستخدام IPC
      const result = await window.api.invoke('execute-direct-query', { query });

      if (result && result.success && result.data && result.data.length > 0) {
        const cashboxData = result.data[0];
        console.log('[INSTANT-CASHBOX] تم الحصول على بيانات الخزينة:', cashboxData);

        // تشخيص مفصل للأرباح في التحديث المباشر
        console.log('[INSTANT-CASHBOX] تشخيص الأرباح في التحديث المباشر:');
        console.log('- profit_total:', cashboxData.profit_total, typeof cashboxData.profit_total);
        console.log('- sales_total:', cashboxData.sales_total, typeof cashboxData.sales_total);
        console.log('- purchases_total:', cashboxData.purchases_total, typeof cashboxData.purchases_total);
        console.log('- transport_total:', cashboxData.transport_total, typeof cashboxData.transport_total);

        // تحديث حالة الخزينة فورياً مع فرض إعادة الرسم
        const newCashboxState = {
          id: cashboxData.id,
          initial_balance: Number(cashboxData.initial_balance || 0),
          current_balance: Number(cashboxData.current_balance || 0),
          profit_total: Number(cashboxData.profit_total || 0),
          sales_total: Number(cashboxData.sales_total || 0),
          purchases_total: Number(cashboxData.purchases_total || 0),
          returns_total: Number(cashboxData.returns_total || 0),
          transport_total: Number(cashboxData.transport_total || 0),
          updated_at: new Date().toISOString(), // دائماً وقت جديد لفرض التحديث
          exists: true,
          _forceUpdate: Date.now() // مفتاح فريد لفرض إعادة الرسم
        };

        setCashbox(newCashboxState);
        console.log('[INSTANT-CASHBOX] تم التحديث الفوري للخزينة');
        console.log(`[INSTANT-CASHBOX] إجمالي الأرباح المحدث: ${newCashboxState.profit_total}`);

        // فرض إعادة رسم إضافية للتأكد من تحديث واجهة المستخدم
        setTimeout(() => {
          setCashbox(prev => ({
            ...prev,
            _forceUpdate: Date.now(),
            // التأكد من أن قيمة الأرباح محدثة
            profit_total: Number(cashboxData.profit_total || 0)
          }));
          console.log(`[INSTANT-CASHBOX] فرض إعادة رسم إضافية - الأرباح: ${Number(cashboxData.profit_total || 0)}`);
        }, 50);

        // فرض إعادة رسم ثالثة للتأكد التام
        setTimeout(() => {
          setCashbox(prev => ({
            ...prev,
            _forceUpdate: Date.now() + 1000,
            profit_total: Number(cashboxData.profit_total || 0)
          }));
          console.log(`[INSTANT-CASHBOX] فرض إعادة رسم ثالثة - الأرباح: ${Number(cashboxData.profit_total || 0)}`);
        }, 200);

        return true;
      } else {
        console.error('[INSTANT-CASHBOX] لم يتم العثور على بيانات الخزينة');
        return false;
      }
    } catch (error) {
      console.error('[INSTANT-CASHBOX] خطأ في التحديث الفوري:', error);
      return false;
    }
  };

  // Component mount/unmount lifecycle
  useEffect(() => {
    isMounted.current = true;

    // إضافة CSS لتحسين مظهر المعاملات
    const style = document.createElement('style');
    style.textContent = `
      .transaction-details {
        display: flex;
        flex-direction: column;
        gap: 4px;
      }
      .transaction-item-name {
        font-weight: bold;
        color: #2563eb;
      }
      .transaction-quantity {
        font-size: 0.85rem;
        color: #4b5563;
      }
      .transaction-invoice {
        font-size: 0.85rem;
        color: #4b5563;
      }
      .transaction-items-count {
        font-size: 0.8rem;
        color: #6b7280;
        margin-right: 5px;
      }
      .transactions-table td {
        vertical-align: top;
        padding: 10px 8px;
      }
    `;
    document.head.appendChild(style);

    // Load cashbox data on component mount
    loadCashbox();

    // تحديث دوري أكثر تكراراً (كل 10 ثواني) لضمان تحديث خانة الأرباح
    const updateInterval = setInterval(() => {
      if (isMounted.current) {
        console.log('[INSTANT-CASHBOX] تحديث دوري للخزينة (كل 10 ثواني)');
        updateCashboxDirectly();
      }
    }, 10000);

    // تحديث إضافي مخصص لخانة الأرباح (كل 5 ثواني)
    const profitUpdateInterval = setInterval(() => {
      if (isMounted.current && cashbox) {
        console.log('[PROFIT-UPDATE] تحديث دوري لخانة الأرباح (كل 5 ثواني)');
        // فرض تحديث قيمة الأرباح من قاعدة البيانات
        forceUpdateProfitFromDatabase();
      }
    }, 5000);

    // تعريف وظيفة loadCashboxInfo للاستخدام في مستمعي الأحداث
    window.loadCashboxInfo = () => {
      console.log('[CASHBOX-FIX] تم استدعاء loadCashboxInfo من مستمع الأحداث');
      if (isMounted.current) {
        updateCashboxDirectly();
      }
    };

    // تعريف وظيفة loadCashbox للاستخدام في مستمعي الأحداث
    window.loadCashbox = () => {
      console.log('[CASHBOX-FIX] تم استدعاء window.loadCashbox من مستمع الأحداث');
      if (isMounted.current) {
        updateCashboxDirectly();
      }
    };

    // إضافة مستمع للإشعار المباشر لتحديث الخزينة
    const handleDirectCashboxUpdate = (event) => {
      console.log('[CASHBOX-FIX] تم استلام حدث direct-cashbox-update في صفحة الخزينة:', event.detail);

      if (isMounted.current) {
        // تحديث الخزينة مباشرة من قاعدة البيانات
        updateCashboxDirectly();

        // تحميل المعاملات بعد تحديث الخزينة
        loadTransactions();
      }
    };

    // إضافة مستمع للإشعار المباشر
    window.addEventListener('direct-cashbox-update', handleDirectCashboxUpdate);

    // إضافة مستمع لحدث تحديث الخزينة
    const handleCashboxUpdated = (data) => {
      console.log('[INSTANT-CASHBOX] تم استلام حدث تحديث الخزينة:', data);

      // التحقق من أن المكون لا يزال مثبتًا
      if (!isMounted.current) {
        console.log('[INSTANT-CASHBOX] المكون غير مثبت، تجاهل الحدث');
        return;
      }

      // تحديث فوري للخزينة
      console.log('[INSTANT-CASHBOX] بدء التحديث الفوري...');

      // إذا كانت البيانات متوفرة، نحدث الحالة فورياً
      if (data && data.current_balance !== undefined) {
        console.log('[INSTANT-CASHBOX] تحديث الحالة فورياً من البيانات المستلمة');
        console.log(`[INSTANT-CASHBOX] إجمالي الأرباح في البيانات المستلمة: ${data.profit_total}`);

        const instantUpdate = {
          id: data.id || cashbox?.id || 1,
          initial_balance: Number(data.initial_balance || cashbox?.initial_balance || 0),
          current_balance: Number(data.current_balance || 0),
          sales_total: Number(data.sales_total || 0),
          purchases_total: Number(data.purchases_total || 0),
          returns_total: Number(data.returns_total || 0),
          transport_total: Number(data.transport_total || 0),
          profit_total: Number(data.profit_total || 0),
          updated_at: new Date().toISOString(),
          exists: true,
          _forceUpdate: Date.now()
        };

        setCashbox(instantUpdate);
        console.log(`[INSTANT-CASHBOX] تم التحديث الفوري للحالة - الأرباح: ${instantUpdate.profit_total}`);

        // فرض إعادة رسم إضافية للأرباح
        setTimeout(() => {
          setCashbox(prev => ({
            ...prev,
            profit_total: Number(data.profit_total || 0),
            _forceUpdate: Date.now() + 500
          }));
          console.log(`[INSTANT-CASHBOX] فرض تحديث إضافي للأرباح: ${Number(data.profit_total || 0)}`);
        }, 100);
      }

      // تحديث إضافي من قاعدة البيانات للتأكد
      setTimeout(() => {
        if (isMounted.current) {
          console.log('[INSTANT-CASHBOX] تحديث إضافي من قاعدة البيانات');
          updateCashboxDirectly();
        }
      }, 200);
    };

    // مستمع محسن لأحداث التحديث
    const handleRefreshNeeded = (data) => {
      if (!isMounted.current) return;

      if (data && (data.target === 'cashbox' || data.target === 'all')) {
        console.log('[INSTANT-CASHBOX] تم استلام حدث الحاجة للتحديث:', data);

        // تحديث فوري دائماً
        console.log('[INSTANT-CASHBOX] تحديث فوري للخزينة');
        updateCashboxDirectly();

        // إذا كانت هناك بيانات محدثة في الحدث، نستخدمها فورياً
        if (data.current_balance !== undefined) {
          console.log('[INSTANT-CASHBOX] تحديث إضافي من بيانات الحدث');
          const quickUpdate = {
            id: data.id || cashbox?.id || 1,
            initial_balance: Number(data.initial_balance || cashbox?.initial_balance || 0),
            current_balance: Number(data.current_balance || 0),
            sales_total: Number(data.sales_total || 0),
            purchases_total: Number(data.purchases_total || 0),
            returns_total: Number(data.returns_total || 0),
            transport_total: Number(data.transport_total || 0),
            profit_total: Number(data.profit_total || 0),
            updated_at: new Date().toISOString(),
            exists: true,
            _forceUpdate: Date.now()
          };

          setCashbox(quickUpdate);
        }
      }
    };

    if (window.api && window.api.on) {
      // تسجيل المستمعين
      window.api.on('cashbox-updated', handleCashboxUpdated);
      window.api.on('refresh-needed', handleRefreshNeeded);
      console.log('[CASHBOX-FIX] تم إضافة مستمعي أحداث تحديث الخزينة بنجاح');
    }

    // Cleanup on unmount
    return () => {
      isMounted.current = false;

      // إزالة مستمعي الأحداث
      if (window.api && window.api.removeListener) {
        window.api.removeListener('cashbox-updated', handleCashboxUpdated);
        window.api.removeListener('refresh-needed', handleRefreshNeeded);
        console.log('[CASHBOX-FIX] تم إزالة مستمعي أحداث تحديث الخزينة');
      }

      // إزالة مستمع الإشعار المباشر
      window.removeEventListener('direct-cashbox-update', handleDirectCashboxUpdate);
      console.log('[CASHBOX-FIX] تم إزالة مستمع الإشعار المباشر');

      // إزالة المؤقتات
      clearInterval(updateInterval);
      clearInterval(profitUpdateInterval);
      console.log('[CASHBOX-DIRECT] تم إزالة المؤقتات');

      // إزالة وظائف التحديث
      delete window.loadCashboxInfo;
      delete window.loadCashbox;
    };
  }, []);

  // Enable form fields after modal opens
  const enableFormFields = () => {
    if (formRef.current && isMounted.current) {
      const inputs = formRef.current.querySelectorAll('input, select, textarea');
      inputs.forEach(input => {
        input.disabled = false;
      });
    }
  };

  // Load cashbox data
  const loadCashbox = async () => {
    if (!isMounted.current) return;

    try {
      setLoading(true);
      setError(null);

      console.log('[CASHBOX-FIX] جاري تحميل بيانات الخزينة...');

      // التحقق من وجود window.api.cashbox
      if (!window.api || !window.api.cashbox) {
        throw new Error('واجهة API للخزينة غير متوفرة');
      }

      // محاولة الحصول على بيانات الخزينة مع إعادة المحاولة
      let cashboxData = null;
      let attempts = 0;
      const maxAttempts = 3;

      while (attempts < maxAttempts && !cashboxData && isMounted.current) {
        attempts++;
        try {
          console.log(`[CASHBOX-FIX] محاولة تحميل بيانات الخزينة (${attempts}/${maxAttempts})...`);
          cashboxData = await window.api.cashbox.get();
          console.log('[CASHBOX-FIX] تم استلام بيانات الخزينة:', cashboxData);
        } catch (attemptError) {
          console.error(`[CASHBOX-FIX] خطأ في محاولة تحميل بيانات الخزينة (${attempts}/${maxAttempts}):`, attemptError);

          if (attempts < maxAttempts) {
            // انتظار قبل المحاولة التالية
            await new Promise(resolve => setTimeout(resolve, 500));
          } else {
            throw attemptError;
          }
        }
      }

      if (!isMounted.current) return;

      // التأكد من أن لدينا كائن خزينة صالح
      if (cashboxData && typeof cashboxData === 'object') {

        // تسجيل قيم الخزينة للتشخيص
        console.log('[CASHBOX-FIX] قيم الخزينة المستلمة:');
        console.log('- الرصيد الافتتاحي:', cashboxData.initial_balance);
        console.log('- الرصيد الحالي:', cashboxData.current_balance);
        console.log('- إجمالي الأرباح:', cashboxData.profit_total);
        console.log('- إجمالي المبيعات:', cashboxData.sales_total);
        console.log('- إجمالي المشتريات:', cashboxData.purchases_total);
        console.log('- إجمالي المرتجعات:', cashboxData.returns_total);
        console.log('- إجمالي مصاريف النقل:', cashboxData.transport_total);

        // تشخيص مفصل لقيمة الأرباح
        console.log('[PROFIT-DEBUG] تشخيص مفصل لقيمة الأرباح:');
        console.log('- نوع البيانات:', typeof cashboxData.profit_total);
        console.log('- القيمة الخام:', cashboxData.profit_total);
        console.log('- هل هي null؟', cashboxData.profit_total === null);
        console.log('- هل هي undefined؟', cashboxData.profit_total === undefined);
        console.log('- القيمة بعد Number():', Number(cashboxData.profit_total || 0));

        // حساب الأرباح المتوقعة
        const expectedProfit = (cashboxData.sales_total || 0) - (cashboxData.purchases_total || 0) - (cashboxData.transport_total || 0);
        console.log('- الأرباح المتوقعة:', expectedProfit);
        console.log('- هل تطابق القيمة المحفوظة؟', Math.abs((cashboxData.profit_total || 0) - expectedProfit) < 0.01);

        // تحديث حالة الخزينة
        console.log('[CASHBOX-FIX] تحديث حالة الخزينة...');

        // تحديث حالة الخزينة بشكل مباشر مع إجبار إعادة الرسم
        setCashbox(prevCashbox => {
          // إنشاء كائن خزينة محدث مع مفتاح إجبار التحديث
          const updatedCashbox = {
            ...cashboxData,
            // التأكد من أن القيم الرقمية صحيحة
            initial_balance: Number(cashboxData.initial_balance || 0),
            current_balance: Number(cashboxData.current_balance || 0),
            profit_total: Number(cashboxData.profit_total || 0),
            sales_total: Number(cashboxData.sales_total || 0),
            purchases_total: Number(cashboxData.purchases_total || 0),
            returns_total: Number(cashboxData.returns_total || 0),
            transport_total: Number(cashboxData.transport_total || 0),
            updated_at: cashboxData.updated_at || new Date().toISOString(),
            // إضافة مفتاح لإجبار إعادة رسم المكونات
            _forceUpdate: Date.now()
          };

          console.log('[CASHBOX-FIX] تحديث حالة الخزينة مع البيانات الجديدة:', updatedCashbox);
          return updatedCashbox;
        });

        // حساب الأرباح الصحيحة
        console.log('[CASHBOX-FIX] حساب الأرباح الصحيحة...');
        const correctProfit = await calculateCorrectProfit();
        setCalculatedProfit(correctProfit);
        console.log(`[CASHBOX-FIX] تم حساب الأرباح: ${correctProfit}`);

        // تحميل المعاملات إذا كانت الخزينة موجودة
        if (cashboxData.exists) {
          console.log('[CASHBOX-FIX] الخزينة موجودة، جاري تحميل المعاملات...');
          await loadTransactions();
        } else {
          console.log('[CASHBOX-FIX] الخزينة غير موجودة');
        }

        // إرسال إشعار بتحديث الخزينة للتأكد من تحديث البطاقات
        if (window.dispatchEvent) {
          console.log('[CASHBOX-FIX] إرسال حدث تحديث الخزينة للمتصفح');
          window.dispatchEvent(new CustomEvent('cashbox-updated-ui', {
            detail: cashboxData
          }));
        }

        // فرض تحديث إضافي للأرباح بعد تحميل البيانات
        setTimeout(() => {
          if (isMounted.current) {
            console.log('[CASHBOX-FIX] فرض تحديث إضافي للأرباح بعد تحميل البيانات');
            forceUpdateProfitFromDatabase();
          }
        }, 1000);
      } else {
        console.error('[CASHBOX-FIX] تم استلام بيانات غير صالحة للخزينة:', cashboxData);
        setError('تم استلام بيانات غير صالحة للخزينة');
      }

      if (isMounted.current) {
        setLoading(false);
      }
    } catch (error) {
      console.error('[CASHBOX-FIX] خطأ في تحميل بيانات الخزينة:', error);

      if (isMounted.current) {
        setError('حدث خطأ أثناء تحميل بيانات الخزينة');
        setLoading(false);
      }
    }
  };

  // تم إزالة دالة fixProfits - الأرباح تتحدث تلقائياً الآن

  // Load transactions
  const loadTransactions = async () => {
    if (!isMounted.current) return;

    try {
      // التأكد من أن الفلاتر تسمح بعرض جميع المعاملات
      if (filters.source !== '' || filters.type !== '') {
        setFilters(prev => ({ ...prev, source: '', type: '' }));
      }
      console.log('[CASHBOX-FIX] جاري تحميل معاملات الخزينة مع الفلاتر:', filters);

      // التحقق من وجود window.api.cashbox
      if (!window.api || !window.api.cashbox) {
        throw new Error('واجهة API للخزينة غير متوفرة');
      }

      // محاولة الحصول على المعاملات مع إعادة المحاولة
      let transactionsData = null;
      let attempts = 0;
      const maxAttempts = 3;

      while (attempts < maxAttempts && transactionsData === null && isMounted.current) {
        attempts++;
        try {
          console.log(`[CASHBOX-FIX] محاولة تحميل المعاملات (${attempts}/${maxAttempts})...`);
          transactionsData = await window.api.cashbox.getTransactions(filters);
          console.log(`[CASHBOX-FIX] تم استلام ${transactionsData ? transactionsData.length : 0} معاملة للخزينة`);
        } catch (attemptError) {
          console.error(`[CASHBOX-FIX] خطأ في محاولة تحميل المعاملات (${attempts}/${maxAttempts}):`, attemptError);

          // إذا كان الخطأ بسبب عدم وجود خزينة، نتوقف عن المحاولة
          if (attemptError.message && attemptError.message.includes('لا يوجد سجل للخزينة')) {
            console.log('[CASHBOX-FIX] لم يتم العثور على خزينة، عرض قائمة معاملات فارغة');
            transactionsData = [];
            break;
          }

          if (attempts < maxAttempts) {
            // انتظار قبل المحاولة التالية
            await new Promise(resolve => setTimeout(resolve, 500));
          } else {
            throw attemptError;
          }
        }
      }

      if (!isMounted.current) return;

      // تحليل أنواع المعاملات المستلمة
      if (transactionsData && transactionsData.length > 0) {
        // تسجيل أنواع المعاملات المستلمة
        const typeCount = {};
        const sourceCount = {};
        const transactionSourceCount = {};

        transactionsData.forEach(transaction => {
          // حساب أنواع المعاملات
          if (!typeCount[transaction.type]) {
            typeCount[transaction.type] = 0;
          }
          typeCount[transaction.type]++;

          // حساب مصادر المعاملات
          if (!sourceCount[transaction.source]) {
            sourceCount[transaction.source] = 0;
          }
          sourceCount[transaction.source]++;

          // حساب مصادر البيانات
          const source = transaction.transaction_source || 'unknown';
          if (!transactionSourceCount[source]) {
            transactionSourceCount[source] = 0;
          }
          transactionSourceCount[source]++;
        });

        console.log('[CASHBOX-FIX] إحصائيات المعاملات المستلمة:');
        console.log('- أنواع المعاملات:', typeCount);
        console.log('- مصادر المعاملات:', sourceCount);
        console.log('- مصادر البيانات:', transactionSourceCount);
      }

      // التأكد من أن لدينا مصفوفة حتى لو كانت الاستجابة فارغة أو غير معرفة
      if (isMounted.current) {
        console.log('[CASHBOX-FIX] تحديث قائمة المعاملات في واجهة المستخدم');
        setTransactions(Array.isArray(transactionsData) ? transactionsData : []);
      }
    } catch (error) {
      console.error('[CASHBOX-FIX] خطأ في تحميل المعاملات:', error);

      if (isMounted.current) {
        // إذا كان الخطأ بسبب عدم وجود خزينة، نتجاهله ونعرض قائمة فارغة
        if (error.message && error.message.includes('لا يوجد سجل للخزينة')) {
          console.log('[CASHBOX-FIX] لم يتم العثور على خزينة، عرض قائمة معاملات فارغة');
          setTransactions([]);
        } else {
          showNotification('حدث خطأ أثناء تحميل المعاملات', 'error');
          // تعيين مصفوفة فارغة في حالة الخطأ لمنع أخطاء غير معرفة
          setTransactions([]);
        }
      }
    }
  };



  // Create new cashbox
  const handleCreateCashbox = async () => {
    if (!initialBalance || isNaN(initialBalance) || initialBalance < 0) {
      showNotification('الرجاء إدخال رصيد افتتاحي صحيح', 'error');
      return;
    }

    try {
      setLoading(true);
      const result = await window.api.cashbox.create(Number(initialBalance));

      if (!isMounted.current) return;

      if (result.success) {
        setCashbox(result.cashbox);
        showNotification('تم إنشاء الخزينة بنجاح', 'success');
        setShowInitialBalanceModal(false);
        await loadTransactions();
      }
    } catch (error) {
      console.error('Error creating cashbox:', error);
      if (isMounted.current) {
        showNotification('حدث خطأ أثناء إنشاء الخزينة', 'error');
      }
    } finally {
      if (isMounted.current) {
        setLoading(false);
      }
    }
  };

  // Add new transaction
  const handleAddTransaction = async () => {
    // Validate form data
    if (!transactionForm.amount || isNaN(transactionForm.amount) || transactionForm.amount <= 0) {
      showNotification('الرجاء إدخال مبلغ صحيح', 'error');
      return;
    }

    if (!transactionForm.source) {
      showNotification('الرجاء إدخال مصدر المعاملة', 'error');
      return;
    }

    try {
      setLoading(true);

      const transaction = {
        ...transactionForm,
        amount: Number(transactionForm.amount)
      };

      // إذا كانت المعاملة دخل وكانت هناك خزينة، نقوم بعرض معلومات توضيحية للمستخدم
      if (transaction.type === 'income' && cashbox && cashbox.exists) {
        const currentBalance = cashbox.current_balance;
        const initialBalance = cashbox.initial_balance;
        const maxAddableAmount = Math.max(0, initialBalance - currentBalance);
        const amountToAddToBalance = Math.min(transaction.amount, maxAddableAmount);
        const excessAmount = Math.max(0, transaction.amount - amountToAddToBalance);

        // إذا كان هناك مبلغ زائد سيتم تحويله للأرباح، نعرض رسالة توضيحية
        if (excessAmount > 0) {
          console.log(`سيتم إضافة ${amountToAddToBalance} إلى الرصيد الحالي و ${excessAmount} إلى الأرباح`);
        }
      }

      const result = await window.api.cashbox.addTransaction(transaction);

      if (!isMounted.current) return;

      if (result.success) {
        // Update cashbox and transactions
        setCashbox(result.cashbox);

        // إذا كان هناك رسالة في النتيجة، نعرضها للمستخدم
        if (result.message) {
          showNotification(result.message, 'info');
        }

        // تحميل المعاملات
        await loadTransactions();

        // Reset form and close modal
        setTransactionForm({
          type: 'income',
          amount: '',
          source: '',
          notes: ''
        });
        setShowAddTransactionModal(false);

        showNotification('تمت إضافة المعاملة بنجاح', 'success');

        // تحديث الصفحة لعرض الخزينة الموجودة بعد إضافة المعاملة
        await loadCashbox();
      }
    } catch (error) {
      console.error('Error adding transaction:', error);
      if (isMounted.current) {
        showNotification(error.message || 'حدث خطأ أثناء إضافة المعاملة', 'error');
      }
    } finally {
      if (isMounted.current) {
        setLoading(false);
      }
    }
  };

  // Apply filters
  const applyFilters = async () => {
    try {
      await loadTransactions();
      if (isMounted.current) {
        setShowFilters(false);
      }
    } catch (error) {
      console.error('Error applying filters:', error);
      showNotification('حدث خطأ أثناء تطبيق التصفية', 'error');
    }
  };

  // Reset filters
  const resetFilters = () => {
    if (!isMounted.current) return;

    // إعادة تعيين جميع الفلاتر
    setFilters({
      type: '',
      source: '',
      startDate: '',
      endDate: ''
    });

    // تحميل المعاملات بعد إعادة تعيين الفلاتر
    console.log('إعادة تعيين جميع الفلاتر وتحميل المعاملات');
    loadTransactions();
    setShowFilters(false);
  };

  // Format date (Gregorian format)
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');

    return `${year}/${month}/${day} ${hours}:${minutes}`;
  };

  // Helper functions for transaction display
  const getAmountColor = (type, source) => {
    // استخدام نوع المعاملة الأصلي من الحقل source أو type
    const transactionType = source || type;

    // تحديد اللون بناءً على نوع المعاملة
    if (transactionType === 'sale' || transactionType === 'income' || transactionType === 'delete-expense') {
      return 'text-green-600'; // أخضر للمبيعات والدخل وحذف المصروفات
    } else if (transactionType === 'purchase' || transactionType === 'expense' || transactionType === 'delete-income') {
      return 'text-red-600'; // أحمر للمشتريات والمصروفات وحذف الدخل
    } else if (transactionType === 'transport') {
      return 'text-orange-600'; // برتقالي لمصاريف النقل
    } else if (transactionType === 'return') {
      return 'text-orange-600'; // برتقالي للمرتجعات
    } else if (transactionType === 'fix') {
      return 'text-blue-600'; // أزرق للتصحيح
    }

    // اللون الافتراضي
    return 'text-gray-600';
  };

  const getTransactionIcon = (type, source) => {
    // استخدام نوع المعاملة الأصلي من الحقل source أو type
    const transactionType = source || type;

    // تحديد الأيقونة بناءً على نوع المعاملة
    if (transactionType === 'sale' || transactionType === 'income') {
      return <FaArrowUp className="text-green-600" />; // سهم لأعلى للمبيعات والدخل
    } else if (transactionType === 'purchase' || transactionType === 'expense') {
      return <FaArrowDown className="text-red-600" />; // سهم لأسفل للمشتريات والمصروفات
    } else if (transactionType === 'transport') {
      return <FaTruck className="text-orange-600" />; // أيقونة شاحنة لمصاريف النقل
    } else if (transactionType === 'return') {
      return <FaExchangeAlt className="text-orange-600" />; // أيقونة تبادل للمرتجعات
    } else if (transactionType === 'fix') {
      return <FaTools className="text-blue-600" />; // أيقونة أدوات للتصحيح
    } else if (transactionType === 'delete-income' || transactionType === 'delete-expense') {
      return <FaTrash className="text-gray-600" />; // أيقونة سلة المهملات للحذف
    }

    // الأيقونة الافتراضية
    return <FaArrowDown className="text-gray-600" />;
  };

  const getTransactionTypeText = (type, source) => {
    // استخدام نوع المعاملة الأصلي من الحقل source أو type
    const transactionType = source || type;

    // تحديد النص بناءً على نوع المعاملة
    if (transactionType === 'sale') {
      return 'مبيعات';
    } else if (transactionType === 'purchase') {
      return 'مشتريات';
    } else if (transactionType === 'transport') {
      return 'مصاريف نقل';
    } else if (transactionType === 'return') {
      if (type === 'income') {
        return 'مرتجعات مشتريات';
      } else if (type === 'expense') {
        return 'مرتجعات مبيعات';
      }
      return 'مرتجعات';
    } else if (transactionType === 'income') {
      return 'إيداع نقدي';
    } else if (transactionType === 'expense') {
      return 'سحب نقدي';
    } else if (transactionType === 'fix') {
      return 'تصحيح الخزينة';
    } else if (transactionType === 'delete-income') {
      return 'حذف إيداع';
    } else if (transactionType === 'delete-expense') {
      return 'حذف مصروف';
    }

    // النص الافتراضي
    return transactionType || 'غير معروف';
  };

  return (
    <div className="cashbox-page">
      <div className="cashbox-header">
        <h1>الخزينة</h1>
        <p>إدارة الخزينة والمعاملات المالية</p>
      </div>

      {loading && <div className="loading">جاري التحميل...</div>}

      {error && <div className="error">{error}</div>}

      {!loading && !error && (
        <>
          {/* عرض معلومات الخزينة */}
          <div className="cashbox-info">
            {cashbox && cashbox.exists ? (
              <div className="cashbox-cards" key={`cashbox-cards-${cashbox._forceUpdate || Date.now()}`}>
                <Card
                  title="الرصيد الافتتاحي"
                  icon={<FaWallet />}
                  className="cashbox-card"
                  key={`initial-balance-${cashbox._forceUpdate || Date.now()}`}
                  actions={
                    <Button
                      variant="secondary"
                      size="small"
                      onClick={() => {
                        setInitialBalance(cashbox.initial_balance);
                        setShowEditInitialBalanceModal(true);
                        // إعادة تمكين حقول النموذج
                        setTimeout(enableFormFields, 100);
                      }}
                    >
                      تعديل
                    </Button>
                  }
                >
                  <div className="cashbox-amount">
                    <FormattedCurrency amount={cashbox.initial_balance} />
                  </div>
                </Card>

                <Card
                  title="الرصيد الحالي"
                  icon={<FaWallet />}
                  className="cashbox-card"
                  key={`current-balance-${cashbox._forceUpdate || Date.now()}`}
                >
                  <div className="cashbox-amount">
                    <FormattedCurrency amount={cashbox.current_balance} />
                  </div>
                </Card>

                <Card
                  title="إجمالي المبيعات"
                  icon={<FaArrowUp />}
                  className="cashbox-card"
                  key={`sales-total-${cashbox._forceUpdate || Date.now()}`}
                >
                  <div className="cashbox-amount">
                    <FormattedCurrency amount={cashbox.sales_total || 0} />
                  </div>
                </Card>

                <Card
                  title="إجمالي المشتريات"
                  icon={<FaArrowDown />}
                  className="cashbox-card"
                  key={`purchases-total-${cashbox._forceUpdate || Date.now()}`}
                >
                  <div className="cashbox-amount">
                    <FormattedCurrency amount={cashbox.purchases_total || 0} />
                  </div>
                </Card>

                <Card
                  title="إجمالي المرتجعات"
                  icon={<FaExchangeAlt />}
                  className="cashbox-card"
                  key={`returns-total-${cashbox._forceUpdate || Date.now()}`}
                >
                  <div className="cashbox-amount">
                    <FormattedCurrency amount={cashbox.returns_total || 0} />
                  </div>
                </Card>

                <Card
                  title="إجمالي مصاريف النقل"
                  icon={<FaTruck />}
                  className="cashbox-card"
                  key={`transport-total-${cashbox._forceUpdate || Date.now()}`}
                >
                  <div className="cashbox-amount">
                    <FormattedCurrency amount={cashbox.transport_total || 0} />
                  </div>
                </Card>

                <Card
                  title="إجمالي الأرباح"
                  icon={<FaChartLine />}
                  className="cashbox-card profit-card"
                  key={`profit-total-${cashbox._forceUpdate || Date.now()}`}
                >
                  <div className="cashbox-amount" style={{ fontSize: '24px', fontWeight: 'bold', color: '#2563eb' }}>
                    {/* عرض مباشر للقيمة مع تنسيق يدوي */}
                    {(cashbox.profit_total || 0).toLocaleString('en-US', {
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0
                    })} د.ل
                  </div>
                  <div style={{ marginTop: '10px', textAlign: 'center', fontSize: '12px', color: '#666' }}>
                    محسوب وفقاً للمعادلة: المبيعات - المشتريات - مصاريف النقل
                  </div>
                  {/* تشخيص مؤقت لمعرفة القيم */}
                  <div style={{ marginTop: '5px', textAlign: 'center', fontSize: '10px', color: '#999' }}>
                    تشخيص: profit_total = {cashbox.profit_total} (نوع: {typeof cashbox.profit_total})
                  </div>
                  <div style={{ marginTop: '2px', textAlign: 'center', fontSize: '10px', color: '#999' }}>
                    المعادلة: {cashbox.sales_total || 0} - {cashbox.purchases_total || 0} - {cashbox.transport_total || 0} = {(cashbox.sales_total || 0) - (cashbox.purchases_total || 0) - (cashbox.transport_total || 0)}
                  </div>
                  {/* زر تحديث مؤقت للاختبار */}
                  <div style={{ marginTop: '10px', textAlign: 'center' }}>
                    <button
                      onClick={() => forceUpdateProfitFromDatabase()}
                      style={{
                        padding: '5px 10px',
                        fontSize: '10px',
                        backgroundColor: '#2563eb',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        cursor: 'pointer'
                      }}
                    >
                      فرض تحديث الأرباح
                    </button>
                  </div>
                </Card>
              </div>
            ) : (
              <div className="no-cashbox">
                <p>يمكنك إضافة معاملة مباشرة لإنشاء خزينة تلقائياً.</p>
                <div className="cashbox-actions">
                  <Button
                    variant="primary"
                    icon={<FaPlus />}
                    onClick={() => {
                      setShowAddTransactionModal(true);
                      setTimeout(enableFormFields, 100);
                    }}
                    disabled={loading}
                    style={{ marginRight: '10px' }}
                  >
                    إضافة معاملة
                  </Button>
                  <Button
                    variant="secondary"
                    icon={<FaPlus />}
                    onClick={() => {
                      setShowInitialBalanceModal(true);
                      setTimeout(enableFormFields, 100);
                    }}
                    disabled={loading}
                  >
                    إنشاء خزينة جديدة
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* عرض المعاملات */}
          <Card
            title="المعاملات المالية"
            icon={<FaMoneyBillWave />}
            actions={
              <>
                <Button
                  variant="secondary"
                  icon={<FaFilter />}
                  onClick={() => setShowFilters(!showFilters)}
                >
                  تصفية متقدمة
                </Button>
                <Button
                  variant="primary"
                  icon={<FaPlus />}
                  onClick={() => {
                    setShowAddTransactionModal(true);
                    setTimeout(enableFormFields, 100);
                  }}
                  disabled={loading}
                >
                  إضافة معاملة
                </Button>

              </>
            }
          >
            {/* تم إزالة أزرار التصفية السريعة بناءً على طلب المستخدم */}
              {/* نموذج التصفية */}
              {showFilters && (
                <div className="filter-form">
                  <div className="filter-row">
                    <div className="filter-group">
                      <label>نوع المعاملة</label>
                      <select
                        value={filters.type}
                        onChange={(e) => setFilters({ ...filters, type: e.target.value })}
                      >
                        <option value="">الكل</option>
                        <option value="income">دخل</option>
                        <option value="expense">مصروفات</option>
                        <option value="return">مرتجعات</option>
                      </select>
                    </div>

                    <div className="filter-group">
                      <label>المصدر</label>
                      <input
                        type="text"
                        value={filters.source}
                        onChange={(e) => setFilters({ ...filters, source: e.target.value })}
                        placeholder="بحث في المصدر"
                      />
                    </div>
                  </div>

                  <div className="filter-row">
                    <div className="filter-group">
                      <label>من تاريخ</label>
                      <input
                        type="date"
                        value={filters.startDate}
                        onChange={(e) => setFilters({ ...filters, startDate: e.target.value })}
                      />
                    </div>

                    <div className="filter-group">
                      <label>إلى تاريخ</label>
                      <input
                        type="date"
                        value={filters.endDate}
                        onChange={(e) => setFilters({ ...filters, endDate: e.target.value })}
                      />
                    </div>
                  </div>

                  <div className="filter-actions">
                    <Button variant="secondary" onClick={resetFilters}>إعادة تعيين</Button>
                    <Button variant="primary" onClick={applyFilters}>تطبيق</Button>
                  </div>
                </div>
              )}

              {/* جدول المعاملات */}
              <div className="transactions-table-container">
                {transactions.length === 0 ? (
                  <div className="no-transactions">
                    <p>لا توجد معاملات مسجلة بعد.</p>
                  </div>
                ) : (
                  <table className="transactions-table">
                    <thead>
                      <tr>
                        <th>#</th>
                        <th>النوع</th>
                        <th>المبلغ</th>
                        <th>المصدر</th>
                        <th>التاريخ</th>
                      </tr>
                    </thead>
                    <tbody>
                      {transactions.map((transaction, index) => (
                        <tr key={`${transaction.id}-${index}`}>
                          <td>{index + 1}</td>
                          <td>
                            <div className="transaction-type">
                              {getTransactionIcon(transaction.type, transaction.source)}
                              <span>{getTransactionTypeText(transaction.type, transaction.source)}</span>
                              {transaction.items_count > 1 && (
                                <span className="transaction-items-count">
                                  ({transaction.items_count} صنف)
                                </span>
                              )}
                            </div>
                          </td>
                          <td className={getAmountColor(transaction.type, transaction.source)}>
                            <FormattedCurrency amount={transaction.amount} />
                          </td>
                          <td>
                            {/* استخدام نفس الدالة getTransactionTypeText للحصول على نص متسق */}
                            {getTransactionTypeText(transaction.type, transaction.source)}
                          </td>
                          <td>{formatDate(transaction.created_at)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}
              </div>
            </Card>
        </>
      )}

      {/* نافذة إنشاء خزينة جديدة */}
      <Modal
        isOpen={showInitialBalanceModal}
        onClose={() => setShowInitialBalanceModal(false)}
        title="إنشاء خزينة جديدة"
      >
        <div className="modal-form" ref={formRef}>
          <div className="form-group">
            <label>الرصيد الافتتاحي</label>
            <input
              type="number"
              value={initialBalance}
              onChange={(e) => setInitialBalance(e.target.value)}
              placeholder="أدخل الرصيد الافتتاحي"
              min="0"
              step="0.01"
            />
          </div>

          <div className="modal-actions">
            <Button
              variant="secondary"
              onClick={() => setShowInitialBalanceModal(false)}
              disabled={loading}
            >
              إلغاء
            </Button>
            <Button
              variant="primary"
              onClick={handleCreateCashbox}
              disabled={loading}
            >
              إنشاء
            </Button>
          </div>
        </div>
      </Modal>

      {/* نافذة تعديل الرصيد الافتتاحي */}
      <Modal
        isOpen={showEditInitialBalanceModal}
        onClose={() => setShowEditInitialBalanceModal(false)}
        title="تعديل الرصيد الافتتاحي"
      >
        <div className="modal-form" ref={formRef}>
          <div className="form-group">
            <label>الرصيد الافتتاحي الجديد</label>
            <input
              type="number"
              value={initialBalance}
              onChange={(e) => setInitialBalance(e.target.value)}
              placeholder="أدخل الرصيد الافتتاحي الجديد"
              min="0"
              step="0.01"
            />
            <p className="form-hint">
              ملاحظة: تعديل الرصيد الافتتاحي قد يؤثر على الرصيد الحالي.
            </p>
          </div>

          <div className="modal-actions">
            <Button
              variant="secondary"
              onClick={() => setShowEditInitialBalanceModal(false)}
              disabled={loading}
            >
              إلغاء
            </Button>
            <Button
              variant="primary"
              onClick={async () => {
                if (!initialBalance || isNaN(initialBalance) || initialBalance < 0) {
                  showNotification('الرجاء إدخال رصيد افتتاحي صحيح', 'error');
                  return;
                }

                try {
                  setLoading(true);
                  console.log('[CASHBOX-UI] بدء تحديث الرصيد الافتتاحي:', Number(initialBalance));

                  const result = await updateCashboxInitialBalance(Number(initialBalance));
                  console.log('[CASHBOX-UI] نتيجة تحديث الرصيد الافتتاحي:', result);

                  if (!isMounted.current) return;

                  // إعادة تحميل بيانات الخزينة فورياً لضمان التحديث
                  console.log('[CASHBOX-UI] إعادة تحميل بيانات الخزينة بعد تحديث الرصيد الافتتاحي...');
                  await loadCashbox();

                  // إرسال حدث تحديث للمكونات الأخرى
                  if (window.dispatchEvent) {
                    window.dispatchEvent(new CustomEvent('cashbox-updated-ui', {
                      detail: {
                        action: 'initial-balance-updated',
                        timestamp: Date.now()
                      }
                    }));
                  }

                  showNotification('تم تحديث الرصيد الافتتاحي بنجاح', 'success');
                  setShowEditInitialBalanceModal(false);
                } catch (error) {
                  console.error('[CASHBOX-UI] خطأ في تحديث الرصيد الافتتاحي:', error);
                  if (isMounted.current) {
                    showNotification('حدث خطأ أثناء تحديث الرصيد الافتتاحي', 'error');
                  }
                } finally {
                  if (isMounted.current) {
                    setLoading(false);
                  }
                }
              }}
              disabled={loading}
            >
              حفظ التغييرات
            </Button>
          </div>
        </div>
      </Modal>

      {/* نافذة إضافة معاملة جديدة */}
      <Modal
        isOpen={showAddTransactionModal}
        onClose={() => setShowAddTransactionModal(false)}
        title="إضافة معاملة جديدة"
      >
        <div className="modal-form" ref={formRef}>
          <div className="form-group">
            <label>نوع المعاملة</label>
            <div className="transaction-type-buttons">
              <button
                className={`type-button ${transactionForm.type === 'income' ? 'active' : ''}`}
                onClick={() => setTransactionForm({ ...transactionForm, type: 'income' })}
              >
                <FaArrowUp /> دخل
              </button>
              <button
                className={`type-button ${transactionForm.type === 'expense' ? 'active' : ''}`}
                onClick={() => setTransactionForm({ ...transactionForm, type: 'expense' })}
              >
                <FaArrowDown /> مصروفات
              </button>
            </div>
          </div>

          <div className="form-group">
            <label>المبلغ</label>
            <input
              type="number"
              value={transactionForm.amount}
              onChange={(e) => setTransactionForm({ ...transactionForm, amount: e.target.value })}
              placeholder="أدخل المبلغ"
              min="0.01"
              step="0.01"
            />
            {transactionForm.type === 'income' && cashbox && (
              <>
                <p className="form-hint">
                  الرصيد الحالي: {cashbox.current_balance} | الرصيد الافتتاحي: {cashbox.initial_balance}
                </p>
              </>
            )}
          </div>

          <div className="form-group">
            <label>المصدر</label>
            <input
              type="text"
              value={transactionForm.source}
              onChange={(e) => setTransactionForm({ ...transactionForm, source: e.target.value })}
              placeholder="أدخل مصدر المعاملة"
            />
          </div>

          <div className="form-group">
            <label>ملاحظات (اختياري)</label>
            <textarea
              value={transactionForm.notes}
              onChange={(e) => setTransactionForm({ ...transactionForm, notes: e.target.value })}
              placeholder="أدخل ملاحظات إضافية"
              rows="3"
            />
          </div>

          <div className="modal-actions">
            <Button
              variant="secondary"
              onClick={() => setShowAddTransactionModal(false)}
              disabled={loading}
            >
              إلغاء
            </Button>
            <Button
              variant="primary"
              onClick={handleAddTransaction}
              disabled={loading}
            >
              إضافة
            </Button>
          </div>
        </div>
      </Modal>

      {/* تم إزالة مكون إصلاح الأرباح - الأرباح تتحدث تلقائياً الآن */}
    </div>
  );
};

export default Cashbox;
