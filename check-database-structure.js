/**
 * فحص بنية قاعدة البيانات
 */

const sqlite3 = require('sqlite3').verbose();

// مسار قاعدة البيانات
const dbPath = 'C:\\Users\\<USER>\\AppData\\Roaming\\warehouse-management-system\\wms-database.db';

console.log('🔍 فحص بنية قاعدة البيانات');
console.log('=' .repeat(50));

function checkDatabaseStructure() {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        console.error('❌ خطأ في الاتصال بقاعدة البيانات:', err.message);
        reject(err);
        return;
      }
      console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
      
      // الحصول على قائمة الجداول
      db.all("SELECT name FROM sqlite_master WHERE type='table'", (err, tables) => {
        if (err) {
          console.error('❌ خطأ في الحصول على قائمة الجداول:', err.message);
          db.close();
          reject(err);
          return;
        }
        
        console.log('\n📋 الجداول الموجودة في قاعدة البيانات:');
        tables.forEach((table, index) => {
          console.log(`   ${index + 1}. ${table.name}`);
        });
        
        // البحث عن جدول الخزينة
        const cashboxTable = tables.find(table => 
          table.name.toLowerCase().includes('cashbox') || 
          table.name.toLowerCase().includes('cash') ||
          table.name.toLowerCase().includes('خزينة')
        );
        
        if (cashboxTable) {
          console.log(`\n✅ تم العثور على جدول الخزينة: ${cashboxTable.name}`);
          
          // فحص بنية جدول الخزينة
          db.all(`PRAGMA table_info(${cashboxTable.name})`, (err, columns) => {
            if (err) {
              console.error('❌ خطأ في فحص بنية الجدول:', err.message);
            } else {
              console.log(`\n📊 بنية جدول ${cashboxTable.name}:`);
              columns.forEach(column => {
                console.log(`   - ${column.name}: ${column.type} ${column.notnull ? '(NOT NULL)' : ''} ${column.pk ? '(PRIMARY KEY)' : ''}`);
              });
              
              // فحص البيانات الموجودة
              db.all(`SELECT * FROM ${cashboxTable.name}`, (err, rows) => {
                if (err) {
                  console.error('❌ خطأ في قراءة البيانات:', err.message);
                } else {
                  console.log(`\n📈 البيانات الموجودة في ${cashboxTable.name}:`);
                  if (rows.length > 0) {
                    rows.forEach((row, index) => {
                      console.log(`   السجل ${index + 1}:`, row);
                    });
                  } else {
                    console.log('   لا توجد بيانات');
                  }
                }
                
                db.close();
                resolve();
              });
            }
          });
        } else {
          console.log('\n❌ لم يتم العثور على جدول الخزينة');
          
          // البحث عن جداول أخرى قد تحتوي على بيانات مالية
          console.log('\n🔍 البحث عن جداول مالية أخرى...');
          const financialTables = tables.filter(table => 
            table.name.toLowerCase().includes('transaction') ||
            table.name.toLowerCase().includes('sale') ||
            table.name.toLowerCase().includes('purchase') ||
            table.name.toLowerCase().includes('profit') ||
            table.name.toLowerCase().includes('financial')
          );
          
          if (financialTables.length > 0) {
            console.log('📊 جداول مالية محتملة:');
            financialTables.forEach(table => {
              console.log(`   - ${table.name}`);
            });
          }
          
          db.close();
          resolve();
        }
      });
    });
  });
}

// تشغيل الفحص
checkDatabaseStructure().catch(console.error);
