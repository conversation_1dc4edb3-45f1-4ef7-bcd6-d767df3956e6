/**
 * وحدة إدارة التقارير المعاد بناؤها
 * تقوم هذه الوحدة بإدارة جميع عمليات التقارير بشكل موحد ومتوافق مع باقي أقسام المنظومة
 *
 * التحسينات:
 * - استخدام مدير قاعدة البيانات الموحد
 * - تحسين معالجة الأخطاء والتسجيل
 * - إضافة تقارير متخصصة جديدة
 * - تحسين أداء الاستعلامات
 * - توافق أفضل مع هيكل قاعدة البيانات الجديد
 */

const { logError, logSystem } = require('./error-handler');
const DatabaseManager = require('./database-singleton');
const transactionManager = require('./unified-transaction-manager');
const returnTransactionsManager = require('./return-transactions-manager');
const inventoryManager = require('./inventory-manager');
const customersManager = require('./customers-manager');
const cashboxManager = require('./cashbox-manager');

// مرجع لقاعدة البيانات (سيتم الحصول عليه من مدير قاعدة البيانات)
let db = null;

/**
 * تهيئة وحدة إدارة التقارير
 * @returns {boolean} - نجاح العملية
 */
function initialize() {
  try {
    console.log('جاري تهيئة وحدة إدارة التقارير...');
    logSystem('جاري تهيئة وحدة إدارة التقارير...', 'info');

    // الحصول على اتصال قاعدة البيانات من مدير قاعدة البيانات
    const dbManager = DatabaseManager.getInstance();

    if (!dbManager) {
      throw new Error('فشل في الحصول على مدير قاعدة البيانات');
    }

    db = dbManager.getConnection();

    if (!db) {
      throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
    }

    // التحقق من صحة اتصال قاعدة البيانات
    try {
      const testStmt = db.prepare('SELECT 1');
      const result = testStmt.get();
      if (!result) {
        throw new Error('فشل اختبار اتصال قاعدة البيانات');
      }
      logSystem('تم التحقق من صحة اتصال قاعدة البيانات', 'info');
    } catch (dbError) {
      logError(dbError, 'initialize - reports-manager - database test');
      throw new Error(`فشل اختبار اتصال قاعدة البيانات: ${dbError.message}`);
    }

    // التحقق من وجود الجداول المطلوبة
    try {
      const tables = ['items', 'inventory', 'transactions', 'customers', 'cashbox', 'cashbox_transactions'];
      for (const table of tables) {
        const checkTableStmt = db.prepare(`SELECT name FROM sqlite_master WHERE type='table' AND name=?`);
        const tableExists = checkTableStmt.get(table);
        if (!tableExists) {
          logSystem(`جدول ${table} غير موجود في قاعدة البيانات`, 'warning');
        }
      }
    } catch (tableCheckError) {
      logError(tableCheckError, 'initialize - reports-manager - table check');
      logSystem(`خطأ في التحقق من وجود الجداول: ${tableCheckError.message}`, 'warning');
    }

    console.log('تم تهيئة وحدة إدارة التقارير بنجاح');
    logSystem('تم تهيئة وحدة إدارة التقارير بنجاح', 'info');

    return true;
  } catch (error) {
    console.error('خطأ في تهيئة وحدة إدارة التقارير:', error);
    logError(error, 'initialize - reports-manager');

    // محاولة إعادة الاتصال بقاعدة البيانات
    try {
      logSystem('محاولة إعادة الاتصال بقاعدة البيانات...', 'info');
      const dbManager = DatabaseManager.getInstance();
      if (dbManager) {
        db = dbManager.getConnection();
        if (db) {
          logSystem('تم إعادة الاتصال بقاعدة البيانات بنجاح', 'info');
          return true;
        }
      }
    } catch (reconnectError) {
      logError(reconnectError, 'initialize - reports-manager - reconnect');
    }

    return false;
  }
}

/**
 * الحصول على تقرير المخزون
 * @param {Object} options - خيارات التقرير
 * @param {boolean} options.includeZeroQuantity - تضمين الأصناف ذات الكمية الصفرية
 * @param {boolean} options.includeDetails - تضمين تفاصيل إضافية مثل تاريخ آخر عملية شراء وبيع
 * @returns {Object} - تقرير المخزون
 */
function getInventoryReport(options = {}) {
  try {
    logSystem('بدء تنفيذ getInventoryReport مع الخيارات: ' + JSON.stringify(options), 'info');

    // التحقق من اتصال قاعدة البيانات
    if (!db) {
      logSystem('قاعدة البيانات غير مهيأة في getInventoryReport', 'error');

      // محاولة إعادة الاتصال بقاعدة البيانات
      const dbManager = DatabaseManager.getInstance();
      if (dbManager) {
        db = dbManager.getConnection();
        if (!db) {
          throw new Error('قاعدة البيانات غير مهيأة ولا يمكن إعادة الاتصال');
        }
        logSystem('تم إعادة الاتصال بقاعدة البيانات في getInventoryReport', 'info');
      } else {
        throw new Error('قاعدة البيانات غير مهيأة ولا يمكن الحصول على مدير قاعدة البيانات');
      }
    }

    // استعلام SQL للحصول على بيانات المخزون
    let query = `
      SELECT i.id, i.name, i.unit,
             COALESCE(inv.current_quantity, 0) as current_quantity,
             COALESCE(inv.minimum_quantity, 0) as minimum_quantity,
             COALESCE(inv.avg_price, 0) as avg_price,
             COALESCE(inv.selling_price, 0) as selling_price,
             inv.last_updated
      FROM items i
      LEFT JOIN inventory inv ON i.id = inv.item_id
    `;

    // إضافة شرط لاستبعاد الأصناف ذات الكمية الصفرية إذا تم تحديد ذلك
    if (!options.includeZeroQuantity) {
      query += ` WHERE COALESCE(inv.current_quantity, 0) > 0`;
    }

    query += ` ORDER BY i.name`;

    logSystem(`تنفيذ استعلام المخزون: ${query}`, 'info');

    const stmt = db.prepare(query);
    const inventory = stmt.all();

    logSystem(`تم الحصول على ${inventory.length} صنف من المخزون`, 'info');

    // إضافة تفاصيل إضافية إذا تم طلبها
    if (options.includeDetails) {
      logSystem('جاري إضافة تفاصيل إضافية للمخزون...', 'info');

      for (const item of inventory) {
        try {
          // الحصول على تاريخ آخر عملية شراء
          const lastPurchaseStmt = db.prepare(`
            SELECT transaction_date, price, quantity
            FROM transactions
            WHERE item_id = ? AND transaction_type = 'purchase'
            ORDER BY transaction_date DESC
            LIMIT 1
          `);
          const lastPurchase = lastPurchaseStmt.get(item.id);

          // الحصول على تاريخ آخر عملية بيع
          const lastSaleStmt = db.prepare(`
            SELECT transaction_date, selling_price, quantity
            FROM transactions
            WHERE item_id = ? AND transaction_type = 'sale'
            ORDER BY transaction_date DESC
            LIMIT 1
          `);
          const lastSale = lastSaleStmt.get(item.id);

          // إضافة المعلومات إلى الصنف
          item.last_purchase = lastPurchase || null;
          item.last_sale = lastSale || null;

          // حساب معدل الدوران (إذا كانت هناك مبيعات)
          if (lastSale) {
            // الحصول على إجمالي المبيعات في آخر 30 يوم
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            const thirtyDaysAgoStr = thirtyDaysAgo.toISOString();

            const monthlySalesStmt = db.prepare(`
              SELECT SUM(quantity) as total_quantity
              FROM transactions
              WHERE item_id = ? AND transaction_type = 'sale' AND transaction_date >= ?
            `);
            const monthlySales = monthlySalesStmt.get(item.id, thirtyDaysAgoStr);

            item.monthly_sales = monthlySales ? monthlySales.total_quantity || 0 : 0;

            // حساب معدل الدوران (المبيعات الشهرية / الكمية الحالية)
            if (item.current_quantity > 0) {
              item.turnover_rate = item.monthly_sales / item.current_quantity;
            } else {
              item.turnover_rate = 0;
            }
          } else {
            item.monthly_sales = 0;
            item.turnover_rate = 0;
          }
        } catch (itemDetailError) {
          logError(itemDetailError, `getInventoryReport - تفاصيل الصنف ${item.id}`);
          // استمر في المعالجة حتى لو فشلت إضافة التفاصيل لصنف واحد
        }
      }
    }

    // حساب إحصائيات المخزون
    let totalItems = inventory.length;
    let totalValue = 0;
    let lowStockItems = 0;
    let outOfStockItems = 0;
    let highValueItems = 0; // الأصناف ذات القيمة العالية (أكثر من 1000)

    for (const item of inventory) {
      const currentQuantity = item.current_quantity || 0;
      const minimumQuantity = item.minimum_quantity || 0;
      const avgPrice = item.avg_price || 0;
      const itemValue = currentQuantity * avgPrice;

      // حساب القيمة الإجمالية للمخزون
      totalValue += itemValue;

      // حساب عدد الأصناف التي تحتاج إلى إعادة طلب
      if (currentQuantity <= minimumQuantity && currentQuantity > 0) {
        lowStockItems++;
      }

      // حساب عدد الأصناف التي نفدت
      if (currentQuantity === 0) {
        outOfStockItems++;
      }

      // حساب عدد الأصناف ذات القيمة العالية
      if (itemValue > 1000) {
        highValueItems++;
      }

      // إضافة قيمة الصنف كحقل منفصل
      item.total_value = itemValue;
    }

    // ترتيب الأصناف حسب القيمة الإجمالية (من الأعلى إلى الأقل)
    inventory.sort((a, b) => b.total_value - a.total_value);

    const result = {
      inventory,
      stats: {
        totalItems,
        totalValue,
        lowStockItems,
        outOfStockItems,
        highValueItems,
        averageItemValue: totalItems > 0 ? totalValue / totalItems : 0
      }
    };

    logSystem(`تم إكمال تقرير المخزون بنجاح: ${totalItems} صنف، ${lowStockItems} صنف تحت الحد الأدنى، ${outOfStockItems} صنف نفدت الكمية`, 'info');

    return result;
  } catch (error) {
    logError(error, 'getInventoryReport');
    logSystem(`خطأ في الحصول على تقرير المخزون: ${error.message}`, 'error');

    // إرجاع هيكل بيانات فارغ مع رسالة الخطأ
    return {
      inventory: [],
      stats: {
        totalItems: 0,
        totalValue: 0,
        lowStockItems: 0,
        outOfStockItems: 0,
        highValueItems: 0,
        averageItemValue: 0
      },
      error: error.message
    };
  }
}

/**
 * الحصول على تقرير المعاملات
 * @param {Object} filters - مرشحات البحث
 * @param {string} filters.transactionType - نوع المعاملة ('purchase', 'sale', 'receiving', 'withdrawal')
 * @param {string} filters.startDate - تاريخ البداية
 * @param {string} filters.endDate - تاريخ النهاية
 * @param {number} filters.itemId - معرف الصنف
 * @param {number} filters.customerId - معرف العميل
 * @param {string} filters.invoiceNumber - رقم الفاتورة
 * @param {boolean} filters.groupByInvoice - تجميع المعاملات حسب رقم الفاتورة
 * @returns {Object} - تقرير المعاملات
 */
function getTransactionsReport(filters = {}) {
  try {
    // تحسين رسالة السجل لتجنب عرض undefined
    const filtersSummary = {
      transactionType: filters.transactionType || 'غير محدد',
      startDate: filters.startDate || 'غير محدد',
      endDate: filters.endDate || 'غير محدد',
      itemId: filters.itemId || 'غير محدد',
      customerId: filters.customerId || 'غير محدد',
      invoiceNumber: filters.invoiceNumber || 'غير محدد',
      groupByInvoice: filters.groupByInvoice || false
    };
    logSystem('بدء تنفيذ getTransactionsReport مع الفلاتر: ' + JSON.stringify(filtersSummary), 'info');

    // التحقق من اتصال قاعدة البيانات
    if (!db) {
      logSystem('قاعدة البيانات غير مهيأة في getTransactionsReport', 'error');

      // محاولة إعادة الاتصال بقاعدة البيانات
      const dbManager = DatabaseManager.getInstance();
      if (dbManager) {
        db = dbManager.getConnection();
        if (!db) {
          throw new Error('قاعدة البيانات غير مهيأة ولا يمكن إعادة الاتصال');
        }
        logSystem('تم إعادة الاتصال بقاعدة البيانات في getTransactionsReport', 'info');
      } else {
        throw new Error('قاعدة البيانات غير مهيأة ولا يمكن الحصول على مدير قاعدة البيانات');
      }
    }

    // استخدام مدير المعاملات الموحد إذا كان متاحًا
    if (transactionManager && typeof transactionManager.getTransactions === 'function') {
      try {
        logSystem('استخدام مدير المعاملات الموحد للحصول على المعاملات', 'info');
        const transactions = transactionManager.getTransactions(filters);

        // حساب الإحصائيات
        const stats = calculateTransactionStats(transactions);

        // تجميع المعاملات حسب رقم الفاتورة إذا تم طلب ذلك
        if (filters.groupByInvoice && transactions.length > 0) {
          const groupedTransactions = groupTransactionsByInvoice(transactions);

          return {
            transactions,
            groupedTransactions,
            stats
          };
        }

        return {
          transactions,
          stats
        };
      } catch (managerError) {
        logError(managerError, 'getTransactionsReport - transactionManager');
        logSystem('فشل استخدام مدير المعاملات الموحد، سيتم استخدام الاستعلام المباشر', 'warning');
        // استمر باستخدام الاستعلام المباشر
      }
    }

    // استعلام SQL للحصول على بيانات المعاملات
    let query = `
      SELECT t.id, t.transaction_id, t.item_id, t.transaction_type, t.quantity, t.price,
             t.selling_price, t.total_price, t.profit, t.customer_id, t.invoice_number,
             t.notes, t.user_id, t.transaction_date, t.skip_inventory_update,
             i.name as item_name, i.unit as item_unit,
             c.name as customer_name, c.customer_type as customer_type,
             u.username as user_name
      FROM transactions t
      JOIN items i ON t.item_id = i.id
      LEFT JOIN customers c ON t.customer_id = c.id
      LEFT JOIN users u ON t.user_id = u.id
    `;

    const queryParams = [];
    const conditions = [];

    if (filters.transactionType) {
      conditions.push('t.transaction_type = ?');
      queryParams.push(filters.transactionType);
    }

    if (filters.startDate) {
      conditions.push('t.transaction_date >= ?');
      queryParams.push(filters.startDate);
    }

    if (filters.endDate) {
      conditions.push('t.transaction_date <= ?');
      queryParams.push(filters.endDate);
    }

    if (filters.itemId) {
      conditions.push('t.item_id = ?');
      queryParams.push(filters.itemId);
    }

    if (filters.customerId) {
      conditions.push('t.customer_id = ?');
      queryParams.push(filters.customerId);
    }

    if (filters.invoiceNumber) {
      conditions.push('t.invoice_number = ?');
      queryParams.push(filters.invoiceNumber);
    }

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`;
    }

    query += ' ORDER BY t.transaction_date DESC';

    logSystem(`تنفيذ استعلام المعاملات: ${query}`, 'info');
    logSystem(`معلمات الاستعلام: ${JSON.stringify(queryParams || [])}`, 'info');

    const stmt = db.prepare(query);
    const transactions = stmt.all(...queryParams);

    logSystem(`تم الحصول على ${transactions.length} معاملة`, 'info');

    // حساب إحصائيات المعاملات
    const stats = calculateTransactionStats(transactions);

    // تجميع المعاملات حسب رقم الفاتورة إذا تم طلب ذلك
    if (filters.groupByInvoice && transactions.length > 0) {
      const groupedTransactions = groupTransactionsByInvoice(transactions);

      return {
        transactions,
        groupedTransactions,
        stats
      };
    }

    return {
      transactions,
      stats
    };
  } catch (error) {
    logError(error, 'getTransactionsReport');
    logSystem(`خطأ في الحصول على تقرير المعاملات: ${error.message}`, 'error');

    // إرجاع هيكل بيانات فارغ مع رسالة الخطأ
    return {
      transactions: [],
      stats: {
        totalTransactions: 0,
        totalPurchases: 0,
        totalSales: 0,
        totalProfit: 0,
        purchaseCount: 0,
        saleCount: 0,
        receivingCount: 0,
        withdrawalCount: 0
      },
      error: error.message
    };
  }
}

/**
 * حساب إحصائيات المعاملات
 * @param {Array} transactions - قائمة المعاملات
 * @returns {Object} - إحصائيات المعاملات
 */
function calculateTransactionStats(transactions) {
  let totalTransactions = transactions.length;
  let totalPurchases = 0;
  let totalSales = 0;
  let totalProfit = 0;
  let purchaseCount = 0;
  let saleCount = 0;
  let receivingCount = 0;
  let withdrawalCount = 0;

  for (const transaction of transactions) {
    if (transaction.transaction_type === 'purchase') {
      totalPurchases += transaction.total_price || 0;
      purchaseCount++;
    } else if (transaction.transaction_type === 'receiving') {
      totalPurchases += transaction.total_price || 0;
      receivingCount++;
    } else if (transaction.transaction_type === 'sale') {
      totalSales += transaction.total_price || 0;
      totalProfit += transaction.profit || 0;
      saleCount++;
    } else if (transaction.transaction_type === 'withdrawal') {
      totalSales += transaction.total_price || 0;
      totalProfit += transaction.profit || 0;
      withdrawalCount++;
    }
  }

  return {
    totalTransactions,
    totalPurchases,
    totalSales,
    totalProfit,
    purchaseCount,
    saleCount,
    receivingCount,
    withdrawalCount
  };
}

/**
 * تجميع المعاملات حسب رقم الفاتورة
 * @param {Array} transactions - قائمة المعاملات
 * @returns {Array} - قائمة الفواتير المجمعة
 */
function groupTransactionsByInvoice(transactions) {
  const invoiceMap = new Map();

  // تجميع المعاملات حسب رقم الفاتورة
  for (const transaction of transactions) {
    const invoiceNumber = transaction.invoice_number || 'unknown';

    if (!invoiceMap.has(invoiceNumber)) {
      invoiceMap.set(invoiceNumber, {
        invoiceNumber,
        transaction_date: transaction.transaction_date,
        customer_id: transaction.customer_id,
        customer_name: transaction.customer_name,
        items: [],
        totalAmount: 0,
        totalProfit: 0,
        transaction_type: transaction.transaction_type
      });
    }

    const invoice = invoiceMap.get(invoiceNumber);
    invoice.items.push(transaction);
    invoice.totalAmount += transaction.total_price || 0;
    invoice.totalProfit += transaction.profit || 0;

    // استخدام أحدث تاريخ للفاتورة
    if (transaction.transaction_date > invoice.transaction_date) {
      invoice.transaction_date = transaction.transaction_date;
    }
  }

  // تحويل الخريطة إلى مصفوفة
  return Array.from(invoiceMap.values());
}

/**
 * الحصول على تقرير الأرباح
 * @param {Object} filters - مرشحات البحث
 * @param {string} filters.startDate - تاريخ البداية
 * @param {string} filters.endDate - تاريخ النهاية
 * @param {string} filters.period - الفترة ('daily', 'weekly', 'monthly', 'quarterly', 'yearly')
 * @param {boolean} filters.includeDetails - تضمين تفاصيل إضافية
 * @param {boolean} filters.forceRefresh - إجبار التحديث وتجاوز التخزين المؤقت
 * @returns {Object} - تقرير الأرباح
 */
function getProfitsReport(filters = {}) {
  try {
    logSystem('بدء تنفيذ getProfitsReport مع الفلاتر: ' + JSON.stringify(filters), 'info');

    // التحقق من اتصال قاعدة البيانات
    if (!db) {
      logSystem('قاعدة البيانات غير مهيأة في getProfitsReport', 'error');
      const dbManager = DatabaseManager.getInstance();
      if (dbManager) {
        db = dbManager.getConnection();
        if (!db) throw new Error('قاعدة البيانات غير مهيأة ولا يمكن إعادة الاتصال');
        logSystem('تم إعادة الاتصال بقاعدة البيانات في getProfitsReport', 'info');
      } else {
        throw new Error('قاعدة البيانات غير مهيأة ولا يمكن الحصول على مدير قاعدة البيانات');
      }
    }

    // إذا كان هناك طلب لإجبار التحديث، نقوم بتحديث قيم الربح أولاً
    if (filters.forceRefresh) {
      logSystem('إجبار تحديث قيم الربح قبل إنشاء التقرير...', 'info');
      try {
        // استدعاء دالة إعادة حساب الأرباح من unified-transaction-manager
        const totalProfit = transactionManager.recalculateTotalProfits();
        const updateSuccess = transactionManager.updateProfitTotalInDatabase(totalProfit);

        if (updateSuccess) {
          logSystem(`تم تحديث قيم الربح بنجاح قبل إنشاء التقرير. إجمالي الأرباح: ${totalProfit}`, 'info');
        } else {
          logSystem('فشل في تحديث قيم الربح في قاعدة البيانات', 'error');
        }
      } catch (updateError) {
        logError(updateError, 'getProfitsReport - forceRefresh');
        logSystem(`خطأ في تحديث قيم الربح قبل إنشاء التقرير: ${updateError.message}`, 'error');
        // نستمر في التنفيذ حتى لو فشل التحديث
      }
    }

    // تحديد فترات زمنية مختلفة للتقرير
    const periods = calculatePeriods(filters);

    // جلب جميع معاملات البيع والإرجاع
    logSystem('جاري الحصول على معاملات البيع والإرجاع...', 'info');
    const transactionsReport = getTransactionsReport({
      startDate: filters.startDate,
      endDate: filters.endDate
    });
    const transactions = transactionsReport.transactions;
    logSystem(`تم الحصول على ${transactions.length} معاملة`, 'info');

    // تصفية معاملات البيع والإرجاع
    const salesTransactions = transactions.filter(t => t.transaction_type === 'sale');
    const returnTransactions = transactions.filter(t => t.transaction_type === 'return');

    // حساب إحصائيات الأرباح
    let totalSales = 0;
    let totalProfit = 0;
    let totalQuantity = 0;
    let profitMargin = 0;

    // استيراد دالة حساب الربح
    const { calculateProfit, calculateProfitWithTransport } = require('./utils/profitCalculator');

    // حساب أرباح البيع
    for (const transaction of salesTransactions) {
      totalSales += transaction.total_price || 0;
      let transactionProfit = transaction.profit || 0;
      if (transactionProfit <= 0 && transaction.selling_price > 0) {
        try {
          const inventoryStmt = db.prepare('SELECT avg_price FROM inventory WHERE item_id = ?');
          const inventory = inventoryStmt.get(transaction.item_id);
          if (inventory && inventory.avg_price > 0) {
            // حساب مصاريف النقل المخصصة لهذا الصنف
            let transportCostPerUnit = 0;
            try {
              // الحصول على جميع معاملات الشراء للصنف التي تحتوي على مصاريف نقل
              const purchaseStmt = db.prepare(`
                SELECT
                  quantity,
                  transport_cost
                FROM transactions
                WHERE item_id = ?
                  AND transaction_type = 'purchase'
                  AND transport_cost > 0
              `);

              const purchases = purchaseStmt.all(transaction.item_id);

              if (purchases && purchases.length > 0) {
                // حساب إجمالي الكمية المشتراة ومصاريف النقل
                let totalPurchasedQuantity = 0;
                let totalTransportCost = 0;

                purchases.forEach(purchase => {
                  totalPurchasedQuantity += purchase.quantity;
                  totalTransportCost += purchase.transport_cost;
                });

                if (totalPurchasedQuantity > 0) {
                  // حساب متوسط مصاريف النقل لكل وحدة
                  transportCostPerUnit = totalTransportCost / totalPurchasedQuantity;
                }
              }
            } catch (error) {
              console.error('[PROFIT-FIX-TRANSPORT] خطأ في حساب مصاريف النقل للصنف:', transaction.item_id, error);
              transportCostPerUnit = 0;
            }

            // حساب الربح مع مصاريف النقل
            transactionProfit = calculateProfitWithTransport(transaction.selling_price, inventory.avg_price, transaction.quantity, transportCostPerUnit);
          } else {
            transactionProfit = transaction.selling_price * transaction.quantity * 0.2;
          }
          // تحديث الربح في قاعدة البيانات
          try {
            const updateProfitStmt = db.prepare('UPDATE transactions SET profit = ? WHERE id = ?');
            updateProfitStmt.run(transactionProfit, transaction.id);
          } catch {}
        } catch {}
      }
      totalProfit += transactionProfit;
      totalQuantity += transaction.quantity || 0;
    }

    // خصم أرباح الإرجاعات
    let totalReturnProfit = 0;
    for (const transaction of returnTransactions) {
      let returnProfit = transaction.profit || 0;
      if (returnProfit <= 0 && transaction.selling_price > 0) {
        try {
          const inventoryStmt = db.prepare('SELECT avg_price FROM inventory WHERE item_id = ?');
          const inventory = inventoryStmt.get(transaction.item_id);
          if (inventory && inventory.avg_price > 0) {
            returnProfit = calculateProfit(transaction.selling_price, inventory.avg_price, transaction.quantity);
          } else {
            returnProfit = transaction.selling_price * transaction.quantity * 0.2;
          }
        } catch {}
      }
      totalReturnProfit += Math.max(0, returnProfit);
      totalQuantity -= Math.max(0, transaction.quantity || 0);
    }
    totalProfit -= totalReturnProfit;

    if (totalSales > 0) {
      profitMargin = (totalProfit / totalSales) * 100;
    }

    logSystem(`إجمالي المبيعات: ${totalSales}، إجمالي الأرباح بعد خصم الإرجاعات: ${totalProfit}، هامش الربح: ${profitMargin.toFixed(2)}%`, 'info');

    // حساب الأرباح حسب الفترات (بيع - إرجاع)
    const periodProfits = calculatePeriodProfitsWithReturns(transactions, periods);

    // الحصول على الأصناف الأكثر ربحية
    logSystem('جاري الحصول على الأصناف الأكثر ربحية...', 'info');
    let topItems = [];
    try {
      const topItemsQuery = `
        SELECT
          t.item_id,
          i.name as item_name,
          i.unit as item_unit,
          SUM(t.quantity) as total_quantity,
          SUM(t.total_price) as total_sales,
          SUM(CASE
            WHEN t.profit > 0 THEN t.profit
            WHEN t.selling_price > 0 AND inv.avg_price > 0 THEN (t.selling_price - inv.avg_price) * t.quantity
            WHEN t.selling_price > 0 THEN t.selling_price * t.quantity * 0.2
            ELSE 0
          END) as total_profit
        FROM transactions t
        JOIN items i ON t.item_id = i.id
        LEFT JOIN inventory inv ON t.item_id = inv.item_id
        WHERE t.transaction_type = 'sale'
        ${filters.startDate ? "AND t.transaction_date >= '" + filters.startDate + "'" : ''}
        ${filters.endDate ? "AND t.transaction_date <= '" + filters.endDate + "'" : ''}
        GROUP BY t.item_id
        ORDER BY total_profit DESC
        LIMIT 10
      `;

      logSystem(`استعلام الأصناف الأكثر ربحية: ${topItemsQuery}`, 'info');

      const topItemsStmt = db.prepare(topItemsQuery);
      topItems = topItemsStmt.all();

      // إضافة نسبة الربح لكل صنف
      for (const item of topItems) {
        if (item.total_sales > 0) {
          item.profit_margin = (item.total_profit / item.total_sales) * 100;
        } else {
          item.profit_margin = 0;
        }
      }

      logSystem(`تم الحصول على ${topItems.length} صنف من الأصناف الأكثر ربحية`, 'info');
    } catch (topItemsError) {
      logError(topItemsError, 'getProfitsReport - topItems');
      logSystem(`خطأ في الحصول على الأصناف الأكثر ربحية: ${topItemsError.message}`, 'error');
      topItems = [];
    }

    // الحصول على العملاء الأكثر شراءً
    logSystem('جاري الحصول على العملاء الأكثر شراءً...', 'info');
    let topCustomers = [];
    try {
      const topCustomersQuery = `
        SELECT
          t.customer_id,
          c.name as customer_name,
          c.customer_type,
          COUNT(DISTINCT t.id) as transaction_count,
          SUM(t.total_price) as total_sales,
          SUM(CASE
            WHEN t.profit > 0 THEN t.profit
            WHEN t.selling_price > 0 AND inv.avg_price > 0 THEN (t.selling_price - inv.avg_price) * t.quantity
            WHEN t.selling_price > 0 THEN t.selling_price * t.quantity * 0.2
            ELSE 0
          END) as total_profit
        FROM transactions t
        JOIN customers c ON t.customer_id = c.id
        LEFT JOIN inventory inv ON t.item_id = inv.item_id
        WHERE t.transaction_type = 'sale'
        ${filters.startDate ? "AND t.transaction_date >= '" + filters.startDate + "'" : ''}
        ${filters.endDate ? "AND t.transaction_date <= '" + filters.endDate + "'" : ''}
        GROUP BY t.customer_id
        ORDER BY total_sales DESC
        LIMIT 10
      `;

      logSystem(`استعلام العملاء الأكثر شراءً: ${topCustomersQuery}`, 'info');

      const topCustomersStmt = db.prepare(topCustomersQuery);
      topCustomers = topCustomersStmt.all();

      // إضافة نسبة الربح لكل عميل
      for (const customer of topCustomers) {
        if (customer.total_sales > 0) {
          customer.profit_margin = (customer.total_profit / customer.total_sales) * 100;
        } else {
          customer.profit_margin = 0;
        }
      }

      logSystem(`تم الحصول على ${topCustomers.length} عميل من العملاء الأكثر شراءً`, 'info');
    } catch (topCustomersError) {
      logError(topCustomersError, 'getProfitsReport - topCustomers');
      logSystem(`خطأ في الحصول على العملاء الأكثر شراءً: ${topCustomersError.message}`, 'error');
      topCustomers = [];
    }

    // الحصول على تحليل الأرباح حسب الشهر
    let monthlyProfits = [];
    try {
      const monthlyProfitsQuery = `
        SELECT
          strftime('%Y-%m', t.transaction_date) as month,
          SUM(t.total_price) as total_sales,
          SUM(COALESCE(t.profit, 0)) as total_profit,
          COUNT(DISTINCT t.id) as transaction_count
        FROM transactions t
        WHERE t.transaction_type = 'sale'
        ${filters.startDate ? "AND t.transaction_date >= '" + filters.startDate + "'" : ''}
        ${filters.endDate ? "AND t.transaction_date <= '" + filters.endDate + "'" : ''}
        GROUP BY month
        ORDER BY month
      `;

      logSystem(`استعلام الأرباح الشهرية: ${monthlyProfitsQuery}`, 'info');

      const monthlyProfitsStmt = db.prepare(monthlyProfitsQuery);
      monthlyProfits = monthlyProfitsStmt.all();

      // إضافة نسبة الربح لكل شهر
      for (const month of monthlyProfits) {
        if (month.total_sales > 0) {
          month.profit_margin = (month.total_profit / month.total_sales) * 100;
        } else {
          month.profit_margin = 0;
        }
      }

      logSystem(`تم الحصول على ${monthlyProfits.length} شهر من تحليل الأرباح الشهرية`, 'info');
    } catch (monthlyProfitsError) {
      logError(monthlyProfitsError, 'getProfitsReport - monthlyProfits');
      logSystem(`خطأ في الحصول على تحليل الأرباح الشهرية: ${monthlyProfitsError.message}`, 'error');
      monthlyProfits = [];
    }

    const result = {
      transactions,
      stats: {
        totalSales,
        totalProfit,
        totalQuantity,
        profitMargin,
        transactionCount: transactions.length
      },
      periods: periodProfits,
      topItems,
      topCustomers,
      monthlyProfits
    };

    logSystem('تم إكمال تقرير الأرباح بنجاح', 'info');

    return result;
  } catch (error) {
    logError(error, 'getProfitsReport');
    logSystem(`خطأ في الحصول على تقرير الأرباح: ${error.message}`, 'error');

    // إرجاع هيكل بيانات فارغ مع رسالة الخطأ
    return {
      transactions: [],
      stats: {
        totalSales: 0,
        totalProfit: 0,
        totalQuantity: 0,
        profitMargin: 0,
        transactionCount: 0
      },
      periods: {},
      topItems: [],
      topCustomers: [],
      monthlyProfits: [],
      error: error.message
    };
  }
}

/**
 * حساب الفترات الزمنية للتقرير
 * @param {Object} filters - مرشحات البحث
 * @returns {Object} - الفترات الزمنية
 */
function calculatePeriods(filters) {
  const now = new Date();
  const periods = {};

  // الربع الحالي
  const currentQuarter = {
    start: new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1),
    end: new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3 + 3, 0)
  };
  periods.currentQuarter = {
    start: currentQuarter.start.toISOString(),
    end: currentQuarter.end.toISOString(),
    label: `الربع ${Math.floor(now.getMonth() / 3) + 1} (${currentQuarter.start.toLocaleDateString()} - ${currentQuarter.end.toLocaleDateString()})`
  };

  // النصف الحالي
  const currentHalf = {
    start: new Date(now.getFullYear(), Math.floor(now.getMonth() / 6) * 6, 1),
    end: new Date(now.getFullYear(), Math.floor(now.getMonth() / 6) * 6 + 6, 0)
  };
  periods.currentHalf = {
    start: currentHalf.start.toISOString(),
    end: currentHalf.end.toISOString(),
    label: `النصف ${Math.floor(now.getMonth() / 6) + 1} (${currentHalf.start.toLocaleDateString()} - ${currentHalf.end.toLocaleDateString()})`
  };

  // السنة الحالية
  const currentYear = {
    start: new Date(now.getFullYear(), 0, 1),
    end: new Date(now.getFullYear(), 11, 31)
  };
  periods.currentYear = {
    start: currentYear.start.toISOString(),
    end: currentYear.end.toISOString(),
    label: `السنة الحالية (${currentYear.start.getFullYear()})`
  };

  // السنة السابقة
  const previousYear = {
    start: new Date(now.getFullYear() - 1, 0, 1),
    end: new Date(now.getFullYear() - 1, 11, 31)
  };
  periods.previousYear = {
    start: previousYear.start.toISOString(),
    end: previousYear.end.toISOString(),
    label: `السنة السابقة (${previousYear.start.getFullYear()})`
  };

  return periods;
}

/**
 * حساب الأرباح حسب الفترات الزمنية
 * @param {Array} transactions - قائمة المعاملات
 * @param {Object} periods - الفترات الزمنية
 * @returns {Object} - الأرباح حسب الفترات
 */
function calculatePeriodProfits(transactions, periods) {
  const periodProfits = {};

  // تهيئة إحصائيات كل فترة
  for (const [periodKey, period] of Object.entries(periods)) {
    periodProfits[periodKey] = {
      label: period.label,
      totalSales: 0,
      totalProfit: 0,
      transactionCount: 0,
      profitMargin: 0
    };
  }

  // تصفية معاملات البيع فقط
  const salesTransactions = transactions.filter(t => t.transaction_type === 'sale');

  // حساب الإحصائيات لكل فترة من معاملات البيع فقط
  for (const transaction of salesTransactions) {
    const transactionDate = new Date(transaction.transaction_date);

    for (const [periodKey, period] of Object.entries(periods)) {
      const periodStart = new Date(period.start);
      const periodEnd = new Date(period.end);

      if (transactionDate >= periodStart && transactionDate <= periodEnd) {
        periodProfits[periodKey].totalSales += transaction.total_price || 0;
        periodProfits[periodKey].totalProfit += transaction.profit || 0;
        periodProfits[periodKey].transactionCount++;
      }
    }
  }

  // حساب نسبة الربح لكل فترة
  for (const [periodKey, stats] of Object.entries(periodProfits)) {
    if (stats.totalSales > 0) {
      stats.profitMargin = (stats.totalProfit / stats.totalSales) * 100;
    }
  }

  return periodProfits;
}

/**
 * حساب الأرباح حسب الفترات الزمنية مع خصم الإرجاعات
 * @param {Array} transactions - قائمة المعاملات
 * @param {Object} periods - الفترات الزمنية
 * @returns {Object} - الأرباح حسب الفترات
 */
function calculatePeriodProfitsWithReturns(transactions, periods) {
  const periodProfits = {};
  for (const [periodKey, period] of Object.entries(periods)) {
    periodProfits[periodKey] = {
      label: period.label,
      totalSales: 0,
      totalProfit: 0,
      transactionCount: 0,
      profitMargin: 0
    };
  }
  for (const transaction of transactions) {
    const transactionDate = new Date(transaction.transaction_date);
    for (const [periodKey, period] of Object.entries(periods)) {
      const periodStart = new Date(period.start);
      const periodEnd = new Date(period.end);
      if (transactionDate >= periodStart && transactionDate <= periodEnd) {
        if (transaction.transaction_type === 'sale') {
          periodProfits[periodKey].totalSales += transaction.total_price || 0;
          periodProfits[periodKey].totalProfit += transaction.profit || 0;
          periodProfits[periodKey].transactionCount++;
        } else if (transaction.transaction_type === 'return') {
          periodProfits[periodKey].totalProfit -= (transaction.profit || 0);
          periodProfits[periodKey].transactionCount++;
        }
      }
    }
  }
  for (const [periodKey, stats] of Object.entries(periodProfits)) {
    if (stats.totalSales > 0) {
      stats.profitMargin = (stats.totalProfit / stats.totalSales) * 100;
    }
  }
  return periodProfits;
}

/**
 * الحصول على تقرير مبيعات العملاء الفرعيين
 * @param {number} parentId - معرف العميل الرئيسي
 * @param {Object} filters - مرشحات البحث
 * @param {string} filters.startDate - تاريخ البداية
 * @param {string} filters.endDate - تاريخ النهاية
 * @param {boolean} filters.includeTransactions - تضمين تفاصيل المعاملات
 * @param {boolean} filters.includeInvoices - تضمين تفاصيل الفواتير
 * @returns {Object} - تقرير مبيعات العملاء الفرعيين
 */
function getSubCustomersSalesReport(parentId, filters = {}) {
  try {
    logSystem(`بدء تنفيذ getSubCustomersSalesReport للعميل: ${parentId} مع الفلاتر: ${JSON.stringify(filters)}`, 'info');

    // التحقق من اتصال قاعدة البيانات
    if (!db) {
      logSystem('قاعدة البيانات غير مهيأة في getSubCustomersSalesReport', 'error');

      // محاولة إعادة الاتصال بقاعدة البيانات
      const dbManager = DatabaseManager.getInstance();
      if (dbManager) {
        db = dbManager.getConnection();
        if (!db) {
          throw new Error('قاعدة البيانات غير مهيأة ولا يمكن إعادة الاتصال');
        }
        logSystem('تم إعادة الاتصال بقاعدة البيانات في getSubCustomersSalesReport', 'info');
      } else {
        throw new Error('قاعدة البيانات غير مهيأة ولا يمكن الحصول على مدير قاعدة البيانات');
      }
    }

    // التحويل إلى رقم
    const numericParentId = Number(parentId);
    if (isNaN(numericParentId) || numericParentId <= 0) {
      throw new Error(`معرف العميل الرئيسي غير صالح: ${parentId}`);
    }

    // التحقق من وجود العميل الرئيسي
    const checkParentStmt = db.prepare('SELECT * FROM customers WHERE id = ?');
    const parent = checkParentStmt.get(numericParentId);

    if (!parent) {
      throw new Error(`العميل الرئيسي غير موجود: ${numericParentId}`);
    }

    logSystem(`تم العثور على العميل الرئيسي: ${parent.name} (${parent.id})`, 'info');

    // استخدام مدير العملاء إذا كان متاحًا
    let subCustomers = [];
    if (customersManager && typeof customersManager.getSubCustomers === 'function') {
      try {
        logSystem('استخدام مدير العملاء للحصول على العملاء الفرعيين', 'info');
        subCustomers = customersManager.getSubCustomers(numericParentId);
      } catch (managerError) {
        logError(managerError, 'getSubCustomersSalesReport - customersManager');
        logSystem('فشل استخدام مدير العملاء، سيتم استخدام الاستعلام المباشر', 'warning');
        // استمر باستخدام الاستعلام المباشر
      }
    }

    // إذا لم يتم الحصول على العملاء الفرعيين من مدير العملاء، استخدم الاستعلام المباشر
    if (!subCustomers || subCustomers.length === 0) {
      // الحصول على العملاء الفرعيين
      const getSubCustomersStmt = db.prepare(`
        SELECT id, name, contact_person, phone, email, address,
               customer_type, parent_id, credit_limit, balance,
               created_at, updated_at
        FROM customers
        WHERE parent_id = ?
        ORDER BY name
      `);

      subCustomers = getSubCustomersStmt.all(numericParentId);
    }

    logSystem(`تم العثور على ${subCustomers.length} عميل فرعي`, 'info');

    // الحصول على مبيعات كل عميل فرعي
    const subCustomerSales = [];
    let totalSales = 0;
    let totalProfit = 0;
    let totalTransactions = 0;
    let totalItems = 0;

    for (const subCustomer of subCustomers) {
      // الحصول على إحصائيات المبيعات
      const getSalesStmt = db.prepare(`
        SELECT
          SUM(t.total_price) as total_sales,
          SUM(CASE
            WHEN t.profit > 0 THEN t.profit
            WHEN t.selling_price > 0 AND inv.avg_price > 0 THEN (t.selling_price - inv.avg_price) * t.quantity
            WHEN t.selling_price > 0 THEN t.selling_price * t.quantity * 0.2
            ELSE 0
          END) as total_profit,
          COUNT(DISTINCT t.id) as transaction_count,
          SUM(t.quantity) as total_quantity,
          COUNT(DISTINCT t.invoice_number) as invoice_count
        FROM transactions t
        LEFT JOIN inventory inv ON t.item_id = inv.item_id
        WHERE t.transaction_type = 'sale'
        AND t.customer_id = ?
        ${filters.startDate ? "AND t.transaction_date >= '" + filters.startDate + "'" : ''}
        ${filters.endDate ? "AND t.transaction_date <= '" + filters.endDate + "'" : ''}
      `);

      const sales = getSalesStmt.get(subCustomer.id);

      // حساب نسبة الربح
      let profitMargin = 0;
      if (sales.total_sales > 0) {
        profitMargin = (sales.total_profit / sales.total_sales) * 100;
      }

      const customerSalesData = {
        customer: subCustomer,
        sales: {
          totalSales: sales.total_sales || 0,
          totalProfit: sales.total_profit || 0,
          transactionCount: sales.transaction_count || 0,
          totalQuantity: sales.total_quantity || 0,
          invoiceCount: sales.invoice_count || 0,
          profitMargin
        }
      };

      // إضافة تفاصيل المعاملات إذا تم طلبها
      if (filters.includeTransactions) {
        const getTransactionsStmt = db.prepare(`
          SELECT t.id, t.transaction_id, t.item_id, t.quantity, t.price,
                 t.selling_price, t.total_price, t.profit, t.invoice_number,
                 t.transaction_date, i.name as item_name, i.unit as item_unit
          FROM transactions t
          JOIN items i ON t.item_id = i.id
          WHERE t.transaction_type = 'sale'
          AND t.customer_id = ?
          ${filters.startDate ? "AND t.transaction_date >= '" + filters.startDate + "'" : ''}
          ${filters.endDate ? "AND t.transaction_date <= '" + filters.endDate + "'" : ''}
          ORDER BY t.transaction_date DESC
        `);

        customerSalesData.transactions = getTransactionsStmt.all(subCustomer.id);
      }

      // إضافة تفاصيل الفواتير إذا تم طلبها
      if (filters.includeInvoices) {
        const getInvoicesStmt = db.prepare(`
          SELECT t.invoice_number, MIN(t.transaction_date) as invoice_date,
                 COUNT(t.id) as item_count, SUM(t.total_price) as total_amount,
                 SUM(t.profit) as total_profit
          FROM transactions t
          WHERE t.transaction_type = 'sale'
          AND t.customer_id = ?
          ${filters.startDate ? "AND t.transaction_date >= '" + filters.startDate + "'" : ''}
          ${filters.endDate ? "AND t.transaction_date <= '" + filters.endDate + "'" : ''}
          GROUP BY t.invoice_number
          ORDER BY invoice_date DESC
        `);

        customerSalesData.invoices = getInvoicesStmt.all(subCustomer.id);
      }

      subCustomerSales.push(customerSalesData);

      totalSales += sales.total_sales || 0;
      totalProfit += sales.total_profit || 0;
      totalTransactions += sales.transaction_count || 0;
      totalItems += sales.total_quantity || 0;
    }

    // ترتيب العملاء الفرعيين حسب إجمالي المبيعات (من الأعلى إلى الأقل)
    subCustomerSales.sort((a, b) => b.sales.totalSales - a.sales.totalSales);

    // حساب نسبة الربح الإجمالية
    let totalProfitMargin = 0;
    if (totalSales > 0) {
      totalProfitMargin = (totalProfit / totalSales) * 100;
    }

    const result = {
      parent,
      subCustomers: subCustomerSales,
      stats: {
        totalSubCustomers: subCustomers.length,
        totalSales,
        totalProfit,
        totalTransactions,
        totalItems,
        totalProfitMargin
      }
    };

    logSystem(`تم إكمال تقرير مبيعات العملاء الفرعيين بنجاح: ${subCustomers.length} عميل، إجمالي المبيعات: ${totalSales}، إجمالي الربح: ${totalProfit}`, 'info');

    return result;
  } catch (error) {
    logError(error, 'getSubCustomersSalesReport');
    logSystem(`خطأ في الحصول على تقرير مبيعات العملاء الفرعيين: ${error.message}`, 'error');

    // إرجاع هيكل بيانات فارغ مع رسالة الخطأ
    return {
      parent: null,
      subCustomers: [],
      stats: {
        totalSubCustomers: 0,
        totalSales: 0,
        totalProfit: 0,
        totalTransactions: 0,
        totalItems: 0,
        totalProfitMargin: 0
      },
      error: error.message
    };
  }
}

/**
 * الحصول على تقرير الخزينة
 * @param {Object} filters - مرشحات البحث
 * @param {string} filters.type - نوع المعاملة ('income', 'expense')
 * @param {string} filters.startDate - تاريخ البداية
 * @param {string} filters.endDate - تاريخ النهاية
 * @param {string} filters.source - مصدر المعاملة
 * @param {boolean} filters.groupBySource - تجميع المعاملات حسب المصدر
 * @param {boolean} filters.groupByDate - تجميع المعاملات حسب التاريخ
 * @returns {Object} - تقرير الخزينة
 */
function getCashboxReport(filters = {}) {
  try {
    logSystem(`بدء تنفيذ getCashboxReport مع الفلاتر: ${JSON.stringify(filters)}`, 'info');

    // التحقق من اتصال قاعدة البيانات
    if (!db) {
      logSystem('قاعدة البيانات غير مهيأة في getCashboxReport', 'error');

      // محاولة إعادة الاتصال بقاعدة البيانات
      const dbManager = DatabaseManager.getInstance();
      if (dbManager) {
        db = dbManager.getConnection();
        if (!db) {
          throw new Error('قاعدة البيانات غير مهيأة ولا يمكن إعادة الاتصال');
        }
        logSystem('تم إعادة الاتصال بقاعدة البيانات في getCashboxReport', 'info');
      } else {
        throw new Error('قاعدة البيانات غير مهيأة ولا يمكن الحصول على مدير قاعدة البيانات');
      }
    }

    // استخدام مدير الخزينة إذا كان متاحًا
    if (cashboxManager && typeof cashboxManager.getCashbox === 'function') {
      try {
        logSystem('استخدام مدير الخزينة للحصول على معلومات الخزينة', 'info');
        const cashboxInfo = cashboxManager.getCashbox();

        if (cashboxInfo) {
          logSystem(`تم الحصول على معلومات الخزينة: الرصيد الحالي ${cashboxInfo.current_balance}`, 'info');
        } else {
          logSystem('لم يتم العثور على معلومات الخزينة من مدير الخزينة', 'warning');
        }
      } catch (managerError) {
        logError(managerError, 'getCashboxReport - cashboxManager');
        logSystem('فشل استخدام مدير الخزينة، سيتم استخدام الاستعلام المباشر', 'warning');
        // استمر باستخدام الاستعلام المباشر
      }
    }

    // الحصول على معلومات الخزينة
    let cashbox = null;
    try {
      const getCashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
      cashbox = getCashboxStmt.get();
    } catch (dbError) {
      logError(dbError, 'getCashboxReport - get cashbox');
      logSystem('خطأ في الحصول على معلومات الخزينة من قاعدة البيانات', 'error');

      // التحقق من وجود جدول الخزينة
      try {
        const tableCheckStmt = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='cashbox'");
        const tableExists = tableCheckStmt.get();

        if (!tableExists) {
          logSystem('جدول cashbox غير موجود في قاعدة البيانات، جاري إنشاؤه...', 'warning');

          // إنشاء جدول الخزينة
          db.exec(`
            CREATE TABLE IF NOT EXISTS cashbox (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              initial_balance REAL NOT NULL DEFAULT 0,
              current_balance REAL NOT NULL DEFAULT 0,
              last_updated TEXT DEFAULT CURRENT_TIMESTAMP
            )
          `);

          // إضافة سجل افتراضي
          db.exec(`
            INSERT INTO cashbox (initial_balance, current_balance, last_updated)
            VALUES (0, 0, CURRENT_TIMESTAMP)
          `);

          logSystem('تم إنشاء جدول cashbox بنجاح', 'info');

          // محاولة الحصول على معلومات الخزينة مرة أخرى
          const getCashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
          cashbox = getCashboxStmt.get();
        }
      } catch (tableCheckError) {
        logError(tableCheckError, 'getCashboxReport - check cashbox table');
      }
    }

    if (!cashbox) {
      logSystem('لم يتم العثور على معلومات الخزينة', 'warning');

      // إنشاء كائن خزينة افتراضي
      cashbox = {
        id: 0,
        initial_balance: 0,
        current_balance: 0,
        last_updated: new Date().toISOString()
      };
    }

    logSystem(`تم العثور على معلومات الخزينة: الرصيد الابتدائي ${cashbox.initial_balance}، الرصيد الحالي ${cashbox.current_balance}`, 'info');

    // الحصول على معاملات الخزينة
    let transactions = [];

    try {
      // التحقق من وجود جدول معاملات الخزينة
      const tableCheckStmt = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='cashbox_transactions'");
      const tableExists = tableCheckStmt.get();

      if (!tableExists) {
        logSystem('جدول cashbox_transactions غير موجود في قاعدة البيانات، جاري إنشاؤه...', 'warning');

        // إنشاء جدول معاملات الخزينة
        db.exec(`
          CREATE TABLE IF NOT EXISTS cashbox_transactions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
            amount REAL NOT NULL,
            source TEXT,
            notes TEXT,
            user_id INTEGER,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id)
          )
        `);

        // إنشاء فهرس للتاريخ
        db.exec('CREATE INDEX IF NOT EXISTS idx_cashbox_transactions_created_at ON cashbox_transactions(created_at)');

        logSystem('تم إنشاء جدول cashbox_transactions بنجاح', 'info');
      }

      let query = `
        SELECT ct.id, ct.type, ct.amount, ct.source, ct.notes, ct.user_id, ct.created_at,
               u.username as user_name
        FROM cashbox_transactions ct
        LEFT JOIN users u ON ct.user_id = u.id
      `;

      const queryParams = [];
      const conditions = [];

      if (filters.type) {
        conditions.push('ct.type = ?');
        queryParams.push(filters.type);
      }

      if (filters.startDate) {
        conditions.push('ct.created_at >= ?');
        queryParams.push(filters.startDate);
      }

      if (filters.endDate) {
        conditions.push('ct.created_at <= ?');
        queryParams.push(filters.endDate);
      }

      if (filters.source) {
        conditions.push('ct.source = ?');
        queryParams.push(filters.source);
      }

      if (conditions.length > 0) {
        query += ` WHERE ${conditions.join(' AND ')}`;
      }

      query += ' ORDER BY ct.created_at DESC';

      logSystem(`تنفيذ استعلام معاملات الخزينة: ${query}`, 'info');
      logSystem(`معلمات الاستعلام: ${JSON.stringify(queryParams)}`, 'info');

      const stmt = db.prepare(query);
      transactions = stmt.all(...queryParams);

    } catch (transactionsError) {
      logError(transactionsError, 'getCashboxReport - get transactions');
      logSystem('خطأ في الحصول على معاملات الخزينة، سيتم إرجاع مصفوفة فارغة', 'error');
      transactions = [];
    }

    logSystem(`تم الحصول على ${transactions.length} معاملة للخزينة`, 'info');

    // حساب إحصائيات الخزينة
    let totalIncome = 0;
    let totalExpenses = 0;
    let incomeCount = 0;
    let expenseCount = 0;
    let sourceStats = {};
    let dateStats = {};

    for (const transaction of transactions) {
      if (transaction.type === 'income') {
        totalIncome += transaction.amount;
        incomeCount++;
      } else if (transaction.type === 'expense') {
        totalExpenses += transaction.amount;
        expenseCount++;
      }

      // تجميع الإحصائيات حسب المصدر
      const source = transaction.source || 'غير محدد';
      if (!sourceStats[source]) {
        sourceStats[source] = {
          income: 0,
          expense: 0,
          count: 0
        };
      }

      sourceStats[source].count++;
      if (transaction.type === 'income') {
        sourceStats[source].income += transaction.amount;
      } else if (transaction.type === 'expense') {
        sourceStats[source].expense += transaction.amount;
      }

      // تجميع الإحصائيات حسب التاريخ (الشهر)
      const date = new Date(transaction.created_at);
      const monthYear = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;

      if (!dateStats[monthYear]) {
        dateStats[monthYear] = {
          income: 0,
          expense: 0,
          count: 0,
          label: `${date.getFullYear()}/${(date.getMonth() + 1).toString().padStart(2, '0')}`
        };
      }

      dateStats[monthYear].count++;
      if (transaction.type === 'income') {
        dateStats[monthYear].income += transaction.amount;
      } else if (transaction.type === 'expense') {
        dateStats[monthYear].expense += transaction.amount;
      }
    }

    // تحويل إحصائيات المصدر إلى مصفوفة
    const sourceStatsArray = Object.entries(sourceStats).map(([source, stats]) => ({
      source,
      income: stats.income,
      expense: stats.expense,
      count: stats.count,
      net: stats.income - stats.expense
    }));

    // ترتيب إحصائيات المصدر حسب صافي الدخل
    sourceStatsArray.sort((a, b) => b.net - a.net);

    // تحويل إحصائيات التاريخ إلى مصفوفة
    const dateStatsArray = Object.entries(dateStats).map(([date, stats]) => ({
      date,
      label: stats.label,
      income: stats.income,
      expense: stats.expense,
      count: stats.count,
      net: stats.income - stats.expense
    }));

    // ترتيب إحصائيات التاريخ حسب التاريخ (من الأحدث إلى الأقدم)
    dateStatsArray.sort((a, b) => b.date.localeCompare(a.date));

    // تجميع المعاملات حسب المصدر إذا تم طلب ذلك
    let groupedBySource = null;
    if (filters.groupBySource) {
      groupedBySource = {};

      for (const transaction of transactions) {
        const source = transaction.source || 'غير محدد';

        if (!groupedBySource[source]) {
          groupedBySource[source] = [];
        }

        groupedBySource[source].push(transaction);
      }
    }

    // تجميع المعاملات حسب التاريخ إذا تم طلب ذلك
    let groupedByDate = null;
    if (filters.groupByDate) {
      groupedByDate = {};

      for (const transaction of transactions) {
        const date = new Date(transaction.created_at);
        const dateStr = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;

        if (!groupedByDate[dateStr]) {
          groupedByDate[dateStr] = [];
        }

        groupedByDate[dateStr].push(transaction);
      }
    }

    const result = {
      cashbox,
      transactions,
      stats: {
        totalIncome,
        totalExpenses,
        balance: totalIncome - totalExpenses,
        incomeCount,
        expenseCount,
        totalCount: transactions.length,
        averageIncome: incomeCount > 0 ? totalIncome / incomeCount : 0,
        averageExpense: expenseCount > 0 ? totalExpenses / expenseCount : 0
      },
      sourceStats: sourceStatsArray,
      dateStats: dateStatsArray
    };

    // إضافة المعاملات المجمعة إذا تم طلبها
    if (groupedBySource) {
      result.groupedBySource = groupedBySource;
    }

    if (groupedByDate) {
      result.groupedByDate = groupedByDate;
    }

    logSystem(`تم إكمال تقرير الخزينة بنجاح: ${transactions.length} معاملة، إجمالي الدخل: ${totalIncome}، إجمالي المصروفات: ${totalExpenses}`, 'info');

    return result;
  } catch (error) {
    logError(error, 'getCashboxReport');
    logSystem(`خطأ في الحصول على تقرير الخزينة: ${error.message}`, 'error');

    // إرجاع هيكل بيانات فارغ مع رسالة الخطأ
    return {
      cashbox: null,
      transactions: [],
      stats: {
        totalIncome: 0,
        totalExpenses: 0,
        balance: 0,
        incomeCount: 0,
        expenseCount: 0,
        totalCount: 0,
        averageIncome: 0,
        averageExpense: 0
      },
      sourceStats: [],
      dateStats: [],
      error: error.message
    };
  }
}

/**
 * الحصول على تقرير الأصناف الأكثر مبيعًا
 * @param {Object} filters - مرشحات البحث
 * @param {string} filters.startDate - تاريخ البداية
 * @param {string} filters.endDate - تاريخ النهاية
 * @param {number} filters.limit - عدد الأصناف المراد عرضها (الافتراضي: 10)
 * @returns {Object} - تقرير الأصناف الأكثر مبيعًا
 */
function getTopSellingItemsReport(filters = {}) {
  try {
    logSystem(`بدء تنفيذ getTopSellingItemsReport مع الفلاتر: ${JSON.stringify(filters)}`, 'info');

    // التحقق من اتصال قاعدة البيانات
    if (!db) {
      logSystem('قاعدة البيانات غير مهيأة في getTopSellingItemsReport', 'error');
      throw new Error('قاعدة البيانات غير مهيأة');
    }

    const limit = filters.limit || 10;

    // استعلام SQL للحصول على الأصناف الأكثر مبيعًا
    let query = `
      SELECT
        t.item_id,
        i.name as item_name,
        i.unit as item_unit,
        SUM(t.quantity) as total_quantity,
        SUM(t.total_price) as total_sales,
        SUM(t.profit) as total_profit,
        COUNT(DISTINCT t.id) as transaction_count,
        COUNT(DISTINCT t.customer_id) as customer_count,
        MAX(t.transaction_date) as last_sale_date
      FROM transactions t
      JOIN items i ON t.item_id = i.id
      WHERE t.transaction_type = 'sale'
    `;

    const queryParams = [];

    if (filters.startDate) {
      query += ' AND t.transaction_date >= ?';
      queryParams.push(filters.startDate);
    }

    if (filters.endDate) {
      query += ' AND t.transaction_date <= ?';
      queryParams.push(filters.endDate);
    }

    query += `
      GROUP BY t.item_id
      ORDER BY total_quantity DESC
      LIMIT ?
    `;
    queryParams.push(limit);

    logSystem(`تنفيذ استعلام الأصناف الأكثر مبيعًا: ${query}`, 'info');
    logSystem(`معلمات الاستعلام: ${JSON.stringify(queryParams)}`, 'info');

    const stmt = db.prepare(query);
    const items = stmt.all(...queryParams);

    logSystem(`تم الحصول على ${items.length} صنف من الأصناف الأكثر مبيعًا`, 'info');

    // حساب إحصائيات إضافية
    let totalQuantity = 0;
    let totalSales = 0;
    let totalProfit = 0;

    for (const item of items) {
      totalQuantity += item.total_quantity || 0;
      totalSales += item.total_sales || 0;
      totalProfit += item.total_profit || 0;

      // حساب نسبة الربح لكل صنف
      if (item.total_sales > 0) {
        item.profit_margin = (item.total_profit / item.total_sales) * 100;
      } else {
        item.profit_margin = 0;
      }

      // الحصول على الكمية المتوفرة حاليًا
      try {
        const inventoryStmt = db.prepare('SELECT current_quantity FROM inventory WHERE item_id = ?');
        const inventory = inventoryStmt.get(item.item_id);
        item.current_quantity = inventory ? inventory.current_quantity : 0;
      } catch (error) {
        logError(error, `getTopSellingItemsReport - الحصول على الكمية المتوفرة للصنف ${item.item_id}`);
        item.current_quantity = 0;
      }
    }

    // حساب نسبة الربح الإجمالية
    let profitMargin = 0;
    if (totalSales > 0) {
      profitMargin = (totalProfit / totalSales) * 100;
    }

    const result = {
      items,
      stats: {
        totalItems: items.length,
        totalQuantity,
        totalSales,
        totalProfit,
        profitMargin
      }
    };

    logSystem(`تم إكمال تقرير الأصناف الأكثر مبيعًا بنجاح: ${items.length} صنف، إجمالي المبيعات: ${totalSales}، إجمالي الربح: ${totalProfit}`, 'info');

    return result;
  } catch (error) {
    logError(error, 'getTopSellingItemsReport');
    logSystem(`خطأ في الحصول على تقرير الأصناف الأكثر مبيعًا: ${error.message}`, 'error');

    // إرجاع هيكل بيانات فارغ مع رسالة الخطأ
    return {
      items: [],
      stats: {
        totalItems: 0,
        totalQuantity: 0,
        totalSales: 0,
        totalProfit: 0,
        profitMargin: 0
      },
      error: error.message
    };
  }
}

/**
 * الحصول على تقرير العملاء الأكثر شراءً
 * @param {Object} filters - مرشحات البحث
 * @param {string} filters.startDate - تاريخ البداية
 * @param {string} filters.endDate - تاريخ النهاية
 * @param {number} filters.limit - عدد العملاء المراد عرضهم (الافتراضي: 10)
 * @returns {Object} - تقرير العملاء الأكثر شراءً
 */
function getTopCustomersReport(filters = {}) {
  try {
    logSystem(`بدء تنفيذ getTopCustomersReport مع الفلاتر: ${JSON.stringify(filters)}`, 'info');

    // التحقق من اتصال قاعدة البيانات
    if (!db) {
      logSystem('قاعدة البيانات غير مهيأة في getTopCustomersReport', 'error');
      throw new Error('قاعدة البيانات غير مهيأة');
    }

    const limit = filters.limit || 10;

    // استعلام SQL للحصول على العملاء الأكثر شراءً
    let query = `
      SELECT
        t.customer_id,
        c.name as customer_name,
        c.customer_type,
        c.phone,
        c.email,
        SUM(t.total_price) as total_sales,
        SUM(t.profit) as total_profit,
        COUNT(DISTINCT t.id) as transaction_count,
        COUNT(DISTINCT t.invoice_number) as invoice_count,
        MAX(t.transaction_date) as last_purchase_date
      FROM transactions t
      JOIN customers c ON t.customer_id = c.id
      WHERE t.transaction_type = 'sale'
    `;

    const queryParams = [];

    if (filters.startDate) {
      query += ' AND t.transaction_date >= ?';
      queryParams.push(filters.startDate);
    }

    if (filters.endDate) {
      query += ' AND t.transaction_date <= ?';
      queryParams.push(filters.endDate);
    }

    query += `
      GROUP BY t.customer_id
      ORDER BY total_sales DESC
      LIMIT ?
    `;
    queryParams.push(limit);

    logSystem(`تنفيذ استعلام العملاء الأكثر شراءً: ${query}`, 'info');
    logSystem(`معلمات الاستعلام: ${JSON.stringify(queryParams)}`, 'info');

    const stmt = db.prepare(query);
    const customers = stmt.all(...queryParams);

    logSystem(`تم الحصول على ${customers.length} عميل من العملاء الأكثر شراءً`, 'info');

    // حساب إحصائيات إضافية
    let totalSales = 0;
    let totalProfit = 0;
    let totalTransactions = 0;
    let totalInvoices = 0;

    for (const customer of customers) {
      totalSales += customer.total_sales || 0;
      totalProfit += customer.total_profit || 0;
      totalTransactions += customer.transaction_count || 0;
      totalInvoices += customer.invoice_count || 0;

      // حساب نسبة الربح لكل عميل
      if (customer.total_sales > 0) {
        customer.profit_margin = (customer.total_profit / customer.total_sales) * 100;
      } else {
        customer.profit_margin = 0;
      }

      // الحصول على الأصناف الأكثر شراءً لكل عميل
      try {
        const topItemsStmt = db.prepare(`
          SELECT
            t.item_id,
            i.name as item_name,
            SUM(t.quantity) as total_quantity,
            SUM(t.total_price) as total_sales
          FROM transactions t
          JOIN items i ON t.item_id = i.id
          WHERE t.transaction_type = 'sale'
          AND t.customer_id = ?
          ${filters.startDate ? "AND t.transaction_date >= '" + filters.startDate + "'" : ''}
          ${filters.endDate ? "AND t.transaction_date <= '" + filters.endDate + "'" : ''}
          GROUP BY t.item_id
          ORDER BY total_quantity DESC
          LIMIT 3
        `);
        customer.top_items = topItemsStmt.all(customer.customer_id);
      } catch (error) {
        logError(error, `getTopCustomersReport - الحصول على الأصناف الأكثر شراءً للعميل ${customer.customer_id}`);
        customer.top_items = [];
      }
    }

    // حساب نسبة الربح الإجمالية
    let profitMargin = 0;
    if (totalSales > 0) {
      profitMargin = (totalProfit / totalSales) * 100;
    }

    const result = {
      customers,
      stats: {
        totalCustomers: customers.length,
        totalSales,
        totalProfit,
        totalTransactions,
        totalInvoices,
        profitMargin,
        averageSalePerCustomer: customers.length > 0 ? totalSales / customers.length : 0
      }
    };

    logSystem(`تم إكمال تقرير العملاء الأكثر شراءً بنجاح: ${customers.length} عميل، إجمالي المبيعات: ${totalSales}، إجمالي الربح: ${totalProfit}`, 'info');

    return result;
  } catch (error) {
    logError(error, 'getTopCustomersReport');
    logSystem(`خطأ في الحصول على تقرير العملاء الأكثر شراءً: ${error.message}`, 'error');

    // إرجاع هيكل بيانات فارغ مع رسالة الخطأ
    return {
      customers: [],
      stats: {
        totalCustomers: 0,
        totalSales: 0,
        totalProfit: 0,
        totalTransactions: 0,
        totalInvoices: 0,
        profitMargin: 0,
        averageSalePerCustomer: 0
      },
      error: error.message
    };
  }
}

/**
 * الحصول على تقرير الأصناف التي تحتاج إلى إعادة طلب
 * @param {Object} options - خيارات التقرير
 * @param {boolean} options.includeZeroQuantity - تضمين الأصناف ذات الكمية الصفرية
 * @returns {Object} - تقرير الأصناف التي تحتاج إلى إعادة طلب
 */
function getLowStockReport(options = {}) {
  try {
    logSystem(`بدء تنفيذ getLowStockReport مع الخيارات: ${JSON.stringify(options)}`, 'info');

    // التحقق من اتصال قاعدة البيانات
    if (!db) {
      logSystem('قاعدة البيانات غير مهيأة في getLowStockReport', 'error');
      throw new Error('قاعدة البيانات غير مهيأة');
    }

    // استعلام SQL للحصول على الأصناف التي تحتاج إلى إعادة طلب
    let query = `
      SELECT i.id, i.name, i.unit,
             inv.current_quantity, inv.minimum_quantity, inv.avg_price, inv.selling_price, inv.last_updated
      FROM items i
      JOIN inventory inv ON i.id = inv.item_id
      WHERE inv.current_quantity <= inv.minimum_quantity
    `;

    if (!options.includeZeroQuantity) {
      query += ' AND inv.current_quantity > 0';
    }

    query += ' ORDER BY (inv.minimum_quantity - inv.current_quantity) DESC';

    logSystem(`تنفيذ استعلام الأصناف التي تحتاج إلى إعادة طلب: ${query}`, 'info');

    const stmt = db.prepare(query);
    const items = stmt.all();

    logSystem(`تم الحصول على ${items.length} صنف من الأصناف التي تحتاج إلى إعادة طلب`, 'info');

    // حساب إحصائيات إضافية
    let totalItems = items.length;
    let totalValue = 0;
    let totalQuantity = 0;
    let zeroQuantityItems = 0;

    for (const item of items) {
      const currentQuantity = item.current_quantity || 0;
      const avgPrice = item.avg_price || 0;

      // حساب القيمة الإجمالية للمخزون
      const itemValue = currentQuantity * avgPrice;
      totalValue += itemValue;
      totalQuantity += currentQuantity;

      // حساب عدد الأصناف التي نفدت
      if (currentQuantity === 0) {
        zeroQuantityItems++;
      }

      // إضافة قيمة الصنف كحقل منفصل
      item.total_value = itemValue;

      // حساب الكمية المطلوبة
      item.quantity_to_order = Math.max(0, item.minimum_quantity - item.current_quantity);

      // الحصول على تاريخ آخر عملية شراء
      try {
        const lastPurchaseStmt = db.prepare(`
          SELECT transaction_date, price, quantity
          FROM transactions
          WHERE item_id = ? AND transaction_type = 'purchase'
          ORDER BY transaction_date DESC
          LIMIT 1
        `);
        item.last_purchase = lastPurchaseStmt.get(item.id) || null;
      } catch (error) {
        logError(error, `getLowStockReport - الحصول على تاريخ آخر عملية شراء للصنف ${item.id}`);
        item.last_purchase = null;
      }

      // الحصول على معدل الاستهلاك الشهري
      try {
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const thirtyDaysAgoStr = thirtyDaysAgo.toISOString();

        const monthlySalesStmt = db.prepare(`
          SELECT SUM(quantity) as total_quantity
          FROM transactions
          WHERE item_id = ? AND transaction_type = 'sale' AND transaction_date >= ?
        `);
        const monthlySales = monthlySalesStmt.get(item.id, thirtyDaysAgoStr);
        item.monthly_consumption = monthlySales ? monthlySales.total_quantity || 0 : 0;

        // حساب عدد الأيام المتبقية بناءً على معدل الاستهلاك
        if (item.monthly_consumption > 0) {
          // معدل الاستهلاك اليومي = الاستهلاك الشهري / 30
          const dailyConsumption = item.monthly_consumption / 30;
          // عدد الأيام المتبقية = الكمية الحالية / معدل الاستهلاك اليومي
          item.days_remaining = Math.floor(currentQuantity / dailyConsumption);
        } else {
          item.days_remaining = null; // لا يمكن حساب عدد الأيام المتبقية
        }
      } catch (error) {
        logError(error, `getLowStockReport - حساب معدل الاستهلاك الشهري للصنف ${item.id}`);
        item.monthly_consumption = 0;
        item.days_remaining = null;
      }
    }

    const result = {
      items,
      stats: {
        totalItems,
        totalValue,
        totalQuantity,
        zeroQuantityItems,
        lowStockItems: totalItems - zeroQuantityItems
      }
    };

    logSystem(`تم إكمال تقرير الأصناف التي تحتاج إلى إعادة طلب بنجاح: ${totalItems} صنف، ${zeroQuantityItems} صنف نفدت الكمية`, 'info');

    return result;
  } catch (error) {
    logError(error, 'getLowStockReport');
    logSystem(`خطأ في الحصول على تقرير الأصناف التي تحتاج إلى إعادة طلب: ${error.message}`, 'error');

    // إرجاع هيكل بيانات فارغ مع رسالة الخطأ
    return {
      items: [],
      stats: {
        totalItems: 0,
        totalValue: 0,
        totalQuantity: 0,
        zeroQuantityItems: 0,
        lowStockItems: 0
      },
      error: error.message
    };
  }
}

/**
 * الحصول على تقرير عمليات الإرجاع
 * @param {Object} filters - مرشحات البحث
 * @param {string} filters.startDate - تاريخ البداية
 * @param {string} filters.endDate - تاريخ النهاية
 * @param {number} filters.itemId - معرف الصنف
 * @param {number} filters.customerId - معرف العميل
 * @param {string} filters.invoiceNumber - رقم الفاتورة
 * @returns {Object} - تقرير عمليات الإرجاع
 */
function getReturnTransactionsReport(filters = {}) {
  try {
    logSystem('بدء تنفيذ getReturnTransactionsReport مع الفلاتر: ' + JSON.stringify(filters), 'info');

    // التحقق من اتصال قاعدة البيانات
    if (!db) {
      logSystem('قاعدة البيانات غير مهيأة في getReturnTransactionsReport', 'error');

      // محاولة إعادة الاتصال بقاعدة البيانات
      const dbManager = DatabaseManager.getInstance();
      if (dbManager) {
        db = dbManager.getConnection();
        if (!db) {
          throw new Error('قاعدة البيانات غير مهيأة ولا يمكن إعادة الاتصال');
        }
        logSystem('تم إعادة الاتصال بقاعدة البيانات في getReturnTransactionsReport', 'info');
      } else {
        throw new Error('قاعدة البيانات غير مهيأة ولا يمكن الحصول على مدير قاعدة البيانات');
      }
    }

    // استخدام مدير عمليات الإرجاع إذا كان متاحًا
    if (returnTransactionsManager && typeof returnTransactionsManager.getAllReturnTransactions === 'function') {
      try {
        logSystem('استخدام مدير عمليات الإرجاع للحصول على عمليات الإرجاع', 'info');
        const returnTransactions = returnTransactionsManager.getAllReturnTransactions(filters);

        // حساب الإحصائيات
        const stats = calculateReturnTransactionStats(returnTransactions);

        return {
          returnTransactions,
          stats
        };
      } catch (managerError) {
        logError(managerError, 'getReturnTransactionsReport - returnTransactionsManager');
        logSystem('فشل استخدام مدير عمليات الإرجاع، سيتم استخدام الاستعلام المباشر', 'warning');
        // استمر باستخدام الاستعلام المباشر
      }
    }

    // استعلام SQL للحصول على بيانات عمليات الإرجاع
    let query = `
      SELECT r.id, r.return_id, r.original_transaction_id, r.item_id, r.quantity, r.price,
             r.total_price, r.customer_id, r.invoice_number, r.notes, r.user_id, r.return_date,
             r.inventory_updated, i.name as item_name, i.unit as item_unit,
             c.name as customer_name, c.customer_type as customer_type,
             u.username as user_name
      FROM return_transactions r
      JOIN items i ON r.item_id = i.id
      LEFT JOIN customers c ON r.customer_id = c.id
      LEFT JOIN users u ON r.user_id = u.id
    `;

    const queryParams = [];
    const conditions = [];

    if (filters.startDate) {
      conditions.push('r.return_date >= ?');
      queryParams.push(filters.startDate);
    }

    if (filters.endDate) {
      conditions.push('r.return_date <= ?');
      queryParams.push(filters.endDate);
    }

    if (filters.itemId) {
      conditions.push('r.item_id = ?');
      queryParams.push(filters.itemId);
    }

    if (filters.customerId) {
      conditions.push('r.customer_id = ?');
      queryParams.push(filters.customerId);
    }

    if (filters.invoiceNumber) {
      conditions.push('r.invoice_number = ?');
      queryParams.push(filters.invoiceNumber);
    }

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`;
    }

    query += ' ORDER BY r.return_date DESC';

    logSystem(`تنفيذ استعلام عمليات الإرجاع: ${query}`, 'info');
    logSystem(`معلمات الاستعلام: ${JSON.stringify(queryParams)}`, 'info');

    // التحقق من وجود جدول عمليات الإرجاع
    try {
      const tableCheckStmt = db.prepare(`SELECT name FROM sqlite_master WHERE type='table' AND name='return_transactions'`);
      const tableExists = tableCheckStmt.get();

      if (!tableExists) {
        logSystem('جدول عمليات الإرجاع غير موجود، سيتم استخدام جدول المعاملات العامة', 'warning');

        // استخدام جدول المعاملات العامة كبديل
        return getFallbackReturnTransactionsReport(filters);
      }
    } catch (tableCheckError) {
      logError(tableCheckError, 'getReturnTransactionsReport - table check');
      logSystem('خطأ في التحقق من وجود جدول عمليات الإرجاع، سيتم استخدام جدول المعاملات العامة', 'warning');

      // استخدام جدول المعاملات العامة كبديل
      return getFallbackReturnTransactionsReport(filters);
    }

    const stmt = db.prepare(query);
    const returnTransactions = stmt.all(...queryParams);

    logSystem(`تم الحصول على ${returnTransactions.length} عملية إرجاع`, 'info');

    // حساب إحصائيات عمليات الإرجاع
    const stats = calculateReturnTransactionStats(returnTransactions);

    return {
      returnTransactions,
      stats
    };
  } catch (error) {
    logError(error, 'getReturnTransactionsReport');
    logSystem(`خطأ في الحصول على تقرير عمليات الإرجاع: ${error.message}`, 'error');

    // إرجاع هيكل بيانات فارغ مع رسالة الخطأ
    return {
      returnTransactions: [],
      stats: {
        totalReturns: 0,
        totalReturnAmount: 0,
        totalReturnQuantity: 0,
        averageReturnAmount: 0
      },
      error: error.message
    };
  }
}

/**
 * الحصول على تقرير عمليات الإرجاع من جدول المعاملات العامة (كبديل)
 * @param {Object} filters - مرشحات البحث
 * @returns {Object} - تقرير عمليات الإرجاع
 */
function getFallbackReturnTransactionsReport(filters = {}) {
  try {
    logSystem('استخدام جدول المعاملات العامة للحصول على عمليات الإرجاع', 'info');

    // استعلام SQL للحصول على بيانات عمليات الإرجاع من جدول المعاملات العامة
    let query = `
      SELECT t.id, t.transaction_id as return_id, NULL as original_transaction_id, t.item_id, t.quantity, t.price,
             t.total_price, t.customer_id, t.invoice_number, t.notes, t.user_id, t.transaction_date as return_date,
             t.skip_inventory_update as inventory_updated, i.name as item_name, i.unit as item_unit,
             c.name as customer_name, c.customer_type as customer_type,
             u.username as user_name
      FROM transactions t
      JOIN items i ON t.item_id = i.id
      LEFT JOIN customers c ON t.customer_id = c.id
      LEFT JOIN users u ON t.user_id = u.id
      WHERE t.transaction_type = 'return'
    `;

    const queryParams = [];
    const conditions = [];

    if (filters.startDate) {
      conditions.push('t.transaction_date >= ?');
      queryParams.push(filters.startDate);
    }

    if (filters.endDate) {
      conditions.push('t.transaction_date <= ?');
      queryParams.push(filters.endDate);
    }

    if (filters.itemId) {
      conditions.push('t.item_id = ?');
      queryParams.push(filters.itemId);
    }

    if (filters.customerId) {
      conditions.push('t.customer_id = ?');
      queryParams.push(filters.customerId);
    }

    if (filters.invoiceNumber) {
      conditions.push('t.invoice_number = ?');
      queryParams.push(filters.invoiceNumber);
    }

    if (conditions.length > 0) {
      query += ` AND ${conditions.join(' AND ')}`;
    }

    query += ' ORDER BY t.transaction_date DESC';

    logSystem(`تنفيذ استعلام عمليات الإرجاع من جدول المعاملات العامة: ${query}`, 'info');
    logSystem(`معلمات الاستعلام: ${JSON.stringify(queryParams)}`, 'info');

    const stmt = db.prepare(query);
    const returnTransactions = stmt.all(...queryParams);

    logSystem(`تم الحصول على ${returnTransactions.length} عملية إرجاع من جدول المعاملات العامة`, 'info');

    // حساب إحصائيات عمليات الإرجاع
    const stats = calculateReturnTransactionStats(returnTransactions);

    return {
      returnTransactions,
      stats
    };
  } catch (error) {
    logError(error, 'getFallbackReturnTransactionsReport');
    logSystem(`خطأ في الحصول على تقرير عمليات الإرجاع من جدول المعاملات العامة: ${error.message}`, 'error');

    // إرجاع هيكل بيانات فارغ مع رسالة الخطأ
    return {
      returnTransactions: [],
      stats: {
        totalReturns: 0,
        totalReturnAmount: 0,
        totalReturnQuantity: 0,
        averageReturnAmount: 0
      },
      error: error.message
    };
  }
}

/**
 * حساب إحصائيات عمليات الإرجاع
 * @param {Array} returnTransactions - قائمة عمليات الإرجاع
 * @returns {Object} - إحصائيات عمليات الإرجاع
 */
function calculateReturnTransactionStats(returnTransactions) {
  let totalReturns = returnTransactions.length;
  let totalReturnAmount = 0;
  let totalReturnQuantity = 0;

  for (const returnTransaction of returnTransactions) {
    totalReturnAmount += returnTransaction.total_price || 0;
    totalReturnQuantity += returnTransaction.quantity || 0;
  }

  return {
    totalReturns,
    totalReturnAmount,
    totalReturnQuantity,
    averageReturnAmount: totalReturns > 0 ? totalReturnAmount / totalReturns : 0
  };
}

// تصدير الوظائف
module.exports = {
  initialize,
  getInventoryReport,
  getTransactionsReport,
  getProfitsReport,
  getSubCustomersSalesReport,
  getCashboxReport,
  getTopSellingItemsReport,
  getTopCustomersReport,
  getLowStockReport,
  getReturnTransactionsReport
};
