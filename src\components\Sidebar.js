import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { FaHome, FaBoxes, FaWarehouse, FaChartBar, FaUsers, FaDatabase, FaSignOutAlt, FaMoon, FaSun, FaShoppingCart, FaMoneyBillWave, FaCog, FaUser, FaUserFriends } from 'react-icons/fa';

const Sidebar = ({ user, onLogout }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [darkMode, setDarkMode] = React.useState(false);

  const toggleDarkMode = () => {
    const newMode = !darkMode;
    setDarkMode(newMode);
    document.body.classList.toggle('dark-theme', newMode);
    localStorage.setItem('wms_dark_mode', JSON.stringify(newMode));
  };

  React.useEffect(() => {
    // Check if dark mode preference is stored
    const storedDarkMode = localStorage.getItem('wms_dark_mode');
    if (storedDarkMode) {
      const isDarkMode = JSON.parse(storedDarkMode);
      setDarkMode(isDarkMode);
      document.body.classList.toggle('dark-theme', isDarkMode);
    }
  }, []);

  const menuItems = [
    { path: '/', icon: <FaHome />, text: 'الرئيسية' },
    { path: '/items', icon: <FaBoxes />, text: 'الأصناف' },
    { path: '/inventory', icon: <FaWarehouse />, text: 'المخزون' },
    { path: '/purchases', icon: <FaShoppingCart />, text: 'المشتريات' },
    { path: '/customers', icon: <FaUserFriends />, text: 'العملاء' },
    { path: '/customer-sales', icon: <FaMoneyBillWave />, text: 'البيع للعملاء' },
    { path: '/reports', icon: <FaChartBar />, text: 'التقارير' },
    { path: '/users', icon: <FaUsers />, text: 'المستخدمين', adminOnly: true },
    { path: '/settings', icon: <FaCog />, text: 'الإعدادات' },
  ];

  const handleNavigation = (path) => {
    navigate(path);
  };

  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <div className="logo-container">
          <div className="logo">WMS</div>
        </div>
        <h3>نظام إدارة المخازن</h3>
      </div>

      <div className="sidebar-menu">
        {menuItems.map((item) => {
          // إذا كان العنصر مخصص للمسؤولين فقط وليس المستخدم مسؤولاً، نتخطى هذا العنصر
          if (item.adminOnly && user?.role !== 'admin') {
            return null;
          }

          return (
            <div
              key={item.path}
              className={`sidebar-menu-item ${location.pathname === item.path ? 'active' : ''}`}
              onClick={() => handleNavigation(item.path)}
            >
              <div className="menu-icon">{item.icon}</div>
              <span className="menu-text">{item.text}</span>
              {location.pathname === item.path && <div className="active-indicator"></div>}
            </div>
          );
        })}
      </div>

      <div className="sidebar-footer">
        <div className="sidebar-menu-item theme-toggle" onClick={toggleDarkMode}>
          <div className="menu-icon">
            {darkMode ? <FaSun /> : <FaMoon />}
          </div>
          <span className="menu-text">{darkMode ? 'الوضع الفاتح' : 'الوضع الداكن'}</span>
        </div>

        <div className="sidebar-menu-item logout" onClick={onLogout}>
          <div className="menu-icon">
            <FaSignOutAlt />
          </div>
          <span className="menu-text">تسجيل الخروج</span>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
