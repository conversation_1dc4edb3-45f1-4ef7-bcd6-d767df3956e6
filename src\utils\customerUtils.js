/**
 * وظائف مساعدة للتعامل مع بيانات العملاء
 */

/**
 * الحصول على اسم العميل من معرفه
 * @param {string|number} customerId - معرف العميل
 * @param {Array} customers - قائمة العملاء
 * @returns {string} - اسم العميل
 */
export const getCustomerNameById = (customerId, customers) => {
  if (!customerId || !customers || !Array.isArray(customers)) {
    return 'غير محدد';
  }

  // إذا كان معرف العميل رقمي أو نصي يبدأ بأرقام أو حروف معينة
  if (
    /^\d+$/.test(customerId) || // رقم فقط
    (typeof customerId === 'string' && (
      customerId.startsWith('SAL-') || // يبدأ بـ SAL-
      customerId.startsWith('PUR-') || // يبدأ بـ PUR-
      customerId.includes('-') // يحتوي على شرطة (قد يكون معرف)
    ))
  ) {
    // البحث عن العميل في قائمة العملاء
    const foundCustomer = customers.find(c => 
      c.id === customerId || 
      c._id === customerId || 
      c.id === Number(customerId) || 
      String(c.id) === customerId
    );
    
    if (foundCustomer) {
      return foundCustomer.name;
    }
  }
  
  // إذا لم يتم العثور على العميل، نعيد معرف العميل كما هو
  return customerId || 'غير محدد';
};
