/**
 * سكريبت الإصلاح السريع لنظام الخزينة
 * يطبق جميع الإصلاحات المطلوبة بشكل تلقائي
 */

const { logError, logSystem } = require('./error-handler');

/**
 * تطبيق الإصلاح السريع لنظام الخزينة
 */
async function quickCashboxFix() {
  console.log('🚀 بدء الإصلاح السريع لنظام الخزينة...');
  console.log('='.repeat(60));
  
  const results = {
    timestamp: new Date().toISOString(),
    steps: [],
    success: true,
    summary: {}
  };

  try {
    // الخطوة 1: التشخيص الأولي
    console.log('\n1️⃣ التشخيص الأولي...');
    try {
      const { diagnoseCashboxIssue } = require('./diagnose-cashbox-issue');
      const diagnosis = await diagnoseCashboxIssue();
      
      results.steps.push({
        step: 'التشخيص الأولي',
        success: true,
        issues: diagnosis.issues.length,
        warnings: diagnosis.warnings.length
      });
      
      console.log(`✅ التشخيص مكتمل - المشاكل: ${diagnosis.issues.length}, التحذيرات: ${diagnosis.warnings.length}`);
      
      if (diagnosis.issues.length > 0) {
        console.log('⚠️ تم العثور على مشاكل تحتاج إصلاح');
      }
      
    } catch (diagnosisError) {
      console.log('❌ فشل التشخيص الأولي:', diagnosisError.message);
      results.steps.push({
        step: 'التشخيص الأولي',
        success: false,
        error: diagnosisError.message
      });
    }

    // الخطوة 2: إصلاح هيكل قاعدة البيانات
    console.log('\n2️⃣ إصلاح هيكل قاعدة البيانات...');
    try {
      const { fixCashboxTransportColumn } = require('./fix-cashbox-transport-column');
      const fixResult = fixCashboxTransportColumn();
      
      if (fixResult.success) {
        console.log(`✅ تم إصلاح هيكل قاعدة البيانات - أعمدة مضافة: ${fixResult.columnsAdded}`);
        results.steps.push({
          step: 'إصلاح هيكل قاعدة البيانات',
          success: true,
          columnsAdded: fixResult.columnsAdded,
          hasTransportColumn: fixResult.hasTransportColumn
        });
      } else {
        console.log('❌ فشل إصلاح هيكل قاعدة البيانات:', fixResult.message);
        results.steps.push({
          step: 'إصلاح هيكل قاعدة البيانات',
          success: false,
          error: fixResult.message
        });
        results.success = false;
      }
      
    } catch (structureError) {
      console.log('❌ خطأ في إصلاح هيكل قاعدة البيانات:', structureError.message);
      results.steps.push({
        step: 'إصلاح هيكل قاعدة البيانات',
        success: false,
        error: structureError.message
      });
      results.success = false;
    }

    // الخطوة 3: اختبار النظام
    console.log('\n3️⃣ اختبار النظام...');
    try {
      const { testCashboxSystem } = require('./test-cashbox-system');
      const testResult = await testCashboxSystem();
      
      if (testResult.success) {
        console.log('✅ اختبار النظام نجح بالكامل');
        results.steps.push({
          step: 'اختبار النظام',
          success: true,
          details: testResult.details
        });
      } else {
        console.log('❌ فشل اختبار النظام:', testResult.message);
        results.steps.push({
          step: 'اختبار النظام',
          success: false,
          error: testResult.message
        });
        results.success = false;
      }
      
    } catch (testError) {
      console.log('❌ خطأ في اختبار النظام:', testError.message);
      results.steps.push({
        step: 'اختبار النظام',
        success: false,
        error: testError.message
      });
      results.success = false;
    }

    // الخطوة 4: اختبار محاكاة عملية شراء
    console.log('\n4️⃣ اختبار محاكاة عملية شراء...');
    try {
      const { simulatePurchaseTransaction } = require('./test-cashbox-system');
      const simulationResult = await simulatePurchaseTransaction();
      
      if (simulationResult && simulationResult.success) {
        console.log('✅ محاكاة عملية الشراء نجحت');
        results.steps.push({
          step: 'محاكاة عملية شراء',
          success: true
        });
      } else {
        console.log('❌ فشلت محاكاة عملية الشراء');
        results.steps.push({
          step: 'محاكاة عملية شراء',
          success: false,
          error: simulationResult ? simulationResult.error : 'نتيجة غير محددة'
        });
        // لا نعتبر هذا فشل كامل للإصلاح
      }
      
    } catch (simulationError) {
      console.log('❌ خطأ في محاكاة عملية الشراء:', simulationError.message);
      results.steps.push({
        step: 'محاكاة عملية شراء',
        success: false,
        error: simulationError.message
      });
    }

    // الخطوة 5: التشخيص النهائي
    console.log('\n5️⃣ التشخيص النهائي...');
    try {
      const { diagnoseCashboxIssue } = require('./diagnose-cashbox-issue');
      const finalDiagnosis = await diagnoseCashboxIssue();
      
      results.steps.push({
        step: 'التشخيص النهائي',
        success: true,
        issues: finalDiagnosis.issues.length,
        warnings: finalDiagnosis.warnings.length
      });
      
      console.log(`✅ التشخيص النهائي مكتمل - المشاكل: ${finalDiagnosis.issues.length}, التحذيرات: ${finalDiagnosis.warnings.length}`);
      
      if (finalDiagnosis.issues.length === 0) {
        console.log('🎉 لم يتم العثور على مشاكل حرجة!');
      } else {
        console.log('⚠️ لا تزال هناك مشاكل تحتاج انتباه');
        finalDiagnosis.issues.forEach((issue, index) => {
          console.log(`   ${index + 1}. ${issue}`);
        });
      }
      
    } catch (finalDiagnosisError) {
      console.log('❌ فشل التشخيص النهائي:', finalDiagnosisError.message);
      results.steps.push({
        step: 'التشخيص النهائي',
        success: false,
        error: finalDiagnosisError.message
      });
    }

    // إنشاء الملخص
    results.summary = {
      totalSteps: results.steps.length,
      successfulSteps: results.steps.filter(step => step.success).length,
      failedSteps: results.steps.filter(step => !step.success).length,
      overallSuccess: results.success
    };

    // عرض النتيجة النهائية
    console.log('\n' + '='.repeat(60));
    console.log('📋 ملخص الإصلاح السريع:');
    console.log('='.repeat(60));
    console.log(`⏰ وقت الإصلاح: ${results.timestamp}`);
    console.log(`📊 إجمالي الخطوات: ${results.summary.totalSteps}`);
    console.log(`✅ خطوات نجحت: ${results.summary.successfulSteps}`);
    console.log(`❌ خطوات فشلت: ${results.summary.failedSteps}`);
    console.log(`🎯 النتيجة الإجمالية: ${results.summary.overallSuccess ? 'نجح' : 'فشل'}`);

    if (results.summary.overallSuccess) {
      console.log('\n🎉 تم إصلاح نظام الخزينة بنجاح!');
      console.log('💡 يمكنك الآن تشغيل التطبيق واختبار عمليات الشراء');
      console.log('📝 تأكد من مراقبة سجلات النظام للتحقق من عمل الإصلاحات');
    } else {
      console.log('\n⚠️ الإصلاح لم يكتمل بنجاح');
      console.log('💡 راجع الأخطاء أعلاه وقم بالإصلاح اليدوي إذا لزم الأمر');
      console.log('📞 اتصل بالدعم التقني إذا استمرت المشاكل');
    }

    // حفظ تقرير الإصلاح
    const fs = require('fs');
    const reportPath = `quick-fix-report-${Date.now()}.json`;
    fs.writeFileSync(reportPath, JSON.stringify(results, null, 2));
    console.log(`\n💾 تم حفظ تقرير الإصلاح في: ${reportPath}`);

    return results;

  } catch (error) {
    console.error('❌ خطأ عام في الإصلاح السريع:', error);
    logError(error, 'quickCashboxFix');
    
    results.success = false;
    results.steps.push({
      step: 'خطأ عام',
      success: false,
      error: error.message
    });
    
    return results;
  }
}

/**
 * عرض تعليمات ما بعد الإصلاح
 */
function showPostFixInstructions() {
  console.log('\n📋 تعليمات ما بعد الإصلاح:');
  console.log('='.repeat(40));
  console.log('1. تشغيل التطبيق: npm start');
  console.log('2. الذهاب إلى قسم المشتريات');
  console.log('3. إجراء عملية شراء تجريبية');
  console.log('4. التحقق من تحديث الخزينة');
  console.log('5. مراجعة سجل المعاملات');
  console.log('6. مراقبة سجلات النظام');
  console.log('='.repeat(40));
}

module.exports = {
  quickCashboxFix,
  showPostFixInstructions
};

// تشغيل الإصلاح السريع إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  console.log('🚀 بدء الإصلاح السريع لنظام الخزينة...');
  
  quickCashboxFix().then(results => {
    console.log('\n🏁 انتهى الإصلاح السريع');
    
    if (results.summary.overallSuccess) {
      showPostFixInstructions();
    }
    
    process.exit(results.summary.overallSuccess ? 0 : 1);
  }).catch(error => {
    console.error('❌ خطأ في تشغيل الإصلاح السريع:', error);
    process.exit(1);
  });
}
