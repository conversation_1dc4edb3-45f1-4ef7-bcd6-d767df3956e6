/**
 * دالة مؤقتة لإصلاح الأرباح - يمكن نسخها ولصقها في وحدة التحكم
 */

// انسخ والصق هذا الكود في وحدة التحكم (F12)
async function fixProfitsNow() {
  try {
    console.log('🔧 بدء إصلاح الأرباح في الخزينة من المعاملات الفعلية...');

    // التحقق من وجود window.api
    if (!window.api || !window.api.executeSQL) {
      console.error('❌ window.api غير متاح. تأكد من أنك في صفحة التطبيق الصحيحة.');
      return;
    }

    // الحصول على إجمالي الأرباح من معاملات البيع
    const salesProfitResult = await window.api.executeSQL(`
      SELECT SUM(profit) as total_sales_profit
      FROM transactions 
      WHERE transaction_type = 'sale'
        AND profit IS NOT NULL
    `);

    const totalSalesProfit = salesProfitResult[0]?.total_sales_profit || 0;
    console.log(`💰 إجمالي أرباح المبيعات: ${totalSalesProfit}`);

    // الحصول على إجمالي الأرباح المفقودة من معاملات الإرجاع
    const returnsProfitResult = await window.api.executeSQL(`
      SELECT SUM(ABS(profit)) as total_returns_profit
      FROM transactions 
      WHERE transaction_type = 'return'
        AND profit IS NOT NULL
    `);

    const totalReturnsProfit = returnsProfitResult[0]?.total_returns_profit || 0;
    console.log(`📉 إجمالي أرباح الإرجاعات: ${totalReturnsProfit}`);

    // حساب صافي الأرباح
    const netProfit = totalSalesProfit - totalReturnsProfit;
    console.log(`📊 صافي الأرباح: ${netProfit}`);

    // الحصول على الخزينة الحالية
    const cashboxResult = await window.api.executeSQL('SELECT * FROM cashbox LIMIT 1');
    const currentCashbox = cashboxResult[0];

    if (!currentCashbox) {
      console.error('❌ لم يتم العثور على خزينة في قاعدة البيانات');
      return;
    }

    const oldProfit = currentCashbox.profit_total || 0;
    console.log(`🏦 الأرباح الحالية في الخزينة: ${oldProfit}`);
    console.log(`🔄 الأرباح الجديدة المحسوبة: ${netProfit}`);
    console.log(`📈 الفرق: ${netProfit - oldProfit}`);

    // تحديث الخزينة بالأرباح الصحيحة
    await window.api.executeSQL(`
      UPDATE cashbox 
      SET profit_total = ?,
          updated_at = ?
      WHERE id = ?
    `, [netProfit, new Date().toISOString(), currentCashbox.id]);

    console.log('✅ تم تحديث الأرباح في الخزينة بنجاح');
    console.log(`🎯 النتيجة النهائية: ${netProfit} د.ل`);
    console.log('💡 يرجى تحديث الصفحة لرؤية التغييرات');

    // إعادة تحميل الصفحة تلقائياً
    setTimeout(() => {
      window.location.reload();
    }, 2000);

    return {
      success: true,
      oldProfit,
      newProfit: netProfit,
      difference: netProfit - oldProfit
    };

  } catch (error) {
    console.error('❌ خطأ في إصلاح الأرباح:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// تشغيل الدالة
fixProfitsNow();
