.profit-test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.page-header h1 {
  margin: 0 0 10px 0;
  font-size: 2.5rem;
}

.page-header p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.test-sections {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.test-section {
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  border: 1px solid #e1e8ed;
}

.test-section h3 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 1.3rem;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.test-button {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  margin: 5px;
  min-width: 150px;
}

.test-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.test-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.test-button.success {
  background: linear-gradient(135deg, #27ae60, #229954);
}

.test-button.success:hover {
  box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

/* بيانات الخزينة */
.cashbox-data {
  margin-top: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
}

.data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.data-item:last-child {
  border-bottom: none;
}

.data-item.highlight {
  background: #e8f5e8;
  margin: 5px -15px;
  padding: 12px 15px;
  border-radius: 6px;
  font-weight: 600;
  color: #27ae60;
}

.data-item span:first-child {
  font-weight: 500;
  color: #495057;
}

.data-item span:last-child {
  font-weight: 600;
  color: #2c3e50;
  font-family: 'Courier New', monospace;
}

/* بيانات المعاملات */
.sales-data {
  margin-top: 15px;
  max-height: 300px;
  overflow-y: auto;
}

.transaction-item {
  display: grid;
  grid-template-columns: 30px 1fr 1fr 1fr;
  gap: 10px;
  align-items: center;
  padding: 10px;
  margin: 5px 0;
  border-radius: 6px;
  font-size: 14px;
}

.transaction-item.correct {
  background: #e8f5e8;
  border: 1px solid #c3e6cb;
}

.transaction-item.incorrect {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
}

/* نتائج الإصلاح */
.fix-results {
  margin-top: 15px;
  background: #e8f5e8;
  border-radius: 8px;
  padding: 15px;
  border: 1px solid #c3e6cb;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #c3e6cb;
}

.result-item:last-child {
  border-bottom: none;
}

.result-item.success {
  font-weight: 600;
  color: #155724;
  font-size: 16px;
}

/* السجل */
.logs-container {
  margin-top: 15px;
  background: #2c3e50;
  color: #ecf0f1;
  border-radius: 8px;
  padding: 15px;
  max-height: 300px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
}

.log-item {
  margin: 5px 0;
  padding: 5px 0;
  border-bottom: 1px solid #34495e;
}

.log-item:last-child {
  border-bottom: none;
}

.log-item.success {
  color: #2ecc71;
}

.log-item.error {
  color: #e74c3c;
}

.log-item.info {
  color: #3498db;
}

.log-time {
  color: #95a5a6;
  margin-left: 10px;
}

.log-message {
  color: inherit;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .test-sections {
    grid-template-columns: 1fr;
  }
  
  .page-header h1 {
    font-size: 2rem;
  }
  
  .transaction-item {
    grid-template-columns: 1fr;
    gap: 5px;
    text-align: center;
  }
  
  .button-group {
    flex-direction: column;
  }
  
  .test-button {
    width: 100%;
  }
}

/* تحسينات للوضع المظلم */
@media (prefers-color-scheme: dark) {
  .test-section {
    background: #2c3e50;
    color: #ecf0f1;
    border-color: #34495e;
  }
  
  .test-section h3 {
    color: #ecf0f1;
    border-color: #3498db;
  }
  
  .cashbox-data {
    background: #34495e;
  }
  
  .data-item {
    border-color: #4a5f7a;
  }
  
  .data-item span:first-child {
    color: #bdc3c7;
  }
  
  .data-item span:last-child {
    color: #ecf0f1;
  }
}
