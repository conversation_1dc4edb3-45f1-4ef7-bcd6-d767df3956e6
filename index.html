<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; font-src 'self'; img-src 'self' data:;">
  <title>Hgorp - نظام إدارة المخازن</title>
  <!-- استخدام الخطوط المحلية بدلاً من Google Fonts -->
  <link rel="stylesheet" href="./assets/css/fonts.css">
  <link rel="stylesheet" href="./assets/css/styles.css">
  <script>
    // تعريف React و ReactDOM قبل تحميل bundle.js
    window.React = {
      createElement: function() { return {}; },
      useState: function() { return [null, function() {}]; },
      useEffect: function() {},
      useRef: function() { return { current: null }; },
      createContext: function() { return { Provider: function() {}, Consumer: function() {} }; },
      Fragment: 'div',
      __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED: {
        ReactCurrentDispatcher: { current: {} },
        ReactCurrentOwner: { current: {} }
      }
    };

    window.ReactDOM = {
      render: function() {},
      createPortal: function() {},
      findDOMNode: function() {},
      createRoot: function() {
        return {
          render: function() {}
        };
      },
      __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED: {
        Events: []
      }
    };

    // تعريف Scheduler
    window.Scheduler = {
      unstable_now: function() { return Date.now(); },
      unstable_scheduleCallback: function(priority, callback) {
        setTimeout(callback, 0);
        return {};
      },
      unstable_cancelCallback: function() {}
    };
  </script>
</head>
<body>
  <div id="root">
    <h1 style="text-align: center; margin-top: 50px;">جاري تحميل نظام إدارة المخازن...</h1>
  </div>

  <script>
    // تعريف global لحل مشكلة "global is not defined"
    window.global = window;

    // تعريف process لحل مشكلة "process is not defined"
    window.process = {
      env: { NODE_ENV: 'development' },
      browser: true,
      nextTick: function(callback) {
        setTimeout(callback, 0);
      }
    };

    // تعريف Buffer لحل مشكلة "Buffer is not defined"
    window.Buffer = {
      isBuffer: function() { return false; },
      from: function(data, encoding) {
        if (encoding === 'base64') {
          return { toString: function() { return atob(data); } };
        }
        return { toString: function() { return String(data); } };
      },
      alloc: function(size) {
        return {
          length: size,
          toString: function() { return ''; }
        };
      }
    };

    // تعريف require لحل مشكلة "require is not defined"
    window.require = function(moduleName) {
      console.log(`محاولة استدعاء require('${moduleName}')`);

      // إرجاع كائنات وهمية للمكتبات الشائعة
      const mockModules = {
        'react': window.React,
        'react-dom': window.ReactDOM,
        'scheduler': window.Scheduler
      };

      return mockModules[moduleName] || {};
    };

    // التحقق من وجود window.api
    document.addEventListener('DOMContentLoaded', function() {
      console.log('تم تحميل المستند بنجاح');

      // التحقق من وجود window.api
      setTimeout(function() {
        if (window.api) {
          console.log('window.api متوفر بنجاح');
          console.log('وظائف window.api المتوفرة:', Object.keys(window.api));

          // مزامنة المخزون مرة واحدة عند بدء التشغيل
          if (window.api.inventory && window.api.inventory.sync) {
            console.log('جاري مزامنة المخزون عند بدء التشغيل...');
            window.api.inventory.sync()
              .then(result => {
                console.log('نتيجة مزامنة المخزون:', result);
              })
              .catch(error => {
                console.error('خطأ في مزامنة المخزون:', error);
              });
          }
        } else {
          console.error('window.api غير متوفر! تأكد من تحميل preload.js بشكل صحيح');
        }
      }, 1000);
    });
  </script>

  <script src="./bundle.js"></script>

  <!-- إضافة سكريبت نظام الأحداث -->
  <script src="./src/renderer/event-listeners.js"></script>

  <script>
    // تحميل سكريبت اختبار window.api بعد تحميل الصفحة
    window.addEventListener('load', function() {
      console.log('تم تحميل الصفحة بالكامل');
      console.log('تم تعطيل التحقق من window.api بعد تحميل الصفحة بالكامل');

      // إعداد مستمعي الأحداث
      if (window.eventListeners && window.eventListeners.setupEventListeners) {
        window.eventListeners.setupEventListeners();
      }
    });
  </script>
</body>
</html>
