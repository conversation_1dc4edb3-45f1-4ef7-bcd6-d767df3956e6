import React, { useState, useEffect, useRef } from 'react';
import {
  FaUndo, FaPlus, FaTrash, FaSearch, FaBoxOpen,
  FaShoppingCart, FaMoneyBillWave, FaCalendarAlt,
  FaReceipt, FaExclamationTriangle, FaInfoCircle,
  FaCheck, FaTimes
} from 'react-icons/fa';
import { useApp } from '../context/AppContext';
import ModernItemAutocomplete from './ModernItemAutocomplete';
import '../assets/css/enhanced-return-form.css';

/**
 * مكون نموذج الإرجاع المحسن
 * يستخدم نظام الإرجاع الجديد مع واجهة مستخدم محسنة
 */
const EnhancedReturnForm = ({ customer, onClose, onSuccess }) => {
  const { items, inventory } = useApp();

  // حالة النموذج
  const [returnItems, setReturnItems] = useState([]);
  const [currentItem, setCurrentItem] = useState({
    item_id: '',
    item_name: '',
    quantity: 1,
    price: 0,
    availableForReturn: 0,
    totalSold: 0,
    totalReturned: 0
  });
  const [invoiceData, setInvoiceData] = useState({
    invoice_number: `RET-${Date.now().toString().slice(-6)}`,
    transaction_date: new Date().toISOString().split('T')[0],
    notes: ''
  });
  const [itemSearchTerm, setItemSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [availableItems, setAvailableItems] = useState([]);
  const [isFixingSystem, setIsFixingSystem] = useState(false);

  // مراجع للنموذج
  const formRef = useRef(null);
  const alertTimeoutRef = useRef(null);

  // تحميل الأصناف المتاحة للإرجاع عند تحميل المكون
  useEffect(() => {
    const loadAvailableItems = async () => {
      if (!customer || !customer.id) return;

      try {
        setLoading(true);
        setError(null);

        // استدعاء API للحصول على الأصناف المتاحة للإرجاع
        const result = await window.api.returns.getAvailableItems(customer.id);

        if (result && result.success && Array.isArray(result.items)) {
          setAvailableItems(result.items);
        } else {
          console.warn('لم يتم العثور على أصناف متاحة للإرجاع:', result);
          setAvailableItems([]);
        }
      } catch (err) {
        console.error('خطأ في تحميل الأصناف المتاحة للإرجاع:', err);
        setError('فشل في تحميل الأصناف المتاحة للإرجاع');
        setAvailableItems([]);
      } finally {
        setLoading(false);
      }
    };

    loadAvailableItems();

    // تنظيف المؤقتات عند إلغاء تحميل المكون
    return () => {
      if (alertTimeoutRef.current) {
        clearTimeout(alertTimeoutRef.current);
      }
    };
  }, [customer]);

  // عرض تنبيه
  const showAlert = (type, message, duration = 5000) => {
    if (type === 'success') {
      setSuccess(message);
      setError(null);
    } else {
      setError(message);
      setSuccess(null);
    }

    // إزالة التنبيه بعد فترة
    if (alertTimeoutRef.current) {
      clearTimeout(alertTimeoutRef.current);
    }

    alertTimeoutRef.current = setTimeout(() => {
      setSuccess(null);
      setError(null);
    }, duration);
  };

  // معالجة اختيار صنف للإرجاع
  const handleItemSelect = async (item) => {
    if (!item || !item.id) {
      showAlert('error', 'الرجاء اختيار صنف صالح');
      return;
    }

    try {
      // الحصول على معرف العميل
      const customerId = parseInt(customer.id || customer._id);

      // الحصول على الكمية المتاحة للإرجاع
      const result = await window.api.returns.getAvailableQuantity(
        parseInt(item.id),
        customerId
      );

      if (!result || !result.success) {
        showAlert('error', result?.error || 'فشل في الحصول على الكمية المتاحة للإرجاع');
        return;
      }

      const availableQuantity = result.availableQuantity || 0;

      if (availableQuantity <= 0) {
        showAlert('error', `لا توجد كمية متاحة للإرجاع من الصنف "${item.name}"`);
        return;
      }

      // الحصول على سعر البيع من المخزون
      const inventoryItem = inventory.find(inv =>
        inv.item_id === item.id ||
        inv.item_id === parseInt(item.id)
      );

      const sellingPrice = inventoryItem ? inventoryItem.selling_price : 0;

      // تعيين الصنف المحدد
      setCurrentItem({
        item_id: item.id,
        item_name: item.name,
        quantity: 1,
        price: sellingPrice,
        availableForReturn: availableQuantity
      });

      setItemSearchTerm(item.name);
    } catch (err) {
      console.error('خطأ في معالجة اختيار الصنف:', err);
      showAlert('error', 'حدث خطأ في معالجة اختيار الصنف');
    }
  };

  // معالجة تغيير قيم الصنف
  const handleItemInputChange = (e) => {
    const { name, value } = e.target;

    if (name === 'quantity') {
      // تحويل القيمة إلى رقم
      const newQuantity = parseInt(value) || 1;

      // التحقق من الكمية المتاحة
      if (newQuantity > currentItem.availableForReturn) {
        showAlert('error', `الكمية المطلوبة (${newQuantity}) تتجاوز الكمية المتاحة للإرجاع (${currentItem.availableForReturn})`);
        return;
      }

      setCurrentItem(prev => ({
        ...prev,
        quantity: newQuantity
      }));
    } else {
      setCurrentItem(prev => ({
        ...prev,
        [name]: name === 'price' ? (parseFloat(value) || 0) : value
      }));
    }
  };

  // معالجة تغيير بيانات الفاتورة
  const handleInvoiceInputChange = (e) => {
    const { name, value } = e.target;
    setInvoiceData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // إضافة صنف إلى قائمة الإرجاع
  const handleAddItem = () => {
    if (!currentItem.item_id) {
      showAlert('error', 'الرجاء اختيار صنف للإرجاع');
      return;
    }

    if (currentItem.quantity <= 0) {
      showAlert('error', 'يجب أن تكون الكمية أكبر من صفر');
      return;
    }

    // التحقق من وجود الصنف في القائمة
    const existingIndex = returnItems.findIndex(item => item.item_id === currentItem.item_id);

    if (existingIndex !== -1) {
      // تحديث الصنف الموجود
      const updatedItems = [...returnItems];
      const newQuantity = updatedItems[existingIndex].quantity + currentItem.quantity;

      // التحقق من الكمية المتاحة
      if (newQuantity > currentItem.availableForReturn) {
        showAlert('error', `إجمالي الكمية المطلوبة (${newQuantity}) تتجاوز الكمية المتاحة للإرجاع (${currentItem.availableForReturn})`);
        return;
      }

      updatedItems[existingIndex] = {
        ...updatedItems[existingIndex],
        quantity: newQuantity,
        price: currentItem.price,
        total: newQuantity * currentItem.price
      };

      setReturnItems(updatedItems);
    } else {
      // إضافة صنف جديد
      setReturnItems([
        ...returnItems,
        {
          ...currentItem,
          total: currentItem.quantity * currentItem.price
        }
      ]);
    }

    // إعادة تعيين الصنف الحالي
    setCurrentItem({
      item_id: '',
      item_name: '',
      quantity: 1,
      price: 0,
      availableForReturn: 0
    });

    setItemSearchTerm('');
    showAlert('success', `تمت إضافة الصنف "${currentItem.item_name}" إلى قائمة الإرجاع`);
  };

  // حذف صنف من قائمة الإرجاع
  const handleRemoveItem = (index) => {
    const updatedItems = [...returnItems];
    updatedItems.splice(index, 1);
    setReturnItems(updatedItems);
  };

  // حساب إجمالي الإرجاع
  const calculateTotal = () => {
    return returnItems.reduce((total, item) => total + (item.quantity * item.price), 0);
  };

  // إرسال نموذج الإرجاع
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      if (!customer || !customer.id) {
        showAlert('error', 'الرجاء اختيار عميل صالح للإرجاع');
        return;
      }

      if (returnItems.length === 0) {
        showAlert('error', 'يرجى إضافة صنف واحد على الأقل للإرجاع');
        return;
      }

      setLoading(true);
      setError(null);

      // إنشاء عمليات الإرجاع
      const returnResults = [];

      for (const item of returnItems) {
        // إنشاء عملية إرجاع جديدة باستخدام النظام الجديد
        const returnData = {
          item_id: parseInt(item.item_id),
          quantity: item.quantity,
          price: item.price,
          total_price: item.quantity * item.price,
          customer_id: parseInt(customer.id || customer._id),
          invoice_number: invoiceData.invoice_number,
          notes: invoiceData.notes,
          return_date: invoiceData.transaction_date ? new Date(invoiceData.transaction_date).toISOString() : new Date().toISOString()
        };

        // استدعاء API لإنشاء عملية إرجاع
        const result = await window.api.returns.create(returnData);

        if (!result || !result.success) {
          throw new Error(result?.error || `فشل في إنشاء عملية إرجاع للصنف "${item.item_name}"`);
        }

        returnResults.push(result);
      }

      // إذا تم إنشاء جميع عمليات الإرجاع بنجاح
      setSuccess('تم إتمام عملية الإرجاع بنجاح');

      // مسح التخزين المؤقت للمخزون
      try {
        console.log('مسح التخزين المؤقت للمخزون بعد عملية الإرجاع');
        await window.api.inventory.clearCache();
        console.log('تم مسح التخزين المؤقت للمخزون بنجاح');
      } catch (cacheError) {
        console.warn('تحذير: فشل في مسح التخزين المؤقت للمخزون:', cacheError);
        // لا نريد إيقاف العملية إذا فشل مسح التخزين المؤقت
      }

      // تحديث واجهة المستخدم
      try {
        console.log('تحديث واجهة المستخدم بعد عملية الإرجاع');

        // إرسال إشعارات تحديث لجميع الأصناف المرتجعة
        for (const returnResult of returnResults) {
          if (returnResult && returnResult.item_id) {
            console.log(`تحديث معلومات الصنف ${returnResult.item_id} في واجهة المستخدم`);

            // إرسال إشعار بتحديث المخزون
            await window.api.invoke('notify-inventory-updated', {
              itemId: returnResult.item_id,
              operation: 'return',
              quantity: returnResult.quantity
            });
            console.log(`تم إرسال إشعار بتحديث المخزون للصنف ${returnResult.item_id}`);
          }
        }

        // إرسال إشعارات بالحاجة للتحديث لجميع الجداول المتأثرة
        try {
          console.log('إرسال إشعار بتحديث المخزون');
          await window.api.invoke('refresh-needed', { target: 'inventory' });
          console.log('تم إرسال إشعار بتحديث المخزون');

          console.log('إرسال إشعار بتحديث المعاملات');
          await window.api.invoke('refresh-needed', { target: 'transactions' });
          console.log('تم إرسال إشعار بتحديث المعاملات');

          console.log('إرسال إشعار بتحديث العملاء');
          await window.api.invoke('refresh-needed', { target: 'customers' });
          console.log('تم إرسال إشعار بتحديث العملاء');
        } catch (refreshEventError) {
          console.error('خطأ في إرسال إشعارات التحديث:', refreshEventError);
        }

        // تحديث المخزون في الذاكرة
        try {
          console.log('تحديث المخزون في الذاكرة');
          if (window.api.inventory && typeof window.api.inventory.clearCache === 'function') {
            await window.api.inventory.clearCache();
            console.log('تم مسح التخزين المؤقت للمخزون بنجاح');
          }
        } catch (inventoryRefreshError) {
          console.warn('تحذير: فشل في مسح التخزين المؤقت للمخزون:', inventoryRefreshError);
        }

        // إظهار إشعار بنجاح عملية الإرجاع
        if (window.api.notifications && window.api.notifications.show) {
          window.api.notifications.show('تم إرجاع البضاعة بنجاح. تم تحديث المخزون.', 'success', 3000);
        }

        // استدعاء دالة النجاح
        if (typeof onSuccess === 'function') {
          onSuccess(returnResults);
        }

        // إغلاق النموذج
        onClose();

        return; // الخروج من الدالة لتجنب تنفيذ الكود التالي
      } catch (refreshError) {
        console.warn('تحذير: فشل في تحديث واجهة المستخدم:', refreshError);
        // لا نريد إيقاف العملية إذا فشل التحديث
      }

      // إذا وصلنا إلى هنا، فهذا يعني أن الكود السابق لم ينفذ بنجاح
      // لذلك نقوم باستدعاء دالة النجاح وإغلاق النموذج هنا كخطة بديلة
      console.log('استخدام الطريقة البديلة لإنهاء عملية الإرجاع');

      // استدعاء دالة النجاح
      if (typeof onSuccess === 'function') {
        onSuccess(returnResults);
      }

      // إغلاق النموذج بعد فترة
      setTimeout(() => {
        onClose();
      }, 2000);
    } catch (err) {
      console.error('خطأ في معالجة عملية الإرجاع:', err);
      setError(err.message || 'فشل في إتمام عملية الإرجاع');
    } finally {
      setLoading(false);
    }
  };

  // إصلاح نظام الاسترجاع
  const handleFixRetrievalSystem = async () => {
    try {
      setIsFixingSystem(true);
      setError(null);
      setSuccess(null);

      // استدعاء API لإصلاح نظام الاسترجاع
      const result = await window.api.returns.repair();

      if (result && result.success) {
        setSuccess(`تم إصلاح نظام الاسترجاع بنجاح: ${result.message}`);

        // تحديث واجهة المستخدم
        try {
          await window.api.invoke('refresh-needed', { target: 'all' });
          console.log('تم إرسال إشعار بتحديث جميع البيانات');
        } catch (refreshError) {
          console.warn('تحذير: فشل في إرسال إشعار التحديث:', refreshError);
        }
      } else {
        setError(`فشل في إصلاح نظام الاسترجاع: ${result?.message || 'سبب غير معروف'}`);
      }
    } catch (err) {
      console.error('خطأ في إصلاح نظام الاسترجاع:', err);
      setError(`خطأ في إصلاح نظام الاسترجاع: ${err.message || 'سبب غير معروف'}`);
    } finally {
      setIsFixingSystem(false);
    }
  };

  return (
    <div className="enhanced-return-form">
      <div className="form-header">
        <h3><FaUndo /> نموذج إرجاع بضاعة</h3>
        <div className="customer-info">
          <span className="label">العميل:</span>
          <span className="value">{customer?.name || 'غير محدد'}</span>
        </div>
      </div>

      {error && (
        <div className="alert alert-danger">
          <FaExclamationTriangle /> {error}
        </div>
      )}

      {success && (
        <div className="alert alert-success">
          <FaCheck /> {success}
        </div>
      )}

      <form ref={formRef} onSubmit={handleSubmit}>
        <div className="form-section">
          <h4>بيانات الفاتورة</h4>
          <div className="form-row">
            <div className="form-group">
              <label><FaReceipt /> رقم الفاتورة</label>
              <input
                type="text"
                name="invoice_number"
                value={invoiceData.invoice_number}
                onChange={handleInvoiceInputChange}
                required
              />
            </div>
            <div className="form-group">
              <label><FaCalendarAlt /> تاريخ الإرجاع</label>
              <input
                type="date"
                name="transaction_date"
                value={invoiceData.transaction_date}
                onChange={handleInvoiceInputChange}
                required
              />
            </div>
          </div>
          <div className="form-group">
            <label>ملاحظات</label>
            <textarea
              name="notes"
              value={invoiceData.notes}
              onChange={handleInvoiceInputChange}
              placeholder="ملاحظات إضافية حول عملية الإرجاع"
              rows="2"
            ></textarea>
          </div>
        </div>

        <div className="form-section">
          <h4>إضافة صنف للإرجاع</h4>
          <div className="item-selection">
            <div className="form-group item-search">
              <label><FaSearch /> البحث عن صنف</label>
              <ModernItemAutocomplete
                items={items}
                value={itemSearchTerm}
                onChange={setItemSearchTerm}
                onSelect={handleItemSelect}
                placeholder="اكتب اسم الصنف للبحث..."
              />
            </div>

            {currentItem.item_id && (
              <div className="selected-item-details">
                <div className="form-row">
                  <div className="form-group">
                    <label>الصنف</label>
                    <input type="text" value={currentItem.item_name} readOnly />
                  </div>
                  <div className="form-group">
                    <label>الكمية المتاحة للإرجاع</label>
                    <input type="text" value={currentItem.availableForReturn} readOnly />
                  </div>
                </div>
                <div className="form-row">
                  <div className="form-group">
                    <label>الكمية</label>
                    <input
                      type="number"
                      name="quantity"
                      value={currentItem.quantity}
                      onChange={handleItemInputChange}
                      min="1"
                      max={currentItem.availableForReturn}
                      required
                    />
                  </div>
                  <div className="form-group">
                    <label>السعر</label>
                    <input
                      type="number"
                      name="price"
                      value={currentItem.price}
                      onChange={handleItemInputChange}
                      min="0.01"
                      step="0.01"
                      required
                    />
                  </div>
                  <div className="form-group">
                    <label>الإجمالي</label>
                    <input
                      type="text"
                      value={(currentItem.quantity * currentItem.price).toFixed(2)}
                      readOnly
                    />
                  </div>
                </div>
                <button
                  type="button"
                  className="btn btn-primary add-item-btn"
                  onClick={handleAddItem}
                >
                  <FaPlus /> إضافة الصنف
                </button>
              </div>
            )}
          </div>
        </div>

        {returnItems.length > 0 && (
          <div className="form-section">
            <h4>الأصناف المضافة للإرجاع</h4>
            <div className="return-items-table">
              <table>
                <thead>
                  <tr>
                    <th>#</th>
                    <th>الصنف</th>
                    <th>الكمية</th>
                    <th>السعر</th>
                    <th>الإجمالي</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {returnItems.map((item, index) => (
                    <tr key={`return-item-${index}`}>
                      <td>{index + 1}</td>
                      <td>{item.item_name}</td>
                      <td>{item.quantity}</td>
                      <td>{item.price.toFixed(2)}</td>
                      <td>{item.total.toFixed(2)}</td>
                      <td>
                        <button
                          type="button"
                          className="btn btn-danger btn-sm"
                          onClick={() => handleRemoveItem(index)}
                        >
                          <FaTrash />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr>
                    <td colSpan="4" className="text-left">الإجمالي</td>
                    <td colSpan="2">{calculateTotal().toFixed(2)}</td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
        )}

        <div className="form-actions">
          <button
            type="submit"
            className="btn btn-success"
            disabled={loading || returnItems.length === 0}
          >
            {loading ? 'جاري المعالجة...' : 'تأكيد الإرجاع'}
          </button>
          <button
            type="button"
            className="btn btn-secondary"
            onClick={onClose}
            disabled={loading}
          >
            إلغاء
          </button>
        </div>
      </form>
    </div>
  );
};

export default EnhancedReturnForm;
