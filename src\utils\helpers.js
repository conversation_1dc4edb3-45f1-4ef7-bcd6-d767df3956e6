// Helper functions for the application

// Format date to locale string (بالتنسيق الميلادي)
export const formatDate = (dateString) => {
  if (!dateString) return '-';

  const date = new Date(dateString);
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();

  return `${year}/${month}/${day}`;
};

// Format date and time (بالتنسيق الميلادي)
export const formatDateTime = (dateString) => {
  if (!dateString) return '-';

  const date = new Date(dateString);
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');

  return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
};

// Format currency
export const formatCurrency = (amount) => {
  if (amount === undefined || amount === null) return '-';

  // تنسيق بدون فواصل
  return amount.toFixed(2) + ' د ل';
};

// Format number with commas
export const formatNumber = (number) => {
  if (number === undefined || number === null) return '-';

  // تنسيق بدون فواصل
  return number.toString();
};

// Generate a random ID
export const generateId = () => {
  return Math.random().toString(36).substring(2, 15) +
         Math.random().toString(36).substring(2, 15);
};

// Truncate text with ellipsis
export const truncateText = (text, maxLength = 50) => {
  if (!text) return '';
  if (text.length <= maxLength) return text;

  return text.substring(0, maxLength) + '...';
};

// Get status class based on inventory level
export const getStatusClass = (currentQuantity, minimumQuantity) => {
  if (currentQuantity === 0) {
    return 'badge-danger';
  } else if (currentQuantity <= minimumQuantity) {
    return 'badge-warning';
  } else {
    return 'badge-secondary';
  }
};

// Get status text based on inventory level
export const getStatusText = (currentQuantity, minimumQuantity) => {
  if (currentQuantity === 0) {
    return 'نفذت الكمية';
  } else if (currentQuantity <= minimumQuantity) {
    return 'تحت الحد الأدنى';
  } else {
    return 'متوفر';
  }
};

// Get transaction type text
export const getTransactionTypeText = (type) => {
  switch (type) {
    case 'receiving':
      return 'استلام';
    case 'withdrawal':
      return 'صرف';
    default:
      return type;
  }
};

// Get user role text
export const getUserRoleText = (role) => {
  switch (role) {
    case 'admin':
      return 'مدير';
    case 'user':
      return 'مستخدم';
    default:
      return role;
  }
};

// Debounce function for search inputs
export const debounce = (func, delay = 300) => {
  let timer;
  return function(...args) {
    clearTimeout(timer);
    timer = setTimeout(() => {
      func.apply(this, args);
    }, delay);
  };
};
