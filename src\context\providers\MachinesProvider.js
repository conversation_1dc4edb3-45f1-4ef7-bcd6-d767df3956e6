import React, { createContext, useState, useEffect, useContext } from 'react';
import * as database from '../../utils/database';
import { NotificationsContext } from './NotificationsProvider';

// إنشاء سياق الآلات
export const MachinesContext = createContext();

/**
 * مزود سياق الآلات
 * 
 * يوفر هذا المكون وظائف إدارة الآلات والمعدات (للزكاة)
 * 
 * @param {Object} props - خصائص المكون
 * @param {React.ReactNode} props.children - المكونات الفرعية
 * @returns {React.ReactElement} مزود سياق الآلات
 */
export const MachinesProvider = ({ children }) => {
  // استخدام سياق الإشعارات
  const { showNotification } = useContext(NotificationsContext);

  // حالة الآلات
  const [machines, setMachines] = useState([]);

  // تحميل الآلات عند بدء التطبيق
  useEffect(() => {
    const loadMachines = async () => {
      try {
        const machinesData = await database.getMachines();
        setMachines(machinesData);
        console.log('تم تحميل الآلات بنجاح:', machinesData.length);
      } catch (error) {
        console.error('خطأ في تحميل الآلات:', error);
        setMachines([]);
      }
    };

    loadMachines();
  }, []);

  // إضافة آلة جديدة
  const addMachine = async (newMachine) => {
    try {
      // إضافة الآلة باستخدام قاعدة البيانات
      const machine = await database.addMachine(newMachine);

      // تحديث حالة الآلات
      setMachines(prevMachines => [...prevMachines, machine]);

      // إظهار إشعار بنجاح إضافة الآلة
      showNotification(`تمت إضافة الآلة ${machine.name} بنجاح`, 'success');

      return machine;
    } catch (error) {
      console.error('Error adding machine:', error);
      
      // إظهار إشعار بفشل إضافة الآلة
      showNotification(`فشل في إضافة الآلة: ${error.message}`, 'error');
      
      throw error;
    }
  };

  // تحديث آلة موجودة
  const updateMachine = async (updatedMachine) => {
    try {
      // تحديث الآلة باستخدام قاعدة البيانات
      const machine = await database.updateMachine(updatedMachine);

      // تحديث حالة الآلات
      setMachines(prevMachines =>
        prevMachines.map(m => (m.id === machine.id || m._id === machine._id) ? machine : m)
      );

      // إظهار إشعار بنجاح تحديث الآلة
      showNotification(`تم تحديث بيانات الآلة ${machine.name} بنجاح`, 'success');

      return machine;
    } catch (error) {
      console.error('Error updating machine:', error);
      
      // إظهار إشعار بفشل تحديث الآلة
      showNotification(`فشل في تحديث الآلة: ${error.message}`, 'error');
      
      throw error;
    }
  };

  // حذف آلة
  const deleteMachine = async (id) => {
    try {
      // البحث عن الآلة
      const machineToDelete = machines.find(machine => machine.id === id || machine._id === id);

      if (!machineToDelete) {
        console.error('الآلة غير موجودة:', id);
        throw new Error('الآلة غير موجودة');
      }

      // حذف الآلة باستخدام قاعدة البيانات
      await database.deleteMachine(id);

      // تحديث حالة الآلات
      setMachines(prevMachines => prevMachines.filter(m => m.id !== id && m._id !== id));

      // إظهار إشعار بنجاح حذف الآلة
      showNotification(`تم حذف الآلة ${machineToDelete.name} بنجاح`, 'success');

      return { id };
    } catch (error) {
      console.error('Error deleting machine:', error);
      
      // إظهار إشعار بفشل حذف الآلة
      showNotification(`فشل في حذف الآلة: ${error.message}`, 'error');
      
      throw error;
    }
  };

  // القيمة التي سيتم توفيرها للمكونات
  const value = {
    machines,
    addMachine,
    updateMachine,
    deleteMachine
  };

  return (
    <MachinesContext.Provider value={value}>
      {children}
    </MachinesContext.Provider>
  );
};

export default MachinesProvider;
