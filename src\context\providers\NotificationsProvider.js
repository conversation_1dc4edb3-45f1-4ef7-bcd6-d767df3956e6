import React, { createContext, useState, useContext } from 'react';
import { SettingsContext } from './SettingsProvider';
import { UsersContext } from './UsersProvider';

// إنشاء سياق الإشعارات
export const NotificationsContext = createContext();

/**
 * مزود سياق الإشعارات
 *
 * يوفر هذا المكون وظائف إدارة الإشعارات
 *
 * @param {Object} props - خصائص المكون
 * @param {React.ReactNode} props.children - المكونات الفرعية
 * @returns {React.ReactElement} مزود سياق الإشعارات
 */
export const NotificationsProvider = ({ children }) => {
  // حالة الإشعارات
  const [notifications, setNotifications] = useState([]);

  // استخدام سياق الإعدادات والمستخدمين
  const { settings } = useContext(SettingsContext);
  const { checkCurrentUserPermission } = useContext(UsersContext);

  // تشغيل صوت الإشعار - تم تعطيله
  // تم تعليق هذه الوظيفة لأنها لم تعد مستخدمة بعد تعطيل الإشعارات
  /*
  const playNotificationSound = () => {
    try {
      // إنشاء عنصر صوتي
      const audio = new Audio();

      // تعيين مصدر الصوت حسب نوع الإشعار
      audio.src = process.env.PUBLIC_URL + '/assets/sounds/notification.mp3';

      // تعيين مستوى الصوت
      audio.volume = 0.5;

      // تشغيل الصوت
      audio.play().catch(error => {
        console.log('لا يمكن تشغيل صوت الإشعار:', error);
      });
    } catch (error) {
      console.log('خطأ في تشغيل صوت الإشعار:', error);
    }
  };
  */

  // إظهار إشعار - تم تعطيله
  const showNotification = (message, type = 'info') => {
    // تسجيل الإشعار في وحدة التحكم فقط دون عرضه للمستخدم
    console.log(`تم تجاهل الإشعار (${type}): ${message}`);

    // إنشاء كائن الإشعار للتوافق مع الواجهة
    const newNotification = {
      id: Date.now(),
      message,
      type,
      time: new Date().toLocaleTimeString(),
      read: true // تعيين الإشعار كمقروء مباشرة
    };

    // لا نقوم بإضافة الإشعار إلى القائمة
    // ولا نقوم بتشغيل صوت الإشعار

    return newNotification;
  };

  // تعليم إشعار كمقروء
  const markNotificationAsRead = (notificationId) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === notificationId
          ? { ...notification, read: true }
          : notification
      )
    );
  };

  // تعليم جميع الإشعارات كمقروءة
  const markAllNotificationsAsRead = () => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, read: true }))
    );
  };

  // القيمة التي سيتم توفيرها للمكونات
  const value = {
    notifications,
    showNotification,
    markNotificationAsRead,
    markAllNotificationsAsRead
  };

  return (
    <NotificationsContext.Provider value={value}>
      {children}
    </NotificationsContext.Provider>
  );
};

export default NotificationsProvider;
