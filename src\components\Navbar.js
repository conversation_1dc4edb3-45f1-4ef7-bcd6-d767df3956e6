import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { FaHome, FaBoxes, FaWarehouse, FaChartBar, FaUsers, FaDatabase, FaSignOutAlt, FaShoppingCart, FaMoneyBillWave, FaCog, FaUser, FaIndustry, FaCalculator, FaUserFriends, FaCode } from 'react-icons/fa';
import { useApp } from '../context/AppContext';

const Navbar = ({ user, onLogout }) => {
  const navigate = useNavigate();
  const location = useLocation();

  const menuItems = [
    { path: '/', icon: <FaHome />, text: 'الرئيسية' },
    { path: '/items', icon: <FaBoxes />, text: 'الأصناف' },
    { path: '/inventory', icon: <FaWarehouse />, text: 'المخزون' },
    { path: '/purchases', icon: <FaShoppingCart />, text: 'المشتريات' },
    { path: '/sales', icon: <FaMoneyBillWave />, text: 'البيع للعملاء' },
    { path: '/customers', icon: <FaUserFriends />, text: 'إدارة العملاء' },
    { path: '/reports', icon: <FaChartBar />, text: 'التقارير' },
    { path: '/zakat', icon: <FaCalculator />, text: 'الزكاة' },
    { path: '/users', icon: <FaUsers />, text: 'المستخدمين', adminOnly: true },
    { path: '/settings', icon: <FaCog />, text: 'الإعدادات' },
    { path: '/api-status', icon: <FaCode />, text: 'حالة API', adminOnly: true },
  ];

  const handleNavigation = (path) => {
    navigate(path);
  };

  // استخراج إعدادات النظام من سياق التطبيق
  const { settings } = useApp();

  return (
    <div className="navbar">
      <div className="navbar-brand">
        <div className="logo">
          {settings.logoUrl ? (
            <img
              src={settings.logoUrl}
              alt="شعار النظام"
              style={{ maxWidth: '100%', maxHeight: '100%', borderRadius: '50%' }}
            />
          ) : (
            <span className="logo-text">WMS</span>
          )}
        </div>
        <h3>{settings.systemName || 'نظام إدارة المخازن'}</h3>
      </div>

      <div className="navbar-menu">
        {menuItems.map((item) => {
          // إذا كان العنصر مخصص للمسؤولين فقط وليس المستخدم مسؤولاً، نتخطى هذا العنصر
          if (item.adminOnly && user?.role !== 'admin') {
            return null;
          }

          return (
            <div
              key={item.path}
              className={`navbar-menu-item ${location.pathname === item.path ? 'active' : ''}`}
              onClick={() => handleNavigation(item.path)}
              title={item.text}
            >
              <div className="menu-icon">{item.icon}</div>
              <span className="menu-text">{item.text}</span>
            </div>
          );
        })}
      </div>

      <div className="navbar-user">
        {user && (
          <div className="user-info">
            <span className="user-name">{user.username}</span>
          </div>
        )}
        <div className="navbar-menu-item logout" onClick={onLogout} title="تسجيل الخروج">
          <div className="menu-icon">
            <FaSignOutAlt />
          </div>
          <span className="menu-text">تسجيل الخروج</span>
        </div>
      </div>
    </div>
  );
};

export default Navbar;
