import React, { useState, useEffect } from 'react';
import { FaDatabase, FaUpload, FaDownload, FaHistory } from 'react-icons/fa';

const Backup = () => {
  // Variable no utilizada - considere eliminarla o usarla
  const [loading, setLoading] = useState(false);
  // Variable no utilizada - considere eliminarla o usarla
  const [alert, setAlert] = useState({ show: false, type: '', message: '' });
  // Variable no utilizada - considere eliminarla o usarla
  const [backupHistory, setBackupHistory] = useState([]);
  // Variable no utilizada - considere eliminarla o usarla
  const [showHistory, setShowHistory] = useState(false);

  useEffect(() => {
    // تحميل بيانات افتراضية لسجل النسخ الاحتياطي
    const defaultHistory = [
      {
        date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // قبل أسبوع
        path: 'C:\\Warehouse_Backup_2023-04-19T10-30-00.db',
        type: 'backup'
      },
      {
        date: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(), // قبل أسبوعين
        path: 'C:\\Warehouse_Backup_2023-04-12T09-15-00.db',
        type: 'backup'
      },
      {
        date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(), // قبل 10 أيام
        path: 'تم اختيار الملف من قبل المستخدم',
        type: 'restore'
      }
    ];

    setBackupHistory(defaultHistory);
  }, []);

  const handleBackup = () => {
    try {
      setLoading(true);

      // محاكاة عملية النسخ الاحتياطي
      setTimeout(() => {
        const backupPath = `C:\\Warehouse_Backup_${new Date().toISOString().replace(/:/g, '-').split('.')[0]}.db`;

        // إضافة إلى سجل النسخ الاحتياطي
        const newBackup = {
          date: new Date().toISOString(),
          path: backupPath,
          type: 'backup'
        };

        setBackupHistory(prev => [newBackup, ...prev]);
        showAlert('success', `تم إنشاء نسخة احتياطية بنجاح في: ${backupPath}`);
        setLoading(false);
      }, 1500); // تأخير لمحاكاة العملية
    } catch (err) {
      console.error('Backup error:', err);
      showAlert('danger', 'حدث خطأ أثناء إنشاء النسخة الاحتياطية');
      setLoading(false);
    }
  };

  const handleRestore = () => {
    if (window.confirm('هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم استبدال جميع البيانات الحالية.')) {
      try {
        setLoading(true);

        // محاكاة عملية استعادة النسخ الاحتياطي
        setTimeout(() => {
          // إضافة إلى سجل النسخ الاحتياطي
          const newRestore = {
            date: new Date().toISOString(),
            path: 'تم اختيار الملف من قبل المستخدم',
            type: 'restore'
          };

          setBackupHistory(prev => [newRestore, ...prev]);
          showAlert('success', 'تم استعادة النسخة الاحتياطية بنجاح');
          setLoading(false);
        }, 2000); // تأخير لمحاكاة العملية
      } catch (err) {
        console.error('Restore error:', err);
        showAlert('danger', 'حدث خطأ أثناء استعادة النسخة الاحتياطية');
        setLoading(false);
      }
    }
  };

  const showAlert = (type, message) => {
    setAlert({ show: true, type, message });

    // Hide alert after 5 seconds
    setTimeout(() => {
      setAlert({ show: false, type: '', message: '' });
    }, 5000);
  };

  return (
    <div>
      <h1 className="mb-4">النسخ الاحتياطي واستعادة البيانات</h1>

      {alert.show && (
        <div className={`alert alert-${alert.type}`}>
          {alert.message}
        </div>
      )}

      <div className="bg-white rounded shadow p-4 mb-4">
        <h2 className="mb-3">إدارة النسخ الاحتياطي</h2>

        <p className="mb-4">
          يمكنك إنشاء نسخة احتياطية من قاعدة البيانات واستعادتها في أي وقت. يُنصح بإنشاء نسخة احتياطية بشكل دوري للحفاظ على بياناتك.
        </p>

        <div className="flex flex-wrap gap-3">
          <button
            className="btn btn-primary"
            onClick={handleBackup}
            disabled={loading}
          >
            {loading ? (
              <span className="spinner" style={{ width: '1.5rem', height: '1.5rem' }}></span>
            ) : (
              <>
                <FaDownload className="ml-1" />
                إنشاء نسخة احتياطية
              </>
            )}
          </button>

          <button
            className="btn btn-danger"
            onClick={handleRestore}
            disabled={loading}
          >
            {loading ? (
              <span className="spinner" style={{ width: '1.5rem', height: '1.5rem' }}></span>
            ) : (
              <>
                <FaUpload className="ml-1" />
                استعادة من نسخة احتياطية
              </>
            )}
          </button>

          <button
            className="btn btn-secondary"
            onClick={() => setShowHistory(!showHistory)}
          >
            <FaHistory className="ml-1" />
            {showHistory ? 'إخفاء السجل' : 'عرض سجل النسخ الاحتياطي'}
          </button>
        </div>
      </div>

      {showHistory && (
        <div className="bg-white rounded shadow p-4">
          <h2 className="mb-3">سجل النسخ الاحتياطي</h2>

          {backupHistory.length > 0 ? (
            <div className="table-container">
              <table className="table">
                <thead>
                  <tr>
                    <th>#</th>
                    <th>التاريخ</th>
                    <th>النوع</th>
                    <th>المسار</th>
                  </tr>
                </thead>
                <tbody>
                  {backupHistory.map((entry, index) => (
                    <tr key={index}>
                      <td>{index + 1}</td>
                      <td>{new Date(entry.date).toLocaleString('ar-LY')}</td>
                      <td>
                        {entry.type === 'backup' ? (
                          <span className="badge badge-primary">
                            <FaDownload className="ml-1" />
                            نسخ احتياطي
                          </span>
                        ) : (
                          <span className="badge badge-warning">
                            <FaUpload className="ml-1" />
                            استعادة
                          </span>
                        )}
                      </td>
                      <td>{entry.path || '-'}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <p>لا توجد عمليات نسخ احتياطي مسجلة</p>
          )}
        </div>
      )}

      <div className="bg-white rounded shadow p-4 mt-4">
        <h2 className="mb-3">
          <FaDatabase className="ml-1" />
          أفضل الممارسات للنسخ الاحتياطي
        </h2>

        <ul className="list-disc pr-5">
          <li className="mb-2">قم بإنشاء نسخة احتياطية بشكل منتظم، يفضل أسبوعياً على الأقل.</li>
          <li className="mb-2">احتفظ بالنسخ الاحتياطية في أماكن متعددة (جهاز خارجي، سحابة، إلخ).</li>
          <li className="mb-2">قم بإنشاء نسخة احتياطية قبل إجراء أي تغييرات كبيرة في النظام.</li>
          <li className="mb-2">تأكد من اختبار استعادة النسخ الاحتياطية بشكل دوري للتأكد من صحتها.</li>
          <li className="mb-2">احتفظ بسجل للنسخ الاحتياطية مع التواريخ والملاحظات.</li>
        </ul>
      </div>
    </div>
  );
};

export default Backup;
