import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  Filler
} from 'chart.js';
import { Line, Bar, Pie, Doughnut } from 'react-chartjs-2';

// تسجيل مكونات Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  Filler
);

// تعريف الألوان المستخدمة في الرسوم البيانية
const chartColors = {
  primary: 'rgba(30, 50, 67, 0.7)',
  primaryBorder: 'rgba(30, 50, 67, 1)',
  secondary: 'rgba(44, 67, 86, 0.7)',
  secondaryBorder: 'rgba(44, 67, 86, 1)',
  accent: 'rgba(230, 126, 34, 0.7)',
  accentBorder: 'rgba(230, 126, 34, 1)',
  success: 'rgba(39, 174, 96, 0.7)',
  successBorder: 'rgba(39, 174, 96, 1)',
  danger: 'rgba(231, 76, 60, 0.7)',
  dangerBorder: 'rgba(231, 76, 60, 1)',
  warning: 'rgba(243, 156, 18, 0.7)',
  warningBorder: 'rgba(243, 156, 18, 1)',
};

// خيارات مشتركة للرسوم البيانية
const commonOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top',
      labels: {
        font: {
          family: 'Cairo, sans-serif',
          size: 12
        },
        color: '#2c3e50'
      }
    },
    tooltip: {
      titleFont: {
        family: 'Cairo, sans-serif',
        size: 14
      },
      bodyFont: {
        family: 'Cairo, sans-serif',
        size: 12
      },
      rtl: true,
      textDirection: 'rtl'
    }
  },
  scales: {
    x: {
      ticks: {
        font: {
          family: 'Cairo, sans-serif',
          size: 12
        },
        color: '#2c3e50'
      },
      grid: {
        display: false
      }
    },
    y: {
      ticks: {
        font: {
          family: 'Cairo, sans-serif',
          size: 12
        },
        color: '#2c3e50'
      },
      grid: {
        color: 'rgba(0, 0, 0, 0.05)'
      }
    }
  }
};

// مخطط المبيعات الشهرية
export const MonthlySalesChart = ({ data }) => {
  const chartData = {
    labels: data.labels,
    datasets: [
      {
        label: 'المبيعات',
        data: data.sales,
        borderColor: chartColors.primaryBorder,
        backgroundColor: chartColors.primary,
        tension: 0.4,
        fill: true
      },
      {
        label: 'المشتريات',
        data: data.purchases,
        borderColor: chartColors.accentBorder,
        backgroundColor: chartColors.accent,
        tension: 0.4,
        fill: true
      }
    ]
  };

  const options = {
    ...commonOptions,
    plugins: {
      ...commonOptions.plugins,
      title: {
        display: true,
        text: 'المبيعات والمشتريات الشهرية',
        font: {
          family: 'Cairo, sans-serif',
          size: 16,
          weight: 'bold'
        },
        color: '#2c3e50',
        padding: 20
      }
    }
  };

  return (
    <div className="chart-container">
      <Line data={chartData} options={options} />
    </div>
  );
};

// مخطط المخزون حسب الفئة
export const InventoryByCategoryChart = ({ data }) => {
  const chartData = {
    labels: data.labels,
    datasets: [
      {
        label: 'المخزون حسب الفئة',
        data: data.values,
        backgroundColor: [
          chartColors.primary,
          chartColors.accent,
          chartColors.success,
          chartColors.warning,
          chartColors.danger,
          chartColors.secondary
        ],
        borderColor: [
          chartColors.primaryBorder,
          chartColors.accentBorder,
          chartColors.successBorder,
          chartColors.warningBorder,
          chartColors.dangerBorder,
          chartColors.secondaryBorder
        ],
        borderWidth: 1
      }
    ]
  };

  const options = {
    ...commonOptions,
    plugins: {
      ...commonOptions.plugins,
      title: {
        display: true,
        text: 'المخزون حسب الفئة',
        font: {
          family: 'Cairo, sans-serif',
          size: 16,
          weight: 'bold'
        },
        color: '#2c3e50',
        padding: 20
      }
    }
  };

  return (
    <div className="chart-container">
      <Doughnut data={chartData} options={options} />
    </div>
  );
};

// مخطط الأصناف الأكثر مبيعاً
export const TopSellingItemsChart = ({ data }) => {
  const chartData = {
    labels: data.labels,
    datasets: [
      {
        label: 'الكمية المباعة',
        data: data.values,
        backgroundColor: chartColors.accent,
        borderColor: chartColors.accentBorder,
        borderWidth: 1
      }
    ]
  };

  const options = {
    ...commonOptions,
    indexAxis: 'y',
    plugins: {
      ...commonOptions.plugins,
      title: {
        display: true,
        text: 'الأصناف الأكثر مبيعاً',
        font: {
          family: 'Cairo, sans-serif',
          size: 16,
          weight: 'bold'
        },
        color: '#2c3e50',
        padding: 20
      }
    }
  };

  return (
    <div className="chart-container">
      <Bar data={chartData} options={options} />
    </div>
  );
};

// مخطط الأرباح الشهرية
export const MonthlyProfitChart = ({ data }) => {
  const chartData = {
    labels: data.labels,
    datasets: [
      {
        type: 'line',
        label: 'صافي الربح',
        data: data.profit,
        borderColor: chartColors.successBorder,
        backgroundColor: 'transparent',
        borderWidth: 2,
        tension: 0.4,
        yAxisID: 'y'
      },
      {
        type: 'bar',
        label: 'الإيرادات',
        data: data.revenue,
        backgroundColor: chartColors.primary,
        borderColor: chartColors.primaryBorder,
        borderWidth: 1,
        yAxisID: 'y'
      },
      {
        type: 'bar',
        label: 'التكاليف',
        data: data.costs,
        backgroundColor: chartColors.accent,
        borderColor: chartColors.accentBorder,
        borderWidth: 1,
        yAxisID: 'y'
      }
    ]
  };

  const options = {
    ...commonOptions,
    plugins: {
      ...commonOptions.plugins,
      title: {
        display: true,
        text: 'الأرباح الشهرية',
        font: {
          family: 'Cairo, sans-serif',
          size: 16,
          weight: 'bold'
        },
        color: '#2c3e50',
        padding: 20
      }
    },
    scales: {
      ...commonOptions.scales,
      y: {
        ...commonOptions.scales.y,
        title: {
          display: true,
          text: 'القيمة (د ل)',
          font: {
            family: 'Cairo, sans-serif',
            size: 12
          },
          color: '#2c3e50'
        }
      }
    }
  };

  return (
    <div className="chart-container">
      <Bar data={chartData} options={options} />
    </div>
  );
};

// مخطط حالة المخزون
export const InventoryStatusChart = ({ data }) => {
  const chartData = {
    labels: ['متوفر', 'منخفض', 'نافد'],
    datasets: [
      {
        label: 'حالة المخزون',
        data: [data.inStock, data.lowStock, data.outOfStock],
        backgroundColor: [
          chartColors.success,
          chartColors.warning,
          chartColors.danger
        ],
        borderColor: [
          chartColors.successBorder,
          chartColors.warningBorder,
          chartColors.dangerBorder
        ],
        borderWidth: 1
      }
    ]
  };

  const options = {
    ...commonOptions,
    plugins: {
      ...commonOptions.plugins,
      title: {
        display: true,
        text: 'حالة المخزون',
        font: {
          family: 'Cairo, sans-serif',
          size: 16,
          weight: 'bold'
        },
        color: '#2c3e50',
        padding: 20
      }
    }
  };

  return (
    <div className="chart-container">
      <Pie data={chartData} options={options} />
    </div>
  );
};
