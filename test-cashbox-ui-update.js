/**
 * اختبار تحديث واجهة المستخدم للخزينة
 * يتحقق من أن خانة إجمالي الأرباح تتحدث بشكل فوري
 */

const path = require('path');
const fs = require('fs');

// تحديد مسار قاعدة البيانات
const dbPath = path.join(require('os').homedir(), 'AppData', 'Roaming', 'warehouse-management-system', 'wms-database', 'warehouse.db');

console.log('🧪 اختبار تحديث واجهة المستخدم للخزينة...');
console.log(`📁 مسار قاعدة البيانات: ${dbPath}`);

// التحقق من وجود قاعدة البيانات
if (!fs.existsSync(dbPath)) {
  console.error('❌ قاعدة البيانات غير موجودة!');
  process.exit(1);
}

// استيراد المكتبات
const Database = require('better-sqlite3');

async function testCashboxUIUpdate() {
  let db;
  
  try {
    // الاتصال بقاعدة البيانات
    db = new Database(dbPath);
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // قراءة الخزينة الحالية
    const cashbox = db.prepare('SELECT * FROM cashbox LIMIT 1').get();
    
    if (!cashbox) {
      console.log('❌ لا توجد خزينة في قاعدة البيانات');
      return;
    }

    console.log('\n📊 بيانات الخزينة الحالية:');
    console.log(`   - الرصيد الافتتاحي: ${cashbox.initial_balance}`);
    console.log(`   - الرصيد الحالي: ${cashbox.current_balance}`);
    console.log(`   - إجمالي المبيعات: ${cashbox.sales_total}`);
    console.log(`   - إجمالي المشتريات: ${cashbox.purchases_total}`);
    console.log(`   - إجمالي مصاريف النقل: ${cashbox.transport_total || 0}`);
    console.log(`   - إجمالي الأرباح: ${cashbox.profit_total}`);

    // محاكاة عملية بيع لاختبار التحديث
    console.log('\n🔄 محاكاة عملية بيع لاختبار التحديث...');
    
    const saleAmount = 500;
    const saleProfit = 100;
    
    // تحديث الخزينة وفقاً للمنطق الجديد
    const newSalesTotal = (cashbox.sales_total || 0) + saleAmount;
    const newProfitTotal = newSalesTotal - (cashbox.purchases_total || 0) - (cashbox.transport_total || 0);
    
    console.log(`   - مبلغ البيع: ${saleAmount}`);
    console.log(`   - إجمالي المبيعات الجديد: ${newSalesTotal}`);
    console.log(`   - إجمالي الأرباح الجديد: ${newProfitTotal}`);
    console.log(`   - الرصيد الحالي: ${cashbox.initial_balance} (يبقى ثابت)`);

    // تطبيق التحديث
    const updateStmt = db.prepare(`
      UPDATE cashbox 
      SET current_balance = ?,
          sales_total = ?,
          profit_total = ?,
          updated_at = ?
      WHERE id = ?
    `);

    const updateResult = updateStmt.run(
      cashbox.initial_balance,  // الرصيد الحالي = الرصيد الافتتاحي
      newSalesTotal,           // إجمالي المبيعات الجديد
      newProfitTotal,          // إجمالي الأرباح الجديد
      new Date().toISOString(), // وقت التحديث
      cashbox.id               // معرف الخزينة
    );

    console.log(`\n✅ تم تحديث الخزينة بنجاح (${updateResult.changes} صف محدث)`);

    // قراءة الخزينة بعد التحديث
    const updatedCashbox = db.prepare('SELECT * FROM cashbox LIMIT 1').get();
    
    console.log('\n📊 بيانات الخزينة بعد التحديث:');
    console.log(`   - الرصيد الافتتاحي: ${updatedCashbox.initial_balance}`);
    console.log(`   - الرصيد الحالي: ${updatedCashbox.current_balance}`);
    console.log(`   - إجمالي المبيعات: ${updatedCashbox.sales_total}`);
    console.log(`   - إجمالي المشتريات: ${updatedCashbox.purchases_total}`);
    console.log(`   - إجمالي مصاريف النقل: ${updatedCashbox.transport_total || 0}`);
    console.log(`   - إجمالي الأرباح: ${updatedCashbox.profit_total}`);

    // التحقق من صحة المعادلات
    console.log('\n🧮 التحقق من صحة المعادلات:');
    
    const equation1Valid = updatedCashbox.current_balance === updatedCashbox.initial_balance;
    console.log(`   1. الرصيد الحالي (${updatedCashbox.current_balance}) = الرصيد الافتتاحي (${updatedCashbox.initial_balance}) ${equation1Valid ? '✅' : '❌'}`);
    
    const expectedProfit = updatedCashbox.sales_total - updatedCashbox.purchases_total - (updatedCashbox.transport_total || 0);
    const equation2Valid = Math.abs(updatedCashbox.profit_total - expectedProfit) < 0.01;
    console.log(`   2. إجمالي الأرباح (${updatedCashbox.profit_total}) = ${updatedCashbox.sales_total} - ${updatedCashbox.purchases_total} - ${updatedCashbox.transport_total || 0} = ${expectedProfit} ${equation2Valid ? '✅' : '❌'}`);

    // اختبار إضافي: محاكاة عملية شراء
    console.log('\n🔄 محاكاة عملية شراء لاختبار التحديث...');
    
    const purchaseAmount = 300;
    const newPurchasesTotal = (updatedCashbox.purchases_total || 0) + purchaseAmount;
    const newProfitTotalAfterPurchase = updatedCashbox.sales_total - newPurchasesTotal - (updatedCashbox.transport_total || 0);
    
    console.log(`   - مبلغ الشراء: ${purchaseAmount}`);
    console.log(`   - إجمالي المشتريات الجديد: ${newPurchasesTotal}`);
    console.log(`   - إجمالي الأرباح الجديد: ${newProfitTotalAfterPurchase}`);
    console.log(`   - الرصيد الحالي: ${updatedCashbox.initial_balance} (يبقى ثابت)`);

    // تطبيق تحديث الشراء
    const purchaseUpdateResult = updateStmt.run(
      updatedCashbox.initial_balance,  // الرصيد الحالي = الرصيد الافتتاحي
      updatedCashbox.sales_total,      // إجمالي المبيعات (لا يتغير)
      newProfitTotalAfterPurchase,     // إجمالي الأرباح الجديد
      new Date().toISOString(),        // وقت التحديث
      updatedCashbox.id                // معرف الخزينة
    );

    // تحديث إجمالي المشتريات
    const purchaseStmt = db.prepare(`
      UPDATE cashbox 
      SET purchases_total = ?,
          updated_at = ?
      WHERE id = ?
    `);

    purchaseStmt.run(
      newPurchasesTotal,
      new Date().toISOString(),
      updatedCashbox.id
    );

    console.log(`\n✅ تم تحديث الخزينة بعد الشراء بنجاح`);

    // قراءة الخزينة النهائية
    const finalCashbox = db.prepare('SELECT * FROM cashbox LIMIT 1').get();
    
    console.log('\n📊 بيانات الخزينة النهائية:');
    console.log(`   - الرصيد الافتتاحي: ${finalCashbox.initial_balance}`);
    console.log(`   - الرصيد الحالي: ${finalCashbox.current_balance}`);
    console.log(`   - إجمالي المبيعات: ${finalCashbox.sales_total}`);
    console.log(`   - إجمالي المشتريات: ${finalCashbox.purchases_total}`);
    console.log(`   - إجمالي مصاريف النقل: ${finalCashbox.transport_total || 0}`);
    console.log(`   - إجمالي الأرباح: ${finalCashbox.profit_total}`);

    // التحقق النهائي من صحة المعادلات
    console.log('\n🧮 التحقق النهائي من صحة المعادلات:');
    
    const finalEquation1Valid = finalCashbox.current_balance === finalCashbox.initial_balance;
    console.log(`   1. الرصيد الحالي (${finalCashbox.current_balance}) = الرصيد الافتتاحي (${finalCashbox.initial_balance}) ${finalEquation1Valid ? '✅' : '❌'}`);
    
    const finalExpectedProfit = finalCashbox.sales_total - finalCashbox.purchases_total - (finalCashbox.transport_total || 0);
    const finalEquation2Valid = Math.abs(finalCashbox.profit_total - finalExpectedProfit) < 0.01;
    console.log(`   2. إجمالي الأرباح (${finalCashbox.profit_total}) = ${finalCashbox.sales_total} - ${finalCashbox.purchases_total} - ${finalCashbox.transport_total || 0} = ${finalExpectedProfit} ${finalEquation2Valid ? '✅' : '❌'}`);

    // النتيجة النهائية
    console.log('\n🎯 النتيجة النهائية:');
    const allTestsPassed = finalEquation1Valid && finalEquation2Valid;
    
    if (allTestsPassed) {
      console.log('🎉 جميع الاختبارات نجحت! نظام الخزينة يعمل بشكل صحيح.');
      console.log('💡 إذا كانت واجهة المستخدم لا تتحدث، فالمشكلة في التحديث الفوري وليس في المنطق.');
    } else {
      console.log('❌ بعض الاختبارات فشلت. هناك حاجة لمراجعة المنطق.');
    }

    return {
      success: allTestsPassed,
      finalCashbox,
      equations: {
        equation1Valid: finalEquation1Valid,
        equation2Valid: finalEquation2Valid
      }
    };

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error);
    return { success: false, error: error.message };
  } finally {
    if (db) {
      db.close();
      console.log('🔒 تم إغلاق الاتصال بقاعدة البيانات');
    }
  }
}

// تشغيل الاختبار
testCashboxUIUpdate()
  .then(result => {
    console.log('\n📋 نتيجة الاختبار:', result);
    console.log('\n🏁 انتهى الاختبار');
  })
  .catch(error => {
    console.error('❌ خطأ في تشغيل الاختبار:', error);
  });
