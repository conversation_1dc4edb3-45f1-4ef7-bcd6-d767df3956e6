/**
 * سكريبت تشغيل سريع لإصلاح مشكلة عرض الأرباح
 * يمكن نسخه ولصقه مباشرة في وحدة التحكم
 */

console.log('🚀 بدء الإصلاح السريع لمشكلة عرض الأرباح...');

// 1. تشخيص سريع للمشكلة
async function quickDiagnosis() {
  console.log('🔍 تشخيص سريع للمشكلة...');

  try {
    // فحص الخزينة
    const cashboxResult = await window.api.executeSQL('SELECT profit_total FROM cashbox LIMIT 1');
    const cashboxProfit = cashboxResult[0]?.profit_total || 0;
    console.log(`💰 أرباح الخزينة: ${cashboxProfit}`);

    // فحص المعاملات
    const salesResult = await window.api.executeSQL(`
      SELECT SUM(profit) as total_profit FROM transactions WHERE transaction_type = 'sale'
    `);
    const transactionsProfit = salesResult[0]?.total_profit || 0;
    console.log(`📊 أرباح المعاملات: ${transactionsProfit}`);

    // فحص العناصر المعروضة في الصفحة
    const profitElements = document.querySelectorAll('.profit-card .stat-card-value, .profit-value, [data-profit-display]');
    console.log(`🖥️ عناصر الأرباح في الصفحة: ${profitElements.length}`);

    profitElements.forEach((element, index) => {
      const text = element.textContent || element.innerText;
      const value = parseFloat(text.replace(/[^\d.-]/g, ''));
      console.log(`   ${index + 1}. ${text} (القيمة: ${value})`);
    });

    return { cashboxProfit, transactionsProfit, elementsCount: profitElements.length };

  } catch (error) {
    console.error('❌ خطأ في التشخيص:', error);
    return null;
  }
}

// 2. إصلاح فوري للعرض
async function forceFixDisplay() {
  console.log('🔧 إصلاح فوري لعرض الأرباح...');

  try {
    // الحصول على القيمة الصحيحة من الخزينة
    const cashboxResult = await window.api.executeSQL('SELECT profit_total FROM cashbox LIMIT 1');
    const correctProfit = cashboxResult[0]?.profit_total || 0;

    console.log(`✅ القيمة الصحيحة: ${correctProfit}`);

    // البحث عن جميع عناصر عرض الأرباح وتصحيحها
    const selectors = [
      '.profit-card .stat-card-value',
      '.profit-value',
      '[data-profit-display]',
      '.ant-statistic-content-value',
      '.financial-card .value',
      '.profit-display'
    ];

    let fixedElements = 0;

    selectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        const currentText = element.textContent || element.innerText;
        const currentValue = parseFloat(currentText.replace(/[^\d.-]/g, ''));

        // إذا كانت القيمة تبدو كأنها قيمة أرباح خاطئة
        if (!isNaN(currentValue) && currentValue > 0 && Math.abs(currentValue - correctProfit) > 0.01) {
          // تحديث النص
          const formattedValue = new Intl.NumberFormat('ar-LY', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
          }).format(correctProfit) + ' د.ل';

          element.textContent = formattedValue;
          element.style.color = '#28a745';
          element.style.fontWeight = 'bold';

          console.log(`🔄 تم تصحيح: ${currentValue} → ${correctProfit}`);
          fixedElements++;
        }
      });
    });

    console.log(`✅ تم تصحيح ${fixedElements} عنصر`);

    // إرسال أحداث للتحديث
    const updateEvent = new CustomEvent('force-profit-update', {
      detail: { correctProfit, timestamp: new Date().toISOString() }
    });
    window.dispatchEvent(updateEvent);

    return { correctProfit, fixedElements };

  } catch (error) {
    console.error('❌ خطأ في الإصلاح:', error);
    return null;
  }
}

// 3. مراقبة مستمرة
function startContinuousMonitoring(correctProfit) {
  console.log('👁️ بدء المراقبة المستمرة...');

  const monitor = setInterval(async () => {
    try {
      // البحث عن عناصر تعرض قيم خاطئة
      const selectors = [
        '.profit-card .stat-card-value',
        '.profit-value',
        '[data-profit-display]',
        '.ant-statistic-content-value'
      ];

      let needsFix = false;

      selectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
          const currentText = element.textContent || element.innerText;
          const currentValue = parseFloat(currentText.replace(/[^\d.-]/g, ''));

          if (!isNaN(currentValue) && currentValue > 0 && Math.abs(currentValue - correctProfit) > 0.01) {
            needsFix = true;

            const formattedValue = new Intl.NumberFormat('ar-LY', {
              minimumFractionDigits: 0,
              maximumFractionDigits: 2
            }).format(correctProfit) + ' د.ل';

            element.textContent = formattedValue;
            element.style.color = '#28a745';

            console.log(`🔄 تصحيح تلقائي: ${currentValue} → ${correctProfit}`);
          }
        });
      });

      if (!needsFix) {
        console.log('✅ جميع القيم صحيحة');
      }

    } catch (error) {
      console.error('خطأ في المراقبة:', error);
    }
  }, 5000); // كل 5 ثوان

  // إيقاف المراقبة بعد 2 دقيقة
  setTimeout(() => {
    clearInterval(monitor);
    console.log('⏹️ تم إيقاف المراقبة');
  }, 2 * 60 * 1000);

  return monitor;
}

// 4. تشغيل الإصلاح الشامل
async function runCompleteFix() {
  console.log('🎯 تشغيل الإصلاح الشامل...');

  // التشخيص
  const diagnosis = await quickDiagnosis();
  if (!diagnosis) return;

  // الإصلاح
  const fix = await forceFixDisplay();
  if (!fix) return;

  // المراقبة
  startContinuousMonitoring(fix.correctProfit);

  // حفظ القيمة الصحيحة
  localStorage.setItem('correctProfitValue', fix.correctProfit.toString());
  localStorage.setItem('profitLastUpdated', new Date().toISOString());

  console.log('🎉 تم إكمال الإصلاح الشامل!');
  console.log(`💰 القيمة الصحيحة: ${fix.correctProfit} د.ل`);
  console.log(`🔧 تم تصحيح: ${fix.fixedElements} عنصر`);

  return fix;
}

// تشغيل الإصلاح
runCompleteFix();

// تصدير الدوال للاستخدام اليدوي
window.quickProfitFix = {
  diagnose: quickDiagnosis,
  fix: forceFixDisplay,
  monitor: startContinuousMonitoring,
  runComplete: runCompleteFix
};

console.log('🛠️ تم تحميل أدوات الإصلاح السريع:');
console.log('- window.quickProfitFix.diagnose() - تشخيص');
console.log('- window.quickProfitFix.fix() - إصلاح');
console.log('- window.quickProfitFix.runComplete() - إصلاح شامل');
