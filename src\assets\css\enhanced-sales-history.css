/* أنماط سجل عمليات العميل المحسن */

.enhanced-sales-history-container {
  max-width: 95%;
  width: 1200px;
  max-height: 90vh;
  overflow-y: auto;
  background-color: #f8f9fa;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* أنماط ملخص العمليات */
.sales-summary {
  background-color: #fff;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.summary-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  justify-content: space-between;
}

.summary-card {
  flex: 1;
  min-width: 150px;
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s, box-shadow 0.2s;
}

.summary-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.card-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f0f4ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 15px;
  font-size: 18px;
  color: #3498db;
}

.summary-card:nth-child(1) .card-icon {
  background-color: #e3f2fd;
  color: #2196f3;
}

.summary-card:nth-child(2) .card-icon {
  background-color: #fff8e1;
  color: #ffc107;
}

.summary-card:nth-child(3) .card-icon {
  background-color: #e8f5e9;
  color: #4caf50;
}

.summary-card:nth-child(4) .card-icon {
  background-color: #f3e5f5;
  color: #9c27b0;
}

.summary-card:nth-child(5) .card-icon {
  background-color: #e1f5fe;
  color: #03a9f4;
}

.summary-card:nth-child(6) .card-icon {
  background-color: #e8eaf6;
  color: #3f51b5;
}

.card-content {
  flex: 1;
}

.card-content h5 {
  margin: 0 0 5px 0;
  font-size: 14px;
  color: #666;
}

.card-content p {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

/* أنماط أدوات التصفية والفرز */
.filter-controls {
  background-color: #fff;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.tabs {
  display: flex;
  overflow-x: auto;
  margin-bottom: 15px;
  padding-bottom: 5px;
  gap: 10px;
}

.tab-btn {
  background-color: #f5f5f5;
  border: none;
  border-radius: 20px;
  padding: 8px 15px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.2s;
  white-space: nowrap;
}

.tab-btn svg {
  font-size: 14px;
}

.tab-btn.active {
  background-color: #3498db;
  color: white;
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
}

.search-box {
  flex: 1;
  min-width: 200px;
  position: relative;
}

.search-box input {
  width: 100%;
  padding: 8px 15px 8px 35px;
  border: 1px solid #ddd;
  border-radius: 20px;
  font-size: 14px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
}

.date-filters {
  display: flex;
  gap: 10px;
}

.date-filter {
  display: flex;
  align-items: center;
  gap: 5px;
}

.date-filter label {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  color: #666;
}

.date-filter input {
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.sort-btn {
  background-color: #f5f5f5;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.sort-btn:hover {
  background-color: #e0e0e0;
}

/* أنماط عرض البيانات */
.data-container {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-title {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.data-table th,
.data-table td {
  padding: 12px 15px;
  text-align: right;
  border-bottom: 1px solid #eee;
}

.data-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #555;
}

.data-table tbody tr:hover {
  background-color: #f5f9ff;
}

.return-row {
  background-color: #fff8e1;
}

.return-row:hover {
  background-color: #ffecb3 !important;
}

.badge {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.sale-badge {
  background-color: #e3f2fd;
  color: #1976d2;
}

.return-badge {
  background-color: #fff8e1;
  color: #f57c00;
}

/* أنماط الفواتير */
.invoice-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  overflow: hidden;
}

.sub-invoice-card {
  border-right: 4px solid #9c27b0;
}

.invoice-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #eee;
}

.invoice-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.invoice-icon {
  font-size: 18px;
  color: #3498db;
}

.sub-invoice-card .invoice-icon {
  color: #9c27b0;
}

.invoice-title h5 {
  margin: 0;
  font-size: 16px;
}

.parent-invoice-badge {
  background-color: #f3e5f5;
  color: #9c27b0;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  margin-right: 10px;
}

.invoice-meta {
  display: flex;
  align-items: center;
  gap: 15px;
}

.invoice-date {
  font-size: 14px;
  color: #666;
}

.print-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.print-btn:hover {
  background-color: #2980b9;
}

.invoice-body {
  padding: 20px;
}

.invoice-items-table {
  width: 100%;
  border-collapse: collapse;
}

.invoice-items-table th,
.invoice-items-table td {
  padding: 10px 12px;
  text-align: right;
  border-bottom: 1px solid #eee;
}

.invoice-items-table th {
  font-weight: 600;
  color: #555;
  background-color: #f8f9fa;
}

.invoice-items-table tfoot td {
  font-weight: bold;
  border-top: 2px solid #eee;
}

.text-left {
  text-align: left;
}

/* حالات فارغة وتحميل وخطأ */
.empty-state,
.loading-spinner,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 20px;
  text-align: center;
}

.empty-icon,
.error-icon {
  font-size: 48px;
  margin-bottom: 20px;
  color: #ccc;
}

.error-icon {
  color: #e74c3c;
}

.empty-title,
.error-title {
  margin: 0 0 10px 0;
  font-size: 20px;
  color: #555;
}

.empty-description,
.error-description {
  margin: 0;
  color: #777;
  max-width: 400px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #3498db;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
