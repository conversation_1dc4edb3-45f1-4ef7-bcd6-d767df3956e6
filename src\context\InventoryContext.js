import React, { createContext, useContext } from 'react';
import { useApp } from './AppContext';

// Create a dummy InventoryContext that forwards to AppContext
const InventoryContext = createContext();

// This hook will now use the AppContext instead of a separate InventoryContext
export const useInventory = () => {
  // Get the AppContext values
  const appContext = useApp();

  // Return the same interface as before, but using AppContext values
  return {
    items: appContext.items,
    inventory: appContext.inventory,
    transactions: appContext.transactions,
    loading: appContext.loading,
    error: null,
    fetchItems: appContext.loadItems,
    fetchInventory: appContext.syncAllInventory,
    fetchTransactions: appContext.getTransactionsByType,
    addItem: appContext.addItem,
    updateItem: appContext.updateItem,
    deleteItem: appContext.deleteItem,
    addTransaction: appContext.addTransaction
  };
};

// This is now just a pass-through component that renders its children
export const InventoryProvider = ({ children }) => {
  // Get the values from AppContext
  const appContext = useApp();

  // Create a compatible value object
  const value = {
    items: appContext.items,
    inventory: appContext.inventory,
    transactions: appContext.transactions,
    loading: appContext.loading,
    error: null,
    fetchItems: appContext.loadItems,
    fetchInventory: appContext.syncAllInventory,
    fetchTransactions: appContext.getTransactionsByType,
    addItem: appContext.addItem,
    updateItem: appContext.updateItem,
    deleteItem: appContext.deleteItem,
    addTransaction: appContext.addTransaction
  };

  // Just render the children directly, no need for a provider since we're using AppContext
  return (
    <InventoryContext.Provider value={value}>
      {children}
    </InventoryContext.Provider>
  );
};
