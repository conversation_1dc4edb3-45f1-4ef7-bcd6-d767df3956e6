/**
 * وحدة مساعدة بسيطة لتصدير التقارير كملفات PDF
 * تستخدم طريقة تحويل التقرير إلى صورة ثم إضافتها إلى ملف PDF
 */

import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';

/**
 * تصدير عنصر HTML كملف PDF
 * @param {HTMLElement} element - عنصر HTML المراد تصديره
 * @param {string} filename - اسم الملف
 * @returns {Promise<boolean>} - نجاح أو فشل العملية
 */
export async function exportToPDF(element, filename) {
  try {
    if (!element) {
      console.error('لم يتم تحديد عنصر HTML للتصدير');
      return false;
    }

    // تحويل العنصر إلى صورة
    const canvas = await html2canvas(element, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      logging: false
    });

    // إنشاء ملف PDF
    const pdf = new jsPDF({
      orientation: 'landscape',
      unit: 'mm',
      format: 'a4'
    });

    // إضافة الصورة إلى ملف PDF
    const imgData = canvas.toDataURL('image/png');
    const imgWidth = pdf.internal.pageSize.getWidth();
    const imgHeight = (canvas.height * imgWidth) / canvas.width;

    pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
    pdf.save(filename);

    return true;
  } catch (error) {
    console.error('خطأ في تصدير عنصر HTML كملف PDF:', error);
    return false;
  }
}

/**
 * طباعة عنصر HTML
 * @param {HTMLElement} element - عنصر HTML المراد طباعته
 * @returns {Promise<boolean>} - نجاح أو فشل العملية
 */
export async function printElement(element) {
  try {
    if (!element) {
      console.error('لم يتم تحديد عنصر HTML للطباعة');
      return false;
    }

    // تحويل العنصر إلى صورة
    const canvas = await html2canvas(element, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      logging: false
    });

    // إنشاء نافذة جديدة للطباعة
    const printWindow = window.open('', '_blank');

    if (!printWindow) {
      alert('يرجى السماح بالنوافذ المنبثقة لطباعة التقرير');
      return false;
    }

    // إضافة الصورة إلى النافذة
    const imgData = canvas.toDataURL('image/png');

    printWindow.document.open();
    printWindow.document.write(`
      <html>
        <head>
          <title>طباعة التقرير</title>
          <style>
            body {
              margin: 0;
              padding: 0;
              text-align: center;
            }
            img {
              max-width: 100%;
              height: auto;
            }
            @media print {
              body {
                margin: 0;
                padding: 0;
              }
            }
          </style>
        </head>
        <body>
          <img src="${imgData}" alt="تقرير" />
          <script>
            window.onload = function() {
              setTimeout(function() {
                window.print();
                window.close();
              }, 500);
            };
          </script>
        </body>
      </html>
    `);
    printWindow.document.close();

    return true;
  } catch (error) {
    console.error('خطأ في طباعة عنصر HTML:', error);
    return false;
  }
}

export default {
  exportToPDF,
  printElement
};
