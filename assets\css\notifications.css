.notifications-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  display: none; /* تم تغييره من flex إلى none لإخفاء الإشعارات */
  flex-direction: column;
  gap: 10px;
  max-width: 350px;
  width: 100%;
}

.notification {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: slide-in 0.3s ease-out forwards;
  background-color: white;
}

.notification-icon {
  margin-right: 15px;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-content {
  flex: 1;
  font-size: 14px;
}

.notification-close {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
  padding: 0 5px;
  margin-left: 10px;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.notification-close:hover {
  opacity: 1;
}

/* نوع الإشعارات */
.notification-success {
  border-right: 4px solid #2ecc71;
}

.notification-success .notification-icon {
  color: #2ecc71;
}

.notification-error {
  border-right: 4px solid #e74c3c;
}

.notification-error .notification-icon {
  color: #e74c3c;
}

.notification-warning {
  border-right: 4px solid #f39c12;
}

.notification-warning .notification-icon {
  color: #f39c12;
}

.notification-info {
  border-right: 4px solid #3498db;
}

.notification-info .notification-icon {
  color: #3498db;
}

/* الرسوم المتحركة */
@keyframes slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-out {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.notification.removing {
  animation: slide-out 0.3s ease-in forwards;
}
