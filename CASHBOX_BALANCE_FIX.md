# إصلاح آلية خصم مبالغ المشتريات من الرصيد الحالي في نظام الخزينة

## وصف المشكلة

كان هناك خلل في نظام الخزينة حيث:

1. **لا يتم خصم مبالغ المشتريات من الرصيد الحالي** عند تنفيذ عمليات الشراء
2. **الرصيد الحالي لا يتغير** أو لا ينقص بالمبلغ المطلوب عند تسجيل المشتريات
3. **يؤثر على دقة حسابات الخزينة** ويجعل الرصيد الحالي غير صحيح

## تحليل السبب

### المشكلة الأساسية:
في جميع أنواع المعاملات (بيع، شراء، إرجاع) كان يتم تعيين الرصيد الحالي إلى الرصيد الافتتاحي بدلاً من تحديثه بناءً على المعاملة:

```javascript
// الكود الخاطئ (قبل الإصلاح)
updateResult = updateStmt.run(
  initialBalance,        // ❌ الرصيد الحالي = الرصيد الافتتاحي (ثابت)
  numericTotalPrice,     // إضافة/خصم من الإجماليات
  // ...
);
```

### المنطق الصحيح المطلوب:
- **عمليات البيع**: `current_balance = current_balance + amount` (إضافة المبلغ)
- **عمليات الشراء**: `current_balance = current_balance - amount` (خصم المبلغ)
- **عمليات الاسترجاع**: `current_balance = current_balance - amount` (خصم المبلغ)
- **مصاريف النقل**: `current_balance = current_balance - amount` (خصم المبلغ)

## الإصلاحات المطبقة

### 1. إصلاح `unified-transaction-manager.js`

#### أ. عمليات البيع (السطر 1248-1258):
```javascript
// حساب الرصيد الحالي الجديد: الرصيد الحالي + مبلغ البيع
const newCurrentBalance = currentBalance + numericTotalPrice;
console.log(`[CASHBOX-FIX] تحديث الرصيد الحالي: ${currentBalance} + ${numericTotalPrice} = ${newCurrentBalance}`);

updateResult = updateStmt.run(
  newCurrentBalance,     // ✅ الرصيد الحالي الجديد (الحالي + مبلغ البيع)
  numericTotalPrice,     // إضافة مبلغ البيع لإجمالي المبيعات
  newProfitTotal,        // الأرباح الجديدة المحسوبة
  now,                   // وقت التحديث
  currentCashbox.id      // معرف الخزينة
);
```

#### ب. عمليات الشراء (السطر 1484-1496):
```javascript
// حساب الرصيد الحالي الجديد: الرصيد الحالي - (مبلغ الشراء + مصاريف النقل)
const totalDeduction = numericTotalPrice + numericTransportCost;
const newCurrentBalance = currentBalance - totalDeduction;
console.log(`[CASHBOX-FIX] حساب الرصيد الحالي الجديد: ${currentBalance} - ${totalDeduction} = ${newCurrentBalance}`);

const updateResult = updateStmt.run(
  newCurrentBalance,     // ✅ الرصيد الحالي الجديد (الحالي - مبلغ الشراء - مصاريف النقل)
  numericTotalPrice,     // إضافة مبلغ الشراء لإجمالي المشتريات
  numericTransportCost,  // إضافة مصاريف النقل لإجمالي مصاريف النقل
  newProfitTotal,        // الأرباح الجديدة المحسوبة
  now,
  cashbox ? cashbox.id : 1
);
```

#### ج. عمليات الاسترجاع (السطر 1695-1706):
```javascript
// حساب الرصيد الحالي الجديد: الرصيد الحالي - مبلغ الاسترجاع
const newCurrentBalance = currentBalance - numericTotalPrice;
console.log(`[CASHBOX-FIX] حساب الرصيد الحالي الجديد: ${currentBalance} - ${numericTotalPrice} = ${newCurrentBalance}`);

const updateResult = updateStmt.run(
  newCurrentBalance,     // ✅ الرصيد الحالي الجديد (الحالي - مبلغ الاسترجاع)
  numericTotalPrice,     // خصم مبلغ الاسترجاع من إجمالي المبيعات
  numericTotalPrice,     // إضافة مبلغ الاسترجاع لإجمالي المرتجعات
  newProfitTotal,        // الأرباح الجديدة المحسوبة
  now,
  cashbox ? cashbox.id : 1
);
```

### 2. إصلاح `cashbox-manager.js`

#### أ. دالة `addTransaction` (السطر 406-457):
```javascript
// حساب الرصيد الحالي الجديد بناءً على نوع المعاملة
let newCurrentBalance = cashbox.current_balance || 0;

if (transaction.type === 'sale' || transaction.type === 'income') {
  newCurrentBalance += numericAmount; // ✅ إضافة المبلغ للرصيد الحالي
  console.log(`[CASHBOX-FIX] عملية بيع/دخل: إضافة ${numericAmount} للرصيد الحالي`);
} else if (transaction.type === 'purchase' || transaction.type === 'expense') {
  newCurrentBalance -= numericAmount; // ✅ خصم المبلغ من الرصيد الحالي
  console.log(`[CASHBOX-FIX] عملية شراء/مصروف: خصم ${numericAmount} من الرصيد الحالي`);
} else if (transaction.type === 'return') {
  newCurrentBalance -= numericAmount; // ✅ خصم المبلغ من الرصيد الحالي
  console.log(`[CASHBOX-FIX] عملية إرجاع: خصم ${numericAmount} من الرصيد الحالي`);
} else if (transaction.type === 'transport') {
  newCurrentBalance -= numericAmount; // ✅ خصم المبلغ من الرصيد الحالي
  console.log(`[CASHBOX-FIX] عملية نقل: خصم ${numericAmount} من الرصيد الحالي`);
}
```

#### ب. دالة `updateCashboxAfterReturn` (السطر 909-935):
```javascript
// حساب الرصيد الحالي الجديد: خصم مبلغ الإرجاع من الرصيد الحالي
const newCurrentBalance = (cashbox.current_balance || 0) - numericAmount;
console.log(`[CASHBOX-RETURN] تحديث الرصيد الحالي: ${cashbox.current_balance} - ${numericAmount} = ${newCurrentBalance}`);

const updateStmt = db.prepare(`
  UPDATE cashbox
  SET current_balance = ?,  // ✅ استخدام الرصيد الحالي الجديد
      sales_total = ?,
      returns_total = ?,
      profit_total = ?,
      updated_at = ?
  WHERE id = ?
`);
```

## كيفية اختبار الإصلاح

### الطريقة الأولى: الاختبار التلقائي

1. **افتح التطبيق**
2. **افتح وحدة التحكم** (F12)
3. **انسخ والصق محتوى ملف** `test-cashbox-balance-fix.js`
4. **راقب النتائج** في وحدة التحكم

### الطريقة الثانية: الاختبار اليدوي

1. **سجل الرصيد الابتدائي** (مثلاً 10000)
2. **قم بعملية بيع** (مثلاً 1000) - يجب أن يصبح الرصيد 11000
3. **قم بعملية شراء** (مثلاً 500) - يجب أن يصبح الرصيد 10500
4. **قم بعملية إرجاع** (مثلاً 200) - يجب أن يصبح الرصيد 10300
5. **تحقق من صحة الحسابات** في واجهة الخزينة

## النتائج المتوقعة

بعد تطبيق الإصلاحات:

✅ **عمليات البيع**: تزيد الرصيد الحالي بمبلغ البيع  
✅ **عمليات الشراء**: تقلل الرصيد الحالي بمبلغ الشراء + مصاريف النقل  
✅ **عمليات الاسترجاع**: تقلل الرصيد الحالي بمبلغ الاسترجاع  
✅ **مصاريف النقل**: تقلل الرصيد الحالي بمبلغ مصاريف النقل  
✅ **دقة حسابات الخزينة**: جميع الحسابات تتم بشكل صحيح  
✅ **تحديث فوري**: الرصيد الحالي يتحدث فوراً بعد كل معاملة  

## الملفات المعدلة

- `unified-transaction-manager.js` - إصلاح آلية تحديث الرصيد الحالي في جميع أنواع المعاملات
- `cashbox-manager.js` - إصلاح دوال `addTransaction` و `updateCashboxAfterReturn`

## الملفات الجديدة

- `test-cashbox-balance-fix.js` - سكريبت اختبار شامل للتأكد من صحة الإصلاحات
- `CASHBOX_BALANCE_FIX.md` - هذا الملف (دليل الإصلاح)

## ملاحظات مهمة

1. **النسخ الاحتياطية**: تأكد من عمل نسخة احتياطية من قاعدة البيانات قبل تطبيق الإصلاحات
2. **اختبار شامل**: اختبر جميع وظائف الخزينة بعد تطبيق الإصلاح
3. **مراقبة الأداء**: راقب أداء النظام للتأكد من عدم وجود مشاكل جديدة
4. **التوافق**: الإصلاحات متوافقة مع النظام الحالي ولا تؤثر على البيانات الموجودة

## في حالة استمرار المشكلة

إذا استمرت المشكلة بعد تطبيق الإصلاحات:

1. تحقق من سجلات وحدة التحكم للحصول على تفاصيل إضافية
2. تأكد من تطبيق جميع الإصلاحات بشكل صحيح
3. جرب تشغيل سكريبت الاختبار للتشخيص
4. تواصل مع فريق التطوير مع تفاصيل الخطأ الجديد
