/**
 * اختبار إصلاح قيد الرصيد الحالي في عمليات البيع
 * 
 * هذا السكريبت يقوم باختبار أن الرصيد الحالي لا يتجاوز الرصيد الافتتاحي
 * حتى أثناء عمليات البيع في الوقت الفعلي
 */

console.log('🧪 بدء اختبار إصلاح قيد الرصيد الحالي في عمليات البيع...');

// التحقق من وجود window.api
if (typeof window !== 'undefined' && window.api) {
  
  // اختبار شامل لقيد الرصيد الحالي
  async function testBalanceConstraintFix() {
    try {
      console.log('📊 بدء الاختبار الشامل لقيد الرصيد الحالي...');
      
      // 1. تعيين رصيد ابتدائي للاختبار
      console.log('\n1️⃣ تعيين رصيد ابتدائي للاختبار (5000)...');
      const initialBalanceResult = await window.api.cashbox.updateInitialBalance(5000);
      if (initialBalanceResult.success) {
        console.log('✅ تم تعيين الرصيد الابتدائي بنجاح');
        console.log('📊 الخزينة الأولية:', {
          initial_balance: initialBalanceResult.cashbox.initial_balance,
          current_balance: initialBalanceResult.cashbox.current_balance,
          profit_total: initialBalanceResult.cashbox.profit_total
        });
        
        // التحقق من أن الرصيد الحالي يساوي الرصيد الافتتاحي
        if (initialBalanceResult.cashbox.current_balance === initialBalanceResult.cashbox.initial_balance) {
          console.log('✅ الرصيد الحالي يساوي الرصيد الافتتاحي (صحيح)');
        } else {
          console.error('❌ الرصيد الحالي لا يساوي الرصيد الافتتاحي!');
        }
      } else {
        console.error('❌ فشل في تعيين الرصيد الابتدائي:', initialBalanceResult.error);
        return;
      }
      
      // 2. اختبار عملية بيع صغيرة (لا تتجاوز الرصيد الافتتاحي)
      console.log('\n2️⃣ اختبار عملية بيع صغيرة (مبلغ 1000 - لا تتجاوز الرصيد الافتتاحي)...');
      const smallSaleTransaction = {
        type: 'sale',
        amount: 1000,
        source: 'test',
        notes: 'بيع صغير لا يتجاوز الرصيد الافتتاحي',
        user_id: 1
      };
      
      const smallSaleResult = await window.api.cashbox.addTransaction(smallSaleTransaction);
      if (smallSaleResult.success) {
        console.log('✅ تم تسجيل عملية البيع الصغيرة');
        console.log('📊 الخزينة بعد البيع الصغير:', {
          initial_balance: smallSaleResult.cashbox.initial_balance,
          current_balance: smallSaleResult.cashbox.current_balance,
          sales_total: smallSaleResult.cashbox.sales_total,
          profit_total: smallSaleResult.cashbox.profit_total,
          expected_current_balance: 5000 // يجب أن يبقى 5000 (لا يتجاوز الرصيد الافتتاحي)
        });
        
        // التحقق من أن الرصيد الحالي لا يتجاوز الرصيد الافتتاحي
        if (smallSaleResult.cashbox.current_balance <= smallSaleResult.cashbox.initial_balance) {
          console.log('✅ الرصيد الحالي لا يتجاوز الرصيد الافتتاحي (صحيح)');
        } else {
          console.error(`❌ الرصيد الحالي (${smallSaleResult.cashbox.current_balance}) يتجاوز الرصيد الافتتاحي (${smallSaleResult.cashbox.initial_balance})!`);
        }
        
        // التحقق من أن الأرباح تم تحديثها بشكل صحيح
        const expectedProfit = 1000; // المبيعات - المشتريات = 1000 - 0 = 1000
        if (smallSaleResult.cashbox.profit_total === expectedProfit) {
          console.log('✅ الأرباح تم تحديثها بشكل صحيح');
        } else {
          console.error(`❌ خطأ في حساب الأرباح! المتوقع: ${expectedProfit}, الفعلي: ${smallSaleResult.cashbox.profit_total}`);
        }
      } else {
        console.error('❌ فشل في تسجيل عملية البيع الصغيرة:', smallSaleResult.error);
        return;
      }
      
      // 3. اختبار عملية بيع كبيرة (تتجاوز الرصيد الافتتاحي)
      console.log('\n3️⃣ اختبار عملية بيع كبيرة (مبلغ 6000 - تتجاوز الرصيد الافتتاحي)...');
      const largeSaleTransaction = {
        type: 'sale',
        amount: 6000,
        source: 'test',
        notes: 'بيع كبير يتجاوز الرصيد الافتتاحي',
        user_id: 1
      };
      
      const largeSaleResult = await window.api.cashbox.addTransaction(largeSaleTransaction);
      if (largeSaleResult.success) {
        console.log('✅ تم تسجيل عملية البيع الكبيرة');
        console.log('📊 الخزينة بعد البيع الكبير:', {
          initial_balance: largeSaleResult.cashbox.initial_balance,
          current_balance: largeSaleResult.cashbox.current_balance,
          sales_total: largeSaleResult.cashbox.sales_total,
          profit_total: largeSaleResult.cashbox.profit_total,
          expected_current_balance: 5000, // يجب أن يبقى 5000 (لا يتجاوز الرصيد الافتتاحي)
          expected_sales_total: 1000 + 6000, // 7000
          expected_profit_total: 7000 // المبيعات - المشتريات = 7000 - 0 = 7000
        });
        
        // التحقق من أن الرصيد الحالي لا يتجاوز الرصيد الافتتاحي
        if (largeSaleResult.cashbox.current_balance <= largeSaleResult.cashbox.initial_balance) {
          console.log('✅ الرصيد الحالي لا يتجاوز الرصيد الافتتاحي حتى بعد البيع الكبير (صحيح)');
        } else {
          console.error(`❌ الرصيد الحالي (${largeSaleResult.cashbox.current_balance}) يتجاوز الرصيد الافتتاحي (${largeSaleResult.cashbox.initial_balance})!`);
        }
        
        // التحقق من أن إجمالي المبيعات تم تحديثه بشكل صحيح
        const expectedSalesTotal = 7000;
        if (largeSaleResult.cashbox.sales_total === expectedSalesTotal) {
          console.log('✅ إجمالي المبيعات تم تحديثه بشكل صحيح');
        } else {
          console.error(`❌ خطأ في إجمالي المبيعات! المتوقع: ${expectedSalesTotal}, الفعلي: ${largeSaleResult.cashbox.sales_total}`);
        }
        
        // التحقق من أن الأرباح تم تحديثها بشكل صحيح
        const expectedProfit = 7000; // المبيعات - المشتريات = 7000 - 0 = 7000
        if (largeSaleResult.cashbox.profit_total === expectedProfit) {
          console.log('✅ الأرباح تم تحديثها بشكل صحيح (المبلغ الزائد ذهب للأرباح)');
        } else {
          console.error(`❌ خطأ في حساب الأرباح! المتوقع: ${expectedProfit}, الفعلي: ${largeSaleResult.cashbox.profit_total}`);
        }
      } else {
        console.error('❌ فشل في تسجيل عملية البيع الكبيرة:', largeSaleResult.error);
        return;
      }
      
      // 4. اختبار عملية شراء (يجب أن تقلل الرصيد الحالي)
      console.log('\n4️⃣ اختبار عملية شراء (مبلغ 2000)...');
      const purchaseTransaction = {
        type: 'purchase',
        amount: 2000,
        source: 'test',
        notes: 'شراء لاختبار تأثيره على الرصيد',
        user_id: 1
      };
      
      const purchaseResult = await window.api.cashbox.addTransaction(purchaseTransaction);
      if (purchaseResult.success) {
        console.log('✅ تم تسجيل عملية الشراء');
        console.log('📊 الخزينة بعد الشراء:', {
          initial_balance: purchaseResult.cashbox.initial_balance,
          current_balance: purchaseResult.cashbox.current_balance,
          sales_total: purchaseResult.cashbox.sales_total,
          purchases_total: purchaseResult.cashbox.purchases_total,
          profit_total: purchaseResult.cashbox.profit_total,
          expected_current_balance: 5000 - 2000, // 3000
          expected_purchases_total: 2000,
          expected_profit_total: 7000 - 2000 // 5000
        });
        
        // التحقق من أن الرصيد الحالي تم خصم مبلغ الشراء منه
        const expectedCurrentBalance = 3000;
        if (purchaseResult.cashbox.current_balance === expectedCurrentBalance) {
          console.log('✅ تم خصم مبلغ الشراء من الرصيد الحالي بشكل صحيح');
        } else {
          console.error(`❌ خطأ في خصم مبلغ الشراء! المتوقع: ${expectedCurrentBalance}, الفعلي: ${purchaseResult.cashbox.current_balance}`);
        }
        
        // التحقق من أن الأرباح تم تحديثها بشكل صحيح
        const expectedProfit = 5000; // المبيعات - المشتريات = 7000 - 2000 = 5000
        if (purchaseResult.cashbox.profit_total === expectedProfit) {
          console.log('✅ الأرباح تم تحديثها بشكل صحيح بعد الشراء');
        } else {
          console.error(`❌ خطأ في حساب الأرباح بعد الشراء! المتوقع: ${expectedProfit}, الفعلي: ${purchaseResult.cashbox.profit_total}`);
        }
      } else {
        console.error('❌ فشل في تسجيل عملية الشراء:', purchaseResult.error);
        return;
      }
      
      // 5. اختبار عملية بيع أخرى بعد الشراء
      console.log('\n5️⃣ اختبار عملية بيع أخرى بعد الشراء (مبلغ 3000)...');
      const finalSaleTransaction = {
        type: 'sale',
        amount: 3000,
        source: 'test',
        notes: 'بيع نهائي لاختبار القيد',
        user_id: 1
      };
      
      const finalSaleResult = await window.api.cashbox.addTransaction(finalSaleTransaction);
      if (finalSaleResult.success) {
        console.log('✅ تم تسجيل عملية البيع النهائية');
        console.log('📊 الخزينة النهائية:', {
          initial_balance: finalSaleResult.cashbox.initial_balance,
          current_balance: finalSaleResult.cashbox.current_balance,
          sales_total: finalSaleResult.cashbox.sales_total,
          purchases_total: finalSaleResult.cashbox.purchases_total,
          profit_total: finalSaleResult.cashbox.profit_total,
          expected_current_balance: 5000, // يجب أن يبقى 5000 (لا يتجاوز الرصيد الافتتاحي)
          expected_sales_total: 7000 + 3000, // 10000
          expected_profit_total: 10000 - 2000 // 8000
        });
        
        // التحقق من أن الرصيد الحالي لا يتجاوز الرصيد الافتتاحي
        if (finalSaleResult.cashbox.current_balance <= finalSaleResult.cashbox.initial_balance) {
          console.log('✅ الرصيد الحالي لا يتجاوز الرصيد الافتتاحي في النهاية (صحيح)');
        } else {
          console.error(`❌ الرصيد الحالي (${finalSaleResult.cashbox.current_balance}) يتجاوز الرصيد الافتتاحي (${finalSaleResult.cashbox.initial_balance}) في النهاية!`);
        }
      } else {
        console.error('❌ فشل في تسجيل عملية البيع النهائية:', finalSaleResult.error);
        return;
      }
      
      // 6. التحقق من الحالة النهائية
      console.log('\n6️⃣ التحقق من الحالة النهائية للخزينة...');
      const finalCashbox = await window.api.cashbox.getCashbox();
      console.log('📊 الحالة النهائية للخزينة:', finalCashbox);
      
      // التحقق من صحة جميع الحسابات النهائية
      const finalExpectedValues = {
        initial_balance: 5000,
        current_balance: 5000, // يجب أن يبقى مساوياً للرصيد الافتتاحي
        sales_total: 10000, // 1000 + 6000 + 3000
        purchases_total: 2000,
        profit_total: 8000 // 10000 - 2000
      };
      
      console.log('\n📋 مقارنة النتائج النهائية:');
      let allTestsPassed = true;
      
      for (const [key, expectedValue] of Object.entries(finalExpectedValues)) {
        const actualValue = finalCashbox[key];
        const isCorrect = actualValue === expectedValue;
        console.log(`${key}: متوقع ${expectedValue}, فعلي ${actualValue} ${isCorrect ? '✅' : '❌'}`);
        if (!isCorrect) allTestsPassed = false;
      }
      
      // الاختبار الأهم: التأكد من أن الرصيد الحالي لا يتجاوز الرصيد الافتتاحي
      const constraintPassed = finalCashbox.current_balance <= finalCashbox.initial_balance;
      console.log(`\n🔒 قيد الرصيد الحالي: ${constraintPassed ? '✅ يعمل بشكل صحيح' : '❌ لا يعمل!'}`);
      
      // النتيجة النهائية
      if (allTestsPassed && constraintPassed) {
        console.log('\n🎉 جميع الاختبارات نجحت! تم إصلاح قيد الرصيد الحالي بنجاح');
        console.log('✅ الرصيد الحالي لا يتجاوز الرصيد الافتتاحي أثناء عمليات البيع');
        console.log('✅ المبلغ الزائد يذهب تلقائياً إلى الأرباح');
        console.log('✅ القيد يعمل في الوقت الفعلي وليس فقط عند إعادة تشغيل التطبيق');
      } else {
        console.log('\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الإصلاحات');
      }
      
    } catch (error) {
      console.error('❌ خطأ في تشغيل اختبار قيد الرصيد الحالي:', error);
    }
  }
  
  // تشغيل الاختبار
  testBalanceConstraintFix();
  
} else {
  console.error('❌ window.api غير متوفر');
  console.log('💡 يجب تشغيل هذا السكريبت من داخل التطبيق');
  console.log('');
  console.log('📋 لتشغيل الاختبار:');
  console.log('1. افتح التطبيق');
  console.log('2. افتح وحدة التحكم (F12)');
  console.log('3. انسخ والصق محتوى هذا الملف');
}
