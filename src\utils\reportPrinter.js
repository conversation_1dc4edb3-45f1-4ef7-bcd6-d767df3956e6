/**
 * وحدة مساعدة لطباعة التقارير
 * تقوم بإنشاء تقارير PDF احترافية من البيانات الفعلية
 */

import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';
import html2canvas from 'html2canvas';
import './pdfLibrarySetup';
import { setupArabicSupport } from './arabicPdfSupport';

/**
 * طباعة تقرير المخزون
 * @param {Array} inventory - بيانات المخزون
 * @param {Object} settings - إعدادات الطباعة
 */
export function printInventoryReport(inventory, settings = {}) {
  try {
    if (!inventory || inventory.length === 0) {
      console.error('لا توجد بيانات للطباعة');
      alert('لا توجد بيانات للطباعة');
      return;
    }

    // إنشاء مستند PDF جديد
    // استخدام المكتبة المسجلة في النافذة العالمية إذا كانت متوفرة
    const jsPDFLib = window.jsPDF || jsPDF;
    const doc = new jsPDFLib({
      orientation: 'landscape',
      unit: 'mm',
      format: 'a4',
      putOnlyUsedFonts: true,
      compress: true
    });

    // إعداد دعم اللغة العربية
    setupArabicSupport(doc);

    // الحصول على أبعاد الصفحة
    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();

    // إعدادات الشركة
    const companyName = settings.companyName || 'شركة اتش قروب';
    const companyAddress = settings.companyAddress || 'للتصميم و تصنيع الأثاث والديكورات';
    const companyLogo = settings.companyLogo || '';
    const reportDate = new Date().toLocaleDateString('ar-SA');

    // إضافة معلومات الشركة
    doc.setFontSize(18);
    doc.text(companyName, pageWidth / 2, 15, { align: 'center' });
    doc.setFontSize(12);
    doc.text(companyAddress, pageWidth / 2, 22, { align: 'center' });
    doc.setFontSize(10);
    doc.text(`تقرير المخزون - ${reportDate}`, pageWidth / 2, 30, { align: 'center' });

    // إضافة الشعار إذا كان متوفراً
    if (companyLogo) {
      try {
        // التحقق من نوع الصورة (Data URL)
        if (typeof companyLogo === 'string' && companyLogo.startsWith('data:image/')) {
          // استخراج نوع الصورة من Data URL
          const imageType = companyLogo.split(';')[0].split('/')[1].toUpperCase();
          doc.addImage(companyLogo, imageType, 15, 10, 25, 25);
        } else {
          console.warn('تنسيق الشعار غير صالح، يجب أن يكون Data URL');
        }
      } catch (logoError) {
        console.error('خطأ في إضافة الشعار:', logoError);
      }
    }

    // إعداد بيانات الجدول
    const tableColumn = ['الإجمالي', 'سعر البيع', 'متوسط السعر', 'الحد الأدنى', 'الكمية', 'وحدة القياس', 'اسم الصنف', '#'];
    const tableRows = inventory.map((item, index) => {
      const totalValue = (item.current_quantity * item.avg_price).toFixed(2);
      return [
        totalValue,
        item.selling_price ? item.selling_price.toFixed(2) : '0.00',
        item.avg_price ? item.avg_price.toFixed(2) : '0.00',
        item.minimum_quantity || '0',
        item.current_quantity || '0',
        item.unit || '-',
        item.name,
        (index + 1).toString()
      ];
    });

    // إنشاء الجدول باستخدام autoTable المستورد
    autoTable(doc, {
      head: [tableColumn],
      body: tableRows,
      startY: 40,
      theme: 'grid',
      styles: {
        font: 'helvetica',
        fontSize: 8,
        cellPadding: 2,
        halign: 'right',
        valign: 'middle',
        lineWidth: 0.1
      },
      headStyles: {
        fillColor: [41, 128, 185],
        textColor: 255,
        fontStyle: 'bold',
        halign: 'center'
      },
      alternateRowStyles: {
        fillColor: [240, 240, 240]
      },
      margin: { top: 40 }
    });

    // إضافة ملخص المخزون
    const finalY = doc.lastAutoTable.finalY || 40;
    doc.setFontSize(10);
    doc.text(`إجمالي الأصناف: ${inventory.length}`, 15, finalY + 10);

    // حساب إجمالي قيمة المخزون
    const totalInventoryValue = inventory.reduce((total, item) => {
      return total + (item.current_quantity * item.avg_price);
    }, 0);
    doc.text(`إجمالي قيمة المخزون: ${totalInventoryValue.toFixed(2)}`, 15, finalY + 18);

    // إضافة تذييل الصفحة
    doc.setFontSize(8);
    doc.text(`${companyName} © ${new Date().getFullYear()} | تم إنشاء هذا التقرير في ${new Date().toLocaleString('ar-SA')}`, pageWidth / 2, pageHeight - 10, { align: 'center' });

    // حفظ الملف أو طباعته
    if (settings.save) {
      doc.save(`تقرير_المخزون_${new Date().toISOString().split('T')[0]}.pdf`);
    } else {
      doc.autoPrint();
      window.open(doc.output('bloburl'), '_blank');
    }
  } catch (error) {
    console.error('خطأ في طباعة تقرير المخزون:', error);
    alert('حدث خطأ أثناء طباعة تقرير المخزون');
  }
}

/**
 * طباعة تقرير المعاملات
 * @param {Array} transactions - بيانات المعاملات
 * @param {Object} settings - إعدادات الطباعة
 */
export function printTransactionsReport(transactions, settings = {}) {
  try {
    if (!transactions || transactions.length === 0) {
      console.error('لا توجد بيانات للطباعة');
      alert('لا توجد بيانات للطباعة');
      return;
    }

    // إنشاء مستند PDF جديد
    // استخدام المكتبة المسجلة في النافذة العالمية إذا كانت متوفرة
    const jsPDFLib = window.jsPDF || jsPDF;
    const doc = new jsPDFLib({
      orientation: 'landscape',
      unit: 'mm',
      format: 'a4',
      putOnlyUsedFonts: true,
      compress: true
    });

    // إعداد دعم اللغة العربية
    setupArabicSupport(doc);

    // الحصول على أبعاد الصفحة
    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();

    // إعدادات الشركة
    const companyName = settings.companyName || 'شركة اتش قروب';
    const companyAddress = settings.companyAddress || 'للتصميم و تصنيع الأثاث والديكورات';
    const companyLogo = settings.companyLogo || '';
    const reportDate = new Date().toLocaleDateString('ar-SA');

    // إضافة معلومات الشركة
    doc.setFontSize(18);
    doc.text(companyName, pageWidth / 2, 15, { align: 'center' });
    doc.setFontSize(12);
    doc.text(companyAddress, pageWidth / 2, 22, { align: 'center' });
    doc.setFontSize(10);
    doc.text(`تقرير المعاملات - ${reportDate}`, pageWidth / 2, 30, { align: 'center' });

    // إضافة الشعار إذا كان متوفراً
    if (companyLogo) {
      try {
        // التحقق من نوع الصورة (Data URL)
        if (typeof companyLogo === 'string' && companyLogo.startsWith('data:image/')) {
          // استخراج نوع الصورة من Data URL
          const imageType = companyLogo.split(';')[0].split('/')[1].toUpperCase();
          doc.addImage(companyLogo, imageType, 15, 10, 25, 25);
        } else {
          console.warn('تنسيق الشعار غير صالح، يجب أن يكون Data URL');
        }
      } catch (logoError) {
        console.error('خطأ في إضافة الشعار:', logoError);
      }
    }

    // تنسيق التاريخ
    const formatDate = (dateString) => {
      const date = new Date(dateString);
      return date.toLocaleDateString('ar-SA');
    };

    // إعداد بيانات الجدول
    const tableColumn = ['الإجمالي', 'السعر', 'الكمية', 'العميل', 'نوع المعاملة', 'التاريخ', 'رقم المعاملة', '#'];
    const tableRows = transactions.map((transaction, index) => {
      return [
        transaction.total_price ? transaction.total_price.toFixed(2) : '0.00',
        transaction.price ? transaction.price.toFixed(2) : '0.00',
        transaction.quantity || '1',
        transaction.customer_name || '-',
        transaction.transaction_type === 'sale' ? 'بيع' :
          transaction.transaction_type === 'purchase' ? 'شراء' :
          transaction.transaction_type === 'return' ? 'إرجاع' : transaction.transaction_type,
        formatDate(transaction.transaction_date),
        transaction.transaction_id || `TRX-${transaction.id}`,
        (index + 1).toString()
      ];
    });

    // إنشاء الجدول باستخدام autoTable المستورد
    autoTable(doc, {
      head: [tableColumn],
      body: tableRows,
      startY: 40,
      theme: 'grid',
      styles: {
        font: 'helvetica',
        fontSize: 8,
        cellPadding: 2,
        halign: 'right',
        valign: 'middle',
        lineWidth: 0.1
      },
      headStyles: {
        fillColor: [41, 128, 185],
        textColor: 255,
        fontStyle: 'bold',
        halign: 'center'
      },
      alternateRowStyles: {
        fillColor: [240, 240, 240]
      },
      margin: { top: 40 }
    });

    // إضافة ملخص المعاملات
    const finalY = doc.lastAutoTable.finalY || 40;
    doc.setFontSize(10);
    doc.text(`إجمالي المعاملات: ${transactions.length}`, 15, finalY + 10);

    // حساب إجماليات المعاملات حسب النوع
    const sales = transactions.filter(t => t.transaction_type === 'sale');
    const purchases = transactions.filter(t => t.transaction_type === 'purchase');
    const returns = transactions.filter(t => t.transaction_type === 'return');

    const totalSales = sales.reduce((total, t) => total + (t.total_price || 0), 0);
    const totalPurchases = purchases.reduce((total, t) => total + (t.total_price || 0), 0);
    const totalReturns = returns.reduce((total, t) => total + (t.total_price || 0), 0);

    doc.text(`إجمالي المبيعات: ${totalSales.toFixed(2)}`, 15, finalY + 18);
    doc.text(`إجمالي المشتريات: ${totalPurchases.toFixed(2)}`, 15, finalY + 26);
    doc.text(`إجمالي الإرجاعات: ${totalReturns.toFixed(2)}`, 15, finalY + 34);

    // إضافة تذييل الصفحة
    doc.setFontSize(8);
    doc.text(`${companyName} © ${new Date().getFullYear()} | تم إنشاء هذا التقرير في ${new Date().toLocaleString('ar-SA')}`, pageWidth / 2, pageHeight - 10, { align: 'center' });

    // حفظ الملف أو طباعته
    if (settings.save) {
      doc.save(`تقرير_المعاملات_${new Date().toISOString().split('T')[0]}.pdf`);
    } else {
      doc.autoPrint();
      window.open(doc.output('bloburl'), '_blank');
    }
  } catch (error) {
    console.error('خطأ في طباعة تقرير المعاملات:', error);
    alert('حدث خطأ أثناء طباعة تقرير المعاملات');
  }
}

/**
 * طباعة تقرير الأرباح
 * @param {Object} profitData - بيانات الأرباح
 * @param {Array} transactions - بيانات المعاملات
 * @param {Object} settings - إعدادات الطباعة
 */
export function printProfitsReport(profitData, transactions, settings = {}) {
  try {
    if (!profitData) {
      console.error('لا توجد بيانات للطباعة');
      alert('لا توجد بيانات للطباعة');
      return;
    }

    // إنشاء مستند PDF جديد
    // استخدام المكتبة المسجلة في النافذة العالمية إذا كانت متوفرة
    const jsPDFLib = window.jsPDF || jsPDF;
    const doc = new jsPDFLib({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4',
      putOnlyUsedFonts: true,
      compress: true
    });

    // إعداد دعم اللغة العربية
    setupArabicSupport(doc);

    // الحصول على أبعاد الصفحة
    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();

    // إعدادات الشركة
    const companyName = settings.companyName || 'شركة اتش قروب';
    const companyAddress = settings.companyAddress || 'للتصميم و تصنيع الأثاث والديكورات';
    const companyLogo = settings.companyLogo || '';
    const reportDate = new Date().toLocaleDateString('ar-SA');

    // إضافة معلومات الشركة
    doc.setFontSize(18);
    doc.text(companyName, pageWidth / 2, 15, { align: 'center' });
    doc.setFontSize(12);
    doc.text(companyAddress, pageWidth / 2, 22, { align: 'center' });
    doc.setFontSize(10);
    doc.text(`تقرير الأرباح - ${reportDate}`, pageWidth / 2, 30, { align: 'center' });

    // إضافة الشعار إذا كان متوفراً
    if (companyLogo) {
      try {
        // التحقق من نوع الصورة (Data URL)
        if (typeof companyLogo === 'string' && companyLogo.startsWith('data:image/')) {
          // استخراج نوع الصورة من Data URL
          const imageType = companyLogo.split(';')[0].split('/')[1].toUpperCase();
          doc.addImage(companyLogo, imageType, 15, 10, 25, 25);
        } else {
          console.warn('تنسيق الشعار غير صالح، يجب أن يكون Data URL');
        }
      } catch (logoError) {
        console.error('خطأ في إضافة الشعار:', logoError);
      }
    }

    // إضافة ملخص الأرباح
    doc.setFontSize(12);
    doc.text('ملخص الأرباح', pageWidth / 2, 40, { align: 'center' });

    // إنشاء جدول ملخص الأرباح
    const profitSummaryData = [
      ['الربع الأول', profitData.quarterly ? profitData.quarterly.toFixed(2) : '0.00'],
      ['النصف الأول', profitData.halfYearly ? profitData.halfYearly.toFixed(2) : '0.00'],
      ['ثلاثة أرباع العام', profitData.threeQuarters ? profitData.threeQuarters.toFixed(2) : '0.00'],
      ['العام كامل', profitData.yearly ? profitData.yearly.toFixed(2) : '0.00']
    ];

    autoTable(doc, {
      head: [['الفترة', 'الربح (ر.س)']],
      body: profitSummaryData,
      startY: 45,
      theme: 'grid',
      styles: {
        font: 'helvetica',
        fontSize: 10,
        cellPadding: 3,
        halign: 'right',
        valign: 'middle',
        lineWidth: 0.1
      },
      headStyles: {
        fillColor: [41, 128, 185],
        textColor: 255,
        fontStyle: 'bold',
        halign: 'center'
      },
      alternateRowStyles: {
        fillColor: [240, 240, 240]
      },
      margin: { top: 40 }
    });

    // إضافة تفاصيل المعاملات إذا كانت متوفرة
    if (transactions && transactions.length > 0) {
      const finalY1 = doc.lastAutoTable.finalY || 45;
      doc.setFontSize(12);
      doc.text('تفاصيل المعاملات المربحة', pageWidth / 2, finalY1 + 10, { align: 'center' });

      // تنسيق التاريخ
      const formatDate = (dateString) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA');
      };

      // إعداد بيانات الجدول
      const tableColumn = ['الربح', 'الإجمالي', 'السعر', 'الكمية', 'الصنف', 'التاريخ', 'رقم المعاملة', '#'];
      const tableRows = transactions
        .filter(t => t.profit > 0)
        .map((transaction, index) => {
          return [
            transaction.profit ? transaction.profit.toFixed(2) : '0.00',
            transaction.total_price ? transaction.total_price.toFixed(2) : '0.00',
            transaction.price ? transaction.price.toFixed(2) : '0.00',
            transaction.quantity || '1',
            transaction.item_name || '-',
            formatDate(transaction.transaction_date),
            transaction.transaction_id || `TRX-${transaction.id}`,
            (index + 1).toString()
          ];
        });

      // إنشاء الجدول
      autoTable(doc, {
        head: [tableColumn],
        body: tableRows,
        startY: finalY1 + 15,
        theme: 'grid',
        styles: {
          font: 'helvetica',
          fontSize: 8,
          cellPadding: 2,
          halign: 'right',
          valign: 'middle',
          lineWidth: 0.1
        },
        headStyles: {
          fillColor: [41, 128, 185],
          textColor: 255,
          fontStyle: 'bold',
          halign: 'center'
        },
        alternateRowStyles: {
          fillColor: [240, 240, 240]
        },
        margin: { top: 40 }
      });
    }

    // إضافة تذييل الصفحة
    doc.setFontSize(8);
    doc.text(`${companyName} © ${new Date().getFullYear()} | تم إنشاء هذا التقرير في ${new Date().toLocaleString('ar-SA')}`, pageWidth / 2, pageHeight - 10, { align: 'center' });

    // حفظ الملف أو طباعته
    if (settings.save) {
      doc.save(`تقرير_الأرباح_${new Date().toISOString().split('T')[0]}.pdf`);
    } else {
      doc.autoPrint();
      window.open(doc.output('bloburl'), '_blank');
    }
  } catch (error) {
    console.error('خطأ في طباعة تقرير الأرباح:', error);
    alert('حدث خطأ أثناء طباعة تقرير الأرباح');
  }
}

/**
 * طباعة تقرير العملاء
 * @param {Array} customers - بيانات العملاء
 * @param {Object} stats - إحصائيات العملاء
 * @param {Object} settings - إعدادات الطباعة
 */
export function printCustomersReport(customers, stats, settings = {}) {
  try {
    if (!customers || customers.length === 0) {
      console.error('لا توجد بيانات للطباعة');
      alert('لا توجد بيانات للطباعة');
      return;
    }

    // إنشاء مستند PDF جديد
    // استخدام المكتبة المسجلة في النافذة العالمية إذا كانت متوفرة
    const jsPDFLib = window.jsPDF || jsPDF;
    const doc = new jsPDFLib({
      orientation: 'landscape',
      unit: 'mm',
      format: 'a4',
      putOnlyUsedFonts: true,
      compress: true
    });

    // إعداد دعم اللغة العربية
    setupArabicSupport(doc);

    // الحصول على أبعاد الصفحة
    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();

    // إعدادات الشركة
    const companyName = settings.companyName || 'شركة اتش قروب';
    const companyAddress = settings.companyAddress || 'للتصميم و تصنيع الأثاث والديكورات';
    const companyLogo = settings.companyLogo || '';
    const reportDate = new Date().toLocaleDateString('ar-SA');

    // إضافة معلومات الشركة
    doc.setFontSize(18);
    doc.text(companyName, pageWidth / 2, 15, { align: 'center' });
    doc.setFontSize(12);
    doc.text(companyAddress, pageWidth / 2, 22, { align: 'center' });
    doc.setFontSize(10);
    doc.text(`تقرير العملاء - ${reportDate}`, pageWidth / 2, 30, { align: 'center' });

    // إضافة الشعار إذا كان متوفراً
    if (companyLogo) {
      try {
        // التحقق من نوع الصورة (Data URL)
        if (typeof companyLogo === 'string' && companyLogo.startsWith('data:image/')) {
          // استخراج نوع الصورة من Data URL
          const imageType = companyLogo.split(';')[0].split('/')[1].toUpperCase();
          doc.addImage(companyLogo, imageType, 15, 10, 25, 25);
        } else {
          console.warn('تنسيق الشعار غير صالح، يجب أن يكون Data URL');
        }
      } catch (logoError) {
        console.error('خطأ في إضافة الشعار:', logoError);
      }
    }

    // تنسيق التاريخ
    const formatDate = (dateString) => {
      if (!dateString) return '-';
      const date = new Date(dateString);
      return date.toLocaleDateString('ar-SA');
    };

    // إعداد بيانات الجدول
    const tableColumn = ['آخر عملية شراء', 'عدد الفواتير', 'عدد المعاملات', 'إجمالي المشتريات', 'البريد الإلكتروني', 'رقم الهاتف', 'اسم العميل', '#'];
    const tableRows = customers.map((customer, index) => {
      return [
        formatDate(customer.last_purchase_date),
        customer.invoice_count || '0',
        customer.transaction_count || '0',
        customer.total_sales ? customer.total_sales.toFixed(2) : '0.00',
        customer.email || '-',
        customer.phone || '-',
        customer.customer_name,
        (index + 1).toString()
      ];
    });

    // إنشاء الجدول
    autoTable(doc, {
      head: [tableColumn],
      body: tableRows,
      startY: 40,
      theme: 'grid',
      styles: {
        font: 'helvetica',
        fontSize: 8,
        cellPadding: 2,
        halign: 'right',
        valign: 'middle',
        lineWidth: 0.1
      },
      headStyles: {
        fillColor: [41, 128, 185],
        textColor: 255,
        fontStyle: 'bold',
        halign: 'center'
      },
      alternateRowStyles: {
        fillColor: [240, 240, 240]
      },
      margin: { top: 40 }
    });

    // إضافة ملخص العملاء
    if (stats) {
      const finalY = doc.lastAutoTable.finalY || 40;
      doc.setFontSize(10);
      doc.text(`إجمالي العملاء: ${stats.totalCustomers || customers.length}`, 15, finalY + 10);
      doc.text(`إجمالي المبيعات: ${stats.totalSales ? stats.totalSales.toFixed(2) : '0.00'}`, 15, finalY + 18);
      doc.text(`إجمالي الأرباح: ${stats.totalProfit ? stats.totalProfit.toFixed(2) : '0.00'}`, 15, finalY + 26);
      doc.text(`متوسط المبيعات لكل عميل: ${stats.averageSalePerCustomer ? stats.averageSalePerCustomer.toFixed(2) : '0.00'}`, 15, finalY + 34);
    }

    // إضافة تذييل الصفحة
    doc.setFontSize(8);
    doc.text(`${companyName} © ${new Date().getFullYear()} | تم إنشاء هذا التقرير في ${new Date().toLocaleString('ar-SA')}`, pageWidth / 2, pageHeight - 10, { align: 'center' });

    // حفظ الملف أو طباعته
    if (settings.save) {
      doc.save(`تقرير_العملاء_${new Date().toISOString().split('T')[0]}.pdf`);
    } else {
      doc.autoPrint();
      window.open(doc.output('bloburl'), '_blank');
    }
  } catch (error) {
    console.error('خطأ في طباعة تقرير العملاء:', error);
    alert('حدث خطأ أثناء طباعة تقرير العملاء');
  }
}

/**
 * طباعة تقرير مالي شامل
 * @param {Object} financialData - البيانات المالية
 * @param {Object} settings - إعدادات الطباعة
 */
export function printFinancialReport(financialData, settings = {}) {
  try {
    if (!financialData) {
      console.error('لا توجد بيانات للطباعة');
      alert('لا توجد بيانات للطباعة');
      return;
    }

    // إنشاء مستند PDF جديد
    // استخدام المكتبة المسجلة في النافذة العالمية إذا كانت متوفرة
    const jsPDFLib = window.jsPDF || jsPDF;
    const doc = new jsPDFLib({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4',
      putOnlyUsedFonts: true,
      compress: true
    });

    // إعداد دعم اللغة العربية
    setupArabicSupport(doc);

    // الحصول على أبعاد الصفحة
    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();

    // إعدادات الشركة
    const companyName = settings.companyName || 'شركة اتش قروب';
    const companyAddress = settings.companyAddress || 'للتصميم و تصنيع الأثاث والديكورات';
    const companyLogo = settings.companyLogo || '';
    const reportDate = new Date().toLocaleDateString('ar-SA');

    // إضافة معلومات الشركة
    doc.setFontSize(18);
    doc.text(companyName, pageWidth / 2, 15, { align: 'center' });
    doc.setFontSize(12);
    doc.text(companyAddress, pageWidth / 2, 22, { align: 'center' });
    doc.setFontSize(10);
    doc.text(`التقرير المالي الشامل - ${reportDate}`, pageWidth / 2, 30, { align: 'center' });

    // إضافة الشعار إذا كان متوفراً
    if (companyLogo) {
      try {
        // التحقق من نوع الصورة (Data URL)
        if (typeof companyLogo === 'string' && companyLogo.startsWith('data:image/')) {
          // استخراج نوع الصورة من Data URL
          const imageType = companyLogo.split(';')[0].split('/')[1].toUpperCase();
          doc.addImage(companyLogo, imageType, 15, 10, 25, 25);
        } else {
          console.warn('تنسيق الشعار غير صالح، يجب أن يكون Data URL');
        }
      } catch (logoError) {
        console.error('خطأ في إضافة الشعار:', logoError);
      }
    }

    // إضافة ملخص مالي
    let yPosition = 40;

    doc.setFontSize(12);
    doc.text('الملخص المالي', pageWidth / 2, yPosition, { align: 'center' });
    yPosition += 10;

    // إنشاء جدول الملخص المالي
    const { transactions = [], cashboxReport = {}, profitValues = {} } = financialData;

    // حساب إجماليات المعاملات
    const sales = transactions.filter(t => t.transaction_type === 'sale');
    const purchases = transactions.filter(t => t.transaction_type === 'purchase');
    const returns = transactions.filter(t => t.transaction_type === 'return');

    const totalSales = sales.reduce((total, t) => total + (t.total_price || 0), 0);
    const totalPurchases = purchases.reduce((total, t) => total + (t.total_price || 0), 0);
    const totalReturns = returns.reduce((total, t) => total + (t.total_price || 0), 0);
    const netSales = totalSales - totalReturns;

    const summaryData = [
      ['إجمالي المبيعات', totalSales.toFixed(2)],
      ['إجمالي المشتريات', totalPurchases.toFixed(2)],
      ['إجمالي الإرجاعات', totalReturns.toFixed(2)],
      ['صافي المبيعات', netSales.toFixed(2)],
      ['إجمالي الأرباح', (profitValues.yearly || 0).toFixed(2)],
      ['نسبة الربح', netSales > 0 ? ((profitValues.yearly / netSales) * 100).toFixed(2) + '%' : '0%']
    ];

    autoTable(doc, {
      head: [['البند', 'القيمة (ر.س)']],
      body: summaryData,
      startY: yPosition,
      theme: 'grid',
      styles: {
        font: 'helvetica',
        fontSize: 10,
        cellPadding: 3,
        halign: 'right',
        valign: 'middle',
        lineWidth: 0.1
      },
      headStyles: {
        fillColor: [41, 128, 185],
        textColor: 255,
        fontStyle: 'bold',
        halign: 'center'
      },
      alternateRowStyles: {
        fillColor: [240, 240, 240]
      },
      margin: { top: 40 }
    });

    // إضافة تذييل الصفحة
    doc.setFontSize(8);
    doc.text(`${companyName} © ${new Date().getFullYear()} | تم إنشاء هذا التقرير في ${new Date().toLocaleString('ar-SA')}`, pageWidth / 2, pageHeight - 10, { align: 'center' });

    // حفظ الملف أو طباعته
    if (settings.save) {
      doc.save(`التقرير_المالي_${new Date().toISOString().split('T')[0]}.pdf`);
    } else {
      doc.autoPrint();
      window.open(doc.output('bloburl'), '_blank');
    }
  } catch (error) {
    console.error('خطأ في طباعة التقرير المالي:', error);
    alert('حدث خطأ أثناء طباعة التقرير المالي');
  }
}

/**
 * طباعة تقرير الأصناف الأكثر مبيعًا
 * @param {Array} topSellingItems - بيانات الأصناف الأكثر مبيعًا
 * @param {Object} settings - إعدادات الطباعة
 */
export function printTopSellingItemsReport(topSellingItems, settings = {}) {
  try {
    if (!topSellingItems || topSellingItems.length === 0) {
      console.error('لا توجد بيانات للطباعة');
      alert('لا توجد بيانات للطباعة');
      return;
    }

    // إنشاء مستند PDF جديد
    // استخدام المكتبة المسجلة في النافذة العالمية إذا كانت متوفرة
    const jsPDFLib = window.jsPDF || jsPDF;
    const doc = new jsPDFLib({
      orientation: 'landscape',
      unit: 'mm',
      format: 'a4',
      putOnlyUsedFonts: true,
      compress: true
    });

    // إعداد دعم اللغة العربية
    setupArabicSupport(doc);

    // الحصول على أبعاد الصفحة
    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();

    // إعدادات الشركة
    const companyName = settings.companyName || 'شركة اتش قروب';
    const companyAddress = settings.companyAddress || 'للتصميم و تصنيع الأثاث والديكورات';
    const companyLogo = settings.companyLogo || '';
    const reportDate = new Date().toLocaleDateString('ar-SA');

    // إضافة معلومات الشركة
    doc.setFontSize(18);
    doc.text(companyName, pageWidth / 2, 15, { align: 'center' });
    doc.setFontSize(12);
    doc.text(companyAddress, pageWidth / 2, 22, { align: 'center' });
    doc.setFontSize(10);
    doc.text(`تقرير الأصناف الأكثر مبيعًا - ${reportDate}`, pageWidth / 2, 30, { align: 'center' });

    // إضافة الشعار إذا كان متوفراً
    if (companyLogo) {
      try {
        // التحقق من نوع الصورة (Data URL)
        if (typeof companyLogo === 'string' && companyLogo.startsWith('data:image/')) {
          // استخراج نوع الصورة من Data URL
          const imageType = companyLogo.split(';')[0].split('/')[1].toUpperCase();
          doc.addImage(companyLogo, imageType, 15, 10, 25, 25);
        } else {
          console.warn('تنسيق الشعار غير صالح، يجب أن يكون Data URL');
        }
      } catch (logoError) {
        console.error('خطأ في إضافة الشعار:', logoError);
      }
    }

    // إعداد بيانات الجدول
    const tableColumn = ['نسبة الربح', 'إجمالي الربح', 'إجمالي المبيعات', 'الكمية المباعة', 'الكمية الحالية', 'وحدة القياس', 'اسم الصنف', '#'];
    const tableRows = topSellingItems.map((item, index) => {
      return [
        item.profit_margin ? item.profit_margin.toFixed(2) + '%' : '0%',
        item.total_profit ? item.total_profit.toFixed(2) : '0.00',
        item.total_sales ? item.total_sales.toFixed(2) : '0.00',
        item.total_quantity || '0',
        item.current_quantity || '0',
        item.item_unit || '-',
        item.item_name,
        (index + 1).toString()
      ];
    });

    // إنشاء الجدول
    autoTable(doc, {
      head: [tableColumn],
      body: tableRows,
      startY: 40,
      theme: 'grid',
      styles: {
        font: 'helvetica',
        fontSize: 8,
        cellPadding: 2,
        halign: 'right',
        valign: 'middle',
        lineWidth: 0.1
      },
      headStyles: {
        fillColor: [41, 128, 185],
        textColor: 255,
        fontStyle: 'bold',
        halign: 'center'
      },
      alternateRowStyles: {
        fillColor: [240, 240, 240]
      },
      margin: { top: 40 }
    });

    // إضافة ملخص التقرير
    const finalY = doc.lastAutoTable.finalY || 40;
    doc.setFontSize(10);
    doc.text(`إجمالي الأصناف: ${topSellingItems.length}`, 15, finalY + 10);

    // حساب إجماليات المبيعات والأرباح
    const totalSales = topSellingItems.reduce((total, item) => total + (item.total_sales || 0), 0);
    const totalProfit = topSellingItems.reduce((total, item) => total + (item.total_profit || 0), 0);
    const totalQuantity = topSellingItems.reduce((total, item) => total + (item.total_quantity || 0), 0);

    doc.text(`إجمالي المبيعات: ${totalSales.toFixed(2)}`, 15, finalY + 18);
    doc.text(`إجمالي الأرباح: ${totalProfit.toFixed(2)}`, 15, finalY + 26);
    doc.text(`إجمالي الكمية المباعة: ${totalQuantity}`, 15, finalY + 34);

    // إضافة تذييل الصفحة
    doc.setFontSize(8);
    doc.text(`${companyName} © ${new Date().getFullYear()} | تم إنشاء هذا التقرير في ${new Date().toLocaleString('ar-SA')}`, pageWidth / 2, pageHeight - 10, { align: 'center' });

    // حفظ الملف أو طباعته
    if (settings.save) {
      doc.save(`تقرير_الأصناف_الأكثر_مبيعًا_${new Date().toISOString().split('T')[0]}.pdf`);
    } else {
      doc.autoPrint();
      window.open(doc.output('bloburl'), '_blank');
    }
  } catch (error) {
    console.error('خطأ في طباعة تقرير الأصناف الأكثر مبيعًا:', error);
    alert('حدث خطأ أثناء طباعة تقرير الأصناف الأكثر مبيعًا');
  }
}

/**
 * طباعة تقرير الأصناف التي تحتاج إعادة طلب
 * @param {Array} lowStockItems - بيانات الأصناف التي تحتاج إعادة طلب
 * @param {Object} settings - إعدادات الطباعة
 */
export function printLowStockReport(lowStockItems, settings = {}) {
  try {
    if (!lowStockItems || lowStockItems.length === 0) {
      console.error('لا توجد بيانات للطباعة');
      alert('لا توجد بيانات للطباعة');
      return;
    }

    // إنشاء مستند PDF جديد
    // استخدام المكتبة المسجلة في النافذة العالمية إذا كانت متوفرة
    const jsPDFLib = window.jsPDF || jsPDF;
    const doc = new jsPDFLib({
      orientation: 'landscape',
      unit: 'mm',
      format: 'a4',
      putOnlyUsedFonts: true,
      compress: true
    });

    // إعداد دعم اللغة العربية
    setupArabicSupport(doc);

    // الحصول على أبعاد الصفحة
    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();

    // إعدادات الشركة
    const companyName = settings.companyName || 'شركة اتش قروب';
    const companyAddress = settings.companyAddress || 'للتصميم و تصنيع الأثاث والديكورات';
    const companyLogo = settings.companyLogo || '';
    const reportDate = new Date().toLocaleDateString('ar-SA');

    // إضافة معلومات الشركة
    doc.setFontSize(18);
    doc.text(companyName, pageWidth / 2, 15, { align: 'center' });
    doc.setFontSize(12);
    doc.text(companyAddress, pageWidth / 2, 22, { align: 'center' });
    doc.setFontSize(10);
    doc.text(`تقرير الأصناف التي تحتاج إعادة طلب - ${reportDate}`, pageWidth / 2, 30, { align: 'center' });

    // إضافة الشعار إذا كان متوفراً
    if (companyLogo) {
      try {
        // التحقق من نوع الصورة (Data URL)
        if (typeof companyLogo === 'string' && companyLogo.startsWith('data:image/')) {
          // استخراج نوع الصورة من Data URL
          const imageType = companyLogo.split(';')[0].split('/')[1].toUpperCase();
          doc.addImage(companyLogo, imageType, 15, 10, 25, 25);
        } else {
          console.warn('تنسيق الشعار غير صالح، يجب أن يكون Data URL');
        }
      } catch (logoError) {
        console.error('خطأ في إضافة الشعار:', logoError);
      }
    }

    // إعداد بيانات الجدول
    const tableColumn = ['الكمية المطلوبة', 'الأيام المتبقية', 'معدل الاستهلاك الشهري', 'الحد الأدنى', 'الكمية الحالية', 'وحدة القياس', 'اسم الصنف', '#'];
    const tableRows = lowStockItems.map((item, index) => {
      return [
        item.quantity_to_order || '0',
        item.days_remaining !== null ? item.days_remaining.toString() : '-',
        item.monthly_consumption || '0',
        item.minimum_quantity || '0',
        item.current_quantity || '0',
        item.unit || '-',
        item.name,
        (index + 1).toString()
      ];
    });

    // إنشاء الجدول
    autoTable(doc, {
      head: [tableColumn],
      body: tableRows,
      startY: 40,
      theme: 'grid',
      styles: {
        font: 'helvetica',
        fontSize: 8,
        cellPadding: 2,
        halign: 'right',
        valign: 'middle',
        lineWidth: 0.1
      },
      headStyles: {
        fillColor: [41, 128, 185],
        textColor: 255,
        fontStyle: 'bold',
        halign: 'center'
      },
      alternateRowStyles: {
        fillColor: [240, 240, 240]
      },
      margin: { top: 40 }
    });

    // إضافة ملخص التقرير
    const finalY = doc.lastAutoTable.finalY || 40;
    doc.setFontSize(10);

    // حساب عدد الأصناف التي نفذت الكمية
    const outOfStockItems = lowStockItems.filter(item => item.current_quantity === 0).length;

    // حساب عدد الأصناف تحت الحد الأدنى
    const belowMinimumItems = lowStockItems.filter(item => item.current_quantity > 0 && item.current_quantity <= item.minimum_quantity).length;

    doc.text(`إجمالي الأصناف التي تحتاج إعادة طلب: ${lowStockItems.length}`, 15, finalY + 10);
    doc.text(`أصناف نفذت الكمية: ${outOfStockItems}`, 15, finalY + 18);
    doc.text(`أصناف تحت الحد الأدنى: ${belowMinimumItems}`, 15, finalY + 26);

    // إضافة تذييل الصفحة
    doc.setFontSize(8);
    doc.text(`${companyName} © ${new Date().getFullYear()} | تم إنشاء هذا التقرير في ${new Date().toLocaleString('ar-SA')}`, pageWidth / 2, pageHeight - 10, { align: 'center' });

    // حفظ الملف أو طباعته
    if (settings.save) {
      doc.save(`تقرير_الأصناف_التي_تحتاج_إعادة_طلب_${new Date().toISOString().split('T')[0]}.pdf`);
    } else {
      doc.autoPrint();
      window.open(doc.output('bloburl'), '_blank');
    }
  } catch (error) {
    console.error('خطأ في طباعة تقرير الأصناف التي تحتاج إعادة طلب:', error);
    alert('حدث خطأ أثناء طباعة تقرير الأصناف التي تحتاج إعادة طلب');
  }
}

// تصدير الوظائف
export default {
  printInventoryReport,
  printTransactionsReport,
  printProfitsReport,
  printCustomersReport,
  printFinancialReport,
  printTopSellingItemsReport,
  printLowStockReport
};
