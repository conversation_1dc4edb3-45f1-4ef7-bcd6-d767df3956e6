/**
 * سكريبت إصلاح الأرباح من المعاملات الفعلية
 * يعيد حساب إجمالي الأرباح في الخزينة بناءً على الأرباح المحفوظة في المعاملات
 */

const Database = require('better-sqlite3');
const path = require('path');

// مسار قاعدة البيانات
const dbPath = path.join(__dirname, 'warehouse.db');

console.log('🔧 بدء إصلاح الأرباح من المعاملات الفعلية...');
console.log(`📁 مسار قاعدة البيانات: ${dbPath}`);

try {
  // فتح قاعدة البيانات
  const db = new Database(dbPath);
  
  console.log('✅ تم فتح قاعدة البيانات بنجاح');

  // الحصول على إجمالي الأرباح من معاملات البيع
  const salesProfitQuery = db.prepare(`
    SELECT SUM(profit) as total_sales_profit
    FROM transactions 
    WHERE transaction_type = 'sale'
      AND profit IS NOT NULL
  `);

  const salesResult = salesProfitQuery.get();
  const totalSalesProfit = salesResult?.total_sales_profit || 0;

  console.log(`💰 إجمالي أرباح المبيعات: ${totalSalesProfit}`);

  // الحصول على إجمالي الأرباح المفقودة من معاملات الإرجاع
  const returnsProfitQuery = db.prepare(`
    SELECT SUM(ABS(profit)) as total_returns_profit
    FROM transactions 
    WHERE transaction_type = 'return'
      AND profit IS NOT NULL
  `);

  const returnsResult = returnsProfitQuery.get();
  const totalReturnsProfit = returnsResult?.total_returns_profit || 0;

  console.log(`📉 إجمالي أرباح الإرجاعات: ${totalReturnsProfit}`);

  // حساب صافي الأرباح
  const netProfit = totalSalesProfit - totalReturnsProfit;

  console.log(`📊 صافي الأرباح: ${netProfit}`);

  // الحصول على الخزينة الحالية
  const getCashboxQuery = db.prepare('SELECT * FROM cashbox LIMIT 1');
  const currentCashbox = getCashboxQuery.get();

  if (!currentCashbox) {
    console.error('❌ لم يتم العثور على خزينة في قاعدة البيانات');
    process.exit(1);
  }

  console.log(`🏦 الأرباح الحالية في الخزينة: ${currentCashbox.profit_total}`);
  console.log(`🔄 الأرباح الجديدة المحسوبة: ${netProfit}`);
  console.log(`📈 الفرق: ${netProfit - (currentCashbox.profit_total || 0)}`);

  // تحديث الخزينة بالأرباح الصحيحة
  const updateCashboxQuery = db.prepare(`
    UPDATE cashbox 
    SET profit_total = ?,
        updated_at = ?
    WHERE id = ?
  `);

  const result = updateCashboxQuery.run(
    netProfit,
    new Date().toISOString(),
    currentCashbox.id
  );

  if (result.changes > 0) {
    console.log('✅ تم تحديث الأرباح في الخزينة بنجاح');
    
    // التحقق من النتيجة
    const verifyQuery = db.prepare('SELECT profit_total FROM cashbox WHERE id = ?');
    const verifyResult = verifyQuery.get(currentCashbox.id);
    
    console.log(`🎯 الأرباح النهائية في الخزينة: ${verifyResult.profit_total}`);
  } else {
    console.error('❌ فشل في تحديث الأرباح في الخزينة');
  }

  // إغلاق قاعدة البيانات
  db.close();
  
  console.log('\n🎉 تم إكمال إصلاح الأرباح بنجاح!');
  console.log('💡 يرجى إعادة تشغيل التطبيق لرؤية التغييرات');

} catch (error) {
  console.error('❌ خطأ في إصلاح الأرباح:', error);
  process.exit(1);
}
