<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تحديث خانات الأرباح</title>
    <style>
        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-results {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .info {
            color: #17a2b8;
        }
    </style>
</head>
<body>
    <h1>🧪 اختبار إصلاح تحديث خانات الأرباح</h1>
    
    <div class="test-container">
        <h2>📊 اختبارات تحديث الأرباح</h2>
        <button class="test-button" onclick="testSaleTransaction()">اختبار عملية بيع</button>
        <button class="test-button" onclick="testPurchaseTransaction()">اختبار عملية شراء</button>
        <button class="test-button" onclick="testDirectUpdate()">اختبار التحديث المباشر</button>
        <button class="test-button" onclick="testMultiSourceUpdate()">اختبار متعدد المصادر</button>
        <button class="test-button" onclick="runAllTests()">تشغيل جميع الاختبارات</button>
        <button class="test-button" onclick="clearResults()">مسح النتائج</button>
    </div>

    <div class="test-container">
        <h2>📈 حالة النظام</h2>
        <button class="test-button" onclick="checkSystemStatus()">فحص حالة النظام</button>
        <button class="test-button" onclick="checkProfitDisplays()">فحص خانات الأرباح</button>
        <button class="test-button" onclick="monitorEvents()">مراقبة الأحداث</button>
    </div>

    <div class="test-container">
        <h2>📝 نتائج الاختبارات</h2>
        <div id="testResults" class="test-results">
            <p class="info">جاهز لبدء الاختبارات...</p>
        </div>
    </div>

    <script>
        let eventMonitoring = false;
        
        function log(message, type = 'info') {
            const results = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString('ar-EG');
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            results.innerHTML += `<p class="${className}">[${timestamp}] ${message}</p>`;
            results.scrollTop = results.scrollHeight;
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '<p class="info">تم مسح النتائج...</p>';
        }

        // محاكاة تحديث الأرباح
        function simulateProfitUpdate(profitData, source = 'test') {
            log(`📊 محاكاة تحديث الأرباح من ${source}`, 'info');
            
            const events = [
                'profits-updated',
                'auto-profits-updated',
                'dashboard-profits-updated',
                'financial-profits-updated',
                'real-time-profits-updated'
            ];

            events.forEach(eventName => {
                const event = new CustomEvent(eventName, {
                    detail: {
                        ...profitData,
                        timestamp: new Date().toISOString(),
                        source: source,
                        test: true
                    }
                });
                
                log(`📡 إرسال حدث: ${eventName}`, 'info');
                window.dispatchEvent(event);
            });
        }

        // اختبار عملية بيع
        function testSaleTransaction() {
            log('🛒 بدء اختبار عملية بيع...', 'info');
            
            const saleData = {
                quarterly: 1250,
                halfYearly: 2500,
                threeQuarters: 3750,
                yearly: 5000,
                transaction_type: 'sale',
                amount: 1000,
                profit: 200,
                auto_update: true
            };
            
            simulateProfitUpdate(saleData, 'sale-test');
            
            // محاكاة تحديث الخزينة
            setTimeout(() => {
                const cashboxEvent = new CustomEvent('cashbox-updated-ui', {
                    detail: {
                        current_balance: 15000,
                        profit_total: 5000,
                        sales_total: 25500,
                        transaction_type: 'sale',
                        success: true,
                        test: true
                    }
                });
                
                log('💰 إرسال حدث تحديث الخزينة', 'info');
                window.dispatchEvent(cashboxEvent);
                log('✅ تم اختبار عملية البيع بنجاح', 'success');
            }, 100);
        }

        // اختبار عملية شراء
        function testPurchaseTransaction() {
            log('🛍️ بدء اختبار عملية شراء...', 'info');
            
            const purchaseData = {
                quarterly: 1200,
                halfYearly: 2400,
                threeQuarters: 3600,
                yearly: 4800,
                transaction_type: 'purchase',
                amount: 800,
                transport_cost: 50,
                auto_update: true
            };
            
            simulateProfitUpdate(purchaseData, 'purchase-test');
            
            setTimeout(() => {
                const cashboxEvent = new CustomEvent('cashbox-updated-ui', {
                    detail: {
                        current_balance: 14150,
                        profit_total: 4800,
                        purchases_total: 19000,
                        transaction_type: 'purchase',
                        success: true,
                        test: true
                    }
                });
                
                log('💰 إرسال حدث تحديث الخزينة للشراء', 'info');
                window.dispatchEvent(cashboxEvent);
                log('✅ تم اختبار عملية الشراء بنجاح', 'success');
            }, 100);
        }

        // اختبار التحديث المباشر
        function testDirectUpdate() {
            log('⚡ بدء اختبار التحديث المباشر...', 'info');
            
            const directData = {
                quarterly: 1300,
                halfYearly: 2600,
                threeQuarters: 3900,
                yearly: 5200,
                source: 'direct-update-test',
                auto_update: true
            };
            
            simulateProfitUpdate(directData, 'direct-test');
            
            const directEvent = new CustomEvent('direct-update', {
                detail: {
                    ...directData,
                    timestamp: new Date().toISOString(),
                    test: true
                }
            });
            
            log('⚡ إرسال حدث التحديث المباشر', 'info');
            window.dispatchEvent(directEvent);
            log('✅ تم اختبار التحديث المباشر بنجاح', 'success');
        }

        // اختبار متعدد المصادر
        function testMultiSourceUpdate() {
            log('🔄 بدء اختبار متعدد المصادر...', 'info');
            
            const sources = [
                { name: 'cashbox', data: { yearly: 5100, source: 'cashbox' } },
                { name: 'transactions', data: { yearly: 5150, source: 'transactions' } },
                { name: 'api', data: { yearly: 5200, source: 'api' } }
            ];
            
            sources.forEach((source, index) => {
                setTimeout(() => {
                    simulateProfitUpdate(source.data, source.name);
                    if (index === sources.length - 1) {
                        log('✅ تم اختبار متعدد المصادر بنجاح', 'success');
                    }
                }, index * 200);
            });
        }

        // فحص حالة النظام
        function checkSystemStatus() {
            log('🔍 فحص حالة النظام...', 'info');
            
            // فحص وجود window.api
            log(`🔌 window.api متوفر: ${typeof window.api !== 'undefined'}`, 'info');
            
            // فحص وجود React
            log(`⚛️ React متوفر: ${typeof window.React !== 'undefined'}`, 'info');
            
            // فحص وجود مكونات النظام
            const dashboardElement = document.querySelector('.dashboard-page, [class*="dashboard"]');
            log(`📊 Dashboard موجود: ${!!dashboardElement}`, 'info');
            
            const profitCards = document.querySelectorAll('[class*="profit"], [class*="stat-card"], [class*="card"]');
            log(`💳 عدد البطاقات الموجودة: ${profitCards.length}`, 'info');
            
            log('✅ تم فحص حالة النظام', 'success');
        }

        // فحص خانات الأرباح
        function checkProfitDisplays() {
            log('📈 فحص خانات الأرباح...', 'info');
            
            // البحث عن عناصر الأرباح
            const profitElements = document.querySelectorAll('[class*="profit"], [data-testid*="profit"]');
            log(`💰 عدد عناصر الأرباح: ${profitElements.length}`, 'info');
            
            profitElements.forEach((element, index) => {
                const text = element.textContent || element.innerText || '';
                log(`💳 عنصر ${index + 1}: ${text.substring(0, 50)}...`, 'info');
            });
            
            log('✅ تم فحص خانات الأرباح', 'success');
        }

        // مراقبة الأحداث
        function monitorEvents() {
            if (eventMonitoring) {
                log('⚠️ مراقبة الأحداث قيد التشغيل بالفعل', 'error');
                return;
            }
            
            log('👁️ بدء مراقبة أحداث تحديث الأرباح...', 'info');
            eventMonitoring = true;
            
            const eventsToMonitor = [
                'profits-updated',
                'auto-profits-updated',
                'dashboard-profits-updated',
                'financial-profits-updated',
                'real-time-profits-updated',
                'cashbox-updated-ui',
                'direct-update'
            ];
            
            eventsToMonitor.forEach(eventName => {
                window.addEventListener(eventName, (event) => {
                    log(`📥 تم استلام حدث ${eventName}: ${JSON.stringify(event.detail).substring(0, 100)}...`, 'success');
                });
            });
            
            log('✅ تم تسجيل مراقبين للأحداث', 'success');
        }

        // تشغيل جميع الاختبارات
        function runAllTests() {
            log('🚀 بدء تشغيل جميع الاختبارات...', 'info');
            
            // بدء مراقبة الأحداث
            if (!eventMonitoring) {
                monitorEvents();
            }
            
            // تشغيل الاختبارات بتأخير
            setTimeout(() => testSaleTransaction(), 1000);
            setTimeout(() => testPurchaseTransaction(), 3000);
            setTimeout(() => testDirectUpdate(), 5000);
            setTimeout(() => testMultiSourceUpdate(), 7000);
            setTimeout(() => {
                log('🎉 تم الانتهاء من جميع الاختبارات!', 'success');
            }, 10000);
            
            log('⏱️ تم جدولة جميع الاختبارات', 'info');
        }

        // تهيئة الصفحة
        window.addEventListener('load', () => {
            log('🎯 تم تحميل صفحة اختبار الأرباح', 'success');
            log('📝 استخدم الأزرار أعلاه لتشغيل الاختبارات', 'info');
        });
    </script>
</body>
</html>
