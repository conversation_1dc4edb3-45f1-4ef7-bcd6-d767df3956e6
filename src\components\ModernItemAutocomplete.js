import React, { useState, useEffect, useRef, useCallback } from 'react';
import { FaSearch, FaBoxOpen, FaMoneyBillWave, FaExclamationTriangle } from 'react-icons/fa';
import './ItemAutocomplete.css';

/**
 * مكون الإكمال التلقائي الحديث للأصناف باستخدام React Hooks
 * @param {Object} props - خصائص المكون
 * @param {Function} props.onSelect - دالة تُستدعى عند اختيار صنف
 * @param {Function} props.onChange - دالة تُستدعى عند تغيير النص (اختياري)
 * @param {Array} props.items - قائمة الأصناف (اختياري)
 * @param {boolean} props.showDetails - عرض تفاصيل الصنف (اختياري)
 * @param {string} props.placeholder - نص توضيحي لحقل البحث (اختياري)
 * @param {string} props.className - اسم الفئة CSS الإضافية (اختياري)
 * @param {boolean} props.hideIcon - إخفاء أيقونة البحث (اختياري)
 */
const ModernItemAutocomplete = ({
  onSelect,
  onChange: onChangeCallback,
  items: propItems,
  showDetails = true,
  placeholder = 'اكتب اسم الصنف أو الرقم...',
  className = '',
  hideIcon = false,
  value: propValue = ''
}) => {
  const [items, setItems] = useState([]);
  const [value, setValue] = useState(propValue);
  const [suggestions, setSuggestions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);

  const inputRef = useRef(null);
  const suggestionsRef = useRef(null);
  const containerRef = useRef(null);

  // تحميل الأصناف من قاعدة البيانات إذا لم يتم توفيرها
  useEffect(() => {
    if (propItems && propItems.length > 0) {
      setItems(propItems);
      return;
    }

    const loadItems = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log('[MODERN-AUTOCOMPLETE] تحميل بيانات الأصناف...');

        // جلب قائمة الأصناف
        const itemsList = await window.api.invoke('get-all-items');
        console.log(`[MODERN-AUTOCOMPLETE] تم جلب ${itemsList.length} صنف`);

        if (!itemsList || itemsList.length === 0) {
          setItems([]);
          setError('لم يتم العثور على أصناف في قاعدة البيانات');
          return;
        }

        // جلب بيانات المخزون
        const inventoryList = await window.api.invoke('get-all-inventory');
        console.log(`[MODERN-AUTOCOMPLETE] تم جلب ${inventoryList.length} عنصر مخزون`);

        // دمج بيانات الأصناف مع بيانات المخزون
        const mergedItems = itemsList.map(item => {
          const inventoryItem = inventoryList.find(inv => inv.item_id === item.id);
          return {
            ...item,
            current_quantity: inventoryItem ? inventoryItem.current_quantity : 0,
            minimum_quantity: inventoryItem ? inventoryItem.minimum_quantity : 0
          };
        });

        // ترتيب الأصناف بحيث تظهر الأصناف ذات الكمية المتوفرة وسعر البيع أولاً
        const sortedItems = [...mergedItems].sort((a, b) => {
          const aValid = a.current_quantity > 0 && a.selling_price > 0;
          const bValid = b.current_quantity > 0 && b.selling_price > 0;

          if (aValid && !bValid) return -1;
          if (!aValid && bValid) return 1;

          return a.name.localeCompare(b.name);
        });

        setItems(sortedItems);
      } catch (error) {
        console.error('[MODERN-AUTOCOMPLETE] خطأ في تحميل الأصناف:', error);
        setError('حدث خطأ أثناء تحميل الأصناف');
      } finally {
        setLoading(false);
      }
    };

    loadItems();
  }, [propItems]);

  // الحصول على اقتراحات البحث
  const getSuggestions = useCallback((inputValue) => {
    const trimmedValue = inputValue.trim().toLowerCase();

    if (trimmedValue.length === 0) {
      return [];
    }

    return items.filter(item => {
      const nameMatch = item.name && item.name.toLowerCase().includes(trimmedValue);
      const idMatch = item.id && item.id.toString().includes(trimmedValue);
      return nameMatch || idMatch;
    }).slice(0, 10);
  }, [items]);

  // تحديث القيمة عند تغيير propValue
  useEffect(() => {
    setValue(propValue);
  }, [propValue]);

  // تحديث الاقتراحات عند تغيير القيمة
  useEffect(() => {
    const newSuggestions = getSuggestions(value);
    setSuggestions(newSuggestions);
    setIsOpen(newSuggestions.length > 0 && value.length > 0);
    setSelectedIndex(-1);
  }, [value, getSuggestions]);

  // إغلاق الاقتراحات عند النقر خارج المكون
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (containerRef.current && !containerRef.current.contains(event.target)) {
        setIsOpen(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // تحديد لون الكمية بناءً على قيمتها
  const getQuantityColorClass = (quantity, minimumQuantity = 0) => {
    if (quantity <= 0) return 'out';
    if (minimumQuantity > 0 && quantity <= minimumQuantity) return 'low';
    return '';
  };

  // معالجة تغيير قيمة الإدخال
  const handleInputChange = (event) => {
    const newValue = event.target.value;
    setValue(newValue);

    if (onChangeCallback) {
      onChangeCallback(newValue);
    }
  };

  // معالجة اختيار اقتراح
  const handleSuggestionSelect = (suggestion) => {
    console.log('[MODERN-AUTOCOMPLETE] تم اختيار الصنف:', suggestion);

    if (onSelect) {
      onSelect(suggestion);
    }

    setValue('');
    setIsOpen(false);
    setSelectedIndex(-1);
  };

  // معالجة أحداث لوحة المفاتيح
  const handleKeyDown = (event) => {
    if (!isOpen || suggestions.length === 0) {
      return;
    }

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        setSelectedIndex(prev =>
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        event.preventDefault();
        setSelectedIndex(prev =>
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      case 'Enter':
        event.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
          handleSuggestionSelect(suggestions[selectedIndex]);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setSelectedIndex(-1);
        break;
      default:
        break;
    }
  };

  // عرض الاقتراح
  const renderSuggestion = (suggestion, index) => (
    <div
      key={suggestion.id}
      className={`item-suggestion ${index === selectedIndex ? 'selected' : ''}`}
      onClick={() => handleSuggestionSelect(suggestion)}
      onMouseEnter={() => setSelectedIndex(index)}
    >
      <div className="item-suggestion-name">{suggestion.name}</div>
      {showDetails && (
        <div className="item-suggestion-details">
          <div className="item-suggestion-detail">
            <span className="item-suggestion-label">الوحدة:</span>
            <span className="item-suggestion-value">{suggestion.unit || 'غير محدد'}</span>
          </div>
          <div className="item-suggestion-detail">
            <span className="item-suggestion-label">
              <FaBoxOpen style={{ marginLeft: '5px' }} />
              الكمية:
            </span>
            <span
              className={`item-suggestion-value item-quantity ${getQuantityColorClass(suggestion.current_quantity, suggestion.minimum_quantity)}`}
            >
              {suggestion.current_quantity || 0}
              {suggestion.current_quantity <= 0 && (
                <FaExclamationTriangle style={{ marginRight: '5px', color: '#e74c3c' }} />
              )}
            </span>
          </div>
          <div className="item-suggestion-detail">
            <span className="item-suggestion-label">
              <FaMoneyBillWave style={{ marginLeft: '5px' }} />
              السعر:
            </span>
            <span className="item-suggestion-value">
              {suggestion.selling_price ? suggestion.selling_price.toFixed(2) : '0.00'}
              {(suggestion.selling_price <= 0) && (
                <FaExclamationTriangle style={{ marginRight: '5px', color: '#e74c3c' }} />
              )}
            </span>
          </div>
        </div>
      )}
    </div>
  );

  return (
    <div className={`item-autocomplete-wrapper ${className}`} ref={containerRef}>
      {!hideIcon && (
        <div className="item-autocomplete-icon">
          <FaSearch />
        </div>
      )}

      <div className="item-autocomplete-container">
        <input
          ref={inputRef}
          type="text"
          className="item-autocomplete-input"
          placeholder={placeholder}
          value={value}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => {
            if (suggestions.length > 0) {
              setIsOpen(true);
            }
          }}
          autoComplete="off"
        />

        {isOpen && suggestions.length > 0 && (
          <div className="item-autocomplete-suggestions-container-open" ref={suggestionsRef}>
            <ul className="item-autocomplete-suggestions-list">
              {suggestions.map((suggestion, index) => (
                <li key={suggestion.id} className="item-autocomplete-suggestion">
                  {renderSuggestion(suggestion, index)}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {loading && <div className="item-autocomplete-loading">جاري التحميل...</div>}
      {error && <div className="item-autocomplete-error">{error}</div>}
    </div>
  );
};

export default ModernItemAutocomplete;
