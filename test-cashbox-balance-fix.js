/**
 * اختبار إصلاح آلية خصم مبالغ المشتريات من الرصيد الحالي
 * 
 * هذا السكريبت يقوم باختبار الإصلاحات المطبقة على نظام الخزينة
 */

console.log('🧪 بدء اختبار إصلاح آلية خصم مبالغ المشتريات من الرصيد الحالي...');

// التحقق من وجود window.api
if (typeof window !== 'undefined' && window.api) {
  
  // اختبار شامل لنظام الخزينة
  async function testCashboxBalanceSystem() {
    try {
      console.log('📊 بدء الاختبار الشامل لنظام الخزينة...');
      
      // 1. الحصول على حالة الخزينة الحالية
      console.log('\n1️⃣ الحصول على حالة الخزينة الحالية...');
      const initialCashbox = await window.api.cashbox.getCashbox();
      console.log('📋 حالة الخزينة الأولية:', {
        initial_balance: initialCashbox.initial_balance,
        current_balance: initialCashbox.current_balance,
        sales_total: initialCashbox.sales_total,
        purchases_total: initialCashbox.purchases_total,
        profit_total: initialCashbox.profit_total
      });
      
      // 2. تعيين رصيد ابتدائي للاختبار
      console.log('\n2️⃣ تعيين رصيد ابتدائي للاختبار (10000)...');
      const initialBalanceResult = await window.api.cashbox.updateInitialBalance(10000);
      if (initialBalanceResult.success) {
        console.log('✅ تم تعيين الرصيد الابتدائي بنجاح');
        console.log('📊 الخزينة بعد تعيين الرصيد الابتدائي:', {
          initial_balance: initialBalanceResult.cashbox.initial_balance,
          current_balance: initialBalanceResult.cashbox.current_balance
        });
      } else {
        console.error('❌ فشل في تعيين الرصيد الابتدائي:', initialBalanceResult.error);
        return;
      }
      
      // 3. اختبار عملية بيع (يجب أن تزيد الرصيد الحالي)
      console.log('\n3️⃣ اختبار عملية بيع (مبلغ 1000)...');
      const saleTransaction = {
        type: 'sale',
        amount: 1000,
        source: 'test',
        notes: 'اختبار عملية بيع',
        user_id: 1
      };
      
      const saleResult = await window.api.cashbox.addTransaction(saleTransaction);
      if (saleResult.success) {
        console.log('✅ تم تسجيل عملية البيع بنجاح');
        console.log('📊 الخزينة بعد البيع:', {
          current_balance: saleResult.cashbox.current_balance,
          sales_total: saleResult.cashbox.sales_total,
          expected_current_balance: 10000 + 1000
        });
        
        if (saleResult.cashbox.current_balance === 11000) {
          console.log('✅ الرصيد الحالي تم تحديثه بشكل صحيح (10000 + 1000 = 11000)');
        } else {
          console.error('❌ خطأ في تحديث الرصيد الحالي بعد البيع');
          console.error(`المتوقع: 11000، الفعلي: ${saleResult.cashbox.current_balance}`);
        }
      } else {
        console.error('❌ فشل في تسجيل عملية البيع:', saleResult.error);
        return;
      }
      
      // 4. اختبار عملية شراء (يجب أن تقلل الرصيد الحالي)
      console.log('\n4️⃣ اختبار عملية شراء (مبلغ 500)...');
      const purchaseTransaction = {
        type: 'purchase',
        amount: 500,
        source: 'test',
        notes: 'اختبار عملية شراء',
        user_id: 1
      };
      
      const purchaseResult = await window.api.cashbox.addTransaction(purchaseTransaction);
      if (purchaseResult.success) {
        console.log('✅ تم تسجيل عملية الشراء بنجاح');
        console.log('📊 الخزينة بعد الشراء:', {
          current_balance: purchaseResult.cashbox.current_balance,
          purchases_total: purchaseResult.cashbox.purchases_total,
          expected_current_balance: 11000 - 500
        });
        
        if (purchaseResult.cashbox.current_balance === 10500) {
          console.log('✅ الرصيد الحالي تم تحديثه بشكل صحيح (11000 - 500 = 10500)');
        } else {
          console.error('❌ خطأ في تحديث الرصيد الحالي بعد الشراء');
          console.error(`المتوقع: 10500، الفعلي: ${purchaseResult.cashbox.current_balance}`);
        }
      } else {
        console.error('❌ فشل في تسجيل عملية الشراء:', purchaseResult.error);
        return;
      }
      
      // 5. اختبار عملية إرجاع (يجب أن تقلل الرصيد الحالي)
      console.log('\n5️⃣ اختبار عملية إرجاع (مبلغ 200)...');
      const returnTransaction = {
        type: 'return',
        amount: 200,
        source: 'test',
        notes: 'اختبار عملية إرجاع',
        user_id: 1
      };
      
      const returnResult = await window.api.cashbox.addTransaction(returnTransaction);
      if (returnResult.success) {
        console.log('✅ تم تسجيل عملية الإرجاع بنجاح');
        console.log('📊 الخزينة بعد الإرجاع:', {
          current_balance: returnResult.cashbox.current_balance,
          returns_total: returnResult.cashbox.returns_total,
          expected_current_balance: 10500 - 200
        });
        
        if (returnResult.cashbox.current_balance === 10300) {
          console.log('✅ الرصيد الحالي تم تحديثه بشكل صحيح (10500 - 200 = 10300)');
        } else {
          console.error('❌ خطأ في تحديث الرصيد الحالي بعد الإرجاع');
          console.error(`المتوقع: 10300، الفعلي: ${returnResult.cashbox.current_balance}`);
        }
      } else {
        console.error('❌ فشل في تسجيل عملية الإرجاع:', returnResult.error);
        return;
      }
      
      // 6. اختبار عملية مصاريف نقل (يجب أن تقلل الرصيد الحالي)
      console.log('\n6️⃣ اختبار عملية مصاريف نقل (مبلغ 100)...');
      const transportTransaction = {
        type: 'transport',
        amount: 100,
        source: 'test',
        notes: 'اختبار مصاريف نقل',
        user_id: 1
      };
      
      const transportResult = await window.api.cashbox.addTransaction(transportTransaction);
      if (transportResult.success) {
        console.log('✅ تم تسجيل مصاريف النقل بنجاح');
        console.log('📊 الخزينة بعد مصاريف النقل:', {
          current_balance: transportResult.cashbox.current_balance,
          transport_total: transportResult.cashbox.transport_total,
          expected_current_balance: 10300 - 100
        });
        
        if (transportResult.cashbox.current_balance === 10200) {
          console.log('✅ الرصيد الحالي تم تحديثه بشكل صحيح (10300 - 100 = 10200)');
        } else {
          console.error('❌ خطأ في تحديث الرصيد الحالي بعد مصاريف النقل');
          console.error(`المتوقع: 10200، الفعلي: ${transportResult.cashbox.current_balance}`);
        }
      } else {
        console.error('❌ فشل في تسجيل مصاريف النقل:', transportResult.error);
        return;
      }
      
      // 7. التحقق من الحالة النهائية للخزينة
      console.log('\n7️⃣ التحقق من الحالة النهائية للخزينة...');
      const finalCashbox = await window.api.cashbox.getCashbox();
      console.log('📊 الحالة النهائية للخزينة:', {
        initial_balance: finalCashbox.initial_balance,
        current_balance: finalCashbox.current_balance,
        sales_total: finalCashbox.sales_total,
        purchases_total: finalCashbox.purchases_total,
        returns_total: finalCashbox.returns_total,
        transport_total: finalCashbox.transport_total,
        profit_total: finalCashbox.profit_total
      });
      
      // التحقق من صحة الحسابات
      const expectedCurrentBalance = 10200; // 10000 + 1000 - 500 - 200 - 100
      const expectedSalesTotal = 1000;
      const expectedPurchasesTotal = 500;
      const expectedReturnsTotal = 200;
      const expectedTransportTotal = 100;
      const expectedProfitTotal = 1000 - 500 - 100; // المبيعات - المشتريات - النقل = 400
      
      console.log('\n📋 مقارنة النتائج المتوقعة مع الفعلية:');
      console.log(`الرصيد الحالي: متوقع ${expectedCurrentBalance}, فعلي ${finalCashbox.current_balance} ${finalCashbox.current_balance === expectedCurrentBalance ? '✅' : '❌'}`);
      console.log(`إجمالي المبيعات: متوقع ${expectedSalesTotal}, فعلي ${finalCashbox.sales_total} ${finalCashbox.sales_total === expectedSalesTotal ? '✅' : '❌'}`);
      console.log(`إجمالي المشتريات: متوقع ${expectedPurchasesTotal}, فعلي ${finalCashbox.purchases_total} ${finalCashbox.purchases_total === expectedPurchasesTotal ? '✅' : '❌'}`);
      console.log(`إجمالي المرتجعات: متوقع ${expectedReturnsTotal}, فعلي ${finalCashbox.returns_total} ${finalCashbox.returns_total === expectedReturnsTotal ? '✅' : '❌'}`);
      console.log(`إجمالي مصاريف النقل: متوقع ${expectedTransportTotal}, فعلي ${finalCashbox.transport_total} ${finalCashbox.transport_total === expectedTransportTotal ? '✅' : '❌'}`);
      console.log(`إجمالي الأرباح: متوقع ${expectedProfitTotal}, فعلي ${finalCashbox.profit_total} ${finalCashbox.profit_total === expectedProfitTotal ? '✅' : '❌'}`);
      
      // النتيجة النهائية
      const allTestsPassed = (
        finalCashbox.current_balance === expectedCurrentBalance &&
        finalCashbox.sales_total === expectedSalesTotal &&
        finalCashbox.purchases_total === expectedPurchasesTotal &&
        finalCashbox.returns_total === expectedReturnsTotal &&
        finalCashbox.transport_total === expectedTransportTotal &&
        finalCashbox.profit_total === expectedProfitTotal
      );
      
      if (allTestsPassed) {
        console.log('\n🎉 جميع الاختبارات نجحت! تم إصلاح آلية خصم مبالغ المشتريات من الرصيد الحالي بنجاح');
      } else {
        console.log('\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الإصلاحات');
      }
      
    } catch (error) {
      console.error('❌ خطأ في تشغيل الاختبار:', error);
    }
  }
  
  // تشغيل الاختبار
  testCashboxBalanceSystem();
  
} else {
  console.error('❌ window.api غير متوفر');
  console.log('💡 يجب تشغيل هذا السكريبت من داخل التطبيق');
  console.log('');
  console.log('📋 لتشغيل الاختبار:');
  console.log('1. افتح التطبيق');
  console.log('2. افتح وحدة التحكم (F12)');
  console.log('3. انسخ والصق محتوى هذا الملف');
}
