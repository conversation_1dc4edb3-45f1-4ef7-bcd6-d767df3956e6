import React, { useState, useEffect } from 'react';
import { FaDatabase, FaExchangeAlt, FaCheck, FaDownload, FaUpload } from 'react-icons/fa';
import './DatabaseSettings.css';

/**
 * مكون إعدادات قاعدة البيانات
 * يوفر واجهة للتحكم في قاعدة البيانات وترحيل البيانات
 */
const DatabaseSettings = () => {
  // حالة نوع قاعدة البيانات
  // Variable no utilizada - considere eliminarla o usarla
  const [databaseType, setDatabaseType] = useState('nedb');
  // حالة عملية الترحيل
  // Variable no utilizada - considere eliminarla o usarla
  const [migrationStatus, setMigrationStatus] = useState({
    inProgress: false,
    success: null,
    message: '',
    details: ''
  });
  // حالة عملية التحقق
  // Variable no utilizada - considere eliminarla o usarla
  const [validationStatus, setValidationStatus] = useState({
    inProgress: false,
    success: null,
    message: '',
    details: ''
  });

  // الحصول على نوع قاعدة البيانات عند تحميل المكون
  useEffect(() => {
    const fetchDatabaseType = async () => {
      try {
        const type = await window.api.database.getType();
        setDatabaseType(type);
      } catch (error) {
        console.error('خطأ في الحصول على نوع قاعدة البيانات:', error);
      }
    };

    fetchDatabaseType();
  }, []);

  // تغيير نوع قاعدة البيانات
  const handleDatabaseTypeChange = async (type) => {
    try {
      const result = await window.api.database.setType(type);
      if (result.success) {
        setDatabaseType(type);
        alert(result.message);
      } else {
        alert(`خطأ: ${result.message}`);
      }
    } catch (error) {
      console.error('خطأ في تغيير نوع قاعدة البيانات:', error);
      alert('حدث خطأ أثناء تغيير نوع قاعدة البيانات');
    }
  };

  // ترحيل البيانات من NeDB إلى SQLite
  const handleMigrateData = async () => {
    if (!confirm('هل أنت متأكد من رغبتك في ترحيل البيانات من NeDB إلى SQLite؟ قد تستغرق هذه العملية بعض الوقت.')) {
      return;
    }

    setMigrationStatus({
      inProgress: true,
      success: null,
      message: 'جاري ترحيل البيانات...',
      details: ''
    });

    try {
      const result = await window.api.database.migrate();

      setMigrationStatus({
        inProgress: false,
        success: result.success,
        message: result.message,
        details: result.details || ''
      });
    } catch (error) {
      console.error('خطأ في ترحيل البيانات:', error);

      setMigrationStatus({
        inProgress: false,
        success: false,
        message: 'حدث خطأ أثناء ترحيل البيانات',
        details: error.message || ''
      });
    }
  };

  // التحقق من صحة البيانات المرحلة
  const handleValidateMigration = async () => {
    setValidationStatus({
      inProgress: true,
      success: null,
      message: 'جاري التحقق من صحة البيانات المرحلة...',
      details: ''
    });

    try {
      const result = await window.api.database.validate();

      setValidationStatus({
        inProgress: false,
        success: result.success,
        message: result.message,
        details: result.details || ''
      });
    } catch (error) {
      console.error('خطأ في التحقق من صحة البيانات المرحلة:', error);

      setValidationStatus({
        inProgress: false,
        success: false,
        message: 'حدث خطأ أثناء التحقق من صحة البيانات المرحلة',
        details: error.message || ''
      });
    }
  };



  // إنشاء نسخة احتياطية من قاعدة البيانات
  const handleBackupDatabase = async () => {
    try {
      const result = await window.api.database.backup();

      if (result.success) {
        alert(`تم إنشاء نسخة احتياطية بنجاح في المسار: ${result.path}`);
      } else {
        alert(`خطأ: ${result.message}`);
      }
    } catch (error) {
      console.error('خطأ في إنشاء نسخة احتياطية:', error);
      alert('حدث خطأ أثناء إنشاء نسخة احتياطية');
    }
  };

  // استعادة قاعدة البيانات من نسخة احتياطية
  const handleRestoreDatabase = async () => {
    if (!confirm('هل أنت متأكد من رغبتك في استعادة قاعدة البيانات من نسخة احتياطية؟ سيتم استبدال البيانات الحالية.')) {
      return;
    }

    try {
      // هنا يمكن فتح مربع حوار لاختيار ملف النسخة الاحتياطية
      // ثم استدعاء وظيفة الاستعادة
      alert('وظيفة استعادة قاعدة البيانات غير متوفرة حاليًا. سيتم تنفيذها في الإصدار القادم.');
    } catch (error) {
      console.error('خطأ في استعادة قاعدة البيانات:', error);
      alert('حدث خطأ أثناء استعادة قاعدة البيانات');
    }
  };

  return (
    <div className="database-settings">
      <h2 className="settings-title">
        <FaDatabase className="settings-icon" />
        إدارة قاعدة البيانات
      </h2>

      <div className="settings-section">
        <h3>نوع قاعدة البيانات</h3>
        <div className="database-type-selector">
          <button
            className={`database-type-button ${databaseType === 'nedb' ? 'active' : ''}`}
            onClick={() => handleDatabaseTypeChange('nedb')}
          >
            NeDB (الافتراضية)
          </button>
          <button
            className={`database-type-button ${databaseType === 'sqlite' ? 'active' : ''}`}
            onClick={() => handleDatabaseTypeChange('sqlite')}
          >
            SQLite (أداء أفضل)
          </button>
        </div>
        <p className="database-type-info">
          {databaseType === 'nedb' ? (
            'NeDB هي قاعدة بيانات خفيفة تعمل بشكل جيد للبيانات الصغيرة والمتوسطة.'
          ) : (
            'SQLite هي قاعدة بيانات أكثر قوة وأداءً، مناسبة للبيانات الكبيرة.'
          )}
        </p>
      </div>

      <div className="settings-section">
        <h3>ترحيل البيانات</h3>
        <p>يمكنك ترحيل البيانات من NeDB إلى SQLite للاستفادة من أداء أفضل مع البيانات الكبيرة.</p>

        <div className="database-actions">
          <button
            className="action-button"
            onClick={handleMigrateData}
            disabled={migrationStatus.inProgress}
          >
            <FaExchangeAlt />
            ترحيل البيانات إلى SQLite
          </button>

          <button
            className="action-button"
            onClick={handleValidateMigration}
            disabled={validationStatus.inProgress}
          >
            <FaCheck />
            التحقق من صحة البيانات المرحلة
          </button>
        </div>

        {migrationStatus.message && (
          <div className={`status-message ${migrationStatus.success === true ? 'success' : migrationStatus.success === false ? 'error' : 'info'}`}>
            <p>{migrationStatus.message}</p>
            {migrationStatus.details && <pre>{migrationStatus.details}</pre>}
          </div>
        )}

        {validationStatus.message && (
          <div className={`status-message ${validationStatus.success === true ? 'success' : validationStatus.success === false ? 'error' : 'info'}`}>
            <p>{validationStatus.message}</p>
            {validationStatus.details && <pre>{validationStatus.details}</pre>}
          </div>
        )}
      </div>

      <div className="settings-section">
        <h3>النسخ الاحتياطي والاستعادة</h3>
        <p>قم بإنشاء نسخة احتياطية من قاعدة البيانات أو استعادتها من نسخة احتياطية سابقة.</p>

        <div className="database-actions">
          <button className="action-button" onClick={handleBackupDatabase}>
            <FaDownload />
            إنشاء نسخة احتياطية
          </button>

          <button className="action-button" onClick={handleRestoreDatabase}>
            <FaUpload />
            استعادة من نسخة احتياطية
          </button>
        </div>
      </div>


    </div>
  );
};

export default DatabaseSettings;
