/**
 * أداة تشخيص شاملة للأرباح
 * تفحص جميع مصادر البيانات وتحدد سبب عدم عرض الأرباح الصحيحة
 */

/**
 * تشخيص شامل للأرباح في النظام
 * @returns {Promise<Object>} - نتائج التشخيص
 */
export const diagnoseProfitIssues = async () => {
  const results = {
    timestamp: new Date().toISOString(),
    issues: [],
    recommendations: [],
    data: {}
  };

  try {
    console.log('🔍 بدء التشخيص الشامل للأرباح...');

    // 1. فحص الخزينة
    console.log('📊 فحص بيانات الخزينة...');
    try {
      const cashboxResult = await window.api.executeSQL('SELECT * FROM cashbox LIMIT 1');
      const cashbox = cashboxResult[0];
      
      if (cashbox) {
        results.data.cashbox = {
          profit_total: cashbox.profit_total,
          sales_total: cashbox.sales_total,
          purchases_total: cashbox.purchases_total,
          transport_total: cashbox.transport_total || 0
        };
        
        console.log(`💰 أرباح الخزينة: ${cashbox.profit_total}`);
        
        // حساب الأرباح المتوقعة
        const expectedProfit = (cashbox.sales_total || 0) - (cashbox.purchases_total || 0) - (cashbox.transport_total || 0);
        if (Math.abs(expectedProfit - (cashbox.profit_total || 0)) > 0.01) {
          results.issues.push(`عدم تطابق أرباح الخزينة: المتوقع ${expectedProfit}, الفعلي ${cashbox.profit_total}`);
        }
      } else {
        results.issues.push('لا توجد خزينة في النظام');
      }
    } catch (error) {
      results.issues.push(`خطأ في فحص الخزينة: ${error.message}`);
    }

    // 2. فحص معاملات البيع
    console.log('📈 فحص معاملات البيع...');
    try {
      const salesResult = await window.api.executeSQL(`
        SELECT 
          COUNT(*) as count,
          SUM(profit) as total_profit_from_transactions,
          SUM(total_price) as total_sales,
          SUM(transport_cost) as total_transport_in_sales
        FROM transactions 
        WHERE transaction_type = 'sale'
      `);
      
      const salesData = salesResult[0];
      results.data.sales = salesData;
      
      console.log(`📊 معاملات البيع: ${salesData.count} معاملة`);
      console.log(`💰 أرباح من المعاملات: ${salesData.total_profit_from_transactions}`);
      
      if (salesData.total_profit_from_transactions === null || salesData.total_profit_from_transactions === 0) {
        results.issues.push('أرباح المعاملات = 0 أو null - قد تكون المعاملات لا تحتوي على أرباح محسوبة');
      }
    } catch (error) {
      results.issues.push(`خطأ في فحص معاملات البيع: ${error.message}`);
    }

    // 3. فحص مصاريف النقل
    console.log('🚛 فحص مصاريف النقل...');
    try {
      const transportResult = await window.api.executeSQL(`
        SELECT 
          SUM(transport_cost) as total_transport_cost,
          COUNT(*) as transactions_with_transport
        FROM transactions 
        WHERE transport_cost > 0
      `);
      
      const transportData = transportResult[0];
      results.data.transport = transportData;
      
      console.log(`🚛 إجمالي مصاريف النقل: ${transportData.total_transport_cost}`);
      console.log(`📦 معاملات بمصاريف نقل: ${transportData.transactions_with_transport}`);
    } catch (error) {
      results.issues.push(`خطأ في فحص مصاريف النقل: ${error.message}`);
    }

    // 4. فحص عينة من المعاملات
    console.log('🔬 فحص عينة من المعاملات...');
    try {
      const sampleResult = await window.api.executeSQL(`
        SELECT 
          id, item_id, quantity, selling_price, profit, transport_cost, transaction_date
        FROM transactions 
        WHERE transaction_type = 'sale'
        ORDER BY transaction_date DESC
        LIMIT 5
      `);
      
      results.data.sampleTransactions = sampleResult;
      
      sampleResult.forEach((transaction, index) => {
        console.log(`📋 معاملة ${index + 1}: الربح=${transaction.profit}, مصاريف النقل=${transaction.transport_cost}`);
      });
    } catch (error) {
      results.issues.push(`خطأ في فحص عينة المعاملات: ${error.message}`);
    }

    // 5. حساب الأرباح الصحيحة يدوياً
    console.log('🧮 حساب الأرباح الصحيحة يدوياً...');
    try {
      // الحصول على جميع معاملات البيع مع تفاصيل المخزون
      const detailedSalesResult = await window.api.executeSQL(`
        SELECT 
          t.id, t.item_id, t.quantity, t.selling_price, t.profit, t.transport_cost,
          i.avg_price
        FROM transactions t
        LEFT JOIN inventory i ON t.item_id = i.item_id
        WHERE t.transaction_type = 'sale'
      `);

      let manualTotalProfit = 0;
      let transactionsWithIssues = 0;

      for (const transaction of detailedSalesResult) {
        let calculatedProfit = 0;

        // استخدام الربح المحفوظ إذا كان متوفراً
        if (transaction.profit !== null && transaction.profit !== undefined) {
          calculatedProfit = transaction.profit;
        } else if (transaction.selling_price > 0 && transaction.avg_price > 0) {
          // حساب الربح يدوياً
          const basicProfit = (transaction.selling_price - transaction.avg_price) * transaction.quantity;
          
          // حساب مصاريف النقل المخصصة
          const transportCostResult = await window.api.executeSQL(`
            SELECT 
              SUM(transport_cost) as total_transport,
              SUM(quantity) as total_quantity
            FROM transactions
            WHERE item_id = ? AND transaction_type = 'purchase' AND transport_cost > 0
          `, [transaction.item_id]);

          let transportCostPerUnit = 0;
          if (transportCostResult[0] && transportCostResult[0].total_quantity > 0) {
            transportCostPerUnit = transportCostResult[0].total_transport / transportCostResult[0].total_quantity;
          }

          calculatedProfit = basicProfit - (transportCostPerUnit * transaction.quantity);
        } else {
          transactionsWithIssues++;
        }

        manualTotalProfit += calculatedProfit;
      }

      results.data.manualCalculation = {
        totalProfit: manualTotalProfit,
        transactionsCount: detailedSalesResult.length,
        transactionsWithIssues
      };

      console.log(`🧮 الأرباح المحسوبة يدوياً: ${manualTotalProfit}`);
      
      // مقارنة مع أرباح الخزينة
      if (results.data.cashbox && Math.abs(manualTotalProfit - results.data.cashbox.profit_total) > 0.01) {
        results.issues.push(`عدم تطابق الأرباح: الخزينة=${results.data.cashbox.profit_total}, المحسوب يدوياً=${manualTotalProfit}`);
      }

    } catch (error) {
      results.issues.push(`خطأ في الحساب اليدوي: ${error.message}`);
    }

    // 6. تقديم التوصيات
    if (results.issues.length === 0) {
      results.recommendations.push('✅ النظام يبدو سليماً - قد تكون المشكلة في التخزين المؤقت أو إعادة التحميل');
    } else {
      results.recommendations.push('🔧 يحتاج النظام إلى إصلاح البيانات');
      results.recommendations.push('💾 تشغيل سكريبت إعادة حساب الأرباح');
      results.recommendations.push('🔄 مسح التخزين المؤقت وإعادة تحميل التطبيق');
    }

    console.log('✅ تم إكمال التشخيص');
    return results;

  } catch (error) {
    results.issues.push(`خطأ عام في التشخيص: ${error.message}`);
    return results;
  }
};

/**
 * عرض نتائج التشخيص بشكل منسق
 * @param {Object} results - نتائج التشخيص
 */
export const displayDiagnosisResults = (results) => {
  console.log('\n📋 ===== تقرير التشخيص الشامل =====');
  console.log(`🕒 الوقت: ${results.timestamp}`);
  
  console.log('\n📊 البيانات المكتشفة:');
  if (results.data.cashbox) {
    console.log(`💰 أرباح الخزينة: ${results.data.cashbox.profit_total}`);
    console.log(`💵 إجمالي المبيعات: ${results.data.cashbox.sales_total}`);
    console.log(`🛒 إجمالي المشتريات: ${results.data.cashbox.purchases_total}`);
    console.log(`🚛 مصاريف النقل: ${results.data.cashbox.transport_total}`);
  }
  
  if (results.data.manualCalculation) {
    console.log(`🧮 الأرباح المحسوبة يدوياً: ${results.data.manualCalculation.totalProfit}`);
  }

  console.log('\n⚠️ المشاكل المكتشفة:');
  if (results.issues.length === 0) {
    console.log('✅ لا توجد مشاكل واضحة');
  } else {
    results.issues.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue}`);
    });
  }

  console.log('\n💡 التوصيات:');
  results.recommendations.forEach((rec, index) => {
    console.log(`${index + 1}. ${rec}`);
  });

  console.log('\n=====================================');
};

// تصدير الدالة للاستخدام العام
if (typeof window !== 'undefined') {
  window.diagnoseProfitIssues = diagnoseProfitIssues;
  window.displayDiagnosisResults = displayDiagnosisResults;
  
  // دالة مختصرة للتشخيص السريع
  window.quickProfitDiagnosis = async () => {
    const results = await diagnoseProfitIssues();
    displayDiagnosisResults(results);
    return results;
  };
  
  console.log('🔧 تم تحميل أدوات التشخيص:');
  console.log('- window.quickProfitDiagnosis() - تشخيص سريع');
  console.log('- window.diagnoseProfitIssues() - تشخيص مفصل');
}
