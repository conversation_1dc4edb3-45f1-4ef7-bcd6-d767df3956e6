# تعديل الإنشاء التلقائي لقاعدة البيانات

تم تعديل الإنشاء التلقائي لقاعدة البيانات لإزالة إنشاء جداول جوجل درايف. فيما يلي التغييرات التي تم إجراؤها:

## 1. إنشاء ملف ترقية لإزالة إعدادات Google Drive

تم إنشاء ملف ترقية جديد `src\db\migrations\remove_google_drive.js` يقوم بإزالة إعدادات Google Drive من جدول الإعدادات عند تهيئة قاعدة البيانات.

```javascript
/**
 * ملف ترقية لإزالة إعدادات Google Drive من قاعدة البيانات
 * 
 * هذا الملف يقوم بإزالة إعدادات Google Drive من جدول الإعدادات
 * ويتم استدعاؤه عند تهيئة قاعدة البيانات
 */

const removeGoogleDriveMigration = `
-- حذف إعدادات Google Drive من جدول الإعدادات
DELETE FROM settings WHERE key = 'googleDriveBackupEnabled';
DELETE FROM settings WHERE key = 'googleDriveAutoBackup';
DELETE FROM settings WHERE key = 'googleDriveBackupInterval';
DELETE FROM settings WHERE key = 'googleDriveKeepBackupsCount';

-- تحديث إعدادات التحديث التلقائي
INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES ('autoUpdateEnabled', 'false', CURRENT_TIMESTAMP);
INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES ('lastUpdateCheck', 'null', CURRENT_TIMESTAMP);
INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES ('isInternetConnection', 'false', CURRENT_TIMESTAMP);
`;

module.exports = {
  removeGoogleDriveMigration
};
```

## 2. تعديل ملف run-migrations.js

تم تعديل ملف `src\db\run-migrations.js` لإضافة ترقية إزالة Google Drive إلى قائمة الترقيات التي يتم تنفيذها عند تهيئة قاعدة البيانات.

```javascript
/**
 * ملف لتنفيذ ترقيات قاعدة البيانات
 */

const { addInitialQuantityMigration } = require('./migrations/add_initial_quantity');
const { removeGoogleDriveMigration } = require('./migrations/remove_google_drive');

// قائمة بالترقيات التي سيتم تنفيذها
const migrations = [
  addInitialQuantityMigration,
  removeGoogleDriveMigration
];
```

## 3. ملاحظات هامة

- لم يتم العثور على أي جداول خاصة بـ Google Drive في قاعدة البيانات. يبدو أن النظام يستخدم فقط جدول الإعدادات (settings) لتخزين إعدادات Google Drive.
- تم إزالة إعدادات Google Drive من الإعدادات الافتراضية في ملف ipc-handlers.js.
- تم إزالة واجهة Google Drive من ملف preload.js.
- تم إزالة مكونات Google Drive من صفحة الإعدادات.

## 4. كيفية التحقق من نجاح التغييرات

بعد تشغيل التطبيق، يمكنك التحقق من نجاح التغييرات بالطرق التالية:

1. فتح قاعدة البيانات باستخدام أداة إدارة قاعدة البيانات SQLite.
2. تنفيذ الاستعلام التالي:
   ```sql
   SELECT * FROM settings WHERE key LIKE 'googleDrive%';
   ```
3. يجب ألا يظهر أي نتائج، مما يعني أن جميع إعدادات Google Drive قد تمت إزالتها بنجاح.

## 5. استكشاف الأخطاء وإصلاحها

إذا واجهت أي مشاكل أثناء تنفيذ هذه التغييرات، يمكنك:

1. التحقق من سجلات الأخطاء في التطبيق.
2. استعادة النسخة الاحتياطية من قاعدة البيانات.
3. تنفيذ ملف `remove_google_drive_settings.sql` يدويًا باستخدام أداة إدارة قاعدة البيانات SQLite.
