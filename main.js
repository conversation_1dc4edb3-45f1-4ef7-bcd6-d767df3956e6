/**
 * نقطة دخول التطبيق الرئيسية
 * تقوم بتهيئة التطبيق وإدارة دورة حياته
 */

const { app, BrowserWindow, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const fs = require('fs');
const os = require('os');
const bcrypt = require('bcryptjs');

// استيراد الوحدات المساعدة
const logger = require('./src/utils/logger');
const dbOptimizer = require('./src/utils/db-optimizer');
const memoryManager = require('./src/utils/memory-manager');
const { logError, logSystem } = require('./error-handler');

// استيراد مدير قاعدة البيانات
const DatabaseManager = require('./database-singleton');

// استيراد إصلاح جدول المعاملات
const { fixTransactionsTable } = require('./fix-transactions-table');

// استيراد إصلاح نظام الاسترجاع
const { fixRetrievalSystem } = require('./fix-retrieval-system');

// استيراد إصلاح حذف العملاء
const customersManagerFix = require('./customers-manager-fix');
const customersManagerPatch = require('./customers-manager-patch');

// استيراد وحدات إدارة النظام
const itemsManager = require('./items-manager');
const inventoryManager = require('./inventory-manager');
const customersManager = require('./customers-manager');
const transactionManager = require('./unified-transaction-manager');
const returnTransactionsManager = require('./return-transactions-manager');
const cashboxManager = require('./cashbox-manager');
const reportsManager = require('./reports-manager');
const UpdateManager = require('./src/services/UpdateManager');

// استيراد معالجات IPC
const ipcHandlers = require('./ipc-handlers');
const missingHandlers = require('./missing-handlers');

// تعريف المتغيرات العامة
let mainWindow = null;
let db = null;
let isAppReady = false;
let isDbInitialized = false;

/**
 * تهيئة قاعدة البيانات ووحدات النظام
 * @returns {Promise<boolean>} - نجاح العملية
 */
async function initializeDatabase() {
  try {
    console.log('جاري تهيئة قاعدة البيانات ووحدات النظام...');

    // تحديد مسار قاعدة البيانات
    const userDataPath = app.getPath('userData');
    const dbDir = path.join(userDataPath, 'wms-database');
    const dbPath = path.join(dbDir, 'warehouse.db');

    console.log(`مسار قاعدة البيانات: ${dbPath}`);

    // تهيئة مدير قاعدة البيانات
    const dbManager = DatabaseManager.getInstance();
    db = await dbManager.initialize(dbPath);

    if (!db) {
      console.error('فشل في تهيئة قاعدة البيانات');
      return false;
    }

    console.log('تم تهيئة قاعدة البيانات بنجاح');

    // إصلاح جدول المعاملات (إضافة عمود selling_price)
    try {
      console.log('جاري إصلاح جدول المعاملات...');
      const fixResult = fixTransactionsTable();
      console.log('نتيجة إصلاح جدول المعاملات:', fixResult);
    } catch (fixError) {
      console.error('خطأ في إصلاح جدول المعاملات:', fixError);
      logError(fixError, 'initializeDatabase - fixTransactionsTable');
      // لا نتوقف عن تشغيل التطبيق في حالة فشل الإصلاح
    }

    // إصلاح نظام الاسترجاع
    try {
      console.log('جاري إصلاح نظام الاسترجاع...');
      const fixRetrievalResult = fixRetrievalSystem();
      console.log('نتيجة إصلاح نظام الاسترجاع:', fixRetrievalResult);
    } catch (fixRetrievalError) {
      console.error('خطأ في إصلاح نظام الاسترجاع:', fixRetrievalError);
      logError(fixRetrievalError, 'initializeDatabase - fixRetrievalSystem');
      // لا نتوقف عن تشغيل التطبيق في حالة فشل الإصلاح
    }

    // إصلاح حذف العملاء
    try {
      console.log('جاري إصلاح حذف العملاء...');
      const customersFixResult = customersManagerFix.initialize();
      console.log('نتيجة إصلاح حذف العملاء:', customersFixResult);

      // تطبيق التصحيح على ملف customers-manager.js
      console.log('جاري تطبيق التصحيح على ملف customers-manager.js...');
      const patchResult = customersManagerPatch.applyPatch();
      console.log('نتيجة تطبيق التصحيح على ملف customers-manager.js:', patchResult);
    } catch (customersFixError) {
      console.error('خطأ في إصلاح حذف العملاء:', customersFixError);
      logError(customersFixError, 'initializeDatabase - customersManagerFix');
      // لا نتوقف عن تشغيل التطبيق في حالة فشل الإصلاح
    }

    // إنشاء مجلدات التحديثات والنسخ الاحتياطية
    try {
      const updatePaths = {
        appUpdatesDir: path.join(app.getPath('userData'), 'updates', 'app'),
        dbUpdatesDir: path.join(app.getPath('userData'), 'updates', 'database'),
        backupsDir: path.join(app.getPath('userData'), 'backups'),
        tempDir: path.join(app.getPath('temp'), 'wms-updates')
      };

      // إنشاء المجلدات إذا لم تكن موجودة
      for (const dir of Object.values(updatePaths)) {
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true });
          console.log(`تم إنشاء مجلد: ${dir}`);
        }
      }

      console.log('تم التحقق من وجود مجلدات التحديثات والنسخ الاحتياطية');
    } catch (error) {
      console.error('خطأ في إنشاء مجلدات التحديثات والنسخ الاحتياطية:', error);
      // لا نريد إيقاف التهيئة إذا فشل إنشاء المجلدات
    }

    // تهيئة وحدات النظام
    try {
      // تهيئة وحدة إدارة الأصناف
      console.log('جاري تهيئة وحدة إدارة الأصناف...');
      const itemsInitResult = itemsManager.initialize();
      console.log('نتيجة تهيئة وحدة إدارة الأصناف:', itemsInitResult);

      // تهيئة وحدة إدارة المخزون
      console.log('جاري تهيئة وحدة إدارة المخزون...');
      const inventoryInitResult = inventoryManager.initialize();
      console.log('نتيجة تهيئة وحدة إدارة المخزون:', inventoryInitResult);

      // تهيئة وحدة إدارة العملاء
      console.log('جاري تهيئة وحدة إدارة العملاء...');
      const customersInitResult = customersManager.initialize();
      console.log('نتيجة تهيئة وحدة إدارة العملاء:', customersInitResult);

      // تهيئة وحدة إدارة المعاملات
      console.log('جاري تهيئة وحدة إدارة المعاملات...');
      const transactionInitResult = transactionManager.initialize();
      console.log('نتيجة تهيئة وحدة إدارة المعاملات:', transactionInitResult);

      // تهيئة وحدة إدارة عمليات الإرجاع
      console.log('جاري تهيئة وحدة إدارة عمليات الإرجاع...');
      const returnTransactionsInitResult = returnTransactionsManager.initialize(db);
      console.log('نتيجة تهيئة وحدة إدارة عمليات الإرجاع:', returnTransactionsInitResult);

      // تهيئة وحدة إدارة الخزينة
      console.log('جاري تهيئة وحدة إدارة الخزينة...');
      const cashboxInitResult = cashboxManager.initialize();
      console.log('نتيجة تهيئة وحدة إدارة الخزينة:', cashboxInitResult);

      // تهيئة وحدة إدارة التقارير
      console.log('جاري تهيئة وحدة إدارة التقارير...');
      const reportsInitResult = reportsManager.initialize();
      console.log('نتيجة تهيئة وحدة إدارة التقارير:', reportsInitResult);

      console.log('تم تهيئة وحدات النظام بنجاح');

      // إصلاح الخزينة تلقائياً بعد تهيئة النظام
      try {
        console.log('جاري إصلاح الخزينة تلقائياً...');

        // استدعاء دالة إصلاح الخزينة مباشرة
        const { ipcMain } = require('electron');

        // إنشاء معالج مؤقت لإصلاح الخزينة
        const fixCashboxResult = await new Promise((resolve) => {
          // استيراد الدالة مباشرة
          const dbManager = DatabaseManager.getInstance();
          const db = dbManager.getConnection();

          if (!db) {
            console.error('فشل في الحصول على اتصال قاعدة البيانات لإصلاح الخزينة');
            resolve({ success: false, error: 'فشل في الحصول على اتصال قاعدة البيانات' });
            return;
          }

          // الحصول على الخزينة الحالية
          const getCashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
          const currentCashbox = getCashboxStmt.get();

          if (!currentCashbox) {
            console.error('لا توجد خزينة في قاعدة البيانات');
            resolve({ success: false, error: 'لا توجد خزينة في قاعدة البيانات' });
            return;
          }

          console.log('الخزينة الحالية قبل الإصلاح:', {
            current_balance: currentCashbox.current_balance,
            sales_total: currentCashbox.sales_total,
            purchases_total: currentCashbox.purchases_total,
            profit_total: currentCashbox.profit_total
          });

          // الحصول على جميع المعاملات
          const getTransactionsStmt = db.prepare(`
            SELECT t.*, inv.avg_price, inv.selling_price as inventory_selling_price
            FROM transactions t
            LEFT JOIN inventory inv ON t.item_id = inv.item_id
            ORDER BY t.transaction_date ASC
          `);
          const transactions = getTransactionsStmt.all();

          console.log(`تم العثور على ${transactions.length} معاملة`);

          // حساب الإجماليات
          let totalSales = 0;
          let totalPurchases = 0;
          let totalReturns = 0;
          let totalProfit = 0;
          let totalTransportCost = 0;

          for (const transaction of transactions) {
            const amount = parseFloat(transaction.total_price) || 0;
            const transportCost = parseFloat(transaction.transport_cost) || 0;

            if (transaction.transaction_type === 'sale') {
              totalSales += amount;

              // حساب الربح
              let profit = 0;
              if (transaction.profit && transaction.profit !== 0) {
                profit = parseFloat(transaction.profit);
              } else {
                // الحصول على سعر البيع
                let sellingPrice = transaction.selling_price;
                if (!sellingPrice || sellingPrice === 0) {
                  sellingPrice = transaction.inventory_selling_price || 0;
                }

                if (sellingPrice > 0 && transaction.avg_price > 0) {
                  // حساب الربح الأساسي
                  profit = (sellingPrice - transaction.avg_price) * transaction.quantity;

                  // خصم مصاريف النقل إذا كانت موجودة
                  if (transportCost > 0) {
                    profit -= transportCost;
                  }
                } else {
                  // تقدير 20% من سعر البيع
                  profit = amount * 0.2;
                }
              }

              totalProfit += profit;

            } else if (transaction.transaction_type === 'purchase') {
              totalPurchases += amount;
              totalTransportCost += transportCost;

            } else if (transaction.transaction_type === 'return') {
              totalReturns += amount;

              // خصم الربح المرتبط بالإرجاع
              let returnProfit = transaction.profit || (amount * 0.2);
              totalProfit -= returnProfit;
            }
          }

          // التأكد من أن الأرباح لا تكون سالبة
          totalProfit = Math.max(0, totalProfit);

          console.log('الإجماليات المحسوبة:', {
            totalSales,
            totalPurchases,
            totalReturns,
            totalProfit,
            totalTransportCost
          });

          // حساب الرصيد الحالي الجديد مع تطبيق قيد الرصيد الافتتاحي
          const calculatedBalance = currentCashbox.initial_balance + totalSales - totalPurchases - totalReturns - totalTransportCost;

          console.log('حساب الخزينة قبل تطبيق القيد:', {
            initialBalance: currentCashbox.initial_balance,
            totalSales,
            totalPurchases,
            totalReturns,
            totalTransportCost,
            calculatedBalance,
            currentProfit: totalProfit
          });

          // حساب الأرباح الصحيحة: المبيعات - المشتريات - مصاريف النقل
          const correctTotalProfit = totalSales - totalPurchases - totalTransportCost;

          // تطبيق قيد الرصيد الحالي: لا يتجاوز الرصيد الافتتاحي
          let newCurrentBalance;

          if (calculatedBalance > currentCashbox.initial_balance) {
            // المبلغ الزائد عن الرصيد الافتتاحي يبقى في الأرباح (لا نضيفه مرة أخرى)
            newCurrentBalance = currentCashbox.initial_balance;

            console.log(`تطبيق قيد الرصيد: الرصيد المحسوب ${calculatedBalance} يتجاوز الرصيد الافتتاحي ${currentCashbox.initial_balance}`);
            console.log(`الرصيد النهائي: ${newCurrentBalance}, الأرباح الصحيحة: ${correctTotalProfit}`);
          } else {
            // الرصيد المحسوب لا يتجاوز الرصيد الافتتاحي
            newCurrentBalance = calculatedBalance;
            console.log(`الرصيد المحسوب (${calculatedBalance}) لا يتجاوز الرصيد الافتتاحي (${currentCashbox.initial_balance})`);
          }

          console.log('حساب الخزينة النهائي:', {
            calculatedBalance,
            newCurrentBalance,
            correctTotalProfit,
            initialBalance: currentCashbox.initial_balance
          });

          // تحديث الخزينة
          const updateCashboxStmt = db.prepare(`
            UPDATE cashbox
            SET current_balance = ?,
                sales_total = ?,
                purchases_total = ?,
                returns_total = ?,
                profit_total = ?,
                transport_total = ?,
                updated_at = ?
            WHERE id = ?
          `);

          const updateResult = updateCashboxStmt.run(
            newCurrentBalance,
            totalSales,
            totalPurchases,
            totalReturns,
            correctTotalProfit,
            totalTransportCost,
            new Date().toISOString(),
            currentCashbox.id
          );

          console.log(`تم تحديث الخزينة بنجاح. عدد الصفوف المتأثرة: ${updateResult.changes}`);

          resolve({
            success: true,
            message: 'تم إصلاح الخزينة تلقائياً بنجاح',
            before: {
              current_balance: currentCashbox.current_balance,
              sales_total: currentCashbox.sales_total,
              purchases_total: currentCashbox.purchases_total,
              profit_total: currentCashbox.profit_total
            },
            after: {
              current_balance: newCurrentBalance,
              sales_total: totalSales,
              purchases_total: totalPurchases,
              profit_total: correctTotalProfit
            }
          });
        });

        console.log('نتيجة إصلاح الخزينة التلقائي:', fixCashboxResult);

      } catch (fixCashboxError) {
        console.error('خطأ في إصلاح الخزينة التلقائي:', fixCashboxError);
        logError(fixCashboxError, 'initializeDatabase - auto fix cashbox');
        // لا نتوقف عن تشغيل التطبيق في حالة فشل إصلاح الخزينة
      }

      isDbInitialized = true;
      return true;
    } catch (error) {
      console.error('خطأ في تهيئة وحدات النظام:', error);
      logError(error, 'initializeDatabase - modules');
      return false;
    }
  } catch (error) {
    console.error('خطأ في تهيئة قاعدة البيانات ووحدات النظام:', error);
    logError(error, 'initializeDatabase');
    return false;
  }
}

/**
 * إنشاء النافذة الرئيسية للتطبيق
 */
function createWindow() {
  console.log('جاري إنشاء النافذة الرئيسية...');

  // إنشاء النافذة الرئيسية
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    show: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      // إضافة خيارات إضافية لتصحيح المشكلة
      worldSafeExecuteJavaScript: true,
      enableRemoteModule: false,
      sandbox: false,
      // إضافة خيارات لضمان عمل contextBridge
      webSecurity: true,
      allowRunningInsecureContent: false,
      experimentalFeatures: false
    }
  });

  // إضافة النافذة الرئيسية إلى المتغيرات العالمية
  global.mainWindow = mainWindow;

  // تسجيل مسار ملف preload.js للتأكد من تحميله بشكل صحيح
  const preloadPath = path.join(__dirname, 'preload.js');
  console.log('مسار ملف preload.js:', preloadPath);

  // التحقق من وجود ملف preload.js
  const fs = require('fs');
  if (fs.existsSync(preloadPath)) {
    console.log('✅ ملف preload.js موجود');
  } else {
    console.error('❌ ملف preload.js غير موجود في:', preloadPath);
  }

  // تحميل الصفحة الرئيسية
  mainWindow.loadFile('index.html');

  // إضافة مستمعين لأحداث التحميل
  mainWindow.webContents.on('did-finish-load', () => {
    console.log('تم تحميل المحتوى بالكامل');

    // اختبار window.api بعد التحميل
    setTimeout(() => {
      mainWindow.webContents.executeJavaScript(`
        console.log('[MAIN] اختبار window.api بعد التحميل:', typeof window.api);
        if (window.api) {
          console.log('[MAIN] ✅ window.api متوفر');
          console.log('[MAIN] window.api.invoke متوفر:', typeof window.api.invoke === 'function');
        } else {
          console.log('[MAIN] ❌ window.api غير متوفر');
          console.log('[MAIN] محاولة إنشاء window.api يدوياً...');
          // محاولة إنشاء API يدوياً للاختبار
          window.api = {
            invoke: (channel, ...args) => {
              console.log('[MANUAL-API] استدعاء:', channel, args);
              return Promise.resolve('test response');
            },
            test: () => 'manual api works'
          };
          console.log('[MAIN] تم إنشاء window.api يدوياً');
        }
      `).catch(err => console.error('خطأ في تنفيذ JavaScript:', err));
    }, 1000);
  });

  mainWindow.webContents.on('preload-error', (event, preloadPath, error) => {
    console.error('خطأ في تحميل preload.js:', preloadPath, error);
  });

  mainWindow.webContents.on('dom-ready', () => {
    console.log('DOM جاهز');
  });

  mainWindow.webContents.on('did-start-loading', () => {
    console.log('بدء تحميل الصفحة');
  });

  // إظهار النافذة عند اكتمال تحميل الصفحة
  mainWindow.once('ready-to-show', () => {
    console.log('النافذة الرئيسية جاهزة للعرض');
    mainWindow.show();
    mainWindow.maximize();
  });

  // معالجة إغلاق النافذة
  mainWindow.on('closed', () => {
    console.log('تم إغلاق النافذة الرئيسية');
    mainWindow = null;
  });

  console.log('تم إنشاء النافذة الرئيسية بنجاح');
}

/**
 * تهيئة التطبيق
 */
app.on('ready', async () => {
  console.log('التطبيق جاهز للتشغيل');
  isAppReady = true;

  // تعريف متغير عالمي لتتبع حالة المزامنة
  global.isSyncInProgress = false;

  // تهيئة قاعدة البيانات ووحدات النظام
  const dbInitialized = await initializeDatabase();
  if (!dbInitialized) {
    console.error('فشل في تهيئة قاعدة البيانات ووحدات النظام');
    app.quit();
    return;
  }

  // تسجيل معالجات IPC
  console.log('[info] جاري تسجيل معالجات IPC...');

  // التحقق من عدم تسجيل المعالجات من قبل
  if (!global.ipcHandlersRegistered) {
    try {
      // تسجيل المعالجات الأساسية
      const ipcHandlersRegistered = ipcHandlers.registerAllHandlers();
      console.log('[info] نتيجة تسجيل معالجات IPC الأساسية:', ipcHandlersRegistered);

      // تعيين علامة لتجنب تسجيل المعالجات مرة أخرى
      global.ipcHandlersRegistered = true;
    } catch (error) {
      console.error('[error] فشل في تسجيل معالجات IPC:', error.message);
    }
  } else {
    console.log('[info] تم تسجيل معالجات IPC مسبقًا، تم تجاهل التسجيل المكرر');
  }

  // إنشاء النافذة الرئيسية
  createWindow();
});

/**
 * معالجة إغلاق التطبيق
 */
app.on('window-all-closed', () => {
  console.log('تم إغلاق جميع النوافذ');
  if (process.platform !== 'darwin') {
    console.log('جاري إغلاق التطبيق...');
    app.quit();
  }
});

/**
 * معالجة إعادة تنشيط التطبيق
 */
app.on('activate', () => {
  console.log('تم تنشيط التطبيق');
  if (mainWindow === null && isAppReady) {
    createWindow();
  }
});

/**
 * معالجة إغلاق التطبيق
 */
app.on('before-quit', () => {
  console.log('جاري إغلاق التطبيق...');
  if (db) {
    console.log('جاري إغلاق اتصال قاعدة البيانات...');
    const dbManager = DatabaseManager.getInstance();
    dbManager.close();
  }
});

// ملاحظة: تم نقل جميع معالجات IPC إلى ملفات ipc-handlers.js و missing-handlers.js
// لتجنب تكرار تسجيل المعالجات وتحسين تنظيم الكود

// تسجيل معالج لتحديث الأصناف في جميع النوافذ
ipcMain.on('refresh-items-all-windows', (event) => {
  try {
    console.log('تم استلام طلب تحديث الأصناف في جميع النوافذ');

    // إرسال حدث تحديث الأصناف إلى جميع النوافذ
    BrowserWindow.getAllWindows().forEach(window => {
      if (window && !window.isDestroyed()) {
        console.log(`إرسال حدث تحديث الأصناف إلى النافذة ${window.id}`);
        window.webContents.send('item-added', {
          id: 'refresh-all',
          name: 'تحديث الأصناف',
          timestamp: new Date().toISOString()
        });
      }
    });

    console.log('تم إرسال حدث تحديث الأصناف إلى جميع النوافذ بنجاح');
  } catch (error) {
    console.error('خطأ في معالج refresh-items-all-windows:', error);
    logError(error, 'refresh-items-all-windows');
  }
});

// تم تعطيل تسجيل معالج get-all-inventory مباشرة هنا لتجنب التكرار
// يتم تسجيله في ملف ipc-handlers.js

// تم تعطيل تسجيل معالج get-customer-available-items-for-return مباشرة هنا لتجنب التكرار
// يتم تسجيله في ملف missing-handlers.js
