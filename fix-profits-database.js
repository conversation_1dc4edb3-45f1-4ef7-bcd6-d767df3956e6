/**
 * سكريبت إصلاح الأرباح في قاعدة البيانات
 * يعيد حساب جميع الأرباح مع طرح مصاريف النقل
 */

const Database = require('better-sqlite3');
const path = require('path');

// مسار قاعدة البيانات
const dbPath = path.join(__dirname, 'warehouse.db');

console.log('🔧 بدء إصلاح الأرباح في قاعدة البيانات...');
console.log(`📁 مسار قاعدة البيانات: ${dbPath}`);

try {
  // فتح قاعدة البيانات
  const db = new Database(dbPath);
  
  console.log('✅ تم فتح قاعدة البيانات بنجاح');

  // الحصول على جميع معاملات البيع
  const salesTransactions = db.prepare(`
    SELECT 
      t.id,
      t.item_id,
      t.quantity,
      t.selling_price,
      t.profit as old_profit,
      t.transport_cost,
      i.avg_price
    FROM transactions t
    LEFT JOIN inventory i ON t.item_id = i.item_id
    WHERE t.transaction_type = 'sale'
    ORDER BY t.id
  `).all();

  console.log(`📊 تم العثور على ${salesTransactions.length} معاملة بيع`);

  let updatedCount = 0;
  let totalOldProfit = 0;
  let totalNewProfit = 0;

  // إعداد استعلام التحديث
  const updateStmt = db.prepare(`
    UPDATE transactions 
    SET profit = ? 
    WHERE id = ?
  `);

  // بدء معاملة قاعدة البيانات
  const updateTransaction = db.transaction(() => {
    for (const transaction of salesTransactions) {
      const {
        id,
        item_id,
        quantity,
        selling_price,
        old_profit,
        transport_cost,
        avg_price
      } = transaction;

      // حساب مصاريف النقل المخصصة لهذا الصنف
      let transportCostPerUnit = 0;
      
      try {
        const purchaseStmt = db.prepare(`
          SELECT quantity, transport_cost
          FROM transactions
          WHERE item_id = ?
            AND transaction_type = 'purchase'
            AND transport_cost > 0
        `);

        const purchases = purchaseStmt.all(item_id);
        if (purchases && purchases.length > 0) {
          let totalPurchasedQuantity = 0;
          let totalTransportCost = 0;

          purchases.forEach(purchase => {
            totalPurchasedQuantity += purchase.quantity || 0;
            totalTransportCost += purchase.transport_cost || 0;
          });

          if (totalPurchasedQuantity > 0) {
            transportCostPerUnit = totalTransportCost / totalPurchasedQuantity;
          }
        }
      } catch (error) {
        console.warn(`⚠️ خطأ في حساب مصاريف النقل للصنف ${item_id}:`, error.message);
        transportCostPerUnit = 0;
      }

      // حساب الربح الجديد مع طرح مصاريف النقل
      let newProfit = 0;
      
      if (selling_price > 0 && avg_price > 0) {
        // حساب الربح الأساسي
        const basicProfit = (selling_price - avg_price) * quantity;
        
        // خصم مصاريف النقل
        newProfit = Math.max(0, basicProfit - (transportCostPerUnit * quantity));
      }

      // تحديث الربح إذا كان مختلفاً
      if (Math.abs(newProfit - (old_profit || 0)) > 0.01) {
        updateStmt.run(newProfit, id);
        updatedCount++;
        
        console.log(`🔄 معاملة ${id}: ${old_profit || 0} → ${newProfit} (فرق: ${newProfit - (old_profit || 0)})`);
        console.log(`   📦 الصنف ${item_id}: (${selling_price} - ${avg_price}) × ${quantity} - ${transportCostPerUnit * quantity} = ${newProfit}`);
      }

      totalOldProfit += old_profit || 0;
      totalNewProfit += newProfit;
    }
  });

  // تنفيذ التحديث
  updateTransaction();

  console.log('\n📈 ملخص الإصلاح:');
  console.log(`✅ تم تحديث ${updatedCount} معاملة من أصل ${salesTransactions.length}`);
  console.log(`💰 إجمالي الأرباح القديمة: ${totalOldProfit.toFixed(2)}`);
  console.log(`💰 إجمالي الأرباح الجديدة: ${totalNewProfit.toFixed(2)}`);
  console.log(`📊 الفرق: ${(totalNewProfit - totalOldProfit).toFixed(2)}`);

  // تحديث إجمالي الأرباح في الخزينة
  console.log('\n🏦 تحديث الخزينة...');
  
  const updateCashboxStmt = db.prepare(`
    UPDATE cashbox 
    SET profit_total = ?,
        updated_at = ?
    WHERE id = 1
  `);

  const result = updateCashboxStmt.run(totalNewProfit, new Date().toISOString());
  
  if (result.changes > 0) {
    console.log(`✅ تم تحديث إجمالي الأرباح في الخزينة إلى: ${totalNewProfit.toFixed(2)}`);
  } else {
    console.log('⚠️ لم يتم العثور على خزينة لتحديثها');
  }

  // إغلاق قاعدة البيانات
  db.close();
  
  console.log('\n🎉 تم إكمال إصلاح الأرباح بنجاح!');
  console.log('💡 يرجى إعادة تشغيل التطبيق لرؤية التغييرات');

} catch (error) {
  console.error('❌ خطأ في إصلاح الأرباح:', error);
  process.exit(1);
}
