// Database utility functions
import { createLimitedCache } from './performance-optimizer';
import { createCachedQuery, createPaginatedQuery } from './database-optimization';
import { generateTransactionId } from './transaction-id';

// إنشاء تخزين مؤقت للبيانات المستخدمة بكثرة
const itemsCache = createLimitedCache(200);
const inventoryCache = createLimitedCache(200);
const transactionsCache = createLimitedCache(500);
const customersCache = createLimitedCache(200);
const usersCache = createLimitedCache(50);

// وقت صلاحية التخزين المؤقت (5 دقائق)
const CACHE_EXPIRY = 5 * 60 * 1000;

// التحقق من وجود window.api
const checkApiAvailability = () => {
  console.log('التحقق من توفر window.api...');

  // التحقق من وجود window
  if (typeof window === 'undefined') {
    console.error('window غير متوفر. هذا غير متوقع في بيئة Electron.');
    return false;
  }

  // التحقق من وجود window.api
  if (!window.api) {
    console.error('window.api غير متوفر. تأكد من تحميل preload.js بشكل صحيح.');

    // إعادة تحميل الصفحة قد يساعد في حل المشكلة
    console.log('محاولة إعادة تحميل الصفحة بعد 3 ثوانٍ...');
    setTimeout(() => {
      try {
        window.location.reload();
      } catch (reloadError) {
        console.error('فشل في إعادة تحميل الصفحة:', reloadError);
      }
    }, 3000);

    return false;
  }

  // التحقق من وجود window.api.invoke
  if (!window.api.invoke) {
    console.error('window.api.invoke غير متوفر. تأكد من تعريف وظيفة invoke في preload.js.');
    console.error('window.api:', window.api);
    return false;
  }

  // التحقق من وجود window.api.customers
  if (window.api.customers) {
    console.log('window.api.customers متوفر:', window.api.customers);

    // التحقق من وظائف العملاء
    if (typeof window.api.customers.getAll !== 'function') {
      console.error('window.api.customers.getAll غير متوفر كوظيفة');
    }
    if (typeof window.api.customers.add !== 'function') {
      console.error('window.api.customers.add غير متوفر كوظيفة');
    }
  } else {
    console.log('window.api.customers غير متوفر. سيتم استخدام window.api.invoke بدلاً من ذلك.');
  }

  console.log('window.api متوفر بنجاح');
  return true;
};

// وظيفة آمنة لاستدعاء API
const safeInvoke = async (channel, data) => {
  console.log(`محاولة استدعاء آمن للقناة ${channel} مع البيانات:`, data);

  // محاولة التحقق من window.api عدة مرات قبل الفشل
  let apiAvailable = false;
  let attempts = 0;
  const maxAttempts = 3;

  while (!apiAvailable && attempts < maxAttempts) {
    attempts++;
    console.log(`محاولة التحقق من window.api (${attempts}/${maxAttempts})...`);
    apiAvailable = checkApiAvailability();

    if (!apiAvailable && attempts < maxAttempts) {
      console.log(`انتظار 500 مللي ثانية قبل المحاولة التالية...`);
      // انتظار 500 مللي ثانية قبل المحاولة التالية
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }

  if (!apiAvailable) {
    console.error(`فشل استدعاء ${channel} بعد ${maxAttempts} محاولات للتحقق من window.api`);

    // محاولة إعادة تحميل الصفحة
    console.log('محاولة إعادة تحميل الصفحة بعد 2 ثانية...');
    setTimeout(() => {
      try {
        window.location.reload();
      } catch (reloadError) {
        console.error('فشل في إعادة تحميل الصفحة:', reloadError);
      }
    }, 2000);

    throw new Error(`فشل استدعاء ${channel} بسبب عدم توفر window.api`);
  }

  try {
    // محاولة استخدام الوظائف المباشرة أولاً
    if (channel === 'get-users' && window.api.users && typeof window.api.users.getAll === 'function') {
      console.log('استخدام window.api.users.getAll() بدلاً من window.api.invoke');
      return await window.api.users.getAll();
    } else if (channel.startsWith('get-customers') && window.api.customers && typeof window.api.customers.getAll === 'function') {
      console.log('استخدام window.api.customers.getAll() بدلاً من window.api.invoke');
      return await window.api.customers.getAll();
    } else if (channel === 'add-customer' && window.api.customers && typeof window.api.customers.add === 'function') {
      console.log('استخدام window.api.customers.add() بدلاً من window.api.invoke');
      return await window.api.customers.add(data);
    } else if (channel === 'update-customer' && window.api.customers && typeof window.api.customers.update === 'function') {
      console.log('استخدام window.api.customers.update() بدلاً من window.api.invoke');
      return await window.api.customers.update(data);
    } else if (channel === 'delete-customer' && window.api.customers && typeof window.api.customers.delete === 'function') {
      console.log('استخدام window.api.customers.delete() بدلاً من window.api.invoke');
      return await window.api.customers.delete(data);
    }

    // استخدام window.api.invoke كخيار احتياطي
    console.log(`استدعاء window.api.invoke للقناة ${channel}`);

    // التحقق مرة أخرى من وجود window.api.invoke
    if (!window.api || typeof window.api.invoke !== 'function') {
      console.error('window.api.invoke غير متوفر عند محاولة الاستدعاء');
      throw new Error('window.api.invoke غير متوفر');
    }

    const result = await window.api.invoke(channel, data);

    // التحقق من النتيجة
    if (result === undefined || result === null) {
      console.warn(`استدعاء ${channel} أرجع قيمة فارغة:`, result);
    }

    return result;
  } catch (error) {
    console.error(`خطأ في استدعاء ${channel}:`, error);

    // إذا كان الخطأ متعلقًا بـ window.api، حاول إعادة تحميل الصفحة
    if (error.message && (
        error.message.includes('window.api') ||
        error.message.includes('invoke') ||
        error.message.includes('not a function')
    )) {
      console.log('الخطأ متعلق بـ window.api، محاولة إعادة تحميل الصفحة بعد 2 ثانية...');
      setTimeout(() => {
        try {
          window.location.reload();
        } catch (reloadError) {
          console.error('فشل في إعادة تحميل الصفحة:', reloadError);
        }
      }, 2000);
    }

    throw error;
  }
};

// Items
export const getItems = async (forceRefresh = false) => {
  try {
    // التحقق من وجود البيانات في التخزين المؤقت (إلا إذا تم طلب تحديث إجباري)
    const cachedItems = itemsCache.get('all_items');
    if (cachedItems && !forceRefresh) {
      console.log('استخدام بيانات الأصناف من التخزين المؤقت');
      return cachedItems;
    }

    console.log('تحميل بيانات الأصناف من قاعدة البيانات' + (forceRefresh ? ' (تحديث إجباري)' : ''));
    const items = await safeInvoke('get-items');

    // تخزين البيانات في التخزين المؤقت
    if (Array.isArray(items)) {
      // معالجة البيانات للتأكد من أن جميع الحقول موجودة
      const processedItems = items.map(item => {
        // تسجيل معلومات تشخيصية للصنف
        console.log(`معالجة الصنف ${item.id || item._id} (${item.name}):`, {
          minimum_quantity: item.minimum_quantity,
          avg_price: item.avg_price,
          selling_price: item.selling_price,
          minimum_quantity_type: typeof item.minimum_quantity,
          avg_price_type: typeof item.avg_price
        });

        return {
          ...item,
          id: item.id || item._id,
          _id: item._id || item.id,
          minimum_quantity: item.minimum_quantity !== undefined ? Number(item.minimum_quantity) : 0,
          avg_price: item.avg_price !== undefined ? Number(item.avg_price) : 0,
          selling_price: item.selling_price !== undefined ? Number(item.selling_price) : 0
        };
      });

      itemsCache.set('all_items', processedItems);
      console.log(`تم تحميل ${processedItems.length} صنف بنجاح`);
      return processedItems;
    } else {
      console.warn('البيانات المستلمة ليست مصفوفة:', items);
      return [];
    }
  } catch (error) {
    console.error('Error fetching items:', error);
    throw error;
  }
};

// الحصول على صنف محدد
export const getItem = async (itemId) => {
  try {
    if (!itemId && itemId !== 0) {
      console.error('معرف الصنف غير صالح أو غير محدد:', itemId);
      return null;
    }

    // تحويل المعرف إلى نص للمقارنة الآمنة
    const itemIdStr = String(itemId);
    const itemIdNum = Number(itemId);
    console.log('الحصول على صنف محدد بالمعرف:', itemIdStr, '(نص) أو', itemIdNum, '(رقم)');

    // محاولة الحصول على الصنف من التخزين المؤقت أولاً
    const cachedItems = itemsCache.get('all_items');
    if (cachedItems && Array.isArray(cachedItems)) {
      const cachedItem = cachedItems.find(item => {
        // التحقق من جميع أشكال المعرف المحتملة (نصية ورقمية)
        return (
          (item.id !== undefined && (item.id === itemIdNum || String(item.id) === itemIdStr)) ||
          (item._id !== undefined && (item._id === itemIdNum || String(item._id) === itemIdStr))
        );
      });

      if (cachedItem) {
        console.log('تم العثور على الصنف في التخزين المؤقت:', cachedItem);
        return cachedItem;
      }
    }

    // إذا لم يتم العثور على الصنف في التخزين المؤقت، قم بتحميل جميع الأصناف
    console.log('الصنف غير موجود في التخزين المؤقت، جاري تحميل جميع الأصناف...');
    const items = await getItems(true);

    if (!Array.isArray(items)) {
      console.error('فشل في الحصول على قائمة الأصناف من قاعدة البيانات');
      return null;
    }

    // البحث عن الصنف في الأصناف المحدثة
    const item = items.find(item => {
      // التحقق من جميع أشكال المعرف المحتملة (نصية ورقمية)
      return (
        (item.id !== undefined && (item.id === itemIdNum || String(item.id) === itemIdStr)) ||
        (item._id !== undefined && (item._id === itemIdNum || String(item._id) === itemIdStr))
      );
    });

    if (item) {
      console.log('تم العثور على الصنف في قاعدة البيانات:', item);
      return item;
    } else {
      console.log('لم يتم العثور على الصنف في قاعدة البيانات بالمعرف:', itemIdStr, '(نص) أو', itemIdNum, '(رقم)');
      return null;
    }
  } catch (error) {
    console.error('Error fetching item:', error);
    return null; // إرجاع null بدلاً من رمي استثناء لتحسين المرونة
  }
};

// الحصول على الأصناف مع التحميل التدريجي
export const getItemsPaginated = async (page = 1, pageSize = 20, filters = {}) => {
  try {
    // إنشاء مفتاح فريد للتخزين المؤقت بناءً على معلمات التصفية
    const cacheKey = `items_page_${page}_size_${pageSize}_${JSON.stringify(filters)}`;

    // التحقق من وجود البيانات في التخزين المؤقت
    const cachedItems = itemsCache.get(cacheKey);
    if (cachedItems) {
      console.log(`استخدام بيانات الأصناف للصفحة ${page} من التخزين المؤقت`);
      return cachedItems;
    }

    console.log(`تحميل بيانات الأصناف للصفحة ${page} من قاعدة البيانات`);

    // حساب معلمات التحميل التدريجي
    const skip = (page - 1) * pageSize;
    const limit = pageSize;

    // إضافة معلمات التحميل التدريجي إلى الفلاتر
    const queryParams = { ...filters, skip, limit };

    // استعلام قاعدة البيانات
    const result = await safeInvoke('get-items-paginated', queryParams);

    // تخزين البيانات في التخزين المؤقت
    if (result && (result.items || result.totalCount)) {
      itemsCache.set(cacheKey, result);
    } else {
      console.warn('البيانات المستلمة غير صالحة:', result);
      return { items: [], totalCount: 0 };
    }

    return result;
  } catch (error) {
    console.error(`Error fetching items for page ${page}:`, error);
    throw error;
  }
};

// Customers
export const getCustomers = async (forceRefresh = false) => {
  console.log(`بدء تنفيذ دالة getCustomers... (forceRefresh: ${forceRefresh})`);

  // دائماً تجاوز التخزين المؤقت عند إضافة عميل فرعي
  // التحقق من وجود البيانات في التخزين المؤقت
  if (!forceRefresh) {
    const cachedCustomers = customersCache.get('all_customers');
    if (cachedCustomers) {
      // تحقق من عدد العملاء في التخزين المؤقت
      console.log(`استخدام بيانات العملاء من التخزين المؤقت (${cachedCustomers.length} عميل)`);

      // إذا كان هناك عميل واحد فقط، نتجاهل التخزين المؤقت ونجلب البيانات من قاعدة البيانات
      if (cachedCustomers.length <= 1) {
        console.log('عدد العملاء في التخزين المؤقت قليل جداً، تجاهل التخزين المؤقت وجلب البيانات من قاعدة البيانات');
        // مسح التخزين المؤقت
        customersCache.delete('all_customers');
      } else {
        return cachedCustomers;
      }
    }
  } else {
    console.log('تجاوز التخزين المؤقت للعملاء بسبب forceRefresh = true');
    // مسح التخزين المؤقت
    customersCache.delete('all_customers');
  }

  // التحقق من توفر window.api
  if (!checkApiAvailability()) {
    console.error('فشل في الحصول على العملاء بسبب عدم توفر window.api');
    console.log('إرجاع مصفوفة فارغة بدلاً من رمي استثناء');
    return [];
  }

  try {
    console.log('جاري الحصول على العملاء من قاعدة البيانات...');

    // استخدام وظيفة العملاء المباشرة
    let customers;
    try {
      if (window.api.customers && typeof window.api.customers.getAll === 'function') {
        console.log('استخدام window.api.customers.getAll()');
        customers = await window.api.customers.getAll();
      } else {
        console.log('استخدام window.api.invoke("get-customers")');
        customers = await window.api.invoke('get-customers');
      }
    } catch (apiError) {
      console.error('خطأ في استدعاء API للحصول على العملاء:', apiError);
      console.log('إرجاع مصفوفة فارغة بسبب خطأ في استدعاء API');
      return [];
    }

    // التحقق من أن البيانات المستلمة هي مصفوفة
    if (!Array.isArray(customers)) {
      console.error('البيانات المستلمة ليست مصفوفة:', customers);
      console.log('إرجاع مصفوفة فارغة بدلاً من البيانات غير الصالحة');
      return [];
    }

    console.log(`تم الحصول على ${customers.length} عميل بنجاح`);

    // التأكد من أن كل عميل لديه حقل id للتوافق مع الواجهة الأمامية
    const processedCustomers = customers.map(customer => {
      if (!customer) {
        console.warn('تم العثور على عميل فارغ أو غير معرف في البيانات المستلمة');
        return null;
      }

      try {
        // التأكد من أن القيم الرقمية هي أرقام
        const processedCustomer = {
          ...customer,
          id: customer._id || customer.id,
          credit_limit: Number(customer.credit_limit || 0),
          balance: Number(customer.balance || 0),
          // التأكد من وجود سجل مبيعات
          sales_history: customer.sales_history || []
        };

        // التأكد من أن parent_id هو نص إذا كان موجودًا
        if (processedCustomer.parent_id) {
          processedCustomer.parent_id = String(processedCustomer.parent_id);
        }

        return processedCustomer;
      } catch (processingError) {
        console.error('خطأ في معالجة بيانات العميل:', processingError, customer);
        return null;
      }
    }).filter(customer => customer !== null); // إزالة العملاء الفارغين

    console.log('تمت معالجة بيانات العملاء بنجاح، عدد العملاء بعد المعالجة:', processedCustomers.length);

    // تخزين البيانات في التخزين المؤقت
    customersCache.set('all_customers', processedCustomers);

    return processedCustomers;
  } catch (error) {
    console.error('خطأ عام في الحصول على العملاء:', error);
    console.log('إرجاع مصفوفة فارغة بسبب خطأ عام');
    return [];
  }
};

// الحصول على عميل محدد بواسطة المعرف
export const getCustomerById = async (customerId) => {
  console.log('بدء تنفيذ دالة getCustomerById للمعرف:', customerId);

  if (!customerId) {
    console.error('معرف العميل غير صالح أو غير محدد:', customerId);
    return null;
  }

  // تحويل المعرف إلى نص ورقم للمقارنة الآمنة
  const customerIdStr = String(customerId);
  const customerIdNum = Number(customerId);
  console.log('البحث عن العميل بالمعرف:', customerIdStr, '(نص) أو', customerIdNum, '(رقم)');

  // محاولة الحصول على العميل من التخزين المؤقت أولاً
  const cachedCustomers = customersCache.get('all_customers');
  if (cachedCustomers && Array.isArray(cachedCustomers)) {
    const cachedCustomer = cachedCustomers.find(customer => {
      // التحقق من جميع أشكال المعرف المحتملة (نصية ورقمية)
      return (
        (customer.id !== undefined && (customer.id === customerIdNum || String(customer.id) === customerIdStr)) ||
        (customer._id !== undefined && (customer._id === customerIdNum || String(customer._id) === customerIdStr))
      );
    });

    if (cachedCustomer) {
      console.log('تم العثور على العميل في التخزين المؤقت:', cachedCustomer);
      return cachedCustomer;
    }
  }

  // إذا لم يتم العثور على العميل في التخزين المؤقت، قم بالبحث عنه في قاعدة البيانات
  console.log('العميل غير موجود في التخزين المؤقت، جاري البحث في قاعدة البيانات...');

  try {
    // استخدام وظيفة العملاء المباشرة
    let customer;
    if (window.api.customers && typeof window.api.customers.getById === 'function') {
      console.log('استخدام window.api.customers.getById()');
      customer = await window.api.customers.getById(customerIdStr);
    } else {
      console.log('استخدام window.api.invoke("get-customer-by-id")');
      customer = await window.api.invoke('get-customer-by-id', customerIdStr);
    }

    if (!customer) {
      console.log('لم يتم العثور على العميل في قاعدة البيانات بالمعرف:', customerIdStr);
      return null;
    }

    console.log('تم العثور على العميل في قاعدة البيانات:', customer);

    // معالجة بيانات العميل
    const processedCustomer = {
      ...customer,
      id: customer._id || customer.id,
      credit_limit: Number(customer.credit_limit || 0),
      balance: Number(customer.balance || 0),
      sales_history: customer.sales_history || []
    };

    // التأكد من أن parent_id هو نص إذا كان موجودًا
    if (processedCustomer.parent_id) {
      processedCustomer.parent_id = String(processedCustomer.parent_id);
    }

    // تحديث التخزين المؤقت بالعميل الجديد
    if (cachedCustomers) {
      const updatedCustomers = [...cachedCustomers];
      const existingIndex = updatedCustomers.findIndex(c =>
        (c.id === customerIdNum || String(c.id) === customerIdStr) ||
        (c._id === customerIdNum || String(c._id) === customerIdStr)
      );

      if (existingIndex >= 0) {
        updatedCustomers[existingIndex] = processedCustomer;
      } else {
        updatedCustomers.push(processedCustomer);
      }

      customersCache.set('all_customers', updatedCustomers);
    }

    return processedCustomer;
  } catch (error) {
    console.error('خطأ في الحصول على العميل من قاعدة البيانات:', error);
    return null;
  }
};

// الحصول على العملاء مع التحميل التدريجي
export const getCustomersPaginated = async (page = 1, pageSize = 20, filters = {}) => {
  try {
    // إنشاء مفتاح فريد للتخزين المؤقت بناءً على معلمات التصفية
    const cacheKey = `customers_page_${page}_size_${pageSize}_${JSON.stringify(filters)}`;

    // التحقق من وجود البيانات في التخزين المؤقت
    const cachedCustomers = customersCache.get(cacheKey);
    if (cachedCustomers) {
      console.log(`استخدام بيانات العملاء للصفحة ${page} من التخزين المؤقت`);
      return cachedCustomers;
    }

    // التحقق من توفر window.api
    if (!checkApiAvailability()) {
      console.error(`فشل في الحصول على العملاء للصفحة ${page} بسبب عدم توفر window.api`);
      return { customers: [], totalCount: 0 };
    }

    console.log(`تحميل بيانات العملاء للصفحة ${page} من قاعدة البيانات`);

    // حساب معلمات التحميل التدريجي
    const skip = (page - 1) * pageSize;
    const limit = pageSize;

    // إضافة معلمات التحميل التدريجي إلى الفلاتر
    const queryParams = { ...filters, skip, limit };

    // استعلام قاعدة البيانات
    const result = await window.api.invoke('get-customers-paginated', queryParams);

    // معالجة البيانات
    if (result && result.customers) {
      const processedCustomers = result.customers.map(customer => ({
        ...customer,
        id: customer._id || customer.id,
        credit_limit: Number(customer.credit_limit || 0),
        balance: Number(customer.balance || 0),
        sales_history: customer.sales_history || []
      }));

      const processedResult = {
        customers: processedCustomers,
        totalCount: result.totalCount || processedCustomers.length
      };

      // تخزين البيانات في التخزين المؤقت
      customersCache.set(cacheKey, processedResult);

      return processedResult;
    }

    return { customers: [], totalCount: 0 };
  } catch (error) {
    console.error(`Error fetching customers for page ${page}:`, error);
    return { customers: [], totalCount: 0 };
  }
};

export const addCustomer = async (customer) => {
  console.log('بدء وظيفة إضافة العميل...');

  // التحقق من توفر window.api
  const apiAvailable = checkApiAvailability();
  if (!apiAvailable) {
    console.error('فشل في إضافة العميل بسبب عدم توفر window.api');

    // محاولة استخدام window.api المؤقت إذا تم إنشاؤه
    if (window.api) {
      console.log('محاولة استخدام window.api المؤقت...');
      try {
        // إعادة تحميل الصفحة قد يساعد في حل المشكلة
        console.log('اقتراح: قم بإعادة تحميل الصفحة لحل المشكلة');

        // إظهار رسالة خطأ للمستخدم
        alert('حدث خطأ في الاتصال بقاعدة البيانات. يرجى إعادة تحميل الصفحة والمحاولة مرة أخرى.');

        throw new Error('فشل في إضافة العميل بسبب عدم توفر window.api');
      } catch (error) {
        console.error('خطأ أثناء محاولة استخدام window.api المؤقت:', error);
        throw error;
      }
    } else {
      throw new Error('فشل في إضافة العميل بسبب عدم توفر window.api');
    }
  }

  try {
    console.log('جاري إضافة العميل:', customer);

    // تنظيف البيانات قبل الإرسال
    const cleanedCustomer = {
      name: String(customer.name || '').trim(),
      contact_person: String(customer.contact_person || '').trim(),
      phone: String(customer.phone || '').trim(),
      email: String(customer.email || '').trim(),
      address: String(customer.address || '').trim(),
      customer_type: String(customer.customer_type || 'normal').trim(),
      parent_id: customer.parent_id || null,
      credit_limit: Number(customer.credit_limit || 0),
      balance: Number(customer.balance || 0),
      sales_history: customer.sales_history || [],
      total_sales: Number(customer.total_sales || 0),
      total_profit: Number(customer.total_profit || 0)
    };

    // معالجة خاصة للعملاء الفرعيين
    if (cleanedCustomer.customer_type === 'sub' && cleanedCustomer.parent_id) {
      console.log('إضافة عميل فرعي مع العميل الدائم:', cleanedCustomer.parent_id);

      // التأكد من أن parent_id هو نص
      if (typeof cleanedCustomer.parent_id !== 'string') {
        cleanedCustomer.parent_id = String(cleanedCustomer.parent_id);
      }
    }

    console.log('بيانات العميل بعد التنظيف:', cleanedCustomer);

    // استخدام وظيفة العملاء المباشرة
    let newCustomer;

    // التحقق من وجود window.api.customers.add
    if (window.api.customers && typeof window.api.customers.add === 'function') {
      console.log('استخدام window.api.customers.add()');
      try {
        newCustomer = await window.api.customers.add(cleanedCustomer);
      } catch (error) {
        console.error('خطأ في استدعاء window.api.customers.add:', error);
        console.log('محاولة استخدام window.api.invoke كبديل...');
        newCustomer = await window.api.invoke('add-customer', cleanedCustomer);
      }
    } else {
      console.log('استخدام window.api.invoke("add-customer")');
      newCustomer = await window.api.invoke('add-customer', cleanedCustomer);
    }

    console.log('تمت إضافة العميل بنجاح:', newCustomer);

    // مسح التخزين المؤقت للعملاء لضمان تحديث البيانات
    console.log('مسح التخزين المؤقت للعملاء بعد إضافة عميل جديد');
    customersCache.clear();

    // التأكد من وجود حقل id للتوافق مع الواجهة الأمامية
    const processedCustomer = {
      ...newCustomer,
      id: newCustomer._id || newCustomer.id,
      sales_history: newCustomer.sales_history || [],
      total_sales: newCustomer.total_sales || 0,
      total_profit: newCustomer.total_profit || 0
    };

    // إذا كان العميل فرعي، تأكد من وجود parent_id
    if (processedCustomer.customer_type === 'sub' && !processedCustomer.parent_id && cleanedCustomer.parent_id) {
      processedCustomer.parent_id = cleanedCustomer.parent_id;

      // تحديث العميل في قاعدة البيانات
      try {
        console.log('تحديث العميل الفرعي مع parent_id:', processedCustomer.parent_id);
        await window.api.invoke('update-customer', processedCustomer);
      } catch (updateError) {
        console.error('خطأ في تحديث العميل الفرعي:', updateError);
      }
    }

    return processedCustomer;
  } catch (error) {
    console.error('خطأ في إضافة العميل:', error);

    // تحسين رسالة الخطأ للمستخدم
    if (error.message.includes('window.api')) {
      throw new Error('فشل في إضافة العميل بسبب مشكلة في الاتصال بقاعدة البيانات. يرجى إعادة تشغيل التطبيق والمحاولة مرة أخرى.');
    } else {
      throw error;
    }
  }
};

export const updateCustomer = async (customer) => {
  if (!checkApiAvailability()) {
    console.error('فشل في تحديث العميل بسبب عدم توفر window.api');
    throw new Error('فشل في تحديث العميل بسبب عدم توفر window.api');
  }

  try {
    console.log('جاري تحديث العميل:', customer);

    // تنظيف البيانات قبل الإرسال
    const cleanedCustomer = {
      name: String(customer.name || '').trim(),
      contact_person: String(customer.contact_person || '').trim(),
      phone: String(customer.phone || '').trim(),
      email: String(customer.email || '').trim(),
      address: String(customer.address || '').trim(),
      customer_type: String(customer.customer_type || 'normal').trim(),
      parent_id: customer.parent_id || null,
      credit_limit: Number(customer.credit_limit || 0),
      balance: Number(customer.balance || 0),
      sales_history: customer.sales_history || [],
      total_sales: Number(customer.total_sales || 0),
      total_profit: Number(customer.total_profit || 0)
    };

    // التأكد من وجود _id للتوافق مع قاعدة البيانات
    const customerData = {
      ...cleanedCustomer,
      _id: customer._id || customer.id,
      id: customer._id || customer.id
    };

    console.log('بيانات العميل بعد التنظيف:', customerData);

    // الحصول على دور المستخدم الحالي من التخزين المحلي
    const currentUserRole = localStorage.getItem('currentUserRole') || 'employee';
    console.log('تحديث العميل بواسطة مستخدم بدور:', currentUserRole);

    // استخدام وظيفة window.api.customers.update بدلاً من safeInvoke
    let result;
    if (window.api.customers && typeof window.api.customers.update === 'function') {
      console.log('استخدام window.api.customers.update');
      result = await window.api.customers.update(customerData.id, customerData);
    } else {
      console.log('استخدام window.api.invoke كبديل');
      result = await window.api.invoke('update-customer', customerData.id, customerData, currentUserRole);
    }

    console.log('تم تحديث العميل بنجاح:', result);

    // مسح التخزين المؤقت للعملاء لضمان تحديث البيانات
    console.log('مسح التخزين المؤقت للعملاء بعد تحديث عميل');
    customersCache.clear();

    // التأكد من وجود حقل id للتوافق مع الواجهة الأمامية
    const updatedCustomer = {
      ...result,
      id: result.id || result._id,
      _id: result._id || result.id,
      sales_history: result.sales_history || customer.sales_history || [],
      total_sales: Number(result.total_sales || customer.total_sales || 0),
      total_profit: Number(result.total_profit || customer.total_profit || 0)
    };

    return updatedCustomer;
  } catch (error) {
    console.error('خطأ في تحديث العميل:', error);

    // تحسين رسالة الخطأ للمستخدم
    if (error.message && error.message.includes('window.api')) {
      throw new Error('فشل في تحديث العميل بسبب مشكلة في الاتصال بقاعدة البيانات. يرجى إعادة تشغيل التطبيق والمحاولة مرة أخرى.');
    } else {
      throw error;
    }
  }
};

export const deleteCustomer = async (id, userRole) => {
  if (!checkApiAvailability()) {
    console.error('فشل في حذف العميل بسبب عدم توفر window.api');
    throw new Error('فشل في حذف العميل بسبب عدم توفر window.api');
  }

  try {
    if (!id) {
      console.error('معرف العميل غير صالح:', id);
      throw new Error('معرف العميل غير صالح');
    }

    console.log('جاري حذف العميل مع المعرف:', id);

    // التأكد من أن المعرف هو نص
    const customerId = String(id);

    // الحصول على دور المستخدم الحالي من التخزين المحلي إذا لم يتم تمريره
    if (!userRole) {
      userRole = localStorage.getItem('currentUserRole') || 'employee';
    }
    console.log('حذف العميل بواسطة مستخدم بدور:', userRole);

    // استخدام وظيفة العملاء المباشرة
    let result;
    if (window.api.customers && typeof window.api.customers.delete === 'function') {
      console.log('استخدام window.api.customers.delete()');
      result = await window.api.customers.delete(customerId, userRole);
    } else {
      console.log('استخدام window.api.invoke("delete-customer")');
      result = await window.api.invoke('delete-customer', customerId, userRole);
    }

    console.log('نتيجة حذف العميل:', result);

    // التحقق من نجاح العملية
    if (result && result.success === false) {
      // التحقق من صلاحيات المستخدم
      if (result.error && result.error.includes('ليس لديك صلاحية')) {
        console.error('خطأ في صلاحيات المستخدم:', result.error);
        throw new Error(result.error);
      }

      console.error('فشل في حذف العميل:', result.error);
      throw new Error(result.error || 'فشل في حذف العميل');
    }

    // مسح التخزين المؤقت للعملاء لضمان تحديث البيانات
    console.log('مسح التخزين المؤقت للعملاء بعد حذف عميل');
    customersCache.clear();

    // إعادة تحميل قائمة العملاء بشكل متزامن
    try {
      console.log('إعادة تحميل قائمة العملاء بعد الحذف...');

      // استخدام Promise.all لضمان اكتمال جميع العمليات
      await Promise.all([
        // تأخير قصير لضمان اكتمال عملية الحذف في قاعدة البيانات
        new Promise(resolve => setTimeout(resolve, 500)),

        // استدعاء وظيفة الحصول على العملاء
        getCustomers(true)
      ]);
    } catch (reloadError) {
      console.warn('فشل في إعادة تحميل قائمة العملاء بعد الحذف:', reloadError);
      // لا نريد إيقاف العملية إذا فشل إعادة التحميل
    }

    console.log('تم حذف العميل بنجاح');
    return result;
  } catch (error) {
    console.error('خطأ في حذف العميل:', error);
    throw error;
  }
};

// تم إزالة وظائف الموردين

// تم إزالة وظائف الموردين

export const getInventory = async (forceRefresh = false) => {
  try {
    // التحقق من وجود البيانات في التخزين المؤقت (إلا إذا تم طلب تحديث إجباري)
    const cachedInventory = inventoryCache.get('all_inventory');
    if (cachedInventory && !forceRefresh) {
      console.log('استخدام بيانات المخزون من التخزين المؤقت');
      return cachedInventory;
    }

    console.log('تحميل بيانات المخزون من قاعدة البيانات' + (forceRefresh ? ' (تحديث إجباري)' : ''));

    // استخدام معلمة forceRefresh للتحكم في تحديث التخزين المؤقت في الخادم
    const inventory = await safeInvoke('get-inventory', forceRefresh);

    // تخزين البيانات في التخزين المؤقت
    if (Array.isArray(inventory)) {
      // معالجة البيانات للتأكد من أن جميع الحقول موجودة
      const processedInventory = inventory.map(item => {
        // تسجيل معلومات تشخيصية للصنف
        console.log(`معالجة صنف المخزون ${item.id || item._id || item.item_id} (${item.name}):`, {
          minimum_quantity: item.minimum_quantity,
          avg_price: item.avg_price,
          selling_price: item.selling_price,
          minimum_quantity_type: typeof item.minimum_quantity,
          avg_price_type: typeof item.avg_price
        });

        return {
          ...item,
          id: item.id || item._id,
          _id: item._id || item.id,
          item_id: item.item_id || item.id || item._id,
          current_quantity: item.current_quantity !== undefined ? Number(item.current_quantity) : 0,
          minimum_quantity: item.minimum_quantity !== undefined ? Number(item.minimum_quantity) : 0,
          avg_price: item.avg_price !== undefined ? Number(item.avg_price) : 0,
          selling_price: item.selling_price !== undefined ? Number(item.selling_price) : 0
        };
      });

      // تخزين البيانات في التخزين المؤقت مع وقت انتهاء الصلاحية
      inventoryCache.set('all_inventory', processedInventory);
      console.log(`تم تحميل ${processedInventory.length} صنف في المخزون بنجاح`);
      return processedInventory;
    } else {
      console.warn('البيانات المستلمة ليست مصفوفة:', inventory);
      return [];
    }
  } catch (error) {
    console.error('Error fetching inventory:', error);
    throw error;
  }
};

// الحصول على عنصر محدد من المخزون
export const getInventoryItem = async (itemId) => {
  try {
    if (!itemId && itemId !== 0) {
      console.error('معرف الصنف غير صالح أو غير محدد:', itemId);
      return null;
    }

    // تحويل المعرف إلى نص ورقم للمقارنة الآمنة
    const itemIdStr = String(itemId);
    const itemIdNum = Number(itemId);
    console.log('الحصول على عنصر محدد من المخزون:', itemIdStr, '(نص) أو', itemIdNum, '(رقم)');

    // محاولة الحصول على العنصر من التخزين المؤقت أولاً
    const cachedInventory = inventoryCache.get('all_inventory');
    if (cachedInventory) {
      const cachedItem = cachedInventory.find(item => {
        // التحقق من جميع أشكال المعرف المحتملة (نصية ورقمية)
        return (
          (item.item_id !== undefined && (item.item_id === itemIdNum || String(item.item_id) === itemIdStr)) ||
          (item.id !== undefined && (item.id === itemIdNum || String(item.id) === itemIdStr)) ||
          (item._id !== undefined && (item._id === itemIdNum || String(item._id) === itemIdStr))
        );
      });

      if (cachedItem) {
        console.log('تم العثور على العنصر في التخزين المؤقت:', cachedItem);
        return cachedItem;
      }
    }

    // إذا لم يتم العثور على العنصر في التخزين المؤقت، قم بتحميل جميع عناصر المخزون
    const inventory = await getInventory(true);

    // البحث عن العنصر في المخزون المحدث
    const item = inventory.find(item => {
      // التحقق من جميع أشكال المعرف المحتملة (نصية ورقمية)
      return (
        (item.item_id !== undefined && (item.item_id === itemIdNum || String(item.item_id) === itemIdStr)) ||
        (item.id !== undefined && (item.id === itemIdNum || String(item.id) === itemIdStr)) ||
        (item._id !== undefined && (item._id === itemIdNum || String(item._id) === itemIdStr))
      );
    });

    if (item) {
      console.log('تم العثور على العنصر في المخزون:', item);
      return item;
    } else {
      console.log('لم يتم العثور على العنصر في المخزون من الذاكرة المؤقتة، محاولة الحصول عليه مباشرة من قاعدة البيانات...');

      // محاولة الحصول على العنصر مباشرة من قاعدة البيانات عبر IPC مع تجاوز التخزين المؤقت
      try {
        console.log('استدعاء get-inventory-item مباشرة من قاعدة البيانات للمعرف:', itemIdStr);
        // تجاوز التخزين المؤقت للحصول على أحدث البيانات
        const directItem = await window.api.invoke('get-inventory-item', itemIdStr, true);

        if (directItem) {
          console.log('تم العثور على العنصر مباشرة من قاعدة البيانات:', directItem);

          // معالجة البيانات للتأكد من أن جميع الحقول موجودة
          const processedItem = {
            ...directItem,
            id: directItem.id || directItem._id,
            _id: directItem._id || directItem.id,
            item_id: directItem.item_id || directItem.id || directItem._id,
            current_quantity: directItem.current_quantity !== undefined ? Number(directItem.current_quantity) : 0,
            minimum_quantity: directItem.minimum_quantity !== undefined ? Number(directItem.minimum_quantity) : 0,
            avg_price: directItem.avg_price !== undefined ? Number(directItem.avg_price) : 0,
            selling_price: directItem.selling_price !== undefined ? Number(directItem.selling_price) : 0
          };

          // تحديث التخزين المؤقت بالعنصر الجديد
          if (cachedInventory) {
            const updatedInventory = [...cachedInventory];
            const existingIndex = updatedInventory.findIndex(i =>
              (i.item_id === itemIdNum || String(i.item_id) === itemIdStr) ||
              (i.id === itemIdNum || String(i.id) === itemIdStr) ||
              (i._id === itemIdNum || String(i._id) === itemIdStr)
            );

            if (existingIndex >= 0) {
              updatedInventory[existingIndex] = processedItem;
            } else {
              updatedInventory.push(processedItem);
            }

            inventoryCache.set('all_inventory', updatedInventory);
          }

          return processedItem;
        } else {
          console.log('لم يتم العثور على العنصر في قاعدة البيانات:', itemIdStr);
          return null;
        }
      } catch (directError) {
        console.error('خطأ في الحصول على العنصر مباشرة من قاعدة البيانات:', directError);
        return null;
      }
    }
  } catch (error) {
    console.error('Error fetching inventory item:', error);
    throw error;
  }
};

// الحصول على المخزون مع التحميل التدريجي
export const getInventoryPaginated = async (page = 1, pageSize = 20, filters = {}) => {
  try {
    // إنشاء مفتاح فريد للتخزين المؤقت بناءً على معلمات التصفية
    const cacheKey = `inventory_page_${page}_size_${pageSize}_${JSON.stringify(filters)}`;

    // التحقق من وجود البيانات في التخزين المؤقت
    const cachedInventory = inventoryCache.get(cacheKey);
    if (cachedInventory) {
      console.log(`استخدام بيانات المخزون للصفحة ${page} من التخزين المؤقت`);
      return cachedInventory;
    }

    console.log(`تحميل بيانات المخزون للصفحة ${page} من قاعدة البيانات`);

    // حساب معلمات التحميل التدريجي
    const skip = (page - 1) * pageSize;
    const limit = pageSize;

    // إضافة معلمات التحميل التدريجي إلى الفلاتر
    const queryParams = { ...filters, skip, limit };

    // استعلام قاعدة البيانات
    const result = await window.api.invoke('get-inventory-paginated', queryParams);

    // تخزين البيانات في التخزين المؤقت
    inventoryCache.set(cacheKey, result);

    return result;
  } catch (error) {
    console.error(`Error fetching inventory for page ${page}:`, error);
    throw error;
  }
};

export const updateInventory = async (itemId, updates) => {
  try {
    // التحقق من توفر window.api
    if (!checkApiAvailability()) {
      console.error('فشل في تحديث المخزون بسبب عدم توفر window.api');
      throw new Error('فشل في تحديث المخزون بسبب عدم توفر window.api');
    }

    console.log('جاري تحديث المخزون للصنف:', itemId, 'بالتحديثات:', updates);

    // تنظيف البيانات وتحويلها إلى الأنواع المناسبة
    const cleanedUpdates = {
      current_quantity: updates.current_quantity !== undefined ? Number(updates.current_quantity) : undefined,
      selling_price: updates.selling_price !== undefined ? Number(updates.selling_price) : undefined,
      minimum_quantity: updates.minimum_quantity !== undefined ? Number(updates.minimum_quantity) : undefined
    };

    // إزالة الحقول غير المحددة
    Object.keys(cleanedUpdates).forEach(key => {
      if (cleanedUpdates[key] === undefined) {
        delete cleanedUpdates[key];
      }
    });

    console.log('البيانات المنظفة للتحديث:', cleanedUpdates);

    // استخدام وظيفة safeInvoke بدلاً من window.api.invoke مباشرة
    const result = await safeInvoke('update-inventory', {
      itemId,
      updates: cleanedUpdates
    });

    // مسح التخزين المؤقت للمخزون والأصناف
    console.log('مسح التخزين المؤقت للمخزون والأصناف بعد تحديث المخزون');
    inventoryCache.clear();
    itemsCache.clear();

    console.log('تم تحديث المخزون بنجاح:', result);
    return result;
  } catch (error) {
    console.error('Error updating inventory:', error);
    throw error;
  }
};

// مزامنة المخزون لجميع الأصناف
export const syncAllInventory = async () => {
  try {
    // التحقق من توفر window.api
    if (!checkApiAvailability()) {
      console.error('فشل في مزامنة المخزون بسبب عدم توفر window.api');
      throw new Error('فشل في مزامنة المخزون بسبب عدم توفر window.api');
    }

    console.log('بدء مزامنة المخزون لجميع الأصناف...');

    // 1. الحصول على جميع الأصناف
    const items = await getItems(true);
    console.log(`تم الحصول على ${items.length} صنف للمزامنة`);

    // 2. الحصول على المخزون الحالي
    const inventory = await getInventory(true);
    console.log(`تم الحصول على ${inventory.length} عنصر في المخزون للمزامنة`);

    // 3. استدعاء وظيفة مزامنة المخزون في قاعدة البيانات
    // هذه الوظيفة تحافظ على الكمية الحالية في المخزون
    try {
      console.log('استدعاء وظيفة مزامنة المخزون في قاعدة البيانات...');
      const syncResult = await safeInvoke('sync-all-inventory');
      console.log('نتيجة مزامنة المخزون:', syncResult);
    } catch (apiError) {
      console.error('خطأ في استدعاء API لمزامنة المخزون:', apiError);
      // نستمر في التنفيذ حتى لو فشل استدعاء API
    }

    // 4. الحصول على المخزون المحدث بعد المزامنة
    const updatedInventory = await getInventory(true);
    console.log(`تم الحصول على ${updatedInventory.length} عنصر في المخزون بعد المزامنة`);

    // 5. إنشاء نتائج المزامنة
    const syncResults = [];
    for (const item of items) {
      try {
        // تحويل معرف الصنف إلى نص ورقم للمقارنة الآمنة
        const itemIdStr = String(item.id || item._id);
        const itemIdNum = Number(item.id || item._id);

        // البحث عن عنصر المخزون المقابل قبل المزامنة
        const oldInventoryItem = inventory.find(inv => {
          // التحقق من جميع أشكال المعرف المحتملة (نصية ورقمية)
          return (
            (inv.item_id !== undefined && (inv.item_id === itemIdNum || String(inv.item_id) === itemIdStr)) ||
            (inv._id !== undefined && (inv._id === itemIdNum || String(inv._id) === itemIdStr)) ||
            (inv.id !== undefined && (inv.id === itemIdNum || String(inv.id) === itemIdStr))
          );
        });

        // البحث عن عنصر المخزون المقابل بعد المزامنة
        const newInventoryItem = updatedInventory.find(inv => {
          // التحقق من جميع أشكال المعرف المحتملة (نصية ورقمية)
          return (
            (inv.item_id !== undefined && (inv.item_id === itemIdNum || String(inv.item_id) === itemIdStr)) ||
            (inv._id !== undefined && (inv._id === itemIdNum || String(inv._id) === itemIdStr)) ||
            (inv.id !== undefined && (inv.id === itemIdNum || String(inv.id) === itemIdStr))
          );
        });

        // تسجيل معلومات المزامنة
        const oldQuantity = oldInventoryItem ? oldInventoryItem.current_quantity : 0;
        const newQuantity = newInventoryItem ? newInventoryItem.current_quantity : 0;
        const updated = oldQuantity !== newQuantity;

        console.log(`مزامنة المخزون للصنف: ${item.name} (${item.id || item._id})`);
        console.log(`- الكمية قبل المزامنة: ${oldQuantity}`);
        console.log(`- الكمية بعد المزامنة: ${newQuantity}`);
        console.log(`- تم التحديث: ${updated ? 'نعم' : 'لا'}`);

        syncResults.push({
          item_id: item.id || item._id,
          name: item.name,
          old_quantity: oldQuantity,
          new_quantity: newQuantity,
          updated: updated
        });
      } catch (itemError) {
        console.error(`خطأ في مزامنة المخزون للصنف ${item.name}:`, itemError);
        syncResults.push({
          item_id: item.id || item._id,
          name: item.name,
          error: itemError.message,
          updated: false
        });
      }
    }

    // مسح التخزين المؤقت للمخزون والأصناف
    console.log('مسح التخزين المؤقت للمخزون والأصناف بعد مزامنة المخزون');
    inventoryCache.clear();
    itemsCache.clear();
    transactionsCache.clear();

    console.log('تمت مزامنة المخزون بنجاح:', syncResults);
    return {
      success: true,
      message: `تمت مزامنة المخزون بنجاح لـ ${items.length} صنف`,
      results: syncResults
    };
  } catch (error) {
    console.error('Error syncing inventory:', error);
    throw error;
  }
};

export const addItem = async (item) => {
  try {
    console.log('جاري إضافة صنف جديد:', item);

    // إضافة الصنف في قاعدة البيانات
    const result = await window.api.invoke('add-item', item);

    // مسح التخزين المؤقت للأصناف والمخزون لضمان تحديث البيانات
    console.log('مسح التخزين المؤقت للأصناف والمخزون بعد إضافة صنف جديد');
    itemsCache.clear();
    inventoryCache.clear();

    // إذا تم تحديد الحد الأدنى، تأكد من تحديث المخزون أيضًا
    if (item.minimum_quantity !== undefined && result && (result.id || result._id)) {
      console.log('تم تحديد الحد الأدنى، جاري تحديث المخزون...');
      try {
        // تحديث المخزون لضمان تحديث الحد الأدنى
        await updateInventory(result.id || result._id, {
          minimum_quantity: item.minimum_quantity
        });
      } catch (inventoryError) {
        console.error('خطأ في تحديث المخزون بعد إضافة الصنف:', inventoryError);
        // لا نرفض هنا لأن إضافة الصنف نجحت بالفعل
      }
    }

    return result;
  } catch (error) {
    console.error('Error adding item:', error);
    throw error;
  }
};

export const updateItem = async (item) => {
  try {
    console.log('جاري تحديث الصنف:', item);

    // التحقق من وجود معرف الصنف
    const itemId = item.id || item._id;
    if (!itemId) {
      console.error('معرف الصنف غير موجود:', item);
      throw new Error('معرف الصنف غير موجود');
    }

    // الحصول على دور المستخدم الحالي من التخزين المحلي
    const currentUserRole = localStorage.getItem('currentUserRole') || 'employee';
    console.log('تحديث الصنف بواسطة مستخدم بدور:', currentUserRole);

    // السماح فقط للموظف أو المدير بالتعديل
    if (currentUserRole !== 'employee' && currentUserRole !== 'admin') {
      throw new Error('ليس لديك صلاحية تعديل بيانات الصنف.');
    }

    // إنشاء كائن التحديثات
    const updates = {};

    // إضافة الاسم فقط إذا كان موجودًا وليس فارغًا
    if (item.name !== undefined && item.name !== null && item.name.trim() !== '') {
      updates.name = item.name.trim();
    }

    // إضافة وحدة القياس فقط إذا كانت موجودة وليست فارغة
    if (item.unit !== undefined && item.unit !== null && item.unit.trim() !== '') {
      updates.unit = item.unit.trim();
    }

    console.log('بيانات التحديث:', updates);

    // التحقق من وجود تحديثات قبل إرسالها للخادم
    if (Object.keys(updates).length === 0) {
      console.log('لا توجد تحديثات للإرسال، سيتم إرجاع نجاح وهمي');
      return {
        success: true,
        item: await getItem(itemId)
      };
    }

    // تحديث الصنف في قاعدة البيانات مع تمرير دور المستخدم
    console.log('استدعاء update-item مع المعرف:', itemId, 'والتحديثات:', updates);
    const result = await window.api.invoke('update-item', itemId, updates, currentUserRole);
    console.log('نتيجة تحديث الصنف:', result);

    // التحقق من نجاح العملية
    if (!result || result.success === false) {
      console.error('فشل في تحديث الصنف:', result ? result.error : 'سبب غير معروف');

      // إذا كان الخطأ هو "لم يتم تحديد أي تحديثات"، نعتبر العملية ناجحة
      if (result && result.error && result.error.includes('لم يتم تحديد أي تحديثات')) {
        console.log('تجاهل خطأ "لم يتم تحديد أي تحديثات" واعتبار العملية ناجحة');

        // إنشاء نتيجة نجاح وهمية
        return {
          success: true,
          item: await getItem(itemId)
        };
      }

      throw new Error(result && result.error ? result.error : 'فشل في تحديث الصنف');
    }

    // مسح التخزين المؤقت للأصناف والمخزون لضمان تحديث البيانات
    console.log('مسح التخزين المؤقت للأصناف والمخزون بعد تحديث الصنف');
    itemsCache.clear();
    inventoryCache.clear();

    // إذا تم تحديث الحد الأدنى، تأكد من تحديث المخزون أيضًا
    if (item.minimum_quantity !== undefined) {
      console.log('تم تحديث الحد الأدنى، جاري تحديث المخزون...');
      try {
        // تحديث المخزون لضمان تحديث الحد الأدنى
        await updateInventory(itemId, {
          minimum_quantity: item.minimum_quantity
        });
      } catch (inventoryError) {
        console.error('خطأ في تحديث المخزون بعد تحديث الصنف:', inventoryError);
        // لا نرفض هنا لأن تحديث الصنف نجح بالفعل
      }
    }

    // إعادة تحميل الأصناف من قاعدة البيانات
    try {
      console.log('إعادة تحميل الأصناف بعد التحديث...');
      await getItems(true);
      console.log('تم إعادة تحميل الأصناف بنجاح');
    } catch (reloadError) {
      console.error('خطأ في إعادة تحميل الأصناف بعد التحديث:', reloadError);
      // لا نريد إيقاف العملية إذا فشل إعادة التحميل
    }

    return result;
  } catch (error) {
    console.error('Error updating item:', error);
    throw error;
  }
};

export const deleteItem = async (id, forceDelete = false) => {
  console.log('Database: Deleting item with ID:', id, 'Force delete:', forceDelete);

  if (!id) {
    console.error('Invalid item ID for deletion');
    return Promise.reject(new Error('معرف الصنف غير صالح'));
  }

  try {
    // الحصول على دور المستخدم الحالي من التخزين المحلي
    const currentUserRole = localStorage.getItem('currentUserRole') || 'employee';
    console.log('حذف الصنف بواسطة مستخدم بدور:', currentUserRole);

    // السماح فقط للمدير بالحذف
    if (currentUserRole !== 'admin') {
      return Promise.reject(new Error('فقط المدير يمكنه حذف الأصناف.'));
    }

    // استخدام وظيفة window.api.invoke مباشرة
    // إرسال معلمة forceDelete للسماح بحذف الأصناف التي لها معاملات بيع
    // وإرسال دور المستخدم للتحقق من الصلاحيات
    const result = await window.api.invoke('delete-item', { id, forceDelete, userRole: currentUserRole });
    console.log('Database: Delete item result:', result);

    // مسح التخزين المؤقت للأصناف والمخزون لضمان تحديث البيانات
    console.log('مسح التخزين المؤقت للأصناف والمخزون بعد حذف الصنف');
    itemsCache.clear();
    inventoryCache.clear();

    return result;
  } catch (error) {
    console.error('Error deleting item from database:', error);
    return Promise.reject(error);
  }
};

// Transactions
export const getTransactions = async (filters = null) => {
  try {
    // إنشاء مفتاح فريد للتخزين المؤقت بناءً على الفلاتر
    const cacheKey = `transactions_${JSON.stringify(filters || 'all')}`;

    // التحقق من وجود البيانات في التخزين المؤقت
    const cachedTransactions = transactionsCache.get(cacheKey);
    if (cachedTransactions) {
      console.log('استخدام بيانات المعاملات من التخزين المؤقت');
      return cachedTransactions;
    }

    console.log('تحميل بيانات المعاملات من قاعدة البيانات');
    const transactions = await safeInvoke('get-transactions', filters);

    // تخزين البيانات في التخزين المؤقت
    if (Array.isArray(transactions)) {
      transactionsCache.set(cacheKey, transactions);
    } else {
      console.warn('البيانات المستلمة ليست مصفوفة:', transactions);
      return [];
    }

    return transactions;
  } catch (error) {
    console.error('Error fetching transactions:', error);
    throw error;
  }
};

// الحصول على المعاملات مع التحميل التدريجي
export const getTransactionsPaginated = async (page = 1, pageSize = 20, filters = {}) => {
  try {
    // إنشاء مفتاح فريد للتخزين المؤقت بناءً على معلمات التصفية
    const cacheKey = `transactions_page_${page}_size_${pageSize}_${JSON.stringify(filters)}`;

    // التحقق من وجود البيانات في التخزين المؤقت
    const cachedTransactions = transactionsCache.get(cacheKey);
    if (cachedTransactions) {
      console.log(`استخدام بيانات المعاملات للصفحة ${page} من التخزين المؤقت`);
      return cachedTransactions;
    }

    console.log(`تحميل بيانات المعاملات للصفحة ${page} من قاعدة البيانات`);

    // حساب معلمات التحميل التدريجي
    const skip = (page - 1) * pageSize;
    const limit = pageSize;

    // إضافة معلمات التحميل التدريجي إلى الفلاتر
    const queryParams = { ...filters, skip, limit };

    // استعلام قاعدة البيانات
    const result = await window.api.invoke('get-transactions-paginated', queryParams);

    // تخزين البيانات في التخزين المؤقت
    transactionsCache.set(cacheKey, result);

    return result;
  } catch (error) {
    console.error(`Error fetching transactions for page ${page}:`, error);
    throw error;
  }
};


export const addTransaction = async (transaction) => {
  try {
    console.log('جاري إضافة معاملة جديدة:', transaction);

    // التحقق من وجود معرف الصنف
    if (!transaction.item_id) {
      console.error('خطأ: معرف الصنف غير محدد في المعاملة');
      return { success: false, error: 'معرف الصنف غير محدد في المعاملة' };
    }

    // التحقق من وجود الصنف في جدول الأصناف أولاً
    console.log('التحقق من وجود الصنف في جدول الأصناف...');
    const item = await getItem(transaction.item_id);

    if (!item) {
      console.error('خطأ: الصنف غير موجود في جدول الأصناف بالمعرف:', transaction.item_id);
      return { success: false, error: 'الصنف غير موجود في النظام' };
    }

    console.log('تم العثور على الصنف في جدول الأصناف:', item);

    // التحقق من توفر الكمية الكافية للبيع
    if ((transaction.transaction_type === 'sale' || transaction.transaction_type === 'withdrawal') && !transaction.skip_inventory_check) {
      console.log('التحقق من توفر الكمية الكافية للبيع...');

      try {
        // الحصول على عنصر المخزون الحالي
        const inventoryItem = await getInventoryItem(transaction.item_id);

        if (inventoryItem) {
          const currentQuantity = inventoryItem.current_quantity || 0;
          const requestedQuantity = transaction.quantity || 0;

          if (currentQuantity < requestedQuantity) {
            console.error(`خطأ: الكمية المتوفرة (${currentQuantity}) أقل من الكمية المطلوبة للبيع (${requestedQuantity})`);
            return {
              success: false,
              error: `الكمية المتوفرة (${currentQuantity}) أقل من الكمية المطلوبة للبيع (${requestedQuantity})`
            };
          }
        } else {
          // إنشاء سجل مخزون جديد للصنف إذا لم يكن موجوداً
          console.log('الصنف غير موجود في المخزون، جاري إنشاء سجل مخزون جديد له...');

          try {
            await updateInventory(transaction.item_id, {
              current_quantity: 0,
              minimum_quantity: 0,
              avg_price: 0,
              selling_price: 0,
              last_updated: new Date().toISOString()
            });

            console.log('تم إنشاء سجل مخزون جديد للصنف بنجاح');

            // التحقق من نوع المعاملة
            if (transaction.transaction_type === 'sale' || transaction.transaction_type === 'withdrawal') {
              console.error('لا يمكن إجراء عملية بيع أو سحب لصنف غير متوفر في المخزون');
              return { success: false, error: 'الكمية المتوفرة (0) أقل من الكمية المطلوبة للبيع' };
            }
          } catch (updateError) {
            console.error('خطأ في إنشاء سجل مخزون جديد للصنف:', updateError);
            return { success: false, error: 'فشل في إنشاء سجل مخزون للصنف' };
          }
        }
      } catch (checkError) {
        console.error('خطأ في التحقق من توفر الكمية:', checkError);
        return {
          success: false,
          error: checkError.message || 'خطأ في التحقق من توفر الكمية'
        };
      }
    }

    // إضافة المعاملة في قاعدة البيانات
    console.log('جاري إضافة المعاملة في قاعدة البيانات...');
    let result;
    try {
      // Ensure unique transaction_id
if (!transaction.transaction_id) {
  transaction.transaction_id = generateTransactionId();
}
result = await window.api.invoke('add-transaction', transaction);
      console.log('تم إضافة المعاملة بنجاح:', result);
    } catch (invokeError) {
      console.error('خطأ في استدعاء API لإضافة المعاملة:', invokeError);
      return {
        success: false,
        error: invokeError.message || 'فشل في إضافة المعاملة: خطأ في الاتصال بالخادم'
      };
    }

    // التحقق من صحة النتيجة
    if (!result) {
      console.error('النتيجة فارغة من إضافة المعاملة');
      return { success: false, error: 'فشل في إضافة المعاملة: النتيجة فارغة' };
    }

    // التحقق من وجود خطأ في النتيجة
    if (result.success === false) {
      console.error('خطأ في إضافة المعاملة:', result.error);
      return result;
    }

    // مسح التخزين المؤقت للمعاملات والمخزون لضمان تحديث البيانات
    console.log('مسح التخزين المؤقت للمعاملات والمخزون بعد إضافة معاملة جديدة');

    // مسح جميع مفاتيح التخزين المؤقت
    transactionsCache.clear();
    inventoryCache.clear();
    itemsCache.clear();

    // تحميل المعاملات مرة أخرى لضمان تحديث البيانات
    try {
      await getTransactions(true);
    } catch (reloadError) {
      console.warn('فشل في إعادة تحميل المعاملات بعد الإضافة:', reloadError);
      // لا نريد إيقاف العملية إذا فشل إعادة التحميل
    }

    // إضافة حقل success إذا لم يكن موجوداً
    if (result.success === undefined) {
      result.success = true;
    }

    return result;
  } catch (error) {
    console.error('Error adding transaction:', error);
    return {
      success: false,
      error: error.message || 'فشل في إضافة المعاملة: خطأ غير معروف'
    };
  }
};

// Users
export const getUsers = async () => {
  console.log('بدء تنفيذ دالة getUsers...');

  // التحقق من وجود البيانات في التخزين المؤقت
  const cachedUsers = usersCache.get('all_users');
  if (cachedUsers) {
    console.log('استخدام بيانات المستخدمين من التخزين المؤقت');
    return cachedUsers;
  }

  // التحقق من توفر window.api
  if (!checkApiAvailability()) {
    console.error('فشل في الحصول على المستخدمين بسبب عدم توفر window.api');
    throw new Error('فشل في الحصول على المستخدمين بسبب عدم توفر window.api');
  }

  try {
    console.log('جاري الحصول على المستخدمين من قاعدة البيانات...');

    // استخدام وظيفة المستخدمين المباشرة
    let users;
    try {
      if (window.api.users && typeof window.api.users.getAll === 'function') {
        console.log('استخدام window.api.users.getAll()');
        users = await window.api.users.getAll();
      } else {
        console.log('استخدام window.api.invoke("get-users")');
        users = await window.api.invoke('get-users');
      }
    } catch (apiError) {
      console.error('خطأ في استدعاء API للحصول على المستخدمين:', apiError);
      throw apiError;
    }

    // التحقق من أن البيانات المستلمة هي مصفوفة
    if (!Array.isArray(users)) {
      console.error('البيانات المستلمة ليست مصفوفة:', users);
      throw new Error('البيانات المستلمة من قاعدة البيانات غير صالحة');
    }

    console.log(`تم الحصول على ${users.length} مستخدم بنجاح`);

    // التأكد من أن كل مستخدم لديه حقل id للتوافق مع الواجهة الأمامية
    const processedUsers = users.map(user => {
      if (!user) {
        console.warn('تم العثور على مستخدم فارغ أو غير معرف في البيانات المستلمة');
        return null;
      }
      try {
        // إصلاح تلقائي: ضمان وجود id وrole
        const safeUser = {
          ...user,
          id: user._id || user.id || '',
          role: user.role || 'employee',
        };
        // تجاهل المستخدمين الذين ليس لديهم id
        if (!safeUser.id) return null;
        return safeUser;
      } catch (processingError) {
        console.error('خطأ في معالجة بيانات المستخدم:', processingError, user);
        return null;
      }
    }).filter(user => user !== null);

    console.log('تمت معالجة بيانات المستخدمين بنجاح، عدد المستخدمين بعد المعالجة:', processedUsers.length);

    // تخزين البيانات في التخزين المؤقت
    usersCache.set('all_users', processedUsers);

    return processedUsers;
  } catch (error) {
    console.error('خطأ عام في الحصول على المستخدمين:', error);
    throw error;
  }
};

export const addUser = async (user) => {
  if (!checkApiAvailability()) {
    console.error('فشل في إضافة المستخدم بسبب عدم توفر window.api');
    throw new Error('فشل في إضافة المستخدم بسبب عدم توفر window.api');
  }

  try {
    console.log('جاري إضافة المستخدم إلى قاعدة البيانات:', user);
    console.log('التحقق من وجود window.api.users:', window.api.users);

    let result;
    if (window.api.users && typeof window.api.users.add === 'function') {
      // استخدام واجهة المستخدمين المحددة
      console.log('استخدام window.api.users.add');
      result = await window.api.users.add(user);
    } else {
      // استخدام واجهة invoke العامة
      console.log('استخدام window.api.invoke كبديل');
      result = await window.api.invoke('add-user', user);
    }

    console.log('تم إضافة المستخدم بنجاح:', result);

    // مسح التخزين المؤقت للمستخدمين لضمان تحديث البيانات
    console.log('مسح التخزين المؤقت للمستخدمين بعد إضافة مستخدم جديد');
    usersCache.clear();

    // التأكد من وجود حقل id للتوافق مع الواجهة الأمامية
    const processedUser = {
      ...result,
      id: result._id || result.id
    };

    return processedUser;
  } catch (error) {
    console.error('خطأ في إضافة المستخدم:', error);
    throw error;
  }
};

export const updateUser = async (user) => {
  if (!checkApiAvailability()) {
    console.error('فشل في تحديث المستخدم بسبب عدم توفر window.api');
    throw new Error('فشل في تحديث المستخدم بسبب عدم توفر window.api');
  }

  try {
    console.log('جاري تحديث المستخدم في قاعدة البيانات:', user);

    let result;
    if (window.api.users && typeof window.api.users.update === 'function') {
      // استخدام واجهة المستخدمين المحددة
      console.log('استخدام window.api.users.update');
      result = await window.api.users.update(user);
    } else {
      // استخدام واجهة invoke العامة
      console.log('استخدام window.api.invoke كبديل');
      result = await window.api.invoke('update-user', user);
    }

    console.log('تم تحديث المستخدم بنجاح:', result);

    // التأكد من وجود حقل id للتوافق مع الواجهة الأمامية
    const processedUser = {
      ...result,
      id: result._id || result.id
    };

    return processedUser;
  } catch (error) {
    console.error('خطأ في تحديث المستخدم:', error);
    throw error;
  }
};

export const deleteUser = async (id) => {
  if (!checkApiAvailability()) {
    console.error('فشل في حذف المستخدم بسبب عدم توفر window.api');
    throw new Error('فشل في حذف المستخدم بسبب عدم توفر window.api');
  }

  try {
    console.log('جاري حذف المستخدم من قاعدة البيانات:', id);

    let result;
    if (window.api.users && typeof window.api.users.delete === 'function') {
      // استخدام واجهة المستخدمين المحددة
      console.log('استخدام window.api.users.delete');
      result = await window.api.users.delete(id);
    } else {
      // استخدام واجهة invoke العامة
      console.log('استخدام window.api.invoke كبديل');
      result = await window.api.invoke('delete-user', id);
    }

    console.log('تم حذف المستخدم بنجاح:', result);
    return result;
  } catch (error) {
    console.error('خطأ في حذف المستخدم:', error);
    throw error;
  }
};

// Authentication
export const login = async (credentials) => {
  if (!checkApiAvailability()) {
    console.error('فشل في تسجيل الدخول بسبب عدم توفر window.api');
    throw new Error('فشل في تسجيل الدخول بسبب عدم توفر window.api');
  }

  try {
    console.log('جاري محاولة تسجيل الدخول...');

    let result;
    if (window.api.users && typeof window.api.users.login === 'function') {
      // استخدام واجهة المستخدمين المحددة
      console.log('استخدام window.api.users.login');
      result = await window.api.users.login(credentials);
    } else {
      // استخدام واجهة invoke العامة
      console.log('استخدام window.api.invoke كبديل');
      result = await window.api.invoke('login', credentials);
    }

    console.log('تم تسجيل الدخول بنجاح:', result);

    // حفظ معرف المستخدم في التخزين المحلي
    if (result && result.user) {
      localStorage.setItem('currentUserId', result.user._id || result.user.id);
      localStorage.setItem('currentUserRole', result.user.role);
      localStorage.setItem('currentUserName', result.user.username);
    }

    return result;
  } catch (error) {
    console.error('خطأ في تسجيل الدخول:', error);
    throw error;
  }
};

// Machines
export const getMachines = async () => {
  if (!checkApiAvailability()) {
    console.error('فشل في الحصول على الآلات بسبب عدم توفر window.api');
    return [];
  }

  try {
    const machines = await safeInvoke('get-machines');

    if (!Array.isArray(machines)) {
      console.warn('البيانات المستلمة ليست مصفوفة:', machines);
      return [];
    }

    return machines;
  } catch (error) {
    console.error('خطأ في الحصول على الآلات:', error);
    return [];
  }
};

export const addMachine = async (machine) => {
  if (!checkApiAvailability()) {
    console.error('فشل في إضافة الآلة بسبب عدم توفر window.api');
    throw new Error('فشل في إضافة الآلة بسبب عدم توفر window.api');
  }

  try {
    return await window.api.invoke('add-machine', machine);
  } catch (error) {
    console.error('خطأ في إضافة الآلة:', error);
    throw error;
  }
};

export const updateMachine = async (machine) => {
  if (!checkApiAvailability()) {
    console.error('فشل في تحديث الآلة بسبب عدم توفر window.api');
    throw new Error('فشل في تحديث الآلة بسبب عدم توفر window.api');
  }

  try {
    return await window.api.invoke('update-machine', machine);
  } catch (error) {
    console.error('خطأ في تحديث الآلة:', error);
    throw error;
  }
};

export const deleteMachine = async (id) => {
  if (!checkApiAvailability()) {
    console.error('فشل في حذف الآلة بسبب عدم توفر window.api');
    throw new Error('فشل في حذف الآلة بسبب عدم توفر window.api');
  }

  try {
    return await window.api.invoke('delete-machine', id);
  } catch (error) {
    console.error('خطأ في حذف الآلة:', error);
    throw error;
  }
};

// Backup and Restore
export const backupDatabase = async () => {
  try {
    return await window.api.invoke('backup-database');
  } catch (error) {
    console.error('Error backing up database:', error);
    throw error;
  }
};

// إفراغ قاعدة البيانات
export const clearDatabase = async (options = {}) => {
  if (!checkApiAvailability()) {
    console.error('فشل في إفراغ قاعدة البيانات بسبب عدم توفر window.api');
    throw new Error('فشل في إفراغ قاعدة البيانات بسبب عدم توفر window.api');
  }

  try {
    console.log('جاري إفراغ قاعدة البيانات مع الخيارات:', options);
    const result = await window.api.invoke('clear-database', options);
    console.log('تم إفراغ قاعدة البيانات بنجاح:', result);
    return result;
  } catch (error) {
    console.error('خطأ في إفراغ قاعدة البيانات:', error);
    throw error;
  }
};

export const restoreDatabase = async () => {
  try {
    return await window.api.invoke('restore-database');
  } catch (error) {
    console.error('Error restoring database:', error);
    throw error;
  }
};
