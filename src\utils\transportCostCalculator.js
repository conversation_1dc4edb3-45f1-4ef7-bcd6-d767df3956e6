/**
 * حاسبة مصاريف النقل المخصصة للأصناف
 *
 * هذا الملف يحتوي على الدوال المسؤولة عن حساب مصاريف النقل المخصصة لكل صنف
 * بناءً على معاملات الشراء التي تحتوي على مصاريف نقل
 */

const Database = require('better-sqlite3');
const { logSystem, logError } = require('./logger');

/**
 * حساب مصاريف النقل المخصصة لصنف معين
 * @param {Object} db - اتصال قاعدة البيانات
 * @param {number} itemId - معرف الصنف
 * @param {number} quantity - الكمية المباعة
 * @returns {number} - مصاريف النقل المخصصة للكمية المباعة
 */
function calculateItemTransportCost(db, itemId, quantity) {
  try {
    if (!db || !itemId || !quantity || quantity <= 0) {
      return 0;
    }

    console.log(`[TRANSPORT-CALC] حساب مصاريف النقل للصنف ${itemId} بكمية ${quantity}`);

    // الحصول على جميع معاملات الشراء للصنف التي تحتوي على مصاريف نقل
    const purchaseStmt = db.prepare(`
      SELECT
        id,
        quantity,
        transport_cost,
        total_price,
        transaction_date
      FROM transactions
      WHERE item_id = ?
        AND transaction_type = 'purchase'
        AND transport_cost > 0
      ORDER BY transaction_date ASC
    `);

    const purchases = purchaseStmt.all(itemId);

    if (!purchases || purchases.length === 0) {
      console.log(`[TRANSPORT-CALC] لا توجد معاملات شراء بمصاريف نقل للصنف ${itemId}`);
      return 0;
    }

    console.log(`[TRANSPORT-CALC] تم العثور على ${purchases.length} معاملة شراء بمصاريف نقل`);

    // حساب إجمالي الكمية المشتراة ومصاريف النقل
    let totalPurchasedQuantity = 0;
    let totalTransportCost = 0;

    purchases.forEach(purchase => {
      totalPurchasedQuantity += purchase.quantity;
      totalTransportCost += purchase.transport_cost;
      console.log(`[TRANSPORT-CALC] معاملة ${purchase.id}: كمية=${purchase.quantity}, مصاريف نقل=${purchase.transport_cost}`);
    });

    if (totalPurchasedQuantity <= 0) {
      console.log(`[TRANSPORT-CALC] إجمالي الكمية المشتراة صفر أو سالب`);
      return 0;
    }

    // حساب متوسط مصاريف النقل لكل وحدة
    const avgTransportCostPerUnit = totalTransportCost / totalPurchasedQuantity;

    // حساب مصاريف النقل للكمية المباعة
    const allocatedTransportCost = avgTransportCostPerUnit * quantity;

    console.log(`[TRANSPORT-CALC] النتائج:`);
    console.log(`[TRANSPORT-CALC] - إجمالي الكمية المشتراة: ${totalPurchasedQuantity}`);
    console.log(`[TRANSPORT-CALC] - إجمالي مصاريف النقل: ${totalTransportCost}`);
    console.log(`[TRANSPORT-CALC] - متوسط مصاريف النقل لكل وحدة: ${avgTransportCostPerUnit}`);
    console.log(`[TRANSPORT-CALC] - مصاريف النقل للكمية المباعة (${quantity}): ${allocatedTransportCost}`);

    return Math.round(allocatedTransportCost * 100) / 100; // تقريب إلى رقمين عشريين

  } catch (error) {
    console.error('[TRANSPORT-CALC] خطأ في حساب مصاريف النقل:', error);
    logError(error, 'calculateItemTransportCost');
    return 0;
  }
}

/**
 * حساب مصاريف النقل المخصصة لفاتورة كاملة
 * @param {Object} db - اتصال قاعدة البيانات
 * @param {string} invoiceNumber - رقم الفاتورة
 * @returns {number} - إجمالي مصاريف النقل للفاتورة
 */
function calculateInvoiceTransportCost(db, invoiceNumber) {
  try {
    if (!db || !invoiceNumber) {
      return 0;
    }

    console.log(`[TRANSPORT-CALC] حساب مصاريف النقل للفاتورة ${invoiceNumber}`);

    // الحصول على جميع معاملات البيع في الفاتورة
    const salesStmt = db.prepare(`
      SELECT
        item_id,
        quantity
      FROM transactions
      WHERE invoice_number = ?
        AND transaction_type = 'sale'
    `);

    const sales = salesStmt.all(invoiceNumber);

    if (!sales || sales.length === 0) {
      console.log(`[TRANSPORT-CALC] لا توجد معاملات بيع في الفاتورة ${invoiceNumber}`);
      return 0;
    }

    let totalInvoiceTransportCost = 0;

    // حساب مصاريف النقل لكل صنف في الفاتورة
    sales.forEach(sale => {
      const itemTransportCost = calculateItemTransportCost(db, sale.item_id, sale.quantity);
      totalInvoiceTransportCost += itemTransportCost;
      console.log(`[TRANSPORT-CALC] الصنف ${sale.item_id}: مصاريف نقل = ${itemTransportCost}`);
    });

    console.log(`[TRANSPORT-CALC] إجمالي مصاريف النقل للفاتورة ${invoiceNumber}: ${totalInvoiceTransportCost}`);

    return Math.round(totalInvoiceTransportCost * 100) / 100;

  } catch (error) {
    console.error('[TRANSPORT-CALC] خطأ في حساب مصاريف النقل للفاتورة:', error);
    logError(error, 'calculateInvoiceTransportCost');
    return 0;
  }
}

/**
 * الحصول على تفاصيل مصاريف النقل لصنف معين
 * @param {Object} db - اتصال قاعدة البيانات
 * @param {number} itemId - معرف الصنف
 * @returns {Object} - تفاصيل مصاريف النقل
 */
function getItemTransportDetails(db, itemId) {
  try {
    if (!db || !itemId) {
      return {
        totalTransportCost: 0,
        totalQuantity: 0,
        avgTransportCostPerUnit: 0,
        purchaseCount: 0
      };
    }

    // الحصول على جميع معاملات الشراء للصنف التي تحتوي على مصاريف نقل
    const purchaseStmt = db.prepare(`
      SELECT
        quantity,
        transport_cost,
        transaction_date
      FROM transactions
      WHERE item_id = ?
        AND transaction_type = 'purchase'
        AND transport_cost > 0
      ORDER BY transaction_date ASC
    `);

    const purchases = purchaseStmt.all(itemId);

    let totalTransportCost = 0;
    let totalQuantity = 0;
    const purchaseCount = purchases.length;

    purchases.forEach(purchase => {
      totalTransportCost += purchase.transport_cost;
      totalQuantity += purchase.quantity;
    });

    const avgTransportCostPerUnit = totalQuantity > 0 ? totalTransportCost / totalQuantity : 0;

    return {
      totalTransportCost: Math.round(totalTransportCost * 100) / 100,
      totalQuantity,
      avgTransportCostPerUnit: Math.round(avgTransportCostPerUnit * 100) / 100,
      purchaseCount
    };

  } catch (error) {
    console.error('[TRANSPORT-CALC] خطأ في الحصول على تفاصيل مصاريف النقل:', error);
    logError(error, 'getItemTransportDetails');
    return {
      totalTransportCost: 0,
      totalQuantity: 0,
      avgTransportCostPerUnit: 0,
      purchaseCount: 0
    };
  }
}

module.exports = {
  calculateItemTransportCost,
  calculateInvoiceTransportCost,
  getItemTransportDetails
};
