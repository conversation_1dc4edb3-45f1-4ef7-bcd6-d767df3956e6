import React, { createContext, useState, useEffect, useRef, useContext } from 'react';
import { testConnection, syncInventory, getSalesData, updateSyncStatus } from '../../services/IntegrationService';
import { SettingsContext } from './SettingsProvider';
import { NotificationsContext } from './NotificationsProvider';

// إنشاء سياق المزامنة
export const SyncContext = createContext();

/**
 * مزود سياق المزامنة
 *
 * يوفر هذا المكون وظائف المزامنة مع منظومة المبيعات
 *
 * @param {Object} props - خصائص المكون
 * @param {React.ReactNode} props.children - المكونات الفرعية
 * @returns {React.ReactElement} مزود سياق المزامنة
 */
export const SyncProvider = ({ children }) => {
  // استخدام سياق الإعدادات والإشعارات
  const { settings } = useContext(SettingsContext);
  const { showNotification } = useContext(NotificationsContext);

  // حالة المزامنة
  const [syncStatus, setSyncStatus] = useState({
    syncing: false,
    lastSyncTime: null,
    lastSyncStatus: null,
    error: null
  });

  // مرجع للمزامنة الدورية مع منظومة المبيعات
  const syncIntervalRef = useRef(null);

  // تحميل آخر وقت للمزامنة من التخزين المحلي عند بدء التطبيق
  useEffect(() => {
    const lastSyncTime = localStorage.getItem('lastSyncTime');
    if (lastSyncTime) {
      setSyncStatus(prev => ({
        ...prev,
        lastSyncTime
      }));
    }
  }, []);

  // إعداد المزامنة الدورية عند تغيير إعدادات المزامنة
  useEffect(() => {
    // إلغاء المزامنة الدورية الحالية إن وجدت
    if (syncIntervalRef.current) {
      clearInterval(syncIntervalRef.current);
      syncIntervalRef.current = null;
    }

    // إذا كانت المزامنة مفعلة وتم تحديد عنوان URL ومفتاح API
    if (settings.integrationEnabled && settings.integrationUrl && settings.integrationApiKey) {
      // إعداد المزامنة الدورية
      syncIntervalRef.current = setInterval(() => {
        // تنفيذ المزامنة فقط إذا لم تكن هناك مزامنة جارية
        if (!syncStatus.syncing) {
          syncWithSalesSystem();
        }
      }, settings.syncInterval * 60 * 1000); // تحويل الدقائق إلى مللي ثانية

      // تنفيذ مزامنة أولية
      if (!syncStatus.syncing) {
        syncWithSalesSystem();
      }
    }

    // تنظيف المزامنة عند إلغاء تحميل المكون
    return () => {
      if (syncIntervalRef.current) {
        clearInterval(syncIntervalRef.current);
        syncIntervalRef.current = null;
      }
    };
  }, [settings.integrationEnabled, settings.integrationUrl, settings.integrationApiKey, settings.syncInterval, syncStatus.syncing]);

  // اختبار الاتصال بمنظومة المبيعات
  const testIntegrationConnection = async (url, apiKey, isInternet) => {
    // استخدام المعلمات المرسلة أو القيم المخزنة في الإعدادات
    const integrationUrl = url || settings.integrationUrl;
    const integrationApiKey = apiKey || settings.integrationApiKey;
    const isInternetConnection = isInternet !== undefined ? isInternet : settings.isInternetConnection;

    console.log('اختبار الاتصال بمنظومة المبيعات:');
    console.log('URL:', integrationUrl);
    console.log('API Key:', integrationApiKey);
    console.log('نوع الاتصال:', isInternetConnection ? 'إنترنت' : 'شبكة محلية');

    if (!integrationUrl || !integrationApiKey) {
      console.log('بيانات الاتصال غير مكتملة');
      return { success: false, message: 'يرجى إدخال عنوان URL ومفتاح API' };
    }

    try {
      // تحديث حالة المزامنة لإظهار أن الاختبار قيد التقدم
      setSyncStatus(prev => ({
        ...prev,
        testing: true
      }));

      // استخدام واجهة الإعدادات الجديدة لاختبار الاتصال
      let result;
      if (window.api && window.api.settings) {
        console.log('استخدام واجهة الإعدادات الجديدة لاختبار الاتصال');
        result = await window.api.settings.testIntegrationConnection(
          integrationUrl,
          integrationApiKey,
          isInternetConnection
        );
      } else {
        console.warn('واجهة الإعدادات غير متوفرة، سيتم استخدام الخدمة المحلية');
        result = await testConnection(integrationUrl, integrationApiKey);
      }

      console.log('نتيجة اختبار الاتصال:', result);

      // تحديث حالة المزامنة بعد اكتمال الاختبار
      setSyncStatus(prev => ({
        ...prev,
        testing: false,
        lastConnectionTest: {
          success: result.success,
          timestamp: new Date().toISOString()
        }
      }));

      return result;
    } catch (error) {
      console.error('Test connection error:', error);

      // تحديث حالة المزامنة في حالة الخطأ
      setSyncStatus(prev => ({
        ...prev,
        testing: false,
        lastConnectionTest: {
          success: false,
          timestamp: new Date().toISOString(),
          error: error.message
        }
      }));

      return { success: false, message: `حدث خطأ أثناء اختبار الاتصال: ${error.message}` };
    }
  };

  // مزامنة البيانات مع منظومة المبيعات
  const syncWithSalesSystem = async (options = {}) => {
    // استخدام الخيارات المرسلة أو القيم المخزنة في الإعدادات
    const enabled = options.enabled !== undefined ? options.enabled : settings.integrationEnabled;
    const url = options.url || settings.integrationUrl;
    const apiKey = options.apiKey || settings.integrationApiKey;
    const isInternet = options.isInternet !== undefined ? options.isInternet : settings.isInternetConnection;

    console.log('مزامنة البيانات مع منظومة المبيعات:');
    console.log('مفعلة:', enabled);
    console.log('URL:', url);
    console.log('API Key:', apiKey);
    console.log('نوع الاتصال:', isInternet ? 'إنترنت' : 'شبكة محلية');

    // التحقق من تفعيل المزامنة وتوفر بيانات الاتصال
    if (!enabled) {
      console.log('المزامنة غير مفعلة');
      return { success: false, message: 'المزامنة غير مفعلة' };
    }

    if (!url || !apiKey) {
      console.log('بيانات الاتصال غير مكتملة');
      return { success: false, message: 'بيانات الاتصال غير مكتملة' };
    }

    // التحقق من عدم وجود مزامنة جارية
    if (syncStatus.syncing) {
      console.log('هناك مزامنة جارية بالفعل');
      return { success: false, message: 'هناك مزامنة جارية بالفعل' };
    }

    try {
      // تحديث حالة المزامنة لإظهار أن المزامنة قيد التقدم
      setSyncStatus(prev => ({
        ...prev,
        syncing: true,
        error: null
      }));

      // إظهار إشعار ببدء المزامنة
      showNotification('جاري مزامنة البيانات مع منظومة المبيعات...', 'info');

      // استخدام واجهة الإعدادات الجديدة للمزامنة
      let result;
      if (window.api && window.api.settings) {
        console.log('استخدام واجهة الإعدادات الجديدة للمزامنة');
        result = await window.api.settings.syncWithSalesSystem({
          enabled,
          url,
          apiKey,
          isInternet
        });
      } else {
        console.warn('واجهة الإعدادات غير متوفرة، سيتم استخدام الخدمة المحلية');
        result = await syncInventory(url, apiKey);
      }

      console.log('نتيجة المزامنة:', result);

      // تحديث حالة المزامنة بعد اكتمال المزامنة
      setSyncStatus(prev => ({
        ...prev,
        syncing: false,
        lastSyncTime: new Date().toISOString(),
        lastSyncStatus: result.success ? 'success' : 'error'
      }));

      // حفظ وقت آخر مزامنة في التخزين المحلي
      localStorage.setItem('lastSyncTime', new Date().toISOString());

      // إظهار إشعار بنتيجة المزامنة
      if (result.success) {
        showNotification('تمت مزامنة البيانات بنجاح', 'success');
      } else {
        showNotification(`فشل في مزامنة البيانات: ${result.message || 'خطأ غير معروف'}`, 'error');
      }

      return result;
    } catch (error) {
      console.error('Sync error:', error);

      // تحديث حالة المزامنة في حالة الخطأ
      setSyncStatus(prev => ({
        ...prev,
        syncing: false,
        lastSyncStatus: 'error',
        error: error.message
      }));

      // إظهار إشعار بفشل المزامنة
      showNotification(`فشل في مزامنة البيانات: ${error.message}`, 'error');

      return { success: false, message: `حدث خطأ أثناء المزامنة: ${error.message}` };
    }
  };

  // القيمة التي سيتم توفيرها للمكونات
  const value = {
    syncStatus,
    testIntegrationConnection,
    syncWithSalesSystem
  };

  return (
    <SyncContext.Provider value={value}>
      {children}
    </SyncContext.Provider>
  );
};

export default SyncProvider;
