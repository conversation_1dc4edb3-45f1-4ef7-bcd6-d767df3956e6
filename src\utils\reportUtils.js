/**
 * وحدة مساعدة لطباعة التقارير
 * توفر وظائف مساعدة لطباعة التقارير
 */

import html2canvas from 'html2canvas';
import { jsPDF } from 'jspdf';
import 'jspdf-autotable';

/**
 * طباعة عنصر HTML
 * @param {HTMLElement} element - عنصر HTML المراد طباعته
 * @param {Object} options - خيارات الطباعة
 */
export function printElement(element, options = {}) {
  try {
    if (!element) {
      console.error('لم يتم تحديد عنصر HTML للطباعة');
      return;
    }

    const { title = 'تقرير' } = options;

    // إنشاء نافذة جديدة للطباعة
    const printWindow = window.open('', '_blank');

    if (!printWindow) {
      alert('يرجى السماح بالنوافذ المنبثقة لطباعة التقرير');
      return;
    }

    // الحصول على محتوى العنصر
    const content = element.innerHTML;

    // إنشاء محتوى HTML للطباعة
    printWindow.document.open();
    printWindow.document.write(`
      <html dir="rtl" lang="ar">
        <head>
          <meta charset="UTF-8">
          <title>${title}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              direction: rtl;
              padding: 20px;
              color: #1a3a5f;
            }

            @media print {
              body {
                padding: 0;
              }

              button {
                display: none;
              }
            }
          </style>
        </head>
        <body>
          <div class="report-content">
            ${content}
          </div>

          <script>
            window.onload = function() {
              setTimeout(function() {
                window.print();
                window.close();
              }, 500);
            };
          </script>
        </body>
      </html>
    `);
    printWindow.document.close();
  } catch (error) {
    console.error('خطأ في طباعة العنصر:', error);
    alert('حدث خطأ أثناء محاولة طباعة التقرير. يرجى المحاولة مرة أخرى.');
  }
}

/**
 * تصدير عنصر HTML كملف PDF
 * @param {HTMLElement} element - عنصر HTML المراد تصديره
 * @param {string} filename - اسم الملف
 * @param {Object} options - خيارات التصدير
 */
export async function exportElementToPDF(element, filename, options = {}) {
  try {
    if (!element) {
      console.error('لم يتم تحديد عنصر HTML للتصدير');
      return;
    }

    const { orientation = 'portrait', format = 'a4' } = options;

    // إنشاء مستند PDF جديد
    const jsPDFLib = window.jsPDF || jsPDF;
    const doc = new jsPDFLib({
      orientation,
      unit: 'mm',
      format,
      putOnlyUsedFonts: true,
      compress: true
    });

    // تحويل عنصر HTML إلى صورة
    const canvas = await html2canvas(element, { scale: 2 });
    const imgData = canvas.toDataURL('image/png');
    const imgWidth = doc.internal.pageSize.getWidth();
    const imgHeight = (canvas.height * imgWidth) / canvas.width;

    doc.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
    doc.save(filename);
  } catch (error) {
    console.error('خطأ في تصدير عنصر HTML كملف PDF:', error);
    alert('حدث خطأ أثناء تصدير الملف. يرجى المحاولة مرة أخرى.');
  }
}

/**
 * تحويل تاريخ إلى تنسيق مقروء
 * @param {string} dateString - سلسلة التاريخ
 * @param {string} locale - اللغة المستخدمة للتنسيق
 * @returns {string} - التاريخ المنسق
 */
export function formatDate(dateString, locale = 'ar-SA') {
  if (!dateString) return '-';

  try {
    const date = new Date(dateString);
    return date.toLocaleDateString(locale);
  } catch (error) {
    console.error('خطأ في تنسيق التاريخ:', error);
    return dateString;
  }
}

/**
 * تنسيق رقم كعملة
 * @param {number} value - القيمة
 * @param {string} currency - رمز العملة
 * @param {string} locale - اللغة المستخدمة للتنسيق
 * @returns {string} - القيمة المنسقة
 */
export function formatCurrency(value, currency = 'LYD', locale = 'ar-LY') {
  if (value === null || value === undefined) return '-';

  try {
    return new Intl.NumberFormat(locale, {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value) + ' د.ل';
  } catch (error) {
    console.error('خطأ في تنسيق العملة:', error);
    return `${value} د.ل`;
  }
}

export default {
  printElement,
  exportElementToPDF,
  formatDate,
  formatCurrency
};
