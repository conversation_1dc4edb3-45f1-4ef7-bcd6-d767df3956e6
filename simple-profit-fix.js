/**
 * سكريبت مبسط لإصلاح الأرباح الموجودة في قاعدة البيانات
 * يعيد حساب الأرباح لجميع معاملات البيع مع تطبيق خصم مصاريف النقل
 */

const Database = require('better-sqlite3');
const path = require('path');

/**
 * حساب الربح مع خصم مصاريف النقل
 */
function calculateProfitWithTransport(sellingPrice, costPrice, quantity, transportCostPerUnit) {
  const profit = (sellingPrice - costPrice - transportCostPerUnit) * quantity;
  return Math.max(0, profit); // التأكد من أن الربح لا يكون سالباً
}

/**
 * إصلاح الأرباح الموجودة في قاعدة البيانات
 */
async function fixExistingProfits() {
  let db;
  
  try {
    console.log('🔧 بدء إصلاح الأرباح الموجودة في قاعدة البيانات...');

    // الاتصال بقاعدة البيانات
    const dbPath = path.join(__dirname, 'wms-database', 'warehouse.db');
    db = new Database(dbPath);
    
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // الحصول على جميع معاملات البيع
    const salesStmt = db.prepare(`
      SELECT t.*, i.name as item_name
      FROM transactions t
      LEFT JOIN items i ON t.item_id = i.id
      WHERE t.transaction_type = 'sale'
      ORDER BY t.id
    `);

    const salesTransactions = salesStmt.all();
    console.log(`📊 تم العثور على ${salesTransactions.length} معاملة بيع`);

    if (salesTransactions.length === 0) {
      console.log('ℹ️ لا توجد معاملات بيع لإصلاحها');
      return;
    }

    let fixedCount = 0;
    let totalOldProfit = 0;
    let totalNewProfit = 0;

    // معالجة كل معاملة بيع
    for (const transaction of salesTransactions) {
      try {
        console.log(`\n🔍 معالجة المعاملة ${transaction.id} - ${transaction.item_name || 'غير محدد'}`);
        
        const oldProfit = Number(transaction.profit || 0);
        totalOldProfit += oldProfit;

        // الحصول على متوسط سعر الشراء
        const inventoryStmt = db.prepare('SELECT avg_price FROM inventory WHERE item_id = ?');
        const inventory = inventoryStmt.get(transaction.item_id);
        const avgPrice = inventory ? inventory.avg_price : 0;

        // حساب مصاريف النقل المخصصة لهذا الصنف
        let transportCostPerUnit = 0;
        try {
          const purchaseStmt = db.prepare(`
            SELECT quantity, transport_cost
            FROM transactions
            WHERE item_id = ? AND transaction_type = 'purchase' AND transport_cost > 0
          `);

          const purchases = purchaseStmt.all(transaction.item_id);

          if (purchases && purchases.length > 0) {
            let totalPurchasedQuantity = 0;
            let totalTransportCost = 0;

            purchases.forEach(purchase => {
              totalPurchasedQuantity += purchase.quantity;
              totalTransportCost += purchase.transport_cost;
            });

            if (totalPurchasedQuantity > 0) {
              transportCostPerUnit = totalTransportCost / totalPurchasedQuantity;
            }
          }
        } catch (error) {
          console.error(`خطأ في حساب مصاريف النقل للصنف ${transaction.item_id}:`, error.message);
          transportCostPerUnit = 0;
        }

        // حساب الربح الجديد مع خصم مصاريف النقل
        const newProfit = calculateProfitWithTransport(
          transaction.selling_price || transaction.price,
          avgPrice,
          transaction.quantity,
          transportCostPerUnit
        );

        totalNewProfit += newProfit;

        // تحديث الربح إذا كان مختلفاً
        if (Math.abs(oldProfit - newProfit) > 0.01) {
          const updateStmt = db.prepare('UPDATE transactions SET profit = ? WHERE id = ?');
          updateStmt.run(newProfit, transaction.id);
          
          console.log(`   ✅ تم تحديث الربح من ${oldProfit.toFixed(2)} إلى ${newProfit.toFixed(2)}`);
          console.log(`   📝 التفاصيل: سعر البيع ${transaction.selling_price || transaction.price}, متوسط الشراء ${avgPrice}, مصاريف النقل ${transportCostPerUnit.toFixed(2)}, الكمية ${transaction.quantity}`);
          
          fixedCount++;
        } else {
          console.log(`   ℹ️ الربح صحيح بالفعل: ${newProfit.toFixed(2)}`);
        }

      } catch (error) {
        console.error(`❌ خطأ في معالجة المعاملة ${transaction.id}:`, error.message);
      }
    }

    // تحديث إجمالي الأرباح في الخزينة
    console.log(`\n📊 تحديث إجمالي الأرباح في الخزينة...`);
    console.log(`   إجمالي الأرباح القديم: ${totalOldProfit.toFixed(2)}`);
    console.log(`   إجمالي الأرباح الجديد: ${totalNewProfit.toFixed(2)}`);
    console.log(`   الفرق: ${(totalNewProfit - totalOldProfit).toFixed(2)}`);

    const updateCashboxStmt = db.prepare(`
      UPDATE cashbox 
      SET profit_total = ?, updated_at = ?
      WHERE id = 1
    `);

    updateCashboxStmt.run(totalNewProfit, new Date().toISOString());

    console.log(`\n🎉 تم إصلاح الأرباح بنجاح!`);
    console.log(`📈 ملخص الإصلاح:`);
    console.log(`   - عدد معاملات البيع: ${salesTransactions.length}`);
    console.log(`   - عدد المعاملات المصلحة: ${fixedCount}`);
    console.log(`   - إجمالي الأرباح القديم: ${totalOldProfit.toFixed(2)} د.ل`);
    console.log(`   - إجمالي الأرباح الجديد: ${totalNewProfit.toFixed(2)} د.ل`);
    console.log(`   - الفرق: ${(totalNewProfit - totalOldProfit).toFixed(2)} د.ل`);

  } catch (error) {
    console.error('❌ فشل في إصلاح الأرباح الموجودة:', error.message);
  } finally {
    if (db) {
      db.close();
      console.log('🔒 تم إغلاق اتصال قاعدة البيانات');
    }
  }
}

/**
 * التحقق من صحة الأرباح الحالية
 */
async function validateCurrentProfits() {
  let db;
  
  try {
    console.log('🔍 التحقق من صحة الأرباح الحالية...');

    // الاتصال بقاعدة البيانات
    const dbPath = path.join(__dirname, 'wms-database', 'warehouse.db');
    db = new Database(dbPath);

    // حساب إجمالي الأرباح من معاملات البيع
    const totalProfitStmt = db.prepare(`
      SELECT COALESCE(SUM(profit), 0) as total_profit
      FROM transactions
      WHERE transaction_type = 'sale'
    `);

    const result = totalProfitStmt.get();
    const calculatedTotalProfit = Number(result ? result.total_profit : 0);

    // الحصول على إجمالي الأرباح من الخزينة
    const cashboxStmt = db.prepare('SELECT profit_total FROM cashbox WHERE id = 1');
    const cashbox = cashboxStmt.get();
    const cashboxTotalProfit = Number(cashbox ? cashbox.profit_total : 0);

    console.log(`📊 نتائج التحقق:`);
    console.log(`   إجمالي الأرباح المحسوب من المعاملات: ${calculatedTotalProfit.toFixed(2)} د.ل`);
    console.log(`   إجمالي الأرباح في الخزينة: ${cashboxTotalProfit.toFixed(2)} د.ل`);
    console.log(`   الفرق: ${Math.abs(calculatedTotalProfit - cashboxTotalProfit).toFixed(2)} د.ل`);

    if (Math.abs(calculatedTotalProfit - cashboxTotalProfit) < 0.01) {
      console.log(`✅ الأرباح متطابقة ومتسقة`);
      return true;
    } else {
      console.log(`⚠️ هناك عدم تطابق في الأرباح`);
      return false;
    }

  } catch (error) {
    console.error('❌ فشل في التحقق من الأرباح:', error.message);
    return false;
  } finally {
    if (db) {
      db.close();
    }
  }
}

// تشغيل الإصلاح إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  (async () => {
    console.log('🚀 بدء عملية إصلاح الأرباح...\n');
    
    // التحقق من الوضع الحالي
    await validateCurrentProfits();
    
    console.log('\n' + '='.repeat(50));
    
    // تنفيذ الإصلاح
    await fixExistingProfits();
    
    console.log('\n' + '='.repeat(50));
    
    // التحقق من النتيجة
    await validateCurrentProfits();
    
    console.log('\n🏁 انتهت عملية إصلاح الأرباح');
  })();
}

module.exports = {
  fixExistingProfits,
  validateCurrentProfits
};
