/**
 * تشغيل إصلاح شامل لنظام الخزينة
 * 
 * هذا السكريبت يقوم بتشغيل جميع إصلاحات نظام الخزينة بالتسلسل
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 بدء إصلاح شامل لنظام الخزينة...');
console.log('=' .repeat(50));

// قائمة السكريبتات للتشغيل
const scripts = [
  {
    name: 'إصلاح بنية قاعدة البيانات',
    file: 'fix-cashbox-database-structure.js',
    description: 'إضافة الحقول المفقودة وإصلاح بنية الجدول'
  },
  {
    name: 'اختبار تحديث الرصيد الابتدائي',
    file: 'test-cashbox-update.js',
    description: 'اختبار دالة تحديث الرصيد الابتدائي'
  }
];

// دالة تشغيل سكريبت
function runScript(script) {
  return new Promise((resolve, reject) => {
    console.log(`\n🔧 تشغيل: ${script.name}`);
    console.log(`📝 الوصف: ${script.description}`);
    console.log(`📁 الملف: ${script.file}`);
    console.log('-'.repeat(30));

    const child = spawn('node', [script.file], {
      stdio: 'inherit',
      cwd: process.cwd()
    });

    child.on('close', (code) => {
      if (code === 0) {
        console.log(`✅ تم إكمال: ${script.name}`);
        resolve();
      } else {
        console.error(`❌ فشل في: ${script.name} (كود الخروج: ${code})`);
        reject(new Error(`فشل في تشغيل ${script.file}`));
      }
    });

    child.on('error', (error) => {
      console.error(`❌ خطأ في تشغيل ${script.name}:`, error.message);
      reject(error);
    });
  });
}

// تشغيل جميع السكريبتات بالتسلسل
async function runAllScripts() {
  try {
    for (const script of scripts) {
      await runScript(script);
    }

    console.log('\n' + '='.repeat(50));
    console.log('🎉 تم إكمال جميع إصلاحات نظام الخزينة بنجاح!');
    console.log('');
    console.log('📋 الخطوات التالية:');
    console.log('1. أعد تشغيل التطبيق');
    console.log('2. اختبر تحديث الرصيد الابتدائي من واجهة الخزينة');
    console.log('3. تأكد من عدم ظهور رسائل خطأ في وحدة التحكم');
    console.log('4. اختبر جميع وظائف الخزينة الأخرى');
    console.log('');
    console.log('💡 في حالة استمرار المشاكل، راجع ملف CASHBOX_ERROR_FIX.md');

  } catch (error) {
    console.error('\n❌ فشل في إكمال إصلاحات نظام الخزينة:', error.message);
    console.log('');
    console.log('🔍 للمساعدة في حل المشكلة:');
    console.log('1. تحقق من رسائل الخطأ أعلاه');
    console.log('2. تأكد من وجود قاعدة البيانات في المسار الصحيح');
    console.log('3. تأكد من صلاحيات الكتابة على قاعدة البيانات');
    console.log('4. جرب تشغيل السكريبتات منفردة للتشخيص');
    
    process.exit(1);
  }
}

// بدء التشغيل
runAllScripts();
