import axios from 'axios';

/**
 * خدمة API للتعامل مع منظومة المبيعات
 */
class SalesApiService {
  /**
   * إنشاء نسخة من خدمة API
   * @param {Object} config - إعدادات الاتصال
   * @param {string} config.baseUrl - عنوان URL الأساسي لمنظومة المبيعات
   * @param {string} config.apiKey - مفتاح API للمصادقة
   * @param {number} config.timeout - مهلة الاتصال بالمللي ثانية
   * @param {boolean} config.isInternet - هل الاتصال عبر الإنترنت
   */
  constructor(config = {}) {
    this.baseUrl = config.baseUrl || '';
    this.apiKey = config.apiKey || '';
    this.isInternet = config.isInternet || false;

    // تحديد مهلة الاتصال بناءً على نوع الاتصال
    // للشبكة المحلية: 10 ثوانٍ، للإنترنت: 30 ثانية
    const timeout = config.timeout || (this.isInternet ? 30000 : 10000);

    // إنشاء نسخة من Axios مع الإعدادات الافتراضية
    this.api = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      },
      timeout: timeout
    });
  }

  /**
   * تحديث إعدادات الاتصال
   * @param {Object} config - إعدادات الاتصال الجديدة
   */
  updateConfig(config) {
    this.baseUrl = config.baseUrl || this.baseUrl;
    this.apiKey = config.apiKey || this.apiKey;

    // تحديد ما إذا كان الاتصال عبر الإنترنت بناءً على شكل URL
    // إذا كان يبدأ بـ https:// أو كان يحتوي على نطاق عام، فهو اتصال عبر الإنترنت
    const isInternetUrl = this.baseUrl.startsWith('https://') ||
                         /\.(com|net|org|io|co|app|cloud)/.test(this.baseUrl);

    this.isInternet = config.isInternet !== undefined ? config.isInternet : isInternetUrl;

    // تحديد مهلة الاتصال بناءً على نوع الاتصال
    const timeout = config.timeout || (this.isInternet ? 30000 : 10000);

    console.log(`تحديث إعدادات الاتصال: ${this.baseUrl} (${this.isInternet ? 'إنترنت' : 'شبكة محلية'}, مهلة: ${timeout}ms)`);

    // تحديث إعدادات Axios
    this.api = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      },
      timeout: timeout
    });
  }

  /**
   * اختبار الاتصال بمنظومة المبيعات
   * @returns {Promise<Object>} - نتيجة الاختبار
   */
  async testConnection() {
    try {
      if (!this.baseUrl) {
        return { success: false, message: 'لم يتم تحديد عنوان URL لمنظومة المبيعات' };
      }

      console.log('اختبار الاتصال بمنظومة المبيعات:', this.baseUrl);

      // الاتصال الفعلي بالخادم
      const response = await this.api.get('/api/test-connection');
      return {
        success: true,
        message: 'تم الاتصال بنجاح بمنظومة المبيعات',
        data: response.data
      };
    } catch (error) {
      console.error('خطأ في اختبار الاتصال:', error);

      // تحسين رسائل الخطأ
      let errorMessage = 'فشل الاتصال بمنظومة المبيعات';

      if (error.code === 'ECONNREFUSED') {
        errorMessage = 'تعذر الاتصال بالخادم. يرجى التحقق من عنوان IP والمنفذ.';
      } else if (error.code === 'ETIMEDOUT') {
        errorMessage = 'انتهت مهلة الاتصال. يرجى التحقق من اتصال الشبكة.';
      } else if (error.response) {
        // الخادم استجاب برمز حالة خارج نطاق 2xx
        errorMessage = `خطأ في الاستجابة: ${error.response.status} - ${error.response.data?.message || error.response.statusText}`;
      } else if (error.request) {
        // تم إرسال الطلب ولكن لم يتم استلام استجابة
        errorMessage = 'لم يتم استلام استجابة من الخادم. يرجى التحقق من اتصال الشبكة.';
      }

      return {
        success: false,
        message: errorMessage,
        error: error.message
      };
    }
  }

  /**
   * الحصول على المخزون من منظومة المبيعات
   * @returns {Promise<Object>} - بيانات المخزون
   */
  async getInventory() {
    try {
      console.log('الحصول على المخزون من منظومة المبيعات');

      // الاتصال الفعلي بالخادم
      const response = await this.api.get('/api/inventory');
      return { success: true, data: response.data };
    } catch (error) {
      console.error('خطأ في الحصول على المخزون:', error);

      // تحسين رسائل الخطأ
      let errorMessage = 'فشل في الحصول على بيانات المخزون';

      if (error.code === 'ECONNREFUSED') {
        errorMessage = 'تعذر الاتصال بالخادم. يرجى التحقق من عنوان IP والمنفذ.';
      } else if (error.code === 'ETIMEDOUT') {
        errorMessage = 'انتهت مهلة الاتصال. يرجى التحقق من اتصال الشبكة.';
      } else if (error.response) {
        errorMessage = `خطأ في الاستجابة: ${error.response.status} - ${error.response.data?.message || error.response.statusText}`;
      } else if (error.request) {
        errorMessage = 'لم يتم استلام استجابة من الخادم. يرجى التحقق من اتصال الشبكة.';
      }

      return {
        success: false,
        message: errorMessage,
        error: error.message
      };
    }
  }

  /**
   * الحصول على المبيعات من منظومة المبيعات
   * @param {Object} params - معلمات الاستعلام
   * @param {string} params.startDate - تاريخ البداية
   * @param {string} params.endDate - تاريخ النهاية
   * @returns {Promise<Object>} - بيانات المبيعات
   */
  async getSales(params = {}) {
    try {
      console.log('الحصول على المبيعات من منظومة المبيعات', params);

      // الاتصال الفعلي بالخادم
      const response = await this.api.get('/api/sales', { params });
      return { success: true, data: response.data };
    } catch (error) {
      console.error('خطأ في الحصول على المبيعات:', error);

      // تحسين رسائل الخطأ
      let errorMessage = 'فشل في الحصول على بيانات المبيعات';

      if (error.code === 'ECONNREFUSED') {
        errorMessage = 'تعذر الاتصال بالخادم. يرجى التحقق من عنوان IP والمنفذ.';
      } else if (error.code === 'ETIMEDOUT') {
        errorMessage = 'انتهت مهلة الاتصال. يرجى التحقق من اتصال الشبكة.';
      } else if (error.response) {
        errorMessage = `خطأ في الاستجابة: ${error.response.status} - ${error.response.data?.message || error.response.statusText}`;
      } else if (error.request) {
        errorMessage = 'لم يتم استلام استجابة من الخادم. يرجى التحقق من اتصال الشبكة.';
      }

      return {
        success: false,
        message: errorMessage,
        error: error.message
      };
    }
  }

  /**
   * إرسال تحديث المخزون إلى منظومة المبيعات
   * @param {Array} inventory - بيانات المخزون المحدثة
   * @returns {Promise<Object>} - نتيجة التحديث
   */
  async syncInventory(inventory) {
    try {
      console.log('مزامنة المخزون مع منظومة المبيعات', inventory ? inventory.length : 0, 'عناصر');

      // الاتصال الفعلي بالخادم
      const response = await this.api.post('/api/sync/inventory', { inventory });
      return { success: true, data: response.data };
    } catch (error) {
      console.error('خطأ في مزامنة المخزون:', error);

      // تحسين رسائل الخطأ
      let errorMessage = 'فشل في مزامنة بيانات المخزون';

      if (error.code === 'ECONNREFUSED') {
        errorMessage = 'تعذر الاتصال بالخادم. يرجى التحقق من عنوان IP والمنفذ.';
      } else if (error.code === 'ETIMEDOUT') {
        errorMessage = 'انتهت مهلة الاتصال. يرجى التحقق من اتصال الشبكة.';
      } else if (error.response) {
        errorMessage = `خطأ في الاستجابة: ${error.response.status} - ${error.response.data?.message || error.response.statusText}`;
      } else if (error.request) {
        errorMessage = 'لم يتم استلام استجابة من الخادم. يرجى التحقق من اتصال الشبكة.';
      }

      return {
        success: false,
        message: errorMessage,
        error: error.message
      };
    }
  }

  /**
   * إرسال معاملة جديدة إلى منظومة المبيعات
   * @param {Object} transaction - بيانات المعاملة
   * @returns {Promise<Object>} - نتيجة الإرسال
   */
  async sendTransaction(transaction) {
    try {
      console.log('إرسال معاملة إلى منظومة المبيعات', transaction);

      // الاتصال الفعلي بالخادم
      const response = await this.api.post('/api/transactions', transaction);
      return { success: true, data: response.data };
    } catch (error) {
      console.error('خطأ في إرسال المعاملة:', error);

      // تحسين رسائل الخطأ
      let errorMessage = 'فشل في إرسال بيانات المعاملة';

      if (error.code === 'ECONNREFUSED') {
        errorMessage = 'تعذر الاتصال بالخادم. يرجى التحقق من عنوان IP والمنفذ.';
      } else if (error.code === 'ETIMEDOUT') {
        errorMessage = 'انتهت مهلة الاتصال. يرجى التحقق من اتصال الشبكة.';
      } else if (error.response) {
        errorMessage = `خطأ في الاستجابة: ${error.response.status} - ${error.response.data?.message || error.response.statusText}`;
      } else if (error.request) {
        errorMessage = 'لم يتم استلام استجابة من الخادم. يرجى التحقق من اتصال الشبكة.';
      }

      return {
        success: false,
        message: errorMessage,
        error: error.message
      };
    }
  }

  /**
   * الحصول على معاملات من منظومة المبيعات
   * @param {Object} params - معلمات الاستعلام
   * @param {string} params.startDate - تاريخ البداية
   * @param {string} params.endDate - تاريخ النهاية
   * @param {string} params.type - نوع المعاملة
   * @returns {Promise<Object>} - بيانات المعاملات
   */
  async getTransactions(params = {}) {
    try {
      console.log('الحصول على المعاملات من منظومة المبيعات', params);

      // الاتصال الفعلي بالخادم
      const response = await this.api.get('/api/transactions', { params });
      return { success: true, data: response.data };
    } catch (error) {
      console.error('خطأ في الحصول على المعاملات:', error);

      // تحسين رسائل الخطأ
      let errorMessage = 'فشل في الحصول على بيانات المعاملات';

      if (error.code === 'ECONNREFUSED') {
        errorMessage = 'تعذر الاتصال بالخادم. يرجى التحقق من عنوان IP والمنفذ.';
      } else if (error.code === 'ETIMEDOUT') {
        errorMessage = 'انتهت مهلة الاتصال. يرجى التحقق من اتصال الشبكة.';
      } else if (error.response) {
        errorMessage = `خطأ في الاستجابة: ${error.response.status} - ${error.response.data?.message || error.response.statusText}`;
      } else if (error.request) {
        errorMessage = 'لم يتم استلام استجابة من الخادم. يرجى التحقق من اتصال الشبكة.';
      }

      return {
        success: false,
        message: errorMessage,
        error: error.message
      };
    }
  }
}

// تصدير نسخة واحدة من الخدمة
const salesApiService = new SalesApiService();

export default salesApiService;
