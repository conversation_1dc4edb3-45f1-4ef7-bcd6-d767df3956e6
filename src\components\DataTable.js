import React, { useState, useEffect, useRef, useMemo } from 'react';
import { FaSort, FaSortUp, FaSortDown, FaSearch, FaFilter } from 'react-icons/fa';
import './DataTable.css';

/**
 * مكون جدول البيانات
 * يوفر جدولًا متقدمًا مع إمكانية الفرز والبحث والتصفية
 */
const DataTable = ({
  columns,
  data,
  pagination = true,
  pageSize = 10,
  searchable = true,
  sortable = true,
  filterable = false,
  loading = false,
  emptyMessage = 'لا توجد بيانات',
  className = '',
  onRowClick,
  searchPlaceholder = "بحث...",
  searchFields = [], // إضافة خاصية حقول البحث
  ...props
}) => {
  // حالة الجدول
  const [currentPage, setCurrentPage] = useState(1);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({});
  const [displayData, setDisplayData] = useState([]);
  const [totalPages, setTotalPages] = useState(1);

  // استخدام useRef لتتبع التغييرات في البيانات
  const prevDataLengthRef = useRef(data.length);
  const isInitialMount = useRef(true);

  // تهيئة البيانات عند التحميل الأولي
  useEffect(() => {
    // تعيين البيانات الأولية
    if (data.length > 0) {
      setDisplayData(data.slice(0, pageSize));
      setTotalPages(Math.ceil(data.length / pageSize) || 1);
    }
  }, []); // تنفيذ مرة واحدة فقط عند التحميل

  // معالجة البيانات باستخدام useMemo لتحسين الأداء
  // تم تحسين آلية البحث لتعمل بشكل أفضل مع حقول البحث المحددة وخاصة رقم الفاتورة
  const processedData = useMemo(() => {
    let result = [...data];

    // تطبيق البحث
    if (searchTerm) {
      result = result.filter(item => {
        // إذا كانت هناك حقول بحث محددة، استخدمها
        if (searchFields && searchFields.length > 0) {
          return searchFields.some(field => {
            const value = item[field];
            // تحسين البحث للتعامل مع القيم الفارغة أو غير المعرفة
            if (value === undefined || value === null) return false;
            return String(value).toLowerCase().includes(searchTerm.toLowerCase());
          });
        }

        // إذا لم تكن هناك حقول بحث محددة، ابحث في جميع الأعمدة
        return columns.some(column => {
          if (!column.searchable) return false;

          const value = column.accessor ? item[column.accessor] : column.cell ? column.cell(item) : '';
          // تحسين البحث للتعامل مع القيم الفارغة أو غير المعرفة
          if (value === undefined || value === null) return false;
          return String(value).toLowerCase().includes(searchTerm.toLowerCase());
        });
      });
    }

    // تطبيق التصفية
    if (filterable && Object.keys(filters).length > 0) {
      result = result.filter(item => {
        return Object.entries(filters).every(([key, value]) => {
          if (!value) return true;
          return String(item[key]).toLowerCase().includes(value.toLowerCase());
        });
      });
    }

    // تطبيق الفرز
    if (sortConfig.key) {
      result.sort((a, b) => {
        const aValue = a[sortConfig.key];
        const bValue = b[sortConfig.key];

        if (aValue < bValue) {
          return sortConfig.direction === 'asc' ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }

    return result;
  }, [data, columns, searchTerm, filters, sortConfig, filterable]);

  // التعامل مع التغييرات في البيانات
  useEffect(() => {
    // إعادة تعيين الصفحة الحالية إلى 1 عند تغيير البيانات
    if (!isInitialMount.current && data.length !== prevDataLengthRef.current) {
      setCurrentPage(1);
    }

    // تحديث المرجع
    prevDataLengthRef.current = data.length;
  }, [data]);

  // حساب إجمالي الصفحات وتطبيق الصفحات
  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    // حساب إجمالي الصفحات
    const calculatedTotalPages = Math.ceil(processedData.length / pageSize) || 1;
    setTotalPages(calculatedTotalPages);

    // تطبيق الصفحات
    if (pagination) {
      const startIndex = (currentPage - 1) * pageSize;
      const paginatedData = processedData.slice(startIndex, startIndex + pageSize);
      setDisplayData(paginatedData);
    } else {
      setDisplayData(processedData);
    }
  }, [processedData, pageSize, pagination, currentPage]);

  // معالج تغيير الفرز
  const handleSort = (key) => {
    if (!sortable) return;

    let direction = 'asc';
    if (sortConfig.key === key) {
      direction = sortConfig.direction === 'asc' ? 'desc' : 'asc';
    }

    setSortConfig({ key, direction });
  };

  // معالج تغيير البحث
  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  // معالج تغيير التصفية
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
    setCurrentPage(1);
  };

  // معالج تغيير الصفحة
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // معالج النقر على الصف
  const handleRowClick = (row, index) => {
    if (onRowClick) {
      onRowClick(row, index);
    }
  };

  // أيقونة الفرز
  const getSortIcon = (key) => {
    if (!sortable) return null;

    if (sortConfig.key === key) {
      return sortConfig.direction === 'asc' ? <FaSortUp /> : <FaSortDown />;
    }
    return <FaSort />;
  };

  // إنشاء أزرار الصفحات
  const renderPagination = () => {
    if (!pagination || totalPages <= 1) return null;

    const pages = [];
    const maxVisiblePages = 5;

    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    // زر الصفحة السابقة
    pages.push(
      <button
        key="prev"
        className="app-datatable-pagination-button"
        onClick={() => handlePageChange(currentPage - 1)}
        disabled={currentPage === 1}
      >
        السابق
      </button>
    );

    // زر الصفحة الأولى
    if (startPage > 1) {
      pages.push(
        <button
          key="1"
          className={`app-datatable-pagination-button ${currentPage === 1 ? 'active' : ''}`}
          onClick={() => handlePageChange(1)}
        >
          1
        </button>
      );

      if (startPage > 2) {
        pages.push(
          <span key="ellipsis1" className="app-datatable-pagination-ellipsis">...</span>
        );
      }
    }

    // أزرار الصفحات
    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <button
          key={i}
          className={`app-datatable-pagination-button ${currentPage === i ? 'active' : ''}`}
          onClick={() => handlePageChange(i)}
        >
          {i}
        </button>
      );
    }

    // زر الصفحة الأخيرة
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        pages.push(
          <span key="ellipsis2" className="app-datatable-pagination-ellipsis">...</span>
        );
      }

      pages.push(
        <button
          key={totalPages}
          className={`app-datatable-pagination-button ${currentPage === totalPages ? 'active' : ''}`}
          onClick={() => handlePageChange(totalPages)}
        >
          {totalPages}
        </button>
      );
    }

    // زر الصفحة التالية
    pages.push(
      <button
        key="next"
        className="app-datatable-pagination-button"
        onClick={() => handlePageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
      >
        التالي
      </button>
    );

    return (
      <div className="app-datatable-pagination">
        <div className="app-datatable-pagination-info">
          عرض {displayData.length} من {data.length} سجل
        </div>
        <div className="app-datatable-pagination-buttons">
          {pages}
        </div>
      </div>
    );
  };

  // إنشاء حقول التصفية
  const renderFilters = () => {
    if (!filterable) return null;

    return (
      <div className="app-datatable-filters">
        {columns.map((column, index) => {
          if (!column.filterable) return null;

          return (
            <div key={index} className="app-datatable-filter">
              <input
                type="text"
                placeholder={`تصفية ${column.header}`}
                value={filters[column.accessor] || ''}
                onChange={(e) => handleFilterChange(column.accessor, e.target.value)}
              />
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className={`app-datatable ${className}`} {...props}>
      {/* شريط الأدوات */}
      <div className="app-datatable-toolbar">
        {searchable && (
          <div className="app-datatable-search">
            <FaSearch className="app-datatable-search-icon" />
            <input
              type="text"
              placeholder={searchPlaceholder}
              value={searchTerm}
              onChange={handleSearch}
            />
          </div>
        )}

        {filterable && (
          <button
            className="app-datatable-filter-toggle"
            onClick={() => setFilters({})}
            disabled={Object.keys(filters).length === 0}
          >
            <FaFilter /> مسح التصفية
          </button>
        )}
      </div>

      {/* حقول التصفية */}
      {renderFilters()}

      {/* الجدول */}
      <div className="app-datatable-table-container">
        <table className="app-datatable-table">
          <thead>
            <tr>
              {columns.map((column, index) => (
                // إضافة شرط للتحقق من خاصية hidden
                !column.hidden && (
                  <th
                    key={index}
                    className={`${column.sortable !== false && sortable ? 'sortable' : ''} ${column.className || ''}`}
                    onClick={() => column.sortable !== false && handleSort(column.accessor)}
                    style={column.style}
                  >
                    <div className="app-datatable-header-content">
                      <span>{column.header}</span>
                      {column.sortable !== false && sortable && (
                        <span className="app-datatable-sort-icon">
                          {getSortIcon(column.accessor)}
                        </span>
                      )}
                    </div>
                  </th>
                )
              ))}
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={columns.length} className="app-datatable-loading">
                  <div className="app-datatable-spinner"></div>
                  <span>جاري التحميل...</span>
                </td>
              </tr>
            ) : displayData.length === 0 ? (
              <tr>
                <td colSpan={columns.length} className="app-datatable-empty">
                  {emptyMessage}
                </td>
              </tr>
            ) : (
              displayData.map((row, rowIndex) => (
                <tr
                  key={rowIndex}
                  onClick={() => handleRowClick(row, rowIndex)}
                  className={onRowClick ? 'clickable' : ''}
                >
                  {columns.map((column, colIndex) => (
                    // إضافة شرط للتحقق من خاصية hidden
                    !column.hidden && (
                      <td
                        key={colIndex}
                        className={column.className || ''}
                        style={column.style}
                      >
                        {column.cell ? column.cell(row, rowIndex) : (
                          row[column.accessor] !== undefined && row[column.accessor] !== null && !isNaN(row[column.accessor])
                            ? row[column.accessor]
                            : '-'
                        )}
                      </td>
                    )
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* الصفحات */}
      {renderPagination()}
    </div>
  );
};

export default DataTable;
