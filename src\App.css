/* تعيين الخط الافتراضي للتطبيق */
body {
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.main-content-full {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  margin-top: 60px;
  background-color: var(--bg-color);
}

/* Navbar Styles */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background-color: var(--primary-color);
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.navbar-brand {
  display: flex;
  align-items: center;
  margin-left: 20px;
}

.navbar-brand .logo {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
  color: white;
  font-weight: bold;
  font-size: 1rem;
}

.navbar-brand h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.navbar-menu {
  display: flex;
  flex: 1;
  justify-content: center;
  overflow-x: auto;
}

.navbar-menu-item {
  padding: 0 15px;
  height: 60px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s;
  white-space: nowrap;
}

.navbar-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.navbar-menu-item.active {
  background-color: rgba(232, 122, 77, 0.3);
  border-bottom: 3px solid var(--accent-color);
}

.navbar-menu-item .menu-icon {
  margin-left: 8px;
}

.navbar-user {
  margin-right: auto;
}

.navbar-menu-item.logout {
  color: var(--accent-color);
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.spinner {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* أنماط النافذة المنبثقة */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* تحسين وضوح النص في النوافذ المنبثقة */
.modal-container small,
.modal-container .form-text,
.modal-container .text-muted {
  color: #000000 !important;
  font-weight: 600 !important;
}

.modal-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 1000px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  color: #000000;
}

.modal-header {
  background-color: #f8f9fa;
  padding: 15px 20px;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6c757d;
}

.modal-body {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
  color: #000000 !important;
}

/* تحسين وضوح النص في النوافذ المنبثقة */
.modal-body label {
  color: #000000 !important;
  font-weight: 700 !important;
}

.modal-body .form-control {
  color: #000000 !important;
}

.modal-body small {
  color: #000000 !important;
  font-weight: 600 !important;
}

/* تحسين وضوح النص في النوافذ المنبثقة في صفحات المبيعات والمشتريات */
.modal-body .form-text,
.modal-body .text-muted,
.modal-body .card-body,
.modal-body .alert,
.modal-body .input-group-text,
.modal-body .form-control.bg-light,
.modal-body .total-price,
.modal-body .d-flex,
.modal-body .justify-content-between,
.modal-body .align-items-center,
.modal-body strong {
  color: #000000 !important;
  font-weight: 600 !important;
}

/* تحسين وضوح النص في النوافذ المنبثقة في صفحات المبيعات والمشتريات */
.modal-body .alert-info,
.modal-body .alert-info div,
.modal-body .alert-info strong,
.modal-body .input-group-append .input-group-text,
.modal-body .form-control.bg-light,
.modal-body .bg-light.font-weight-bold,
.modal-body .card.bg-light .card-body,
.modal-body .card.bg-light .card-title,
.modal-body .card.bg-light .font-weight-bold,
.modal-body .d-flex.justify-content-between div,
.modal-body .d-flex.justify-content-between strong {
  color: #000000 !important;
  font-weight: 700 !important;
}

/* تحسين وضوح النص في ملخص العملية */
.modal-body .card-title,
.modal-body .card-body .font-weight-bold {
  color: #000000 !important;
  font-weight: 700 !important;
}

.modal-footer {
  padding: 15px 20px;
  border-top: 1px solid #dee2e6;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.modal {
  background-color: white;
  border-radius: 8px;
  width: 500px;
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  z-index: 1001;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.modal-title {
  margin: 0;
  font-size: 1.25rem;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 15px 20px;
  border-top: 1px solid #eee;
  gap: 10px;
}

/* أنماط النماذج */
.form-group {
  margin-bottom: 15px;
}

.form-row {
  display: flex;
  margin: 0 -10px;
  margin-bottom: 15px;
}

.form-row .form-group {
  flex: 1;
  padding: 0 10px;
  margin-bottom: 0;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

/* أنماط الأزرار */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 0.875rem;
}

/* أنماط التنبيهات */
.alert {
  padding: 12px 16px;
  margin-bottom: 15px;
  border-radius: 4px;
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.alert-info {
  background-color: #d1ecf1;
  color: #000000 !important;
  border: 1px solid #bee5eb;
}

.alert-info strong,
.alert-info div,
.alert-info span {
  color: #000000 !important;
  font-weight: 700 !important;
}

/* أنماط الجدول */
.table-container {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 12px 15px;
  text-align: right;
  border-bottom: 1px solid #ddd;
}

.table th {
  background-color: #f8f9fa;
  font-weight: bold;
}

.badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
}

.badge-primary {
  background-color: #007bff;
  color: white;
}

.badge-secondary {
  background-color: #6c757d;
  color: white;
}

.badge-danger {
  background-color: #dc3545;
  color: white;
}

.badge-warning {
  background-color: #ffc107;
  color: #212529;
}

/* أنماط البطاقات الإحصائية */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.stat-card {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.stat-card-value {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 10px;
  color: var(--primary-color);
}

.stat-card-title {
  font-size: 1rem;
  color: #6c757d;
}

.stat-card-subtitle {
  font-size: 0.8rem;
  color: #666;
  margin-top: 0.25rem;
}

/* أنماط التبويب */
.tabs {
  display: flex;
  border-bottom: 1px solid #ddd;
  margin-bottom: 1rem;
}

.tab-btn {
  padding: 0.5rem 1rem;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 1rem;
  color: #666;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
  display: flex;
  align-items: center;
}

.tab-btn:hover {
  color: #007bff;
}

.tab-btn.active {
  color: #007bff;
  border-bottom-color: #007bff;
  font-weight: bold;
}

.text-success {
  color: #28a745;
}

.font-bold {
  font-weight: bold;
}

/* تم نقل أنماط تقسيم الأرباح إلى Reports.css */

/* تحسين وضوح النص في النوافذ المنبثقة في صفحات المبيعات والمشتريات */
.input-group-text {
  color: #000000 !important;
  font-weight: 600 !important;
}

.form-control.bg-light {
  color: #000000 !important;
  font-weight: 600 !important;
}

.card.bg-light .card-body {
  color: #000000 !important;
}

.card.bg-light .card-title {
  color: #000000 !important;
  font-weight: 700 !important;
}

.card.bg-light .font-weight-bold {
  color: #000000 !important;
  font-weight: 700 !important;
}
