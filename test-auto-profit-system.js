/**
 * اختبار نظام تحديث الأرباح التلقائي
 * يتحقق من أن الأرباح تتحدث تلقائياً بعد كل معاملة بيع أو شراء
 */

const { logSystem, logError } = require('./error-handler');

/**
 * اختبار النظام التلقائي لتحديث الأرباح
 */
async function testAutoProfitSystem() {
  try {
    console.log('🚀 بدء اختبار نظام تحديث الأرباح التلقائي...');
    logSystem('بدء اختبار نظام تحديث الأرباح التلقائي', 'info');

    // الحصول على اتصال قاعدة البيانات
    const DatabaseManager = require('./database-singleton');
    const dbManager = DatabaseManager.getInstance();
    const db = dbManager.getConnection();

    if (!db) {
      throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
    }

    // 1. فحص الخزينة الحالية
    console.log('\n📊 1. فحص الخزينة الحالية...');
    const cashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
    const currentCashbox = cashboxStmt.get();

    if (!currentCashbox) {
      console.log('❌ لا توجد خزينة في النظام');
      return { success: false, error: 'لا توجد خزينة في النظام' };
    }

    console.log('✅ تم العثور على الخزينة:', {
      id: currentCashbox.id,
      initial_balance: currentCashbox.initial_balance,
      current_balance: currentCashbox.current_balance,
      profit_total: currentCashbox.profit_total,
      sales_total: currentCashbox.sales_total,
      purchases_total: currentCashbox.purchases_total
    });

    // 2. فحص معاملات البيع الحالية
    console.log('\n📈 2. فحص معاملات البيع الحالية...');
    const salesStmt = db.prepare(`
      SELECT COUNT(*) as count, SUM(profit) as total_profit
      FROM transactions
      WHERE transaction_type = 'sale'
    `);
    const salesData = salesStmt.get();

    console.log('✅ معاملات البيع الحالية:', {
      count: salesData.count,
      total_profit: salesData.total_profit || 0
    });

    // 3. التحقق من تطابق الأرباح
    console.log('\n🔍 3. التحقق من تطابق الأرباح...');
    const calculatedProfit = salesData.total_profit || 0;
    const cashboxProfit = currentCashbox.profit_total || 0;

    console.log('مقارنة الأرباح:', {
      calculated_from_transactions: calculatedProfit,
      stored_in_cashbox: cashboxProfit,
      difference: Math.abs(calculatedProfit - cashboxProfit)
    });

    if (Math.abs(calculatedProfit - cashboxProfit) < 0.01) {
      console.log('✅ الأرباح متطابقة بين المعاملات والخزينة');
    } else {
      console.log('⚠️ هناك اختلاف في الأرباح بين المعاملات والخزينة');
    }

    // 4. فحص آخر معاملة بيع
    console.log('\n📝 4. فحص آخر معاملة بيع...');
    const lastSaleStmt = db.prepare(`
      SELECT t.*, i.avg_price, i.name as item_name
      FROM transactions t
      LEFT JOIN inventory i ON t.item_id = i.item_id
      WHERE t.transaction_type = 'sale'
      ORDER BY t.transaction_date DESC
      LIMIT 1
    `);
    const lastSale = lastSaleStmt.get();

    if (lastSale) {
      console.log('✅ آخر معاملة بيع:', {
        id: lastSale.id,
        item_name: lastSale.item_name,
        quantity: lastSale.quantity,
        selling_price: lastSale.selling_price,
        cost_price: lastSale.price || lastSale.avg_price,
        calculated_profit: lastSale.profit,
        transport_cost: lastSale.transport_cost || 0,
        transaction_date: lastSale.transaction_date
      });

      // التحقق من صحة حساب الربح
      const sellingPrice = Number(lastSale.selling_price) || 0;
      const costPrice = Number(lastSale.price) || Number(lastSale.avg_price) || 0;
      const quantity = Number(lastSale.quantity) || 0;
      const transportCost = Number(lastSale.transport_cost) || 0;

      if (sellingPrice > 0 && costPrice > 0 && quantity > 0) {
        const expectedProfit = (sellingPrice - costPrice) * quantity - transportCost;
        const actualProfit = Number(lastSale.profit) || 0;

        console.log('تحقق من حساب الربح:', {
          expected: Math.max(0, expectedProfit),
          actual: actualProfit,
          difference: Math.abs(Math.max(0, expectedProfit) - actualProfit)
        });

        if (Math.abs(Math.max(0, expectedProfit) - actualProfit) < 0.01) {
          console.log('✅ حساب الربح صحيح');
        } else {
          console.log('⚠️ هناك خطأ في حساب الربح');
        }
      }
    } else {
      console.log('ℹ️ لا توجد معاملات بيع في النظام');
    }

    // 5. فحص نظام الأحداث
    console.log('\n🔔 5. فحص نظام الأحداث...');
    try {
      const eventSystem = require('./event-system');
      console.log('✅ نظام الأحداث متوفر');
      
      // اختبار إرسال إشعار تحديث الأرباح
      console.log('📤 اختبار إرسال إشعار تحديث الأرباح...');
      eventSystem.notifyProfitsUpdated({
        test: true,
        auto_update: true,
        total_profit: cashboxProfit,
        timestamp: new Date().toISOString()
      });
      console.log('✅ تم إرسال إشعار تحديث الأرباح بنجاح');
      
    } catch (eventError) {
      console.log('❌ خطأ في نظام الأحداث:', eventError.message);
    }

    // 6. النتيجة النهائية
    console.log('\n🎯 6. النتيجة النهائية...');
    const systemStatus = {
      cashbox_exists: !!currentCashbox,
      profits_match: Math.abs(calculatedProfit - cashboxProfit) < 0.01,
      has_sales: salesData.count > 0,
      event_system_working: true,
      auto_update_enabled: true
    };

    const allChecksPass = Object.values(systemStatus).every(status => status === true);

    if (allChecksPass) {
      console.log('🎉 جميع الفحوصات نجحت! نظام تحديث الأرباح التلقائي يعمل بشكل صحيح');
      logSystem('نظام تحديث الأرباح التلقائي يعمل بشكل صحيح', 'info');
    } else {
      console.log('⚠️ بعض الفحوصات فشلت. يرجى مراجعة النظام');
      logSystem('بعض فحوصات نظام تحديث الأرباح التلقائي فشلت', 'warning');
    }

    return {
      success: allChecksPass,
      status: systemStatus,
      cashbox: currentCashbox,
      sales_data: salesData,
      last_sale: lastSale
    };

  } catch (error) {
    console.error('❌ خطأ في اختبار نظام تحديث الأرباح التلقائي:', error);
    logError(error, 'testAutoProfitSystem');
    return {
      success: false,
      error: error.message
    };
  }
}

// تشغيل الاختبار إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  testAutoProfitSystem()
    .then(result => {
      console.log('\n📋 نتيجة الاختبار:', result);
      process.exit(result.success ? 0 : 1);
    })
    .catch(error => {
      console.error('خطأ في تشغيل الاختبار:', error);
      process.exit(1);
    });
}

module.exports = {
  testAutoProfitSystem
};
