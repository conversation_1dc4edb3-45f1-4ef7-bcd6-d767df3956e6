/**
 * سكريبت لتطبيق التغييرات على قاعدة البيانات
 * يقوم بإزالة إعدادات Google Drive من قاعدة البيانات
 */

const { app } = require('electron');
const path = require('path');
const fs = require('fs');
const sqlite3 = require('sqlite3').verbose();

// الحصول على مسار قاعدة البيانات
const dbPath = path.join(app.getPath('userData'), 'wms-database', 'warehouse.db');

// التحقق من وجود قاعدة البيانات
if (!fs.existsSync(dbPath)) {
  console.error('قاعدة البيانات غير موجودة في المسار:', dbPath);
  process.exit(1);
}

// فتح اتصال بقاعدة البيانات
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('خطأ في فتح قاعدة البيانات:', err.message);
    process.exit(1);
  }
  console.log('تم الاتصال بقاعدة البيانات بنجاح');
});

// تنفيذ التغييرات
db.serialize(() => {
  // حذف إعدادات Google Drive
  db.run("DELETE FROM settings WHERE key = 'googleDriveBackupEnabled'", function(err) {
    if (err) {
      console.error('خطأ في حذف googleDriveBackupEnabled:', err.message);
    } else {
      console.log(`تم حذف googleDriveBackupEnabled، عدد الصفوف المتأثرة: ${this.changes}`);
    }
  });

  db.run("DELETE FROM settings WHERE key = 'googleDriveAutoBackup'", function(err) {
    if (err) {
      console.error('خطأ في حذف googleDriveAutoBackup:', err.message);
    } else {
      console.log(`تم حذف googleDriveAutoBackup، عدد الصفوف المتأثرة: ${this.changes}`);
    }
  });

  db.run("DELETE FROM settings WHERE key = 'googleDriveBackupInterval'", function(err) {
    if (err) {
      console.error('خطأ في حذف googleDriveBackupInterval:', err.message);
    } else {
      console.log(`تم حذف googleDriveBackupInterval، عدد الصفوف المتأثرة: ${this.changes}`);
    }
  });

  db.run("DELETE FROM settings WHERE key = 'googleDriveKeepBackupsCount'", function(err) {
    if (err) {
      console.error('خطأ في حذف googleDriveKeepBackupsCount:', err.message);
    } else {
      console.log(`تم حذف googleDriveKeepBackupsCount، عدد الصفوف المتأثرة: ${this.changes}`);
    }
  });

  // تحديث إعدادات التحديث التلقائي
  db.run("INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES ('autoUpdateEnabled', 'false', CURRENT_TIMESTAMP)", function(err) {
    if (err) {
      console.error('خطأ في تحديث autoUpdateEnabled:', err.message);
    } else {
      console.log('تم تحديث autoUpdateEnabled بنجاح');
    }
  });

  db.run("INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES ('lastUpdateCheck', 'null', CURRENT_TIMESTAMP)", function(err) {
    if (err) {
      console.error('خطأ في تحديث lastUpdateCheck:', err.message);
    } else {
      console.log('تم تحديث lastUpdateCheck بنجاح');
    }
  });

  db.run("INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES ('isInternetConnection', 'false', CURRENT_TIMESTAMP)", function(err) {
    if (err) {
      console.error('خطأ في تحديث isInternetConnection:', err.message);
    } else {
      console.log('تم تحديث isInternetConnection بنجاح');
    }
  });

  // عرض الإعدادات المتبقية
  db.all("SELECT * FROM settings", [], (err, rows) => {
    if (err) {
      console.error('خطأ في استعلام الإعدادات:', err.message);
    } else {
      console.log('الإعدادات المتبقية:');
      console.table(rows);
    }
  });
});

// إغلاق الاتصال بقاعدة البيانات
setTimeout(() => {
  db.close((err) => {
    if (err) {
      console.error('خطأ في إغلاق قاعدة البيانات:', err.message);
    } else {
      console.log('تم إغلاق الاتصال بقاعدة البيانات بنجاح');
    }
    process.exit(0);
  });
}, 1000);
