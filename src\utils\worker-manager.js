/**
 * وحدة إدارة Web Workers
 * تستخدم لإدارة Web Workers وتنفيذ العمليات الثقيلة في خلفية التطبيق
 */

// إنشاء معرف فريد للرسائل
let messageId = 0;

// تخزين الوعود المعلقة
const pendingPromises = new Map();

// إنشاء Web Worker
let worker = null;

/**
 * تهيئة Web Worker
 * @returns {Worker} - كائن Web Worker
 */
export const initWorker = () => {
  if (worker) {
    return worker;
  }
  
  try {
    // إنشاء Web Worker جديد
    worker = new Worker(new URL('../workers/calculation-worker.js', import.meta.url));
    
    // إضافة مستمع للرسائل
    worker.addEventListener('message', (event) => {
      const { id, result, error } = event.data;
      
      // الحصول على الوعد المعلق
      const promise = pendingPromises.get(id);
      
      if (promise) {
        // حل أو رفض الوعد بناءً على النتيجة
        if (error) {
          promise.reject(new Error(error));
        } else {
          promise.resolve(result);
        }
        
        // إزالة الوعد من القائمة
        pendingPromises.delete(id);
      }
    });
    
    // إضافة مستمع للأخطاء
    worker.addEventListener('error', (error) => {
      console.error('خطأ في Web Worker:', error);
      
      // رفض جميع الوعود المعلقة
      for (const [id, promise] of pendingPromises.entries()) {
        promise.reject(new Error('حدث خطأ في Web Worker'));
        pendingPromises.delete(id);
      }
    });
    
    console.log('تم تهيئة Web Worker بنجاح');
    
    return worker;
  } catch (error) {
    console.error('فشل في تهيئة Web Worker:', error);
    return null;
  }
};

/**
 * إرسال رسالة إلى Web Worker
 * @param {String} type - نوع العملية
 * @param {Object} data - بيانات العملية
 * @returns {Promise} - وعد بنتيجة العملية
 */
export const sendToWorker = (type, data) => {
  // التحقق من وجود Web Worker
  if (!worker) {
    initWorker();
    
    if (!worker) {
      return Promise.reject(new Error('فشل في تهيئة Web Worker'));
    }
  }
  
  // إنشاء معرف فريد للرسالة
  const id = messageId++;
  
  // إنشاء وعد جديد
  return new Promise((resolve, reject) => {
    // تخزين الوعد في القائمة
    pendingPromises.set(id, { resolve, reject });
    
    // إرسال الرسالة إلى Web Worker
    worker.postMessage({ id, type, data });
  });
};

/**
 * حساب الأرباح باستخدام Web Worker
 * @param {Array} transactions - قائمة المعاملات
 * @returns {Promise<Object>} - وعد بإحصائيات الأرباح
 */
export const calculateProfitsAsync = (transactions) => {
  return sendToWorker('calculateProfits', { transactions });
};

/**
 * حساب إحصائيات المخزون باستخدام Web Worker
 * @param {Array} inventory - قائمة المخزون
 * @returns {Promise<Object>} - وعد بإحصائيات المخزون
 */
export const calculateInventoryStatsAsync = (inventory) => {
  return sendToWorker('calculateInventoryStats', { inventory });
};

/**
 * حساب إحصائيات المعاملات باستخدام Web Worker
 * @param {Array} transactions - قائمة المعاملات
 * @param {String} dateRange - النطاق الزمني
 * @param {String} startDate - تاريخ البداية
 * @param {String} endDate - تاريخ النهاية
 * @returns {Promise<Object>} - وعد بإحصائيات المعاملات
 */
export const calculateTransactionStatsAsync = (transactions, dateRange, startDate, endDate) => {
  return sendToWorker('calculateTransactionStats', { transactions, dateRange, startDate, endDate });
};

/**
 * حساب الأصناف الأكثر مبيعًا باستخدام Web Worker
 * @param {Array} transactions - قائمة المعاملات
 * @param {Array} items - قائمة الأصناف
 * @param {Number} limit - الحد الأقصى لعدد النتائج
 * @returns {Promise<Array>} - وعد بقائمة الأصناف الأكثر مبيعًا
 */
export const calculateTopSellingItemsAsync = (transactions, items, limit = 5) => {
  return sendToWorker('calculateTopSellingItems', { transactions, items, limit });
};

/**
 * حساب الأصناف الأكثر ربحًا باستخدام Web Worker
 * @param {Array} transactions - قائمة المعاملات
 * @param {Array} items - قائمة الأصناف
 * @param {Number} limit - الحد الأقصى لعدد النتائج
 * @returns {Promise<Array>} - وعد بقائمة الأصناف الأكثر ربحًا
 */
export const calculateMostProfitableItemsAsync = (transactions, items, limit = 5) => {
  return sendToWorker('calculateMostProfitableItems', { transactions, items, limit });
};

/**
 * تصفية المعاملات باستخدام Web Worker
 * @param {Array} transactions - قائمة المعاملات
 * @param {Object} filters - فلاتر التصفية
 * @returns {Promise<Array>} - وعد بقائمة المعاملات المصفاة
 */
export const filterTransactionsAsync = (transactions, filters) => {
  return sendToWorker('filterTransactions', { transactions, filters });
};

/**
 * إيقاف Web Worker
 */
export const terminateWorker = () => {
  if (worker) {
    worker.terminate();
    worker = null;
    console.log('تم إيقاف Web Worker');
  }
};
