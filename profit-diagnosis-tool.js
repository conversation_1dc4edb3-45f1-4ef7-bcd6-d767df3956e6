/**
 * أداة تشخيص شاملة لمشاكل حفظ الأرباح في نظام الخزينة
 * تقوم بفحص جميع جوانب نظام حفظ الأرباح وتحديد المشاكل
 */

const { logError, logSystem } = require('./error-handler');
const DatabaseManager = require('./database-singleton');

/**
 * تشخيص شامل لنظام حفظ الأرباح
 */
async function diagnoseProfitSavingIssues() {
  console.log('🔍 بدء التشخيص الشامل لمشاكل حفظ الأرباح...');
  
  const diagnosis = {
    timestamp: new Date().toISOString(),
    issues: [],
    recommendations: [],
    databaseState: {},
    transactionTests: {}
  };

  try {
    // الحصول على اتصال قاعدة البيانات
    const dbManager = DatabaseManager.getInstance();
    const db = dbManager.getConnection();

    if (!db) {
      diagnosis.issues.push('❌ فشل في الحصول على اتصال قاعدة البيانات');
      return diagnosis;
    }

    console.log('✅ تم الحصول على اتصال قاعدة البيانات');

    // 1. فحص حالة جدول cashbox
    await diagnoseCashboxTable(db, diagnosis);

    // 2. فحص معاملات البيع وحساب الأرباح
    await diagnoseSalesTransactions(db, diagnosis);

    // 3. اختبار عمليات UPDATE على profit_total
    await testProfitUpdateOperations(db, diagnosis);

    // 4. فحص تضارب العمليات
    await diagnoseOperationConflicts(db, diagnosis);

    // 5. اختبار آلية الحفظ
    await testSavingMechanism(db, diagnosis);

    // 6. تحليل سجلات الأخطاء
    await analyzeLogs(diagnosis);

  } catch (error) {
    console.error('❌ خطأ في التشخيص:', error);
    diagnosis.issues.push(`❌ خطأ في التشخيص: ${error.message}`);
    logError(error, 'diagnoseProfitSavingIssues');
  }

  // إنشاء التوصيات
  generateRecommendations(diagnosis);

  return diagnosis;
}

/**
 * فحص حالة جدول cashbox
 */
async function diagnoseCashboxTable(db, diagnosis) {
  console.log('🔍 فحص حالة جدول cashbox...');

  try {
    // فحص بنية الجدول
    const tableInfo = db.prepare("PRAGMA table_info(cashbox)").all();
    const profitColumn = tableInfo.find(col => col.name === 'profit_total');
    
    if (!profitColumn) {
      diagnosis.issues.push('❌ حقل profit_total غير موجود في جدول cashbox');
      return;
    }

    console.log('✅ حقل profit_total موجود في جدول cashbox');

    // فحص البيانات الحالية
    const cashboxData = db.prepare('SELECT * FROM cashbox LIMIT 1').get();
    
    if (!cashboxData) {
      diagnosis.issues.push('❌ لا توجد بيانات في جدول cashbox');
      return;
    }

    diagnosis.databaseState.cashbox = {
      id: cashboxData.id,
      initial_balance: cashboxData.initial_balance,
      current_balance: cashboxData.current_balance,
      profit_total: cashboxData.profit_total,
      sales_total: cashboxData.sales_total,
      purchases_total: cashboxData.purchases_total,
      returns_total: cashboxData.returns_total,
      transport_total: cashboxData.transport_total,
      updated_at: cashboxData.updated_at
    };

    console.log('📊 حالة الخزينة الحالية:', diagnosis.databaseState.cashbox);

    // فحص قيمة profit_total
    if (cashboxData.profit_total === null || cashboxData.profit_total === undefined) {
      diagnosis.issues.push('⚠️ قيمة profit_total هي null أو undefined');
    } else if (cashboxData.profit_total === 0) {
      diagnosis.issues.push('⚠️ قيمة profit_total هي صفر - قد تكون هناك مشكلة في الحساب');
    } else {
      console.log(`✅ قيمة profit_total: ${cashboxData.profit_total}`);
    }

  } catch (error) {
    diagnosis.issues.push(`❌ خطأ في فحص جدول cashbox: ${error.message}`);
    console.error('❌ خطأ في فحص جدول cashbox:', error);
  }
}

/**
 * فحص معاملات البيع وحساب الأرباح
 */
async function diagnoseSalesTransactions(db, diagnosis) {
  console.log('🔍 فحص معاملات البيع وحساب الأرباح...');

  try {
    // حساب إجمالي الأرباح من معاملات البيع
    const profitQuery = db.prepare(`
      SELECT 
        COUNT(*) as sales_count,
        COALESCE(SUM(profit), 0) as calculated_profit_total,
        COALESCE(SUM(total_price), 0) as total_sales_amount
      FROM transactions 
      WHERE transaction_type = 'sale'
    `);

    const profitData = profitQuery.get();
    
    diagnosis.databaseState.calculatedProfits = {
      salesCount: profitData.sales_count,
      calculatedProfitTotal: profitData.calculated_profit_total,
      totalSalesAmount: profitData.total_sales_amount
    };

    console.log('📊 الأرباح المحسوبة من المعاملات:', diagnosis.databaseState.calculatedProfits);

    // مقارنة مع قيمة profit_total في الخزينة
    const cashboxProfit = diagnosis.databaseState.cashbox?.profit_total || 0;
    const calculatedProfit = profitData.calculated_profit_total;

    if (Math.abs(cashboxProfit - calculatedProfit) > 0.01) {
      diagnosis.issues.push(
        `❌ عدم تطابق الأرباح: الخزينة (${cashboxProfit}) ≠ المحسوب (${calculatedProfit})`
      );
    } else {
      console.log('✅ الأرباح متطابقة بين الخزينة والمعاملات');
    }

    // فحص معاملات بأرباح سالبة أو صفر
    const negativeProfit = db.prepare(`
      SELECT COUNT(*) as count 
      FROM transactions 
      WHERE transaction_type = 'sale' AND profit <= 0
    `).get();

    if (negativeProfit.count > 0) {
      diagnosis.issues.push(`⚠️ يوجد ${negativeProfit.count} معاملة بيع بربح سالب أو صفر`);
    }

  } catch (error) {
    diagnosis.issues.push(`❌ خطأ في فحص معاملات البيع: ${error.message}`);
    console.error('❌ خطأ في فحص معاملات البيع:', error);
  }
}

/**
 * اختبار عمليات UPDATE على profit_total
 */
async function testProfitUpdateOperations(db, diagnosis) {
  console.log('🔍 اختبار عمليات UPDATE على profit_total...');

  try {
    // الحصول على القيمة الحالية
    const currentValue = db.prepare('SELECT profit_total FROM cashbox WHERE id = 1').get();
    const originalProfit = currentValue ? currentValue.profit_total : 0;

    console.log(`📊 القيمة الأصلية لـ profit_total: ${originalProfit}`);

    // اختبار تحديث بسيط
    const testValue = originalProfit + 1;
    const updateStmt = db.prepare(`
      UPDATE cashbox 
      SET profit_total = ?, updated_at = ? 
      WHERE id = 1
    `);

    const updateResult = updateStmt.run(testValue, new Date().toISOString());
    
    diagnosis.transactionTests.updateTest = {
      originalValue: originalProfit,
      testValue: testValue,
      changes: updateResult.changes,
      success: updateResult.changes > 0
    };

    if (updateResult.changes === 0) {
      diagnosis.issues.push('❌ فشل في تحديث profit_total - لا توجد صفوف متأثرة');
    } else {
      console.log(`✅ تم تحديث profit_total بنجاح. الصفوف المتأثرة: ${updateResult.changes}`);
    }

    // التحقق من حفظ القيمة
    const verifyValue = db.prepare('SELECT profit_total FROM cashbox WHERE id = 1').get();
    const savedValue = verifyValue ? verifyValue.profit_total : null;

    diagnosis.transactionTests.verificationTest = {
      expectedValue: testValue,
      savedValue: savedValue,
      success: Math.abs(savedValue - testValue) < 0.01
    };

    if (Math.abs(savedValue - testValue) > 0.01) {
      diagnosis.issues.push(
        `❌ فشل في حفظ القيمة: متوقع (${testValue}) ≠ محفوظ (${savedValue})`
      );
    } else {
      console.log('✅ تم حفظ القيمة بنجاح');
    }

    // إعادة القيمة الأصلية
    updateStmt.run(originalProfit, new Date().toISOString());
    console.log('🔄 تم إعادة القيمة الأصلية');

  } catch (error) {
    diagnosis.issues.push(`❌ خطأ في اختبار عمليات UPDATE: ${error.message}`);
    console.error('❌ خطأ في اختبار عمليات UPDATE:', error);
  }
}

/**
 * فحص تضارب العمليات
 */
async function diagnoseOperationConflicts(db, diagnosis) {
  console.log('🔍 فحص تضارب العمليات...');

  try {
    // فحص المعاملات المتزامنة
    const activeConnections = db.prepare("PRAGMA busy_timeout").get();
    diagnosis.databaseState.busyTimeout = activeConnections;

    // فحص الأقفال
    const lockInfo = db.prepare("PRAGMA lock_status").all();
    diagnosis.databaseState.lockStatus = lockInfo;

    console.log('📊 حالة الأقفال:', lockInfo);

  } catch (error) {
    diagnosis.issues.push(`❌ خطأ في فحص تضارب العمليات: ${error.message}`);
    console.error('❌ خطأ في فحص تضارب العمليات:', error);
  }
}

/**
 * اختبار آلية الحفظ
 */
async function testSavingMechanism(db, diagnosis) {
  console.log('🔍 اختبار آلية الحفظ...');

  try {
    // اختبار معاملة قاعدة البيانات
    const transactionTest = db.transaction(() => {
      const currentValue = db.prepare('SELECT profit_total FROM cashbox WHERE id = 1').get();
      const testValue = (currentValue ? currentValue.profit_total : 0) + 0.01;
      
      const updateStmt = db.prepare(`
        UPDATE cashbox 
        SET profit_total = ?, updated_at = ? 
        WHERE id = 1
      `);
      
      const result = updateStmt.run(testValue, new Date().toISOString());
      
      // التحقق من الحفظ داخل المعاملة
      const verifyStmt = db.prepare('SELECT profit_total FROM cashbox WHERE id = 1');
      const verifyResult = verifyStmt.get();
      
      return {
        updateResult: result,
        verifyResult: verifyResult,
        testValue: testValue
      };
    });

    const testResult = transactionTest();
    
    diagnosis.transactionTests.transactionTest = {
      success: testResult.updateResult.changes > 0,
      changes: testResult.updateResult.changes,
      savedCorrectly: Math.abs(testResult.verifyResult.profit_total - testResult.testValue) < 0.01
    };

    if (!diagnosis.transactionTests.transactionTest.success) {
      diagnosis.issues.push('❌ فشل في اختبار معاملة قاعدة البيانات');
    } else {
      console.log('✅ اختبار معاملة قاعدة البيانات نجح');
    }

  } catch (error) {
    diagnosis.issues.push(`❌ خطأ في اختبار آلية الحفظ: ${error.message}`);
    console.error('❌ خطأ في اختبار آلية الحفظ:', error);
  }
}

/**
 * تحليل سجلات الأخطاء
 */
async function analyzeLogs(diagnosis) {
  console.log('🔍 تحليل سجلات الأخطاء...');

  try {
    // هذا مكان لتحليل سجلات الأخطاء إذا كانت متوفرة
    // يمكن إضافة منطق لقراءة ملفات السجلات وتحليلها
    
    diagnosis.databaseState.logAnalysis = {
      analyzed: true,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    diagnosis.issues.push(`❌ خطأ في تحليل السجلات: ${error.message}`);
    console.error('❌ خطأ في تحليل السجلات:', error);
  }
}

/**
 * إنشاء التوصيات بناءً على التشخيص
 */
function generateRecommendations(diagnosis) {
  console.log('💡 إنشاء التوصيات...');

  if (diagnosis.issues.length === 0) {
    diagnosis.recommendations.push('✅ لم يتم العثور على مشاكل واضحة في نظام حفظ الأرباح');
    return;
  }

  // توصيات عامة
  diagnosis.recommendations.push('🔧 التوصيات لإصلاح مشاكل حفظ الأرباح:');

  // توصيات محددة بناءً على المشاكل المكتشفة
  diagnosis.issues.forEach(issue => {
    if (issue.includes('عدم تطابق الأرباح')) {
      diagnosis.recommendations.push('📊 إعادة حساب الأرباح من المعاملات وتحديث الخزينة');
    }
    
    if (issue.includes('فشل في تحديث')) {
      diagnosis.recommendations.push('🔧 فحص صلاحيات قاعدة البيانات وحالة الاتصال');
    }
    
    if (issue.includes('فشل في حفظ القيمة')) {
      diagnosis.recommendations.push('💾 فحص آلية commit المعاملات في قاعدة البيانات');
    }
    
    if (issue.includes('ربح سالب')) {
      diagnosis.recommendations.push('🧮 إصلاح حسابات الأرباح في معاملات البيع');
    }
  });

  // توصيات إضافية
  diagnosis.recommendations.push('🔄 تطبيق آلية تحقق مزدوجة لضمان حفظ الأرباح');
  diagnosis.recommendations.push('📝 إضافة سجلات مفصلة لتتبع عمليات تحديث الأرباح');
  diagnosis.recommendations.push('⚡ تحسين آلية التحديث الفوري للواجهة');
}

/**
 * طباعة تقرير التشخيص
 */
function printDiagnosisReport(diagnosis) {
  console.log('\n' + '='.repeat(60));
  console.log('📋 تقرير التشخيص الشامل لمشاكل حفظ الأرباح');
  console.log('='.repeat(60));
  
  console.log(`🕐 وقت التشخيص: ${diagnosis.timestamp}`);
  console.log(`🔍 عدد المشاكل المكتشفة: ${diagnosis.issues.length}`);
  
  if (diagnosis.issues.length > 0) {
    console.log('\n❌ المشاكل المكتشفة:');
    diagnosis.issues.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue}`);
    });
  }
  
  console.log('\n💡 التوصيات:');
  diagnosis.recommendations.forEach((rec, index) => {
    console.log(`${index + 1}. ${rec}`);
  });
  
  console.log('\n📊 حالة قاعدة البيانات:');
  console.log(JSON.stringify(diagnosis.databaseState, null, 2));
  
  console.log('\n🧪 نتائج اختبارات المعاملات:');
  console.log(JSON.stringify(diagnosis.transactionTests, null, 2));
  
  console.log('\n' + '='.repeat(60));
}

module.exports = {
  diagnoseProfitSavingIssues,
  printDiagnosisReport
};
