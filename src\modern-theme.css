/* ملف الثيم الحديث للتطبيق - Hgorp (اتش قروب) */

:root {
  /* الألوان الأساسية من شعار Hgorp */
  --primary-color: #1a3a5f; /* اللون الأزرق الداكن من الشعار */
  --primary-dark: #122a45; /* نسخة أغمق من اللون الأزرق */
  --primary-rgb: 26, 58, 95;
  --secondary-color: #2d5b8e; /* درجة أفتح من اللون الأزرق */
  --secondary-dark: #1a3a5f;
  --secondary-rgb: 45, 91, 142;
  --accent-color: #e67e22; /* اللون البرتقالي من الشعار */
  --accent-rgb: 230, 126, 34;
  --danger-color: #e74c3c;
  --danger-rgb: 231, 76, 60;
  --success-color: #27ae60;
  --success-rgb: 39, 174, 96;
  --warning-color: #f39c12;
  --warning-rgb: 243, 156, 18;
  --info-color: #3498db;
  --info-rgb: 52, 152, 219;

  /* رمز العملة */
  --currency-symbol: 'د ل';

  /* ألوان الخلفية والنص */
  --bg-color: #f7f9fc; /* خلفية فاتحة مائلة للأزرق */
  --card-bg: #ffffff;
  --text-color: #2c3e50; /* لون نص متوافق مع اللون الأزرق الداكن */
  --text-light: #5d7b8c; /* لون نص فاتح متوافق مع اللون الأزرق */
  --text-dark: #1e3243; /* نفس اللون الأزرق الداكن من الشعار */
  --border-color: #e6eaee; /* لون حدود فاتح متوافق مع الألوان */

  /* الظلال */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);

  /* الزوايا */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 20px;

  /* المسافات */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
}

/* إعادة تعيين الأنماط الأساسية */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--bg-color);
  color: var(--text-color);
  line-height: 1.6;
  direction: rtl;
}

/* تنسيق الشريط العلوي */
.navbar {
  background: linear-gradient(to left, #1e3243, #162330); /* تدرج من اللون الأزرق الداكن من الشعار */
  color: white;
  height: 60px;
  padding: 0 var(--spacing-sm);
  box-shadow: var(--shadow-md);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: space-between;
  overflow-x: visible;
  width: 100%;
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  letter-spacing: 0.2px;
}

.navbar-brand {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  margin-left: var(--spacing-sm);
}

.navbar-brand .logo {
  width: 36px;
  height: 36px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: var(--spacing-sm);
  box-shadow: var(--shadow-sm);
  color: var(--accent-color); /* تغيير لون الشعار إلى اللون البرتقالي */
  font-weight: bold;
  font-size: 0.9rem;
}

.navbar-brand h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  white-space: nowrap;
}

.navbar-menu {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  flex: 1;
  overflow-x: visible;
  max-width: 80%;
  margin: 0 auto;
}

.navbar-menu-item {
  padding: 0 var(--spacing-sm);
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  white-space: nowrap;
  min-width: auto;
  text-align: center;
}

.navbar-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.navbar-menu-item.active {
  background-color: rgba(255, 255, 255, 0.15);
  font-weight: bold;
}

.navbar-menu-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background-color: var(--accent-color); /* اللون البرتقالي من الشعار */
}

.navbar-menu-item .menu-icon {
  margin-left: var(--spacing-xs);
  font-size: 1rem;
}

.navbar-menu-item .menu-text {
  font-size: 0.9rem;
  font-weight: 500;
}

.navbar-user {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  margin-right: var(--spacing-sm);
}

.user-info {
  margin-left: var(--spacing-sm);
  font-size: 0.9rem;
  white-space: nowrap;
}

.navbar-menu-item.logout {
  color: #ff6b6b;
  padding: 0 var(--spacing-sm);
}

/* تنسيق المحتوى الرئيسي */
.main-content-full {
  margin-top: 60px;
  padding: var(--spacing-xl);
  min-height: calc(100vh - 60px);
}

/* تنسيق العنوان الرئيسي */
.page-header {
  margin-bottom: var(--spacing-xl);
}

.page-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: var(--spacing-xs);
}

.page-subtitle {
  font-size: 1rem;
  color: var(--text-light);
}

/* تنسيق البطاقات */
.card {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-lg);
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
  border: none;
}

.card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.card-header {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background-color: rgba(0, 0, 0, 0.01);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.card-body {
  padding: var(--spacing-lg);
}

/* تنسيق الإحصائيات */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
}

.stat-card {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
  overflow: hidden;
  height: 100%;
  transition: transform 0.3s, box-shadow 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

/* ألوان البطاقات الإحصائية المختلفة */
.stat-card:nth-child(1)::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(to right, var(--primary-color), var(--primary-dark)); /* تدرج من اللون الأزرق الداكن */
}

.stat-card:nth-child(2)::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(to right, var(--accent-color), #f39c12); /* تدرج من اللون البرتقالي */
}

.stat-card:nth-child(3)::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(to right, var(--secondary-color), var(--primary-color)); /* تدرج من اللون الأزرق */
}

.stat-card:nth-child(4)::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(to right, var(--accent-color), var(--warning-color)); /* تدرج من اللون البرتقالي */
}

.stat-card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-md);
  font-size: 1.5rem;
  margin-left: auto;
  margin-right: auto;
  transition: transform 0.3s;
}

.stat-card:hover .stat-card-icon {
  transform: scale(1.1);
}

.stat-card:nth-child(1) .stat-card-icon {
  background-color: rgba(30, 50, 67, 0.1); /* خلفية شفافة من اللون الأزرق الداكن */
  color: var(--primary-color);
}

.stat-card:nth-child(2) .stat-card-icon {
  background-color: rgba(230, 126, 34, 0.1); /* خلفية شفافة من اللون البرتقالي */
  color: var(--accent-color);
}

.stat-card:nth-child(3) .stat-card-icon {
  background-color: rgba(44, 67, 86, 0.1); /* خلفية شفافة من اللون الأزرق */
  color: var(--secondary-color);
}

.stat-card:nth-child(4) .stat-card-icon {
  background-color: rgba(230, 126, 34, 0.1); /* خلفية شفافة من اللون البرتقالي */
  color: var(--accent-color);
}

.stat-card-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: var(--spacing-xs);
}

.stat-card-title {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-top: var(--spacing-xs);
}

/* تنسيق حاويات الرسوم البيانية */
.chart-container {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-lg);
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
  transition: transform 0.3s, box-shadow 0.3s;
}

.chart-container:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
}

.chart-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-dark);
  display: flex;
  align-items: center;
}

.chart-title svg {
  margin-left: var(--spacing-xs);
  color: var(--primary-color);
}

/* تنسيق الجداول */
.table-container {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  margin-bottom: var(--spacing-lg);
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
  transition: transform 0.3s, box-shadow 0.3s;
}

.table-container:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th {
  background-color: rgba(0, 0, 0, 0.02);
  color: var(--text-dark);
  font-weight: 600;
  text-align: right;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.table td {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  vertical-align: middle;
}

.table tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.01);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* تنسيق الأزرار */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  outline: none;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.btn-primary {
  background-color: var(--primary-color); /* اللون الأزرق الداكن من الشعار */
  color: white;
  transition: all 0.3s;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--accent-color); /* اللون البرتقالي من الشعار */
  color: white;
  transition: all 0.3s;
}

.btn-secondary:hover {
  background-color: #d35400; /* نسخة أغمق من اللون البرتقالي */
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
}

.btn-warning {
  background-color: var(--warning-color);
  color: white;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1.1rem;
}

/* تنسيق النماذج */
.form-group {
  margin-bottom: var(--spacing-md);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 600;
  color: var(--text-dark);
}

.form-control {
  width: 100%;
  padding: 0.6rem 0.8rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
  background-color: white;
  color: #000000;
}

/* تحسين وضوح النص في حقول الإدخال في النوافذ المنبثقة */
.modal-container .form-control {
  color: #000000 !important;
  font-weight: 500 !important;
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
  outline: none;
}

.form-row {
  display: flex;
  margin: 0 -0.5rem;
}

.form-row > .form-group {
  flex: 1;
  padding: 0 0.5rem;
}

/* تنسيق حقول التاريخ */
.date-input-container {
  position: relative;
  display: inline-block;
}

input[type="date"] {
  direction: ltr;
  text-align: right;
  position: relative;
  width: 100%;
  padding-left: 30px; /* مساحة للأيقونة */
}

input[type="date"]::-webkit-calendar-picker-indicator {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
}

/* تنسيق مفاتيح التبديل (Toggle Switches) */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ecf0f1;
  transition: .4s;
  border-radius: 34px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #2ecc71;
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

/* تنسيق النوافذ المنبثقة */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1050;
  backdrop-filter: blur(3px);
}

/* تحسين وضوح النص الصغير في النوافذ المنبثقة */
.modal-container small,
.modal-container .form-text,
.modal-container .text-muted {
  color: #000000 !important;
  font-weight: 600 !important;
}

/* تحسين وضوح النص في حقول الإدخال */
.modal-container input::placeholder,
.modal-container textarea::placeholder {
  color: #666;
  opacity: 0.8;
}

/* تحسين وضوح النص في النوافذ المنبثقة */
.modal-container label,
.modal-container p,
.modal-container span,
.modal-container div {
  color: #000000 !important;
}

/* تحسين وضوح العناوين في النوافذ المنبثقة */
.modal-container .form-label {
  color: #000000 !important;
  font-weight: 700 !important;
  font-size: 1.1rem !important;
}

/* تحسين وضوح النص في النوافذ المنبثقة في صفحات المبيعات والمشتريات */
.modal-container .alert-info,
.modal-container .alert-info strong,
.modal-container .input-group-text,
.modal-container .form-control.bg-light,
.modal-container .bg-light.font-weight-bold {
  color: #000000 !important;
  font-weight: 600 !important;
}

/* تحسين وضوح النص في ملخص العملية */
.modal-container .card-title,
.modal-container .font-weight-bold {
  color: #000000 !important;
  font-weight: 700 !important;
}

/* تحسين وضوح النص في العناوين الفرعية */
.modal-container h4,
.modal-container h5,
.modal-container h6 {
  color: var(--text-dark);
  font-weight: 600;
}

/* تنسيق شريط التمرير (slider) */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 8px;
  background: #ddd;
  border-radius: 4px;
  outline: none;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  background: var(--primary-color);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s;
}

input[type="range"]::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: var(--primary-color);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

input[type="range"]::-webkit-slider-thumb:hover {
  background: var(--primary-dark);
  transform: scale(1.1);
}

input[type="range"]::-moz-range-thumb:hover {
  background: var(--primary-dark);
  transform: scale(1.1);
}

.modal-container {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  width: 90%;
  max-width: 1000px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  animation: modal-in 0.3s ease-out;
  color: var(--text-dark);
}

@keyframes modal-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  background-color: rgba(0, 0, 0, 0.02);
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-dark);
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-light);
  transition: color 0.2s;
}

.close-btn:hover {
  color: var(--danger-color);
}

.modal-body {
  padding: var(--spacing-lg);
  overflow-y: auto;
  flex: 1;
  color: var(--text-dark);
}

.modal-footer {
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  background-color: rgba(0, 0, 0, 0.01);
}

/* تنسيق الشارات */
.badge {
  display: inline-block;
  padding: 0.25em 0.6em;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: 50px;
  text-align: center;
}

.badge-primary {
  background-color: var(--primary-color);
  color: white;
}

.badge-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.badge-success {
  background-color: var(--success-color);
  color: white;
}

.badge-danger {
  background-color: var(--danger-color);
  color: white;
}

.badge-warning {
  background-color: var(--warning-color);
  color: #333;
}

.badge-info {
  background-color: var(--info-color);
  color: white;
}

/* تنسيق صفحة الإعدادات */
.settings-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
  padding: 0 var(--spacing-md);
}

/* تنسيقات للشاشات المتوسطة */
@media (max-width: 1200px) {
  .settings-container {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* تنسيقات للشاشات الصغيرة */
@media (max-width: 768px) {
  .settings-container {
    grid-template-columns: 1fr;
    padding: 0 var(--spacing-sm);
  }
}

/* تنسيق ترويسة الصفحات */
.dashboard-welcome {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
}

.dashboard-welcome::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 3px;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
  border-radius: 3px;
}

.dashboard-welcome h1 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: var(--spacing-xs);
  color: var(--text-dark);
  position: relative;
  display: inline-block;
}

.dashboard-welcome p {
  color: var(--text-light);
  font-size: 1.1rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.settings-card {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-lg);
  transition: transform 0.3s, box-shadow 0.3s;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.settings-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

/* ألوان البطاقات المختلفة */
.settings-card:nth-child(1)::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to left, var(--primary-color), var(--primary-dark)); /* تدرج من اللون الأزرق الداكن */
  opacity: 0.8;
}

.settings-card:nth-child(2)::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to left, var(--accent-color), #f39c12); /* تدرج من اللون البرتقالي */
  opacity: 0.8;
}

.settings-card:nth-child(3)::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to left, var(--secondary-color), var(--primary-color)); /* تدرج من اللون الأزرق */
  opacity: 0.8;
}

.settings-card:nth-child(4)::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to left, var(--accent-color), var(--warning-color)); /* تدرج من اللون البرتقالي */
  opacity: 0.8;
}

.settings-card h3 {
  font-size: 1.2rem;
  margin-bottom: var(--spacing-md);
  color: var(--text-dark);
  display: flex;
  align-items: center;
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
  font-weight: 600;
}

.settings-card h3 svg {
  margin-left: var(--spacing-xs);
  font-size: 1.5em;
  background-color: rgba(0, 0, 0, 0.05);
  padding: 8px;
  border-radius: 50%;
  margin-left: 10px;
  transition: transform 0.3s;
}

.settings-card:hover h3 svg {
  transform: scale(1.1);
}

/* ألوان أيقونات البطاقات */
.settings-card:nth-child(1) h3 svg {
  color: var(--primary-color); /* اللون الأزرق الداكن من الشعار */
  background-color: rgba(30, 50, 67, 0.1); /* خلفية شفافة من اللون الأزرق الداكن */
}

.settings-card:nth-child(2) h3 svg {
  color: var(--accent-color); /* اللون البرتقالي من الشعار */
  background-color: rgba(230, 126, 34, 0.1); /* خلفية شفافة من اللون البرتقالي */
}

.settings-card:nth-child(3) h3 svg {
  color: var(--secondary-color); /* درجة أفتح من اللون الأزرق */
  background-color: rgba(44, 67, 86, 0.1); /* خلفية شفافة من اللون الأزرق */
}

.settings-card:nth-child(4) h3 svg {
  color: var(--accent-color); /* اللون البرتقالي من الشعار */
  background-color: rgba(230, 126, 34, 0.1); /* خلفية شفافة من اللون البرتقالي */
}

.settings-card .form-group:last-child {
  margin-bottom: 0;
}

/* تنسيق بطاقة النسخ الاحتياطي */
.backup-card {
  grid-column: span 3;
}

@media (max-width: 1200px) {
  .backup-card {
    grid-column: span 2;
  }
}

@media (max-width: 768px) {
  .backup-card {
    grid-column: span 1;
  }
}

.backup-options {
  display: flex;
  flex-direction: row;
  gap: 24px;
  margin-top: 16px;
}

@media (max-width: 768px) {
  .backup-options {
    flex-direction: column;
  }
}

.backup-option {
  flex: 1;
  padding: 24px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: transform 0.3s, box-shadow 0.3s;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.backup-option:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.backup-option-export {
  background-color: rgba(52, 152, 219, 0.05);
  border-color: rgba(52, 152, 219, 0.2);
}

.backup-option-import {
  background-color: rgba(243, 156, 18, 0.05);
  border-color: rgba(243, 156, 18, 0.2);
}

.settings-actions {
  margin-top: var(--spacing-lg);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
}

/* تنسيق زر حفظ الإعدادات */
.settings-save-container {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-xl);
  padding: var(--spacing-md);
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
}

.settings-save-button {
  background: linear-gradient(to left, var(--accent-color), var(--primary-color));
  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  padding: 12px 36px;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s;
  box-shadow: var(--shadow-md);
}

.settings-save-button:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
  background: linear-gradient(to left, var(--primary-color), var(--accent-color));
}

.settings-save-button:active {
  transform: translateY(-1px);
}

/* تنسيق لوحة التحكم */
.dashboard-welcome {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: linear-gradient(to left, #1e3243, #162330); /* تدرج من اللون الأزرق الداكن من الشعار */
  border-radius: var(--border-radius-lg);
  color: white;
  box-shadow: var(--shadow-md);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.dashboard-welcome::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(to right, var(--accent-color), #f39c12); /* تدرج من اللون البرتقالي */
}

.dashboard-welcome h1 {
  font-size: 2rem;
  margin-bottom: var(--spacing-xs);
  font-weight: 700;
}

.dashboard-welcome p {
  opacity: 0.9;
  font-size: 1.1rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* تنسيق الرسوم البيانية */
.chart-container {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.chart-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-dark);
}

/* تنسيق التنبيهات */
.alert {
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius-md);
  margin-bottom: var(--spacing-lg);
  border-right: 4px solid transparent;
}

.alert-success {
  background-color: rgba(46, 204, 113, 0.1);
  border-right-color: var(--success-color);
  color: #27ae60;
}

.alert-danger {
  background-color: rgba(231, 76, 60, 0.1);
  border-right-color: var(--danger-color);
  color: #c0392b;
}

.alert-warning {
  background-color: rgba(255, 204, 0, 0.1);
  border-right-color: var(--warning-color);
  color: #996600;
}

.alert-info {
  background-color: rgba(52, 152, 219, 0.1);
  border-right-color: var(--info-color);
  color: #2980b9;
}

/* تنسيق الشاشات الصغيرة */
/* تعديلات إضافية لشريط التنقل */
.menu-text {
  font-size: 0.9rem;
}

@media (min-width: 1200px) {
  .navbar {
    padding: 0 var(--spacing-lg);
  }

  .navbar-menu-item {
    padding: 0 var(--spacing-md);
  }
}

@media (max-width: 1199px) and (min-width: 769px) {
  .navbar {
    padding: 0 var(--spacing-sm);
  }

  .navbar-menu-item {
    padding: 0 var(--spacing-xs);
  }

  .menu-text {
    font-size: 0.85rem;
  }
}

@media (max-width: 768px) {
  .navbar {
    padding: 0 var(--spacing-md);
    overflow-x: auto;
  }

  .navbar-brand h3 {
    display: none;
  }

  .navbar-menu-item {
    padding: 0 var(--spacing-xs);
  }

  .navbar-menu-item .menu-text {
    display: none;
  }

  .main-content-full {
    padding: var(--spacing-md);
  }

  .form-row {
    flex-direction: column;
  }

  .form-row > .form-group {
    padding: 0;
  }

  .modal-container {
    flex-direction: column;
    width: 95%;
  }

  .settings-container {
    grid-template-columns: 1fr;
  }
}

/* تنسيق صفحة تسجيل الدخول */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3498db, #2c3e50);
  padding: var(--spacing-lg);
}

.login-card {
  background-color: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  width: 100%;
  max-width: 400px;
  overflow: hidden;
  animation: fade-in 0.5s ease-out;
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.login-header {
  padding: var(--spacing-xl) var(--spacing-lg);
  text-align: center;
  background-color: rgba(0, 0, 0, 0.02);
  border-bottom: 1px solid var(--border-color);
}

.login-logo {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #3498db, #2c3e50);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-md);
  color: white;
  font-weight: bold;
  font-size: 1.5rem;
  box-shadow: var(--shadow-md);
}

.login-title {
  font-size: 1.5rem;
  color: var(--text-dark);
  margin-bottom: var(--spacing-xs);
}

.login-subtitle {
  color: var(--text-light);
  font-size: 0.9rem;
}

.login-body {
  padding: var(--spacing-lg);
}

.login-body .form-control {
  transition: all 0.3s;
  border: 1px solid var(--border-color);
}

.login-body .form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.login-body .form-label {
  font-weight: 500;
  margin-bottom: 6px;
  color: var(--text-dark);
}

.login-body .btn-primary {
  background-color: var(--primary-color);
  border: none;
  transition: all 0.3s;
}

.login-body .btn-primary:hover:not(:disabled) {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.login-body .btn-primary:disabled {
  background-color: var(--primary-color);
  opacity: 0.7;
}

.login-body .alert-danger {
  background-color: rgba(231, 76, 60, 0.1);
  border-right: 4px solid var(--danger-color);
  color: #c0392b;
  padding: 12px 16px;
  margin-bottom: 20px;
  border-radius: 4px;
  font-size: 0.9rem;
}

.login-footer {
  padding: var(--spacing-md) var(--spacing-lg);
  text-align: center;
  background-color: rgba(0, 0, 0, 0.02);
  border-top: 1px solid var(--border-color);
}

.login-footer p {
  margin: 5px 0;
  color: var(--text-light);
  font-size: 0.9rem;
}

.login-footer strong {
  color: var(--primary-color);
  font-weight: 600;
}

/* تأثيرات إضافية */
.hover-lift {
  transition: transform 0.2s, box-shadow 0.2s;
}

.hover-lift:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--secondary-color); }
.text-success { color: var(--success-color); }
.text-danger { color: var(--danger-color); }
.text-warning { color: var(--warning-color); }
.text-info { color: var(--info-color); }
.text-light { color: var(--text-light); }
.text-dark { color: var(--text-dark); }

.bg-primary { background-color: var(--primary-color); }
.bg-secondary { background-color: var(--secondary-color); }
.bg-success { background-color: var(--success-color); }
.bg-danger { background-color: var(--danger-color); }
.bg-warning { background-color: rgba(255, 204, 0, 0.15); }
.bg-info { background-color: var(--info-color); }
.bg-light { background-color: var(--bg-color); }
.bg-dark { background-color: var(--text-dark); }

/* تنسيق معرف المعاملة */
.transaction-id {
  font-family: monospace;
  font-size: 0.9rem;
  font-weight: bold;
  padding: 0.25rem 0.5rem;
  background-color: rgba(52, 152, 219, 0.1);
  border-radius: 4px;
  color: var(--primary-color);
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}
