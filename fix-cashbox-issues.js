/**
 * أداة إصلاح مشاكل الخزينة
 * تقوم بإعادة حساب جميع قيم الخزينة من المعاملات الفعلية
 */

const { logError, logSystem } = require('./error-handler');
const DatabaseManager = require('./database-singleton');
const path = require('path');

/**
 * إصلاح جميع مشاكل الخزينة
 */
async function fixCashboxIssues() {
  console.log('🔧 بدء إصلاح مشاكل الخزينة...');
  
  try {
    // 1. تهيئة قاعدة البيانات
    console.log('\n1️⃣ تهيئة قاعدة البيانات...');
    const dbManager = DatabaseManager.getInstance();
    const dbPath = 'C:\\Users\\<USER>\\AppData\\Roaming\\warehouse-management-system\\wms-database\\warehouse.db';
    await dbManager.initialize(dbPath);
    const db = dbManager.getConnection();
    
    if (!db) {
      throw new Error('فشل في الاتصال بقاعدة البيانات');
    }
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // 2. قراءة البيانات الحالية
    console.log('\n2️⃣ قراءة البيانات الحالية...');
    const currentCashbox = db.prepare('SELECT * FROM cashbox LIMIT 1').get();
    
    if (!currentCashbox) {
      throw new Error('لا توجد خزينة في قاعدة البيانات');
    }
    
    console.log('📊 البيانات الحالية:');
    console.log(`   - الرصيد الافتتاحي: ${currentCashbox.initial_balance}`);
    console.log(`   - الرصيد الحالي: ${currentCashbox.current_balance}`);
    console.log(`   - إجمالي المبيعات: ${currentCashbox.sales_total}`);
    console.log(`   - إجمالي المشتريات: ${currentCashbox.purchases_total}`);
    console.log(`   - إجمالي المرتجعات: ${currentCashbox.returns_total || 0}`);
    console.log(`   - إجمالي مصاريف النقل: ${currentCashbox.transport_total || 0}`);
    console.log(`   - إجمالي الأرباح: ${currentCashbox.profit_total}`);

    // 3. إعادة حساب القيم من المعاملات
    console.log('\n3️⃣ إعادة حساب القيم من المعاملات...');
    
    // حساب إجمالي المبيعات
    const salesResult = db.prepare(`
      SELECT COALESCE(SUM(total_price), 0) as total_sales
      FROM transactions 
      WHERE transaction_type = 'sale'
    `).get();
    const calculatedSales = salesResult.total_sales;
    
    // حساب إجمالي المشتريات
    const purchasesResult = db.prepare(`
      SELECT COALESCE(SUM(total_price), 0) as total_purchases
      FROM transactions 
      WHERE transaction_type = 'purchase'
    `).get();
    const calculatedPurchases = purchasesResult.total_purchases;
    
    // حساب إجمالي المرتجعات
    const returnsResult = db.prepare(`
      SELECT COALESCE(SUM(total_price), 0) as total_returns
      FROM transactions 
      WHERE transaction_type = 'return'
    `).get();
    const calculatedReturns = returnsResult.total_returns;
    
    // حساب إجمالي مصاريف النقل
    const transportResult = db.prepare(`
      SELECT COALESCE(SUM(transport_cost), 0) as total_transport
      FROM transactions 
      WHERE transport_cost > 0
    `).get();
    const calculatedTransport = transportResult.total_transport;
    
    // حساب إجمالي الأرباح الصحيح
    const profitResult = db.prepare(`
      SELECT COALESCE(SUM(profit), 0) as total_profit
      FROM transactions 
      WHERE transaction_type = 'sale'
    `).get();
    let calculatedProfit = profitResult.total_profit;
    
    // خصم أرباح المرتجعات
    const returnProfitResult = db.prepare(`
      SELECT COALESCE(SUM(ABS(profit)), 0) as return_profit
      FROM transactions 
      WHERE transaction_type = 'return'
    `).get();
    calculatedProfit -= returnProfitResult.return_profit;
    
    // حساب الرصيد الحالي الصحيح
    const calculatedCurrentBalance = currentCashbox.initial_balance + calculatedSales - calculatedPurchases - calculatedReturns - calculatedTransport;
    
    console.log('📊 القيم المحسوبة من المعاملات:');
    console.log(`   - إجمالي المبيعات: ${calculatedSales}`);
    console.log(`   - إجمالي المشتريات: ${calculatedPurchases}`);
    console.log(`   - إجمالي المرتجعات: ${calculatedReturns}`);
    console.log(`   - إجمالي مصاريف النقل: ${calculatedTransport}`);
    console.log(`   - إجمالي الأرباح: ${calculatedProfit}`);
    console.log(`   - الرصيد الحالي المحسوب: ${calculatedCurrentBalance}`);

    // 4. مقارنة القيم
    console.log('\n4️⃣ مقارنة القيم...');
    const differences = {
      sales: Math.abs(currentCashbox.sales_total - calculatedSales),
      purchases: Math.abs(currentCashbox.purchases_total - calculatedPurchases),
      returns: Math.abs((currentCashbox.returns_total || 0) - calculatedReturns),
      transport: Math.abs((currentCashbox.transport_total || 0) - calculatedTransport),
      profit: Math.abs(currentCashbox.profit_total - calculatedProfit),
      current_balance: Math.abs(currentCashbox.current_balance - calculatedCurrentBalance)
    };
    
    console.log('📊 الفروقات:');
    Object.entries(differences).forEach(([key, diff]) => {
      if (diff > 0.01) {
        console.log(`   ❌ ${key}: فرق ${diff}`);
      } else {
        console.log(`   ✅ ${key}: متطابق`);
      }
    });

    // 5. تطبيق الإصلاحات
    console.log('\n5️⃣ تطبيق الإصلاحات...');
    
    const hasSignificantDifferences = Object.values(differences).some(diff => diff > 0.01);
    
    if (hasSignificantDifferences) {
      console.log('🔧 تطبيق الإصلاحات...');
      
      const updateStmt = db.prepare(`
        UPDATE cashbox
        SET current_balance = ?,
            sales_total = ?,
            purchases_total = ?,
            returns_total = ?,
            transport_total = ?,
            profit_total = ?,
            updated_at = ?
        WHERE id = ?
      `);
      
      const updateResult = updateStmt.run(
        calculatedCurrentBalance,
        calculatedSales,
        calculatedPurchases,
        calculatedReturns,
        calculatedTransport,
        calculatedProfit,
        new Date().toISOString(),
        currentCashbox.id
      );
      
      if (updateResult.changes > 0) {
        console.log('✅ تم تطبيق الإصلاحات بنجاح');
        
        // التحقق من النتائج
        const updatedCashbox = db.prepare('SELECT * FROM cashbox WHERE id = ?').get(currentCashbox.id);
        console.log('\n📊 البيانات بعد الإصلاح:');
        console.log(`   - الرصيد الحالي: ${updatedCashbox.current_balance}`);
        console.log(`   - إجمالي المبيعات: ${updatedCashbox.sales_total}`);
        console.log(`   - إجمالي المشتريات: ${updatedCashbox.purchases_total}`);
        console.log(`   - إجمالي المرتجعات: ${updatedCashbox.returns_total}`);
        console.log(`   - إجمالي مصاريف النقل: ${updatedCashbox.transport_total}`);
        console.log(`   - إجمالي الأرباح: ${updatedCashbox.profit_total}`);
      } else {
        console.log('❌ فشل في تطبيق الإصلاحات');
      }
    } else {
      console.log('✅ لا توجد حاجة لإصلاحات - جميع القيم صحيحة');
    }

    // 6. تنظيف معاملات الخزينة المكررة
    console.log('\n6️⃣ تنظيف معاملات الخزينة المكررة...');
    
    const duplicateTransactions = db.prepare(`
      SELECT COUNT(*) as count
      FROM cashbox_transactions
      WHERE notes LIKE '%اختبار%' OR notes LIKE '%test%'
    `).get();
    
    if (duplicateTransactions.count > 0) {
      console.log(`🗑️ حذف ${duplicateTransactions.count} معاملة اختبار...`);
      
      const deleteResult = db.prepare(`
        DELETE FROM cashbox_transactions
        WHERE notes LIKE '%اختبار%' OR notes LIKE '%test%'
      `).run();
      
      console.log(`✅ تم حذف ${deleteResult.changes} معاملة اختبار`);
    } else {
      console.log('✅ لا توجد معاملات اختبار للحذف');
    }

    console.log('\n🎉 تم إكمال إصلاح مشاكل الخزينة بنجاح!');
    
    return {
      success: true,
      message: 'تم إصلاح جميع مشاكل الخزينة بنجاح',
      differences,
      applied_fixes: hasSignificantDifferences
    };

  } catch (error) {
    console.error('❌ خطأ في إصلاح مشاكل الخزينة:', error);
    logError(error, 'fixCashboxIssues');
    
    return {
      success: false,
      message: `خطأ في إصلاح مشاكل الخزينة: ${error.message}`
    };
  }
}

// تشغيل الإصلاح إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  console.log('🚀 بدء إصلاح مشاكل الخزينة...');
  
  fixCashboxIssues().then(result => {
    console.log('\n📋 نتيجة الإصلاح:', result);
    console.log('\n🏁 انتهى الإصلاح');
  }).catch(error => {
    console.error('❌ خطأ في تشغيل الإصلاح:', error);
  });
}

module.exports = {
  fixCashboxIssues
};
