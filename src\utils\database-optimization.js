/**
 * وحدة تحسين أداء قاعدة البيانات
 * تحتوي على وظائف لتحسين أداء قاعدة البيانات وتسريع الاستعلامات
 */

// نظام التخزين المؤقت المركزي
const cacheSystem = {
  caches: {},
  autoCleanupInterval: null,

  // إنشاء مفتاح فريد للتخزين المؤقت
  createCacheKey: (functionName, args) => {
    try {
      return `${functionName}:${JSON.stringify(args)}`;
    } catch (e) {
      // في حالة وجود كائنات غير قابلة للتحويل إلى JSON
      return `${functionName}:${Date.now()}`;
    }
  },

  // الحصول على بيانات من التخزين المؤقت
  get: (key) => {
    const cache = cacheSystem.caches[key];
    if (!cache) return null;

    const now = Date.now();
    if (now - cache.timestamp < cache.expiry) {
      return cache.data;
    }

    // حذف البيانات منتهية الصلاحية
    delete cacheSystem.caches[key];
    return null;
  },

  // تخزين بيانات في التخزين المؤقت
  set: (key, data, expiry) => {
    cacheSystem.caches[key] = {
      data,
      timestamp: Date.now(),
      expiry
    };

    // بدء التنظيف التلقائي إذا لم يكن قد بدأ بالفعل
    cacheSystem.startAutoCleanup();
  },

  // مسح التخزين المؤقت
  clear: (pattern = null) => {
    if (pattern) {
      // مسح التخزين المؤقت الذي يطابق النمط
      Object.keys(cacheSystem.caches).forEach(key => {
        if (key.includes(pattern)) {
          delete cacheSystem.caches[key];
        }
      });
    } else {
      // مسح كل التخزين المؤقت
      cacheSystem.caches = {};
    }
  },

  // الحصول على إحصائيات التخزين المؤقت
  getStats: () => {
    const keys = Object.keys(cacheSystem.caches);
    return {
      totalCaches: keys.length,
      cacheKeys: keys,
      totalSize: JSON.stringify(cacheSystem.caches).length
    };
  },

  // تنظيف البيانات منتهية الصلاحية تلقائيًا
  cleanupExpiredCache: () => {
    const now = Date.now();
    let cleanedCount = 0;

    Object.keys(cacheSystem.caches).forEach(key => {
      const cache = cacheSystem.caches[key];
      if (now - cache.timestamp >= cache.expiry) {
        delete cacheSystem.caches[key];
        cleanedCount++;
      }
    });

    if (cleanedCount > 0) {
      console.log(`[Cache] تم تنظيف ${cleanedCount} عنصر منتهي الصلاحية من التخزين المؤقت`);
    }

    return cleanedCount;
  },

  // بدء التنظيف التلقائي
  startAutoCleanup: () => {
    if (cacheSystem.autoCleanupInterval) return;

    // تنظيف البيانات منتهية الصلاحية كل 5 دقائق
    cacheSystem.autoCleanupInterval = setInterval(() => {
      cacheSystem.cleanupExpiredCache();
    }, 5 * 60 * 1000);

    console.log('[Cache] تم بدء التنظيف التلقائي للتخزين المؤقت');
  },

  // إيقاف التنظيف التلقائي
  stopAutoCleanup: () => {
    if (cacheSystem.autoCleanupInterval) {
      clearInterval(cacheSystem.autoCleanupInterval);
      cacheSystem.autoCleanupInterval = null;
      console.log('[Cache] تم إيقاف التنظيف التلقائي للتخزين المؤقت');
    }
  }
};

/**
 * تحسين استعلامات قاعدة البيانات باستخدام التخزين المؤقت
 * @param {Function} queryFunction - وظيفة الاستعلام الأصلية
 * @param {Object} options - خيارات التخزين المؤقت
 * @param {Number} options.cacheTime - وقت صلاحية التخزين المؤقت بالمللي ثانية (الافتراضي: 5 دقائق)
 * @param {String} options.functionName - اسم الوظيفة للتمييز في التخزين المؤقت
 * @param {Boolean} options.useArguments - استخدام المعلمات في مفتاح التخزين المؤقت
 * @param {Boolean} options.autoRefresh - تحديث البيانات تلقائيًا بعد انتهاء صلاحيتها
 * @param {Number} options.refreshThreshold - نسبة الوقت المتبقي قبل انتهاء الصلاحية للبدء في التحديث التلقائي (0-1)
 * @param {Boolean} options.backgroundRefresh - تحديث البيانات في الخلفية مع إرجاع البيانات المخزنة مؤقتًا
 * @returns {Function} - وظيفة استعلام محسنة مع تخزين مؤقت
 */
export const createCachedQuery = (queryFunction, options = {}) => {
  const {
    cacheTime = 5 * 60 * 1000,
    functionName = queryFunction.name || 'query',
    useArguments = true,
    autoRefresh = true,
    refreshThreshold = 0.75, // تحديث البيانات عندما يتبقى 25% من وقت الصلاحية
    backgroundRefresh = true
  } = options;

  // تخزين مؤقت للاستعلامات قيد التنفيذ
  const pendingQueries = {};
  // تخزين مؤقت لأوقات آخر تحديث
  const lastRefreshTimes = {};

  // دالة لتنفيذ الاستعلام وتخزين النتيجة
  const executeQuery = async (cacheKey, args) => {
    try {
      const startTime = Date.now();
      const result = await queryFunction(...args);
      const endTime = Date.now();

      console.log(`[Cache Store] تخزين نتيجة الاستعلام لـ ${cacheKey} (استغرق ${endTime - startTime}ms)`);

      // تخزين النتيجة في التخزين المؤقت
      cacheSystem.set(cacheKey, result, cacheTime);
      // تحديث وقت آخر تحديث
      lastRefreshTimes[cacheKey] = Date.now();

      return result;
    } catch (error) {
      console.error(`[Cache Error] خطأ في تنفيذ الاستعلام لـ ${cacheKey}:`, error);
      throw error;
    }
  };

  // دالة للتحديث في الخلفية
  const refreshInBackground = async (cacheKey, args) => {
    // تجنب التحديث المتكرر
    const now = Date.now();
    const lastRefresh = lastRefreshTimes[cacheKey] || 0;

    // تحديث البيانات فقط إذا مر وقت كافٍ منذ آخر تحديث (على الأقل 10% من وقت الصلاحية)
    if (now - lastRefresh < cacheTime * 0.1) {
      return;
    }

    try {
      console.log(`[Cache Background] تحديث البيانات في الخلفية لـ ${cacheKey}`);
      await executeQuery(cacheKey, args);
    } catch (error) {
      console.error(`[Cache Background] خطأ في تحديث البيانات في الخلفية لـ ${cacheKey}:`, error);
    }
  };

  return async (...args) => {
    // إنشاء مفتاح التخزين المؤقت
    const cacheKey = useArguments
      ? cacheSystem.createCacheKey(functionName, args)
      : functionName;

    // محاولة الحصول على البيانات من التخزين المؤقت
    const cachedData = cacheSystem.get(cacheKey);

    if (cachedData) {
      // التحقق مما إذا كان يجب تحديث البيانات في الخلفية
      if (autoRefresh) {
        const cache = cacheSystem.caches[cacheKey];
        if (cache) {
          const now = Date.now();
          const elapsed = now - cache.timestamp;
          const remaining = cache.expiry - elapsed;

          // إذا كان الوقت المتبقي أقل من الحد المحدد، قم بالتحديث في الخلفية
          if (remaining < cache.expiry * (1 - refreshThreshold)) {
            if (backgroundRefresh) {
              // تحديث البيانات في الخلفية مع إرجاع البيانات المخزنة مؤقتًا
              setTimeout(() => refreshInBackground(cacheKey, args), 0);
              console.log(`[Cache Auto] جدولة تحديث تلقائي في الخلفية لـ ${cacheKey}`);
            } else {
              // تجاهل البيانات المخزنة مؤقتًا وتنفيذ استعلام جديد
              console.log(`[Cache Auto] تحديث تلقائي لـ ${cacheKey} (متبقي ${Math.round(remaining / 1000)}s)`);
              return executeQuery(cacheKey, args);
            }
          }
        }
      }

      console.log(`[Cache Hit] استخدام البيانات المخزنة مؤقتًا لـ ${cacheKey}`);
      return cachedData;
    }

    // إذا كان هناك استعلام قيد التنفيذ بنفس المفتاح، انتظر نتيجته
    if (pendingQueries[cacheKey]) {
      console.log(`[Cache Pending] انتظار استعلام قيد التنفيذ لـ ${cacheKey}`);
      return pendingQueries[cacheKey];
    }

    // تنفيذ استعلام جديد
    console.log(`[Cache Miss] تنفيذ استعلام جديد لـ ${cacheKey}`);

    // إنشاء وعد جديد وتخزينه في قائمة الاستعلامات قيد التنفيذ
    const queryPromise = executeQuery(cacheKey, args).finally(() => {
      // إزالة الاستعلام من قائمة الاستعلامات قيد التنفيذ
      delete pendingQueries[cacheKey];
    });

    // تخزين الوعد في قائمة الاستعلامات قيد التنفيذ
    pendingQueries[cacheKey] = queryPromise;

    return queryPromise;
  };
};

/**
 * تحسين استعلامات قاعدة البيانات باستخدام التحميل التدريجي
 * @param {Function} queryFunction - وظيفة الاستعلام الأصلية
 * @param {Number} pageSize - حجم الصفحة (عدد العناصر في كل صفحة)
 * @returns {Function} - وظيفة استعلام محسنة مع تحميل تدريجي
 */
export const createPaginatedQuery = (queryFunction, pageSize = 20) => {
  return async (page = 1, ...args) => {
    try {
      console.log(`تنفيذ استعلام للصفحة ${page} بحجم ${pageSize}`);

      // إضافة معلمات التحميل التدريجي إلى الاستعلام
      const paginationParams = {
        skip: (page - 1) * pageSize,
        limit: pageSize
      };

      // دمج معلمات التحميل التدريجي مع المعلمات الأصلية
      const params = { ...args[0], ...paginationParams };

      // تنفيذ الاستعلام مع معلمات التحميل التدريجي
      const result = await queryFunction(params);

      return result;
    } catch (error) {
      console.error('خطأ في تنفيذ الاستعلام التدريجي:', error);
      throw error;
    }
  };
};

/**
 * تحسين أداء قاعدة البيانات باستخدام الفهارس
 * @param {Object} db - كائن قاعدة البيانات
 */
export const optimizeDatabaseIndexes = (db) => {
  try {
    console.log('تحسين فهارس قاعدة البيانات');

    // إنشاء فهارس للحقول المستخدمة بكثرة في الاستعلامات
    if (db.items) {
      db.items.ensureIndex({ fieldName: 'name' });
      db.items.ensureIndex({ fieldName: 'category' });
    }

    if (db.transactions) {
      db.transactions.ensureIndex({ fieldName: 'item_id' });
      db.transactions.ensureIndex({ fieldName: 'transaction_type' });
      db.transactions.ensureIndex({ fieldName: 'transaction_date' });
      db.transactions.ensureIndex({ fieldName: 'customer' });
    }

    if (db.customers) {
      db.customers.ensureIndex({ fieldName: 'name' });
      db.customers.ensureIndex({ fieldName: 'customer_type' });
      db.customers.ensureIndex({ fieldName: 'parent_id' });
    }

    console.log('تم تحسين فهارس قاعدة البيانات بنجاح');
  } catch (error) {
    console.error('خطأ في تحسين فهارس قاعدة البيانات:', error);
  }
};

/**
 * تحسين أداء قاعدة البيانات باستخدام التنظيف الدوري
 * @param {Object} db - كائن قاعدة البيانات
 * @param {Number} interval - الفاصل الزمني بين عمليات التنظيف بالمللي ثانية (الافتراضي: ساعة واحدة)
 */
export const setupDatabaseCompaction = (db, interval = 60 * 60 * 1000) => {
  try {
    console.log(`إعداد التنظيف الدوري لقاعدة البيانات كل ${interval / (60 * 1000)} دقيقة`);

    // تنظيف قاعدة البيانات دوريًا
    const compactDatabase = () => {
      if (db.items) db.items.persistence.compactDatafile();
      if (db.transactions) db.transactions.persistence.compactDatafile();
      if (db.customers) db.customers.persistence.compactDatafile();
      if (db.users) db.users.persistence.compactDatafile();
      if (db.machines) db.machines.persistence.compactDatafile();

      console.log('تم تنظيف قاعدة البيانات بنجاح');
    };

    // تنفيذ التنظيف الأولي
    compactDatabase();

    // إعداد التنظيف الدوري
    const compactionInterval = setInterval(compactDatabase, interval);

    // إرجاع وظيفة لإيقاف التنظيف الدوري
    return () => {
      clearInterval(compactionInterval);
      console.log('تم إيقاف التنظيف الدوري لقاعدة البيانات');
    };
  } catch (error) {
    console.error('خطأ في إعداد التنظيف الدوري لقاعدة البيانات:', error);
  }
};

/**
 * تحسين أداء قاعدة البيانات باستخدام التحميل الكسول
 * @param {Function} loadFunction - وظيفة تحميل البيانات
 * @param {Object} options - خيارات التحميل الكسول
 * @param {Boolean} options.autoRefresh - تحديث البيانات تلقائيًا بعد فترة زمنية
 * @param {Number} options.refreshInterval - الفترة الزمنية للتحديث التلقائي بالمللي ثانية
 * @returns {Object} - كائن يحتوي على وظائف التحميل والتحديث
 */
export const createLazyLoader = (loadFunction, options = {}) => {
  const {
    autoRefresh = false,
    refreshInterval = 5 * 60 * 1000 // 5 دقائق افتراضيًا
  } = options;

  let isLoaded = false;
  let isLoading = false;
  let data = null;
  let callbacks = [];
  let lastLoadTime = 0;
  let refreshTimer = null;

  // وظيفة التحميل الأساسية
  const load = async (force = false) => {
    // إذا كانت البيانات محملة بالفعل ولم يتم طلب التحميل القسري
    if (isLoaded && !force) {
      return data;
    }

    // إذا كان التحميل قيد التنفيذ
    if (isLoading) {
      return new Promise((resolve) => {
        callbacks.push(resolve);
      });
    }

    // بدء التحميل
    isLoading = true;

    try {
      const startTime = Date.now();
      data = await loadFunction();
      const endTime = Date.now();

      console.log(`[Lazy Load] تم تحميل البيانات بنجاح (استغرق ${endTime - startTime}ms)`);

      isLoaded = true;
      lastLoadTime = Date.now();

      // استدعاء جميع الدوال المنتظرة
      callbacks.forEach(callback => callback(data));
      callbacks = [];

      return data;
    } catch (error) {
      console.error('[Lazy Load] خطأ في التحميل الكسول:', error);

      // إعادة تعيين حالة التحميل في حالة الخطأ
      callbacks.forEach(callback => callback(Promise.reject(error)));
      callbacks = [];

      throw error;
    } finally {
      isLoading = false;
    }
  };

  // وظيفة تحديث البيانات
  const refresh = async () => {
    console.log('[Lazy Load] تحديث البيانات...');
    return load(true);
  };

  // وظيفة بدء التحديث التلقائي
  const startAutoRefresh = () => {
    if (refreshTimer) {
      clearInterval(refreshTimer);
    }

    refreshTimer = setInterval(async () => {
      try {
        console.log('[Lazy Load] تنفيذ التحديث التلقائي...');
        await refresh();
      } catch (error) {
        console.error('[Lazy Load] خطأ في التحديث التلقائي:', error);
      }
    }, refreshInterval);

    console.log(`[Lazy Load] تم بدء التحديث التلقائي كل ${refreshInterval / 1000} ثانية`);
  };

  // وظيفة إيقاف التحديث التلقائي
  const stopAutoRefresh = () => {
    if (refreshTimer) {
      clearInterval(refreshTimer);
      refreshTimer = null;
      console.log('[Lazy Load] تم إيقاف التحديث التلقائي');
    }
  };

  // بدء التحديث التلقائي إذا تم تمكينه
  if (autoRefresh) {
    startAutoRefresh();
  }

  // إرجاع كائن يحتوي على وظائف التحميل والتحديث
  return {
    load,
    refresh,
    startAutoRefresh,
    stopAutoRefresh,
    getLastLoadTime: () => lastLoadTime,
    isDataLoaded: () => isLoaded
  };
};

/**
 * تصدير نظام التخزين المؤقت للاستخدام المباشر
 */
export const cacheManager = {
  // مسح التخزين المؤقت
  clearCache: (pattern) => cacheSystem.clear(pattern),

  // الحصول على إحصائيات التخزين المؤقت
  getCacheStats: () => cacheSystem.getStats(),

  // تحديث التخزين المؤقت لوظيفة معينة
  invalidateCache: (functionName) => cacheSystem.clear(functionName),

  // تحديث جميع التخزين المؤقت
  invalidateAllCaches: () => cacheSystem.clear(),

  // تنظيف البيانات منتهية الصلاحية
  cleanupExpiredCache: () => cacheSystem.cleanupExpiredCache(),

  // بدء التنظيف التلقائي
  startAutoCleanup: () => cacheSystem.startAutoCleanup(),

  // إيقاف التنظيف التلقائي
  stopAutoCleanup: () => cacheSystem.stopAutoCleanup(),

  // الحصول على بيانات من التخزين المؤقت
  getCachedData: (key) => cacheSystem.get(key),

  // تخزين بيانات في التخزين المؤقت
  setCachedData: (key, data, expiry = 5 * 60 * 1000) => cacheSystem.set(key, data, expiry),

  // التحقق من وجود بيانات في التخزين المؤقت
  hasCachedData: (key) => !!cacheSystem.get(key)
};
