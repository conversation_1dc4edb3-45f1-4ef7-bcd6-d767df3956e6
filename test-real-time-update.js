/**
 * اختبار التحديث الفوري في التطبيق الفعلي
 * 
 * هذا الملف يحتوي على دوال لاختبار التحديث الفوري
 * لبطاقات الأرباح في التطبيق الفعلي
 */

// دالة لمحاكاة إضافة معاملة بيع
function simulateRealSaleTransaction() {
  console.log('🔄 محاكاة معاملة بيع حقيقية...');
  
  // إرسال أحداث متعددة لضمان التحديث
  const events = [
    'profits-updated',
    'auto-profits-updated', 
    'direct-update',
    'transaction-added',
    'transactions-refreshed'
  ];
  
  const saleData = {
    transaction_type: 'sale',
    amount: 500,
    profit: 150,
    auto_update: true,
    timestamp: new Date().toISOString()
  };
  
  events.forEach(eventType => {
    const event = new CustomEvent(eventType, { detail: saleData });
    window.dispatchEvent(event);
    console.log(`📤 تم إرسال حدث: ${eventType}`);
  });
  
  console.log('✅ تم إرسال جميع أحداث معاملة البيع');
}

// دالة لمحاكاة إضافة معاملة شراء
function simulateRealPurchaseTransaction() {
  console.log('🔄 محاكاة معاملة شراء حقيقية...');
  
  const events = [
    'cashbox-updated-ui',
    'direct-update',
    'transaction-added',
    'transactions-refreshed'
  ];
  
  const purchaseData = {
    transaction_type: 'purchase',
    amount: 300,
    timestamp: new Date().toISOString()
  };
  
  events.forEach(eventType => {
    const event = new CustomEvent(eventType, { detail: purchaseData });
    window.dispatchEvent(event);
    console.log(`📤 تم إرسال حدث: ${eventType}`);
  });
  
  console.log('✅ تم إرسال جميع أحداث معاملة الشراء');
}

// دالة لمحاكاة إضافة معاملة إرجاع
function simulateRealReturnTransaction() {
  console.log('🔄 محاكاة معاملة إرجاع حقيقية...');
  
  const events = [
    'profits-updated',
    'auto-profits-updated',
    'direct-update',
    'transaction-added',
    'transactions-refreshed'
  ];
  
  const returnData = {
    transaction_type: 'return',
    amount: 200,
    profit: -75,
    auto_update: true,
    timestamp: new Date().toISOString()
  };
  
  events.forEach(eventType => {
    const event = new CustomEvent(eventType, { detail: returnData });
    window.dispatchEvent(event);
    console.log(`📤 تم إرسال حدث: ${eventType}`);
  });
  
  console.log('✅ تم إرسال جميع أحداث معاملة الإرجاع');
}

// دالة لاختبار التحديث الفوري المتتالي
function testSequentialUpdates() {
  console.log('🚀 بدء اختبار التحديث الفوري المتتالي...');
  
  // معاملة بيع
  setTimeout(() => {
    console.log('\n1️⃣ معاملة بيع:');
    simulateRealSaleTransaction();
  }, 1000);
  
  // معاملة شراء
  setTimeout(() => {
    console.log('\n2️⃣ معاملة شراء:');
    simulateRealPurchaseTransaction();
  }, 3000);
  
  // معاملة إرجاع
  setTimeout(() => {
    console.log('\n3️⃣ معاملة إرجاع:');
    simulateRealReturnTransaction();
  }, 5000);
  
  // تقرير نهائي
  setTimeout(() => {
    console.log('\n📋 انتهى اختبار التحديث الفوري المتتالي');
    console.log('🔍 تحقق من تحديث بطاقات الأرباح في واجهة المستخدم');
  }, 7000);
}

// دالة لاختبار الأداء مع معاملات متعددة
function testPerformanceWithMultipleTransactions() {
  console.log('⚡ بدء اختبار الأداء مع معاملات متعددة...');
  
  const startTime = Date.now();
  
  // إرسال 10 معاملات متتالية
  for (let i = 1; i <= 10; i++) {
    setTimeout(() => {
      const transactionType = i % 3 === 0 ? 'return' : (i % 2 === 0 ? 'purchase' : 'sale');
      
      console.log(`📊 معاملة ${i}/10 - نوع: ${transactionType}`);
      
      if (transactionType === 'sale') {
        simulateRealSaleTransaction();
      } else if (transactionType === 'purchase') {
        simulateRealPurchaseTransaction();
      } else {
        simulateRealReturnTransaction();
      }
      
      // تقرير نهائي بعد آخر معاملة
      if (i === 10) {
        setTimeout(() => {
          const endTime = Date.now();
          const duration = endTime - startTime;
          console.log(`\n⏱️  وقت معالجة 10 معاملات: ${duration}ms`);
          console.log(`📈 متوسط الوقت لكل معاملة: ${duration / 10}ms`);
          console.log('✅ انتهى اختبار الأداء');
        }, 1000);
      }
    }, i * 500); // تأخير 500ms بين كل معاملة
  }
}

// دالة لمراقبة الأحداث
function monitorEvents() {
  console.log('👁️  بدء مراقبة الأحداث...');
  
  const eventsToMonitor = [
    'profits-updated',
    'auto-profits-updated',
    'direct-update',
    'transaction-added',
    'transactions-refreshed',
    'cashbox-updated-ui',
    'local-transactions-updated'
  ];
  
  eventsToMonitor.forEach(eventType => {
    window.addEventListener(eventType, (event) => {
      console.log(`🔔 حدث مستلم: ${eventType}`, event.detail);
    });
  });
  
  console.log('✅ تم تفعيل مراقبة الأحداث');
}

// دالة لإيقاف مراقبة الأحداث
function stopMonitoring() {
  console.log('🛑 إيقاف مراقبة الأحداث...');
  // يمكن إضافة منطق إزالة المستمعين هنا إذا لزم الأمر
}

// دالة لعرض تعليمات الاستخدام
function showInstructions() {
  console.log(`
🎯 تعليمات اختبار التحديث الفوري:

📋 الدوال المتاحة:
1. simulateRealSaleTransaction() - محاكاة معاملة بيع
2. simulateRealPurchaseTransaction() - محاكاة معاملة شراء  
3. simulateRealReturnTransaction() - محاكاة معاملة إرجاع
4. testSequentialUpdates() - اختبار متتالي للمعاملات
5. testPerformanceWithMultipleTransactions() - اختبار الأداء
6. monitorEvents() - مراقبة الأحداث
7. stopMonitoring() - إيقاف المراقبة

🚀 للبدء:
1. افتح قسم التقارير المالية
2. انتقل إلى تبويب "الأرباح"
3. شغل monitorEvents() لمراقبة الأحداث
4. شغل أي من دوال الاختبار
5. راقب تحديث بطاقات الأرباح فورياً

💡 نصائح:
- تأكد من أن قسم التقارير المالية مفتوح
- راقب وحدة التحكم (Console) للرسائل
- تحقق من تحديث القيم في البطاقات
- استخدم testSequentialUpdates() للاختبار الشامل
  `);
}

// تصدير الدوال للاستخدام العالمي
if (typeof window !== 'undefined') {
  window.testRealTimeUpdate = {
    simulateRealSaleTransaction,
    simulateRealPurchaseTransaction,
    simulateRealReturnTransaction,
    testSequentialUpdates,
    testPerformanceWithMultipleTransactions,
    monitorEvents,
    stopMonitoring,
    showInstructions
  };
  
  console.log('🎯 تم تحميل أدوات اختبار التحديث الفوري');
  console.log('📝 استخدم window.testRealTimeUpdate.showInstructions() لعرض التعليمات');
}

// عرض التعليمات تلقائياً
if (typeof window !== 'undefined') {
  setTimeout(() => {
    showInstructions();
  }, 1000);
}
