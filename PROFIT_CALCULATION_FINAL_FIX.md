# إصلاح نهائي لحساب الأرباح مع مصاريف النقل

## المشكلة
كان هناك خلل في نظام حساب إجمالي أرباح الخزينة حيث يتم خصم مصاريف النقل من إجمالي الأرباح عند تنفيذ عمليات الشراء، وهذا السلوك خاطئ.

## السلوك المطلوب
- **في عمليات الشراء**: مصاريف النقل تخصم فقط من الرصيد الحالي للخزينة ولا تؤثر على حساب إجمالي الأرباح
- **في عمليات البيع**: مصاريف النقل تخصم من الأرباح (كما هو مطلوب)

## الإصلاحات المطبقة

### 1. تحديث دالة `recalculateTotalProfits` في `unified-transaction-manager.js`

```javascript
function recalculateTotalProfits() {
  // الحصول على جميع معاملات البيع
  const salesStmt = db.prepare(`
    SELECT 
      t.id,
      t.item_id,
      t.quantity,
      t.selling_price,
      t.profit as stored_profit,
      i.avg_price
    FROM transactions t
    LEFT JOIN inventory i ON t.item_id = i.item_id
    WHERE t.transaction_type = 'sale'
      AND t.selling_price > 0
  `);

  const salesTransactions = salesStmt.all();
  let totalCalculatedProfit = 0;

  // إعادة حساب الربح لكل معاملة بيع مع مراعاة مصاريف النقل
  for (const transaction of salesTransactions) {
    // حساب مصاريف النقل المخصصة لهذا الصنف من عمليات الشراء
    let transportCostPerUnit = 0;
    const purchaseStmt = db.prepare(`
      SELECT quantity, transport_cost
      FROM transactions
      WHERE item_id = ? 
        AND transaction_type = 'purchase' 
        AND transport_cost > 0
    `);

    const purchases = purchaseStmt.all(item_id);
    if (purchases && purchases.length > 0) {
      let totalPurchasedQuantity = 0;
      let totalTransportCost = 0;

      purchases.forEach(purchase => {
        totalPurchasedQuantity += purchase.quantity || 0;
        totalTransportCost += purchase.transport_cost || 0;
      });

      if (totalPurchasedQuantity > 0) {
        transportCostPerUnit = totalTransportCost / totalPurchasedQuantity;
      }
    }

    // حساب الربح الصحيح: (سعر البيع - متوسط سعر الشراء - مصاريف النقل لكل وحدة) × الكمية
    const calculatedProfit = (selling_price - avg_price - transportCostPerUnit) * quantity;
    totalCalculatedProfit += calculatedProfit;
  }

  // خصم أرباح المرتجعات
  const returnsStmt = db.prepare(`
    SELECT COALESCE(SUM(ABS(profit)), 0) as total_return_profit
    FROM transactions
    WHERE transaction_type = 'return'
  `);

  const returnsResult = returnsStmt.get();
  const totalReturnProfit = Number(returnsResult ? returnsResult.total_return_profit : 0);

  // الربح النهائي = أرباح المبيعات - أرباح المرتجعات
  const finalTotalProfit = totalCalculatedProfit - totalReturnProfit;

  return Math.round(finalTotalProfit * 100) / 100;
}
```

### 2. تحديث حساب الربح في عمليات البيع

تم تعديل الكود في `createTransaction` لاستخدام `calculateProfitWithTransport` بدلاً من `calculateProfit`:

```javascript
if (transaction_type === 'sale' && selling_price > 0) {
  // حساب الربح في حالة البيع مع خصم مصاريف النقل
  const inventoryStmt = db.prepare('SELECT avg_price FROM inventory WHERE item_id = ?');
  const inventory = inventoryStmt.get(item_id);
  const avgPrice = inventory ? inventory.avg_price : 0;

  // حساب مصاريف النقل المخصصة لهذا الصنف من عمليات الشراء
  let transportCostPerUnit = 0;
  const purchaseStmt = db.prepare(`
    SELECT quantity, transport_cost
    FROM transactions
    WHERE item_id = ? 
      AND transaction_type = 'purchase' 
      AND transport_cost > 0
  `);

  const purchases = purchaseStmt.all(item_id);
  if (purchases && purchases.length > 0) {
    let totalPurchasedQuantity = 0;
    let totalTransportCost = 0;

    purchases.forEach(purchase => {
      totalPurchasedQuantity += purchase.quantity || 0;
      totalTransportCost += purchase.transport_cost || 0;
    });

    if (totalPurchasedQuantity > 0) {
      transportCostPerUnit = totalTransportCost / totalPurchasedQuantity;
    }
  }

  // حساب الربح مع خصم مصاريف النقل
  profit = calculateProfitWithTransport(selling_price, avgPrice, quantity, transportCostPerUnit);
}
```

### 3. التأكد من عدم تأثير مصاريف النقل في المشتريات على الأرباح

الكود الموجود في `updateCashboxAfterTransaction` للمشتريات يبقى كما هو:

```javascript
// في حالة الشراء، نخصم المبلغ من الرصيد الحالي وتحديث إجمالي المشتريات
// مصاريف النقل في المشتريات تخصم من الرصيد الحالي فقط ولا تؤثر على الأرباح
const updateStmt = db.prepare(`
  UPDATE cashbox
  SET current_balance = current_balance - ?,
      purchases_total = purchases_total + ?,
      transport_total = transport_total + ?,
      updated_at = ?
  WHERE id = ?
`);

updateStmt.run(
  totalDeductionAmount,  // خصم المبلغ الإجمالي (شراء + نقل) من الرصيد
  numericTotalPrice,     // إضافة مبلغ الشراء فقط لإجمالي المشتريات
  numericTransportCost,  // إضافة مصاريف النقل لإجمالي مصاريف النقل
  now,
  cashbox.id
);

// ملاحظة: لا يتم تحديث profit_total هنا
```

## الملفات المنشأة للاختبار والإصلاح

### 1. `test-profit-calculation-fix.js`
سكريبت اختبار شامل للتحقق من صحة الإصلاح:
- يختبر دالة إعادة حساب الأرباح الجديدة
- يحاكي عملية شراء مع مصاريف نقل
- يتحقق من عدم تأثير مصاريف النقل في المشتريات على الأرباح

### 2. `fix-profit-calculation-final.js`
سكريبت إصلاح نهائي لتحديث الأرباح الموجودة:
- يعيد حساب جميع الأرباح بالطريقة الصحيحة
- يحدث إجمالي الأرباح في قاعدة البيانات
- يعرض تقرير مفصل عن الإصلاح

## كيفية تطبيق الإصلاح

1. **تشغيل سكريبت الاختبار**:
   ```bash
   node test-profit-calculation-fix.js
   ```

2. **تطبيق الإصلاح**:
   ```bash
   node fix-profit-calculation-final.js
   ```

## النتائج المتوقعة

- ✅ مصاريف النقل في المشتريات تخصم من الرصيد الحالي فقط
- ✅ مصاريف النقل في البيع تخصم من الأرباح
- ✅ إجمالي الأرباح يحسب بشكل صحيح ودقيق
- ✅ لا يوجد خصم مزدوج لمصاريف النقل
- ✅ التحديث الفوري للأرباح بعد كل معاملة

## ملاحظات مهمة

1. **الاتساق**: تم توحيد منطق حساب الأرباح في جميع أنحاء النظام
2. **الدقة**: يتم حساب مصاريف النقل لكل صنف بناءً على متوسط مصاريف النقل في المشتريات
3. **الأمان**: تم إضافة معالجة شاملة للأخطاء
4. **الشفافية**: تم إضافة تسجيلات مفصلة لتتبع العمليات

هذا الإصلاح يضمن حساب الأرباح بشكل صحيح ومستدام دون تكرار المشكلة في المستقبل.
