import React, { useState, useEffect } from 'react';
import Autosuggest from 'react-autosuggest';
import { FaSearch, FaBoxOpen, FaMoneyBillWave, FaExclamationTriangle } from 'react-icons/fa';
import './ItemAutocomplete.css';

/**
 * مكون الإكمال التلقائي للأصناف
 * @param {Object} props - خصائص المكون
 * @param {Function} props.onSelect - دالة تُستدعى عند اختيار صنف
 * @param {Function} props.onChange - دالة تُستدعى عند تغيير النص (اختياري)
 * @param {Array} props.items - قائمة الأصناف (اختياري)
 * @param {boolean} props.showDetails - عرض تفاصيل الصنف (اختياري)
 * @param {string} props.placeholder - نص توضيحي لحقل البحث (اختياري)
 * @param {string} props.className - اسم الفئة CSS الإضافية (اختياري)
 * @param {boolean} props.hideIcon - إخفاء أيقونة البحث (اختياري)
 */
const ItemAutocomplete = ({
  onSelect,
  onChange: onChangeCallback,
  items: propItems,
  showDetails = true,
  placeholder = 'اكتب اسم الصنف أو الرقم...',
  className = '',
  hideIcon = false
}) => {
  const [items, setItems] = useState([]);
  const [value, setValue] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // تحميل الأصناف من قاعدة البيانات إذا لم يتم توفيرها
  useEffect(() => {
    if (propItems && propItems.length > 0) {
      setItems(propItems);
      return;
    }

    const loadItems = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log('تحميل بيانات الأصناف للإكمال التلقائي...');

        // جلب قائمة الأصناف
        const itemsList = await window.api.invoke('get-all-items');
        console.log(`تم جلب ${itemsList.length} صنف من قاعدة البيانات`);

        if (!itemsList || itemsList.length === 0) {
          setItems([]);
          setError('لم يتم العثور على أصناف في قاعدة البيانات');
          return;
        }

        // جلب المخزون
        const inventoryList = await window.api.invoke('get-inventory', true);
        console.log(`تم جلب ${inventoryList.length} عنصر من المخزون`);

        // إنشاء خريطة للمخزون للوصول السريع
        const inventoryMap = new Map();
        inventoryList.forEach(item => {
          if (item && item.item_id) {
            inventoryMap.set(item.item_id, item);
          } else if (item && item.id) {
            inventoryMap.set(item.id, item);
          }
        });

        // دمج بيانات الأصناف مع بيانات المخزون
        const mergedItems = itemsList.map(item => {
          const inventoryItem = inventoryMap.get(item.id);
          return {
            id: item.id,
            name: item.name || 'صنف بدون اسم',
            unit: item.unit || 'قطعة',
            current_quantity: inventoryItem && typeof inventoryItem.current_quantity === 'number' ? inventoryItem.current_quantity : 0,
            selling_price: inventoryItem && typeof inventoryItem.selling_price === 'number' ? inventoryItem.selling_price : 0,
            minimum_quantity: inventoryItem && typeof inventoryItem.minimum_quantity === 'number' ? inventoryItem.minimum_quantity : 0
          };
        });

        // ترتيب الأصناف بحيث تظهر الأصناف ذات الكمية المتوفرة وسعر البيع أولاً
        const sortedItems = [...mergedItems].sort((a, b) => {
          // الأصناف ذات الكمية المتوفرة وسعر البيع أولاً
          const aValid = a.current_quantity > 0 && a.selling_price > 0;
          const bValid = b.current_quantity > 0 && b.selling_price > 0;

          if (aValid && !bValid) return -1;
          if (!aValid && bValid) return 1;

          // ثم ترتيب حسب الاسم
          return a.name.localeCompare(b.name);
        });

        setItems(sortedItems);
      } catch (error) {
        console.error('خطأ في تحميل الأصناف للإكمال التلقائي:', error);
        setError('حدث خطأ أثناء تحميل الأصناف');
      } finally {
        setLoading(false);
      }
    };

    loadItems();
  }, [propItems]);

  // الحصول على اقتراحات البحث
  const getSuggestions = (value) => {
    const inputValue = value.trim().toLowerCase();
    const inputLength = inputValue.length;

    return inputLength === 0
      ? []
      : items.filter(item => {
          const nameMatch = item.name && item.name.toLowerCase().includes(inputValue);
          const idMatch = item.id && item.id.toString().includes(inputValue);
          return nameMatch || idMatch;
        }).slice(0, 10); // الحد الأقصى للاقتراحات هو 10
  };

  // الحصول على نص الاقتراح
  const getSuggestionValue = (suggestion) => suggestion.name;

  // تحديد لون الكمية بناءً على قيمتها
  const getQuantityColorClass = (quantity, minimumQuantity = 0) => {
    if (quantity <= 0) return 'out';
    if (minimumQuantity > 0 && quantity <= minimumQuantity) return 'low';
    return '';
  };

  // عرض الاقتراح
  const renderSuggestion = (suggestion) => (
    <div className="item-suggestion">
      <div className="item-suggestion-name">{suggestion.name}</div>
      {showDetails && (
        <div className="item-suggestion-details">
          <div className="item-suggestion-detail">
            <span className="item-suggestion-label">الوحدة:</span>
            <span className="item-suggestion-value">{suggestion.unit || 'غير محدد'}</span>
          </div>
          <div className="item-suggestion-detail">
            <span className="item-suggestion-label">
              <FaBoxOpen style={{ marginLeft: '5px' }} />
              الكمية:
            </span>
            <span
              className={`item-suggestion-value item-quantity ${getQuantityColorClass(suggestion.current_quantity, suggestion.minimum_quantity)}`}
            >
              {suggestion.current_quantity || 0}
              {suggestion.current_quantity <= 0 && (
                <FaExclamationTriangle style={{ marginRight: '5px', color: '#e74c3c' }} />
              )}
            </span>
          </div>
          <div className="item-suggestion-detail">
            <span className="item-suggestion-label">
              <FaMoneyBillWave style={{ marginLeft: '5px' }} />
              السعر:
            </span>
            <span className="item-suggestion-value">
              {suggestion.selling_price ? suggestion.selling_price.toFixed(2) : '0.00'}
              {(suggestion.selling_price <= 0) && (
                <FaExclamationTriangle style={{ marginRight: '5px', color: '#e74c3c' }} />
              )}
            </span>
          </div>
        </div>
      )}
    </div>
  );

  // معالجة تغيير قيمة الإدخال
  const onChange = (event, { newValue }) => {
    setValue(newValue);

    // استدعاء دالة التغيير إذا كانت موجودة
    if (onChangeCallback) {
      onChangeCallback(newValue);
    }
  };

  // معالجة طلب الاقتراحات
  const onSuggestionsFetchRequested = ({ value }) => {
    setSuggestions(getSuggestions(value));
  };

  // معالجة مسح الاقتراحات
  const onSuggestionsClearRequested = () => {
    setSuggestions([]);
  };

  // معالجة اختيار اقتراح
  const onSuggestionSelected = (event, { suggestion }) => {
    console.log('تم اختيار الصنف:', suggestion);

    // استدعاء دالة الاختيار مع الصنف المحدد
    if (onSelect) {
      onSelect(suggestion);
    }

    // مسح حقل البحث بعد الاختيار
    setValue('');
  };

  // خصائص الإدخال
  const inputProps = {
    placeholder,
    value,
    onChange,
    className: 'item-autocomplete-input'
  };

  // تخصيص مظهر المكون
  const theme = {
    container: `item-autocomplete-container ${className}`,
    suggestionsContainer: 'item-autocomplete-suggestions-container',
    suggestionsContainerOpen: 'item-autocomplete-suggestions-container-open',
    suggestionsList: 'item-autocomplete-suggestions-list',
    suggestion: 'item-autocomplete-suggestion',
    suggestionHighlighted: 'item-autocomplete-suggestion-highlighted'
  };

  return (
    <div className="item-autocomplete-wrapper">
      {!hideIcon && (
        <div className="item-autocomplete-icon">
          <FaSearch />
        </div>
      )}
      <Autosuggest
        suggestions={suggestions}
        onSuggestionsFetchRequested={onSuggestionsFetchRequested}
        onSuggestionsClearRequested={onSuggestionsClearRequested}
        onSuggestionSelected={onSuggestionSelected}
        getSuggestionValue={getSuggestionValue}
        renderSuggestion={renderSuggestion}
        inputProps={inputProps}
        theme={theme}
      />
      {loading && <div className="item-autocomplete-loading">جاري التحميل...</div>}
      {error && <div className="item-autocomplete-error">{error}</div>}
    </div>
  );
};

export default ItemAutocomplete;
