import React from 'react';
import { FaCheckCircle, FaExclamationCircle, FaInfoCircle, FaTimesCircle } from 'react-icons/fa';
import { useApp } from '../context/AppContext';
import './Notifications.css';

/**
 * مكون الإشعارات
 * يعرض رسائل النجاح والخطأ والتحذير والمعلومات
 */
const Notifications = () => {
  // استخدام سياق التطبيق للوصول إلى الإشعارات
  const { notifications } = useApp();

  // إزالة إشعار
  const removeNotification = (id) => {
    // لا نحتاج إلى هذه الوظيفة لأن الإشعارات تتم إزالتها تلقائيًا
    // من خلال وظيفة showNotification في AppContext
  };

  // الحصول على أيقونة الإشعار بناءً على النوع
  const getIcon = (type) => {
    switch (type) {
      case 'success':
        return <FaCheckCircle />;
      case 'error':
        return <FaTimesCircle />;
      case 'warning':
        return <FaExclamationCircle />;
      case 'info':
      default:
        return <FaInfoCircle />;
    }
  };

  // تم تعديل المكون لإخفاء جميع الإشعارات تمامًا
  return null; // لا يتم عرض أي شيء على الإطلاق
};

export default Notifications;
