import React, { createContext, useState, useEffect, useContext } from 'react';
import * as database from '../../utils/database';
import { InventoryContext } from './InventoryProvider';
import { NotificationsContext } from './NotificationsProvider';

// إنشاء سياق المعاملات
export const TransactionsContext = createContext();

/**
 * مزود سياق المعاملات
 *
 * يوفر هذا المكون وظائف إدارة المعاملات (المشتريات والمبيعات)
 *
 * @param {Object} props - خصائص المكون
 * @param {React.ReactNode} props.children - المكونات الفرعية
 * @returns {React.ReactElement} مزود سياق المعاملات
 */
export const TransactionsProvider = ({ children }) => {
  // استخدام سياق المخزون والإشعارات
  const { inventory, items, syncAllInventory } = useContext(InventoryContext);
  const { showNotification } = useContext(NotificationsContext);

  // حالة المعاملات
  const [transactions, setTransactions] = useState([]);

  // تحميل المعاملات عند بدء التطبيق
  useEffect(() => {
    const loadTransactions = async () => {
      try {
        const transactionsData = await database.getTransactions();
        setTransactions(transactionsData);
        console.log('تم تحميل المعاملات بنجاح:', transactionsData.length);
      } catch (error) {
        console.error('خطأ في تحميل المعاملات:', error);
        setTransactions([]);
      }
    };

    loadTransactions();
  }, []);

  // إضافة مستمعي أحداث لتحديث المعاملات
  useEffect(() => {
    // دالة لإعادة تحميل المعاملات عند تحديثها
    const reloadTransactions = async (force = true) => {
      try {
        console.log('إعادة تحميل المعاملات بعد التحديث...');

        // مسح التخزين المؤقت أولاً
        if (window.api && window.api.invoke) {
          try {
            await window.api.invoke('clear-transactions-cache');
            console.log('تم مسح التخزين المؤقت للمعاملات');
          } catch (cacheError) {
            console.warn('تحذير: فشل في مسح التخزين المؤقت للمعاملات:', cacheError);
          }
        }

        // إعادة تحميل المعاملات من قاعدة البيانات
        const transactionsData = await database.getTransactions();
        setTransactions(transactionsData);
        console.log('تم إعادة تحميل المعاملات بنجاح:', transactionsData.length);

        // إرسال إشعار إضافي لتحديث المكونات الأخرى
        const refreshEvent = new CustomEvent('transactions-refreshed', {
          detail: {
            count: transactionsData.length,
            timestamp: new Date().toISOString()
          }
        });
        window.dispatchEvent(refreshEvent);

      } catch (error) {
        console.error('خطأ في إعادة تحميل المعاملات:', error);
      }
    };

    // مستمع لحدث تحديث الأرباح - فقط لعمليات البيع
    const handleProfitsUpdated = (event) => {
      console.log('تم استلام حدث تحديث الأرباح في TransactionsProvider:', event.detail);

      // فحص نوع المعاملة - إعادة تحميل المعاملات فقط لعمليات البيع
      if (event.detail && event.detail.transaction_type) {
        if (event.detail.transaction_type === 'sale') {
          console.log('إعادة تحميل المعاملات بعد عملية بيع');
          // تأخير قصير للسماح بحفظ المعاملة في قاعدة البيانات
          setTimeout(() => reloadTransactions(true), 100);
        } else {
          console.log(`تجاهل إعادة تحميل المعاملات لعملية ${event.detail.transaction_type} (حسب المتطلبات الجديدة)`);
        }
      } else {
        // إذا لم يكن هناك نوع معاملة محدد، قم بإعادة التحميل (للتوافق مع الأحداث القديمة)
        setTimeout(() => reloadTransactions(true), 100);
      }
    };

    // مستمع لحدث تحديث الخزينة (يعني أن هناك معاملة جديدة)
    const handleCashboxUpdated = (event) => {
      console.log('تم استلام حدث تحديث الخزينة في TransactionsProvider:', event.detail);
      // تأخير قصير للسماح بحفظ المعاملة في قاعدة البيانات
      setTimeout(() => reloadTransactions(true), 100);
    };

    // مستمع لحدث التحديث التلقائي للأرباح - فقط لعمليات البيع
    const handleAutoProfitsUpdated = (event) => {
      console.log('تم استلام حدث التحديث التلقائي للأرباح في TransactionsProvider:', event.detail);

      // فحص نوع المعاملة - إعادة تحميل المعاملات فقط لعمليات البيع
      if (event.detail && event.detail.transaction_type) {
        if (event.detail.transaction_type === 'sale') {
          console.log('إعادة تحميل تلقائية للمعاملات بعد عملية بيع');
          // تأخير قصير للسماح بحفظ المعاملة في قاعدة البيانات
          setTimeout(() => reloadTransactions(true), 50);
        } else {
          console.log(`تجاهل إعادة التحميل التلقائية للمعاملات لعملية ${event.detail.transaction_type} (حسب المتطلبات الجديدة)`);
        }
      } else {
        // إذا لم يكن هناك نوع معاملة محدد، قم بإعادة التحميل (للتوافق مع الأحداث القديمة)
        setTimeout(() => reloadTransactions(true), 50);
      }
    };

    // مستمع لحدث التحديث المباشر
    const handleDirectUpdate = (event) => {
      console.log('تم استلام حدث التحديث المباشر في TransactionsProvider:', event.detail);
      // تأخير قصير للسماح بحفظ المعاملة في قاعدة البيانات
      setTimeout(() => reloadTransactions(true), 25);
    };

    // مستمع لحدث إضافة معاملة مباشرة
    const handleTransactionAdded = (event) => {
      console.log('تم استلام حدث إضافة معاملة في TransactionsProvider:', event.detail);
      // تأخير قصير للسماح بحفظ المعاملة في قاعدة البيانات
      setTimeout(() => reloadTransactions(true), 50);
    };

    // تسجيل المستمعين
    window.addEventListener('profits-updated', handleProfitsUpdated);
    window.addEventListener('cashbox-updated-ui', handleCashboxUpdated);
    window.addEventListener('auto-profits-updated', handleAutoProfitsUpdated);
    window.addEventListener('direct-update', handleDirectUpdate);
    window.addEventListener('transaction-added', handleTransactionAdded);

    // إزالة المستمعين عند تفكيك المكون
    return () => {
      window.removeEventListener('profits-updated', handleProfitsUpdated);
      window.removeEventListener('cashbox-updated-ui', handleCashboxUpdated);
      window.removeEventListener('auto-profits-updated', handleAutoProfitsUpdated);
      window.removeEventListener('direct-update', handleDirectUpdate);
      window.removeEventListener('transaction-added', handleTransactionAdded);
    };
  }, []);

  // إضافة معاملة جديدة (شراء، بيع)
  const addTransaction = async (transaction) => {
    try {
      console.log('استلام طلب إضافة معاملة:', transaction);

      // التحقق من وجود المعاملة بشكل أكثر تفصيلاً
      if (!transaction) {
        console.error('المعاملة غير موجودة (قيمة فارغة)');
        throw new Error('المعاملة غير موجودة أو غير صالحة');
      }

      // التحقق من أن المعاملة كائن وليست قيمة بدائية
      if (typeof transaction !== 'object') {
        console.error('المعاملة ليست كائناً صالحاً:', typeof transaction);
        throw new Error('المعاملة ليست بالتنسيق الصحيح');
      }

      // تحميل البيانات إذا كان المخزون أو الأصناف فارغة
      if (!inventory || inventory.length === 0 || !items || items.length === 0) {
        console.log('المخزون أو قائمة الأصناف فارغة، جاري تحميل البيانات...');
        await syncAllInventory();
      }

      console.log('بدء إضافة معاملة جديدة:', transaction);

      // التحقق من وجود معرف الصنف
      if (!transaction.item_id) {
        console.error('معرف الصنف غير موجود في المعاملة:', transaction);
        throw new Error('معرف الصنف غير موجود في المعاملة');
      }

      // تحويل معرف الصنف إلى نص للمقارنة
      const transactionItemId = String(transaction.item_id);
      console.log('معرف الصنف:', transactionItemId, 'نوع المعرف:', typeof transaction.item_id);

      // التحقق من حالة المخزون
      if (!inventory || !Array.isArray(inventory) || inventory.length === 0) {
        console.log('المخزون فارغ أو غير محمل، جاري تحميل المخزون...');
        // يمكن إضافة منطق لتحميل المخزون هنا إذا لزم الأمر
        // على سبيل المثال: await fetchInventory();
      } else {
        console.log('عدد عناصر المخزون:', inventory.length);
      }

      // البحث عن الصنف في المخزون بطريقة أكثر مرونة
      let inventoryItem = null;

      // محاولة البحث باستخدام المعرف المباشر أولاً
      inventoryItem = inventory.find(item => {
        const itemId = String(item.id || '');
        const itemItemId = String(item.item_id || '');
        const item_id = String(item._id || '');

        return (
          itemId === transactionItemId ||
          itemItemId === transactionItemId ||
          item_id === transactionItemId
        );
      });

      // إذا لم يتم العثور على الصنف، نحاول البحث باستخدام الاسم
      if (!inventoryItem && transaction.item_name) {
        console.log('محاولة البحث عن الصنف باستخدام الاسم:', transaction.item_name);
        inventoryItem = inventory.find(item => item.name === transaction.item_name);
      }

      console.log('العنصر الموجود في المخزون:', inventoryItem);

      // إذا لم يتم العثور على الصنف في المخزون، نبحث في قائمة الأصناف
      if (!inventoryItem) {
        console.log('الصنف غير موجود في المخزون. جاري البحث في قائمة الأصناف...');

        // البحث عن الصنف في قائمة الأصناف
        const itemFromItems = items.find(item => {
          const itemId = String(item.id || '');
          const item_id = String(item._id || '');

          return (
            itemId === transactionItemId ||
            item_id === transactionItemId ||
            (transaction.item_name && item.name === transaction.item_name)
          );
        });

        if (itemFromItems) {
          console.log('تم العثور على الصنف في قائمة الأصناف ولكن ليس في المخزون:', itemFromItems);

          // إضافة الصنف إلى المخزون تلقائيًا
          const newInventoryItem = {
            id: itemFromItems.id || itemFromItems._id,
            _id: itemFromItems._id || itemFromItems.id,
            item_id: itemFromItems.id || itemFromItems._id,
            name: itemFromItems.name,
            unit: itemFromItems.unit || 'قطعة',
            current_quantity: 0,
            minimum_quantity: itemFromItems.minimum_quantity || 0,
            avg_price: itemFromItems.avg_price || 0,
            selling_price: itemFromItems.selling_price || 0,
            last_updated: new Date().toISOString()
          };

          console.log('إضافة الصنف إلى المخزون تلقائيًا:', newInventoryItem);

          // استخدام الصنف المضاف للمعاملة
          inventoryItem = newInventoryItem;
        } else {
          console.error('الصنف غير موجود في قائمة الأصناف أو المخزون:', transactionItemId);
          throw new Error('الصنف غير موجود في النظام');
        }
      }

      // التحقق من صحة معاملة البيع أو السحب
      if (transaction.transaction_type === 'sale' || transaction.transaction_type === 'withdrawal') {
        // التحقق من الكمية المتوفرة - لا نسمح بالبيع إلا إذا كانت الكمية 1 أو أكبر
        if (inventoryItem.current_quantity < 1) {
          throw new Error(`الكمية المتوفرة صفر لهذا الصنف`);
        }

        // التحقق من الكمية المطلوبة
        if (transaction.quantity > inventoryItem.current_quantity) {
          throw new Error(`الكمية المطلوبة (${transaction.quantity}) أكبر من الكمية المتوفرة (${inventoryItem.current_quantity})`);
        }

        // حساب الربح (فقط لمعاملات البيع)
        if (transaction.transaction_type === 'sale') {
          const profit = (transaction.price - inventoryItem.avg_price) * transaction.quantity;
          transaction.profit = profit;
        }
      }

      // التحقق من وجود علامة تجاهل تحديث المخزون
      const skipInventoryUpdate = transaction.skip_inventory_update || false;
      console.log(`معاملة ${transaction.transaction_type} مع علامة تجاهل تحديث المخزون:`, skipInventoryUpdate);

      // إعداد بيانات المعاملة للإرسال إلى قاعدة البيانات
      const dbTransaction = {
        ...transaction,
        transaction_date: transaction.transaction_date || new Date().toISOString(),
        user_id: transaction.user_id || 1, // يمكن تغييره لاحقًا عندما نضيف نظام المستخدمين
        user_name: transaction.user_name || 'admin', // يمكن تغييره لاحقًا عندما نضيف نظام المستخدمين
        item_name: inventoryItem.name, // إضافة اسم الصنف للمعاملة
        skip_inventory_update: skipInventoryUpdate // تأكيد إرسال علامة تجاهل تحديث المخزون
      };

      // تأكد من أن معرف الصنف متوافق مع المخزون
      dbTransaction.item_id = inventoryItem.id || inventoryItem._id || inventoryItem.item_id;

      // إضافة المعاملة إلى قاعدة البيانات
      console.log('جاري إضافة المعاملة إلى قاعدة البيانات:', dbTransaction);
      const result = await database.addTransaction(dbTransaction);
      console.log('تم إضافة المعاملة بنجاح:', result);

      // التحقق من أن النتيجة تحتوي على بيانات المعاملة
      if (!result) {
        console.error('النتيجة غير موجودة:', result);
        throw new Error('فشل في إضافة المعاملة: النتيجة غير موجودة');
      }

      // استخراج بيانات المعاملة من النتيجة
      const addedTransaction = result.transaction || result;

      // إضافة المعاملة إلى قائمة المعاملات مباشرة (بدون إعادة تحميل)
      // تأكد من أن المعاملة تحتوي على جميع البيانات المطلوبة
      const completeTransaction = {
        ...addedTransaction,
        id: addedTransaction.id || addedTransaction._id,
        _id: addedTransaction._id || addedTransaction.id,
        item_id: addedTransaction.item_id,
        item_name: addedTransaction.item_name || inventoryItem.name,
        transaction_type: addedTransaction.transaction_type || dbTransaction.transaction_type,
        quantity: addedTransaction.quantity || dbTransaction.quantity,
        price: addedTransaction.price || dbTransaction.price,
        total_price: addedTransaction.total_price || (dbTransaction.price * dbTransaction.quantity),
        transaction_date: addedTransaction.transaction_date || dbTransaction.transaction_date || new Date().toISOString()
      };

      console.log('إضافة المعاملة المكتملة إلى الحالة:', completeTransaction);
      setTransactions(prevTransactions => [completeTransaction, ...prevTransactions]);

      // مزامنة المخزون بعد إضافة المعاملة
      if (!skipInventoryUpdate) {
        try {
          await syncAllInventory();
        } catch (syncError) {
          console.error('خطأ في مزامنة المخزون بعد إضافة المعاملة:', syncError);
        }
      }

      return result;
    } catch (error) {
      console.error('خطأ في إضافة المعاملة:', error);
      throw error;
    }
  };

  // الحصول على معاملات صنف معين
  const getItemTransactions = (itemId) => {
    return transactions.filter(transaction => {
      if (!transaction) return false;

      return (
        (transaction.item_id === itemId) ||
        (transaction.item_id && typeof transaction.item_id === 'object' && transaction.item_id._id === itemId) ||
        (transaction.item_id && typeof transaction.item_id === 'object' && transaction.item_id.id === itemId) ||
        (transaction.item && transaction.item._id === itemId) ||
        (transaction.item && transaction.item.id === itemId)
      );
    });
  };

  // الحصول على معاملات من نوع معين
  const getTransactionsByType = (type) => {
    return transactions.filter(transaction => transaction && transaction.transaction_type === type);
  };

  // حساب الأرباح
  const calculateProfits = () => {
    const now = new Date();
    const oneYearAgo = new Date(now);
    oneYearAgo.setFullYear(now.getFullYear() - 1);

    const sixMonthsAgo = new Date(now);
    sixMonthsAgo.setMonth(now.getMonth() - 6);

    const threeMonthsAgo = new Date(now);
    threeMonthsAgo.setMonth(now.getMonth() - 3);

    const nineMonthsAgo = new Date(now);
    nineMonthsAgo.setMonth(now.getMonth() - 9);

    // الحصول على معاملات البيع
    const salesTransactions = transactions.filter(t => t && t.transaction_type === 'sale');

    // حساب الربح الربع سنوي
    const quarterlyProfit = salesTransactions
      .filter(t => new Date(t.transaction_date) >= threeMonthsAgo)
      .reduce((total, t) => total + (t.profit || 0), 0);

    // حساب الربح النصف سنوي
    const halfYearlyProfit = salesTransactions
      .filter(t => new Date(t.transaction_date) >= sixMonthsAgo)
      .reduce((total, t) => total + (t.profit || 0), 0);

    // حساب الربح الثلاثة أرباع سنوي
    const threeQuartersProfit = salesTransactions
      .filter(t => new Date(t.transaction_date) >= nineMonthsAgo)
      .reduce((total, t) => total + (t.profit || 0), 0);

    // حساب الربح السنوي
    const yearlyProfit = salesTransactions
      .filter(t => new Date(t.transaction_date) >= oneYearAgo)
      .reduce((total, t) => total + (t.profit || 0), 0);

    return {
      quarterly: quarterlyProfit,
      halfYearly: halfYearlyProfit,
      threeQuarters: threeQuartersProfit,
      yearly: yearlyProfit
    };
  };

  // القيمة التي سيتم توفيرها للمكونات
  const value = {
    transactions,
    addTransaction,
    getItemTransactions,
    getTransactionsByType,
    calculateProfits
  };

  return (
    <TransactionsContext.Provider value={value}>
      {children}
    </TransactionsContext.Provider>
  );
};

export default TransactionsProvider;
