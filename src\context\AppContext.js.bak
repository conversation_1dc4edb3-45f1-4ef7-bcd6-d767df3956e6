import React, { createContext, useContext, useState, useEffect, useRef } from 'react';
import { testConnection, syncInventory, getSalesData, updateSyncStatus } from '../services/IntegrationService';
import * as database from '../utils/database';
import { initBrightness } from '../utils/brightness';
import DatabaseAdapter from '../utils/database-adapter';

// إنشاء سياق التطبيق
export const AppContext = createContext();

// مزود السياق
export const AppProvider = ({ children }) => {
  // حالة الأصناف
  // Variable no utilizada - considere eliminarla o usarla
  const [items, setItems] = useState([]);

  // حالة المخزون
  // Variable no utilizada - considere eliminarla o usarla
  const [inventory, setInventory] = useState([]);

  // حالة المعاملات (المشتريات والمبيعات والاستلام والصرف)
  // Variable no utilizada - considere eliminarla o usarla
  const [transactions, setTransactions] = useState([]);

  // حالة المستخدمين
  // Variable no utilizada - considere eliminarla o usarla
  const [users, setUsers] = useState([]);

  // حالة العملاء
  // Variable no utilizada - considere eliminarla o usarla
  const [customers, setCustomers] = useState([]);

  // تم إزالة حالة الموردين

  // حالة الآلات والمعدات (للزكاة)
  // Variable no utilizada - considere eliminarla o usarla
  const [machines, setMachines] = useState([]);

  // حالة الخزينة
  // Variable no utilizada - considere eliminarla o usarla
  const [cashbox, setCashbox] = useState(null);
  // Variable no utilizada - considere eliminarla o usarla
  const [cashboxTransactions, setCashboxTransactions] = useState([]);

  // حالة إعدادات النظام
  // Variable no utilizada - considere eliminarla o usarla
  const [settings, setSettings] = useState({
    currency: 'د ل',
    lowStockNotification: true,
    defaultMinimumQuantity: 5,
    systemName: 'شركة أثاث غروب',
    address: 'للتصميم و تصنيع الأثاث والديكورات',
    phone: '+218 92-3000151',
    logoUrl: '',
    integrationEnabled: false,
    integrationUrl: '',
    integrationApiKey: '',
    syncInterval: 30,
    // إعدادات التحديث
    appVersion: '1.1.0',
    autoUpdateEnabled: false,
    lastUpdateCheck: null,
    // تم إزالة إعداد السطوع اليدوي واستبداله بنظام تلقائي
  });

  // حالة التحميل
  // Variable no utilizada - considere eliminarla o usarla
  const [loading, setLoading] = useState(true);

  // حالة المزامنة
  // Variable no utilizada - considere eliminarla o usarla
  const [syncStatus, setSyncStatus] = useState({
    syncing: false,
    lastSyncTime: null,
    lastSyncStatus: null,
    error: null
  });

  // حالة الإشعارات
  // Variable no utilizada - considere eliminarla o usarla
  const [notifications, setNotifications] = useState([]);

  // مرجع للمزامنة الدورية مع منظومة المبيعات
  const syncIntervalRef = useRef(null);

  // مرجع للمزامنة الدورية للمخزون
  const inventorySyncIntervalRef = useRef(null);

  // تعيين وظيفة تحديث بيانات العميل العالمية
  useEffect(() => {
    // تعيين وظيفة تحديث بيانات العميل العالمية
    window.updateCustomerData = (customerData) => {
      if (!customerData || !customerData.id) {
        console.error('بيانات العميل غير صالحة للتحديث:', customerData);
        return false;
      }

      console.log('تحديث بيانات العميل من خلال الوظيفة العالمية:', customerData);

      // تحديث العميل في قائمة العملاء
      setCustomers(prevCustomers => {
        return prevCustomers.map(customer => {
          if (customer.id === customerData.id || customer._id === customerData.id) {
            console.log('تحديث بيانات العميل في القائمة:', customerData);
            return { ...customer, ...customerData };
          }
          return customer;
        });
      });

      return true;
    };

    // تنظيف الوظيفة عند إلغاء تحميل المكون
    return () => {
      window.updateCustomerData = null;
    };
  }, []);

  // تحميل البيانات عند بدء التطبيق
  useEffect(() => {
    // محاولة تحميل البيانات من قاعدة البيانات
    loadData()
      .then(() => {
        // مزامنة المخزون تلقائياً بعد تحميل البيانات (مع إظهار مؤشر التحميل والإشعارات)
        console.log('تحميل البيانات تم بنجاح، جاري مزامنة المخزون تلقائياً...');
        syncAllInventory({
          showLoadingIndicator: true,
          showNotifications: true
        })
          .then(() => {
            console.log('تمت مزامنة المخزون تلقائياً عند بدء التطبيق');
          })
          .catch(syncError => {
            console.error('خطأ في المزامنة التلقائية للمخزون عند بدء التطبيق:', syncError);
          });
      })
      .catch(error => {
        console.error('Error loading data from database:', error);
        // في حالة فشل تحميل البيانات من قاعدة البيانات، نستخدم البيانات الافتراضية
        loadDefaultData();
      });

    // تحميل إعدادات المزامنة من التخزين المحلي
    const storedSettings = localStorage.getItem('settings');
    if (storedSettings) {
      try {
        const parsedSettings = JSON.parse(storedSettings);
        setSettings(prevSettings => ({
          ...prevSettings,
          ...parsedSettings
        }));

        // تم إزالة تطبيق السطوع اليدوي واستبداله بنظام تلقائي
      } catch (error) {
        console.error('Error parsing stored settings:', error);
      }
    }

    // تهيئة نظام السطوع المحسن
    initBrightness([
      '.main-content-full',
      '.navbar',
      '.card',
      '.table',
      '.stat-card',
      '.dashboard-welcome',
      '.page-header'
    ]);

    // تحميل آخر وقت للمزامنة من التخزين المحلي
    const lastSyncTime = localStorage.getItem('lastSyncTime');
    if (lastSyncTime) {
      setSyncStatus(prev => ({
        ...prev,
        lastSyncTime
      }));
    }
  }, []);

  // تحميل البيانات من قاعدة البيانات
  const loadData = async () => {
    try {
      console.log('بدء تحميل البيانات...');
      setLoading(true);

      // متغير للتحقق من نجاح تحميل البيانات
      let loadedSuccessfully = true;
      // تحميل الأصناف مع فرض تحديث التخزين المؤقت
      try {
        console.log('تحميل الأصناف مع فرض تحديث التخزين المؤقت...');
        const itemsData = await database.getItems(true); // فرض تحديث التخزين المؤقت
        setItems(itemsData);
        console.log('تم تحميل الأصناف بنجاح:', itemsData.length);
      } catch (itemsError) {
        console.error('خطأ في تحميل الأصناف:', itemsError);
        loadedSuccessfully = false;
      }

      // تحميل المخزون مع فرض تحديث التخزين المؤقت
      try {
        console.log('تحميل المخزون مع فرض تحديث التخزين المؤقت...');
        const inventoryData = await database.getInventory(true); // فرض تحديث التخزين المؤقت
        setInventory(inventoryData);
        console.log('تم تحميل المخزون بنجاح:', inventoryData.length);
      } catch (inventoryError) {
        console.error('خطأ في تحميل المخزون:', inventoryError);
        loadedSuccessfully = false;
      }

      // تحميل المعاملات
      try {
        const transactionsData = await database.getTransactions();
        setTransactions(transactionsData);
        console.log('تم تحميل المعاملات بنجاح:', transactionsData.length);
      } catch (transactionsError) {
        console.error('خطأ في تحميل المعاملات:', transactionsError);
        loadedSuccessfully = false;
      }

      // تحميل المستخدمين
      try {
        const usersData = await database.getUsers();
        setUsers(usersData);
        console.log('تم تحميل المستخدمين بنجاح:', usersData.length);
      } catch (usersError) {
        console.error('خطأ في تحميل المستخدمين:', usersError);
        loadedSuccessfully = false;
      }

      // تحميل العملاء
      try {
        console.log('جاري تحميل العملاء...');
        const customersData = await database.getCustomers();
        console.log('تم استلام بيانات العملاء:', customersData);

        if (Array.isArray(customersData)) {
          setCustomers(customersData);
          console.log('تم تحميل العملاء بنجاح:', customersData.length);
        } else {
          console.error('بيانات العملاء ليست مصفوفة:', customersData);
          setCustomers([]);
          loadedSuccessfully = false;
        }
      } catch (customersError) {
        console.error('خطأ في تحميل العملاء:', customersError);
        setCustomers([]);
        loadedSuccessfully = false;
      }

      // تم إزالة تحميل الموردين

      // تحميل الآلات
      try {
        const machinesData = await database.getMachines();
        setMachines(machinesData);
        console.log('تم تحميل الآلات بنجاح:', machinesData.length);
      } catch (machinesError) {
        console.error('خطأ في تحميل الآلات:', machinesError);
        loadedSuccessfully = false;
      }

      // تحميل بيانات الخزينة
      try {
        console.log('جاري تحميل بيانات الخزينة...');

        // التحقق من وجود window.api قبل استدعاء وظائف الخزينة
        if (typeof window === 'undefined' || !window.api || !window.api.cashbox) {
          console.error('window.api.cashbox غير متوفر. لن يتم تحميل بيانات الخزينة.');

          // إعداد بيانات افتراضية للخزينة
          setCashbox({ exists: false, balance: 0, initial_balance: 0 });
          setCashboxTransactions([]);

          // تسجيل الخطأ فقط دون محاولة إعادة التحميل
          localStorage.setItem('lastFailedLoadTime', new Date().getTime().toString());

          loadedSuccessfully = false;
        } else {
          // محاولة تحميل بيانات الخزينة مع محاولات متعددة
          let attempts = 0;
          const maxAttempts = 3;
          let cashboxData = null;

          while (attempts < maxAttempts && !cashboxData) {
            attempts++;
            try {
              console.log(`محاولة تحميل بيانات الخزينة (${attempts}/${maxAttempts})...`);
              cashboxData = await window.api.cashbox.get();
              console.log('تم تحميل بيانات الخزينة بنجاح:', cashboxData);
              setCashbox(cashboxData);

              if (cashboxData && cashboxData.exists) {
                // تحميل معاملات الخزينة
                const transactionsData = await window.api.cashbox.getTransactions();
                setCashboxTransactions(transactionsData);
                console.log('تم تحميل معاملات الخزينة بنجاح:', transactionsData.length);
              } else {
                setCashboxTransactions([]);
              }
            } catch (attemptError) {
              console.error(`خطأ في محاولة تحميل بيانات الخزينة ${attempts}/${maxAttempts}:`, attemptError);

              if (attempts >= maxAttempts) {
                console.error('فشلت جميع محاولات تحميل بيانات الخزينة');
                setCashbox({ exists: false, balance: 0, initial_balance: 0 });
                setCashboxTransactions([]);
                loadedSuccessfully = false;
              } else {
                // انتظار قبل المحاولة التالية
                await new Promise(resolve => setTimeout(resolve, 1000));
              }
            }
          }
        }
      } catch (cashboxError) {
        console.error('خطأ عام في تحميل بيانات الخزينة:', cashboxError);
        setCashbox({ exists: false, balance: 0, initial_balance: 0 });
        setCashboxTransactions([]);
        loadedSuccessfully = false;
      }

      setLoading(false);
      return loadedSuccessfully;
    } catch (error) {
      console.error('خطأ عام في تحميل البيانات:', error);
      setLoading(false);
      return false;
    }
  };

  // تحميل الأصناف فقط
  const loadItems = async () => {
    try {
      console.log('بدء تحميل الأصناف...');

      // تحميل الأصناف باستخدام window.api.invoke مباشرة
      const itemsData = await window.api.invoke('get-all-items');
      setItems(itemsData);
      console.log('تم تحميل الأصناف بنجاح:', itemsData.length);
      return itemsData;
    } catch (itemsError) {
      console.error('خطأ في تحميل الأصناف:', itemsError);
      return [];
    }
  };

  // إعداد المزامنة الدورية عند تغيير إعدادات المزامنة
  useEffect(() => {
    // إلغاء المزامنة الدورية الحالية إن وجدت
    if (syncIntervalRef.current) {
      clearInterval(syncIntervalRef.current);
      syncIntervalRef.current = null;
    }

    // إذا كانت المزامنة مفعلة وتم تحديد عنوان URL ومفتاح API
    if (settings.integrationEnabled && settings.integrationUrl && settings.integrationApiKey) {
      // إعداد المزامنة الدورية
      syncIntervalRef.current = setInterval(() => {
        // تنفيذ المزامنة فقط إذا لم تكن هناك مزامنة جارية
        if (!syncStatus.syncing) {
          syncWithSalesSystem();
        }
      }, settings.syncInterval * 60 * 1000); // تحويل الدقائق إلى مللي ثانية

      // تنفيذ مزامنة أولية
      if (!syncStatus.syncing) {
        syncWithSalesSystem();
      }
    }

    // تنظيف المزامنة عند إلغاء تحميل المكون
    return () => {
      if (syncIntervalRef.current) {
        clearInterval(syncIntervalRef.current);
        syncIntervalRef.current = null;
      }
    };
  }, [settings.integrationEnabled, settings.integrationUrl, settings.integrationApiKey, settings.syncInterval]);

  // إعداد المزامنة الدورية للمخزون
  useEffect(() => {
    // إلغاء المزامنة الدورية الحالية للمخزون إن وجدت
    if (inventorySyncIntervalRef.current) {
      clearInterval(inventorySyncIntervalRef.current);
      inventorySyncIntervalRef.current = null;
    }

    // إعداد المزامنة الدورية للمخزون (كل 30 دقيقة)
    const INVENTORY_SYNC_INTERVAL = 30 * 60 * 1000; // 30 دقيقة بالمللي ثانية

    console.log('إعداد المزامنة الدورية للمخزون كل 30 دقيقة');

    inventorySyncIntervalRef.current = setInterval(() => {
      // تنفيذ المزامنة فقط إذا لم تكن هناك تحميل جاري
      if (!loading) {
        console.log('تنفيذ المزامنة الدورية للمخزون...');
        syncAllInventory()
          .then(() => {
            console.log('تمت المزامنة الدورية للمخزون بنجاح');
          })
          .catch(error => {
            console.error('خطأ في المزامنة الدورية للمخزون:', error);
          });
      }
    }, INVENTORY_SYNC_INTERVAL);

    // تنظيف المزامنة عند إلغاء تحميل المكون
    return () => {
      if (inventorySyncIntervalRef.current) {
        clearInterval(inventorySyncIntervalRef.current);
        inventorySyncIntervalRef.current = null;
      }
    };
  }, []);

  // تحميل البيانات الافتراضية
  const loadDefaultData = () => {
    setLoading(true);

    // بيانات افتراضية للأصناف (فارغة)
    const defaultItems = [];

    // بيانات افتراضية للمخزون (فارغة)
    const defaultInventory = [];

    // بيانات افتراضية للمعاملات (فارغة)
    const defaultTransactions = [];

    // بيانات افتراضية للمستخدمين (مدير النظام فقط)
    const defaultUsers = [
      {
        id: 1,
        username: 'admin',
        full_name: 'مدير النظام',
        role: 'admin',
        created_at: new Date().toISOString()
      }
    ];

    // بيانات افتراضية للعملاء (فارغة)
    const defaultCustomers = [];

    // تم إزالة بيانات الموردين الافتراضية

    // بيانات افتراضية للآلات والمعدات (فارغة)
    const defaultMachines = [];

    setItems(defaultItems);
    setInventory(defaultInventory);
    setTransactions(defaultTransactions);
    setUsers(defaultUsers);
    setCustomers(defaultCustomers);
    // تم إزالة setSuppliers
    setMachines(defaultMachines);
    setLoading(false);
  };

  // إضافة صنف جديد
  const addItem = async (newItem) => {
    try {
      console.log('بدء إضافة صنف جديد:', newItem);

      // تنظيف البيانات الرقمية
      const cleanedItem = {
        ...newItem
      };

      // التحقق مما إذا كان الصنف موجود مسبقًا
      const existingItem = items.find(item =>
        item && item.name && cleanedItem && cleanedItem.name &&
        item.name.toLowerCase() === cleanedItem.name.toLowerCase()
      );

      if (existingItem) {
        console.log('الصنف موجود بالفعل، سيتم تحديثه:', existingItem);

        // تحديث الصنف الموجود بدلاً من إضافة صنف جديد
        const updatedItem = {
          ...existingItem,
          avg_price: !isNaN(cleanedItem.avg_price) ? cleanedItem.avg_price : existingItem.avg_price || 0,
          selling_price: !isNaN(cleanedItem.selling_price) ? cleanedItem.selling_price : existingItem.selling_price || 0,
          minimum_quantity: !isNaN(cleanedItem.minimum_quantity) ? cleanedItem.minimum_quantity : existingItem.minimum_quantity || 0,
          unit: cleanedItem.unit || existingItem.unit || 'قطعة'
        };

        // استدعاء وظيفة تحديث الصنف في قاعدة البيانات
        console.log('جاري تحديث الصنف في قاعدة البيانات:', updatedItem);
        const result = await database.updateItem(updatedItem);
        console.log('تم تحديث الصنف في قاعدة البيانات بنجاح:', result);

        // تحديث الصنف في قائمة الأصناف
        setItems(prevItems =>
          prevItems.map(item => (item.id === existingItem.id || item._id === existingItem._id) ? result : item)
        );

        // تحديث المخزون إذا تم تغيير الكمية الحالية
        if (cleanedItem.current_quantity !== undefined) {
          const currentQuantity = parseInt(cleanedItem.current_quantity) || 0;

          // تم نقل منطق إضافة الكمية الحالية إلى backend، لذا لا داعي لاستدعاء updateInventory هنا
          // await database.updateInventory(result.id || result._id, {
          //   current_quantity: currentQuantity,
          //   last_updated: new Date().toISOString()
          // });

          // تحديث المخزون في الحالة
          setInventory(prevInventory =>
            prevInventory.map(item => {
              if (item.item_id === result.id || item.item_id === result._id) {
                return {
                  ...item,
                  current_quantity: currentQuantity,
                  last_updated: new Date().toISOString()
                };
              }
              return item;
            })
          );
        }

        return result;
      } else {
        console.log('إضافة صنف جديد بالبيانات:', cleanedItem);

        // استدعاء وظيفة إضافة الصنف في قاعدة البيانات
        const result = await database.addItem(cleanedItem);
        console.log('تم إضافة الصنف في قاعدة البيانات بنجاح:', result);

        // إعادة تحميل قائمة الأصناف من قاعدة البيانات
        const updatedItems = await database.getItems();
        setItems(updatedItems);

        // إضافة الصنف إلى المخزون بكمية حالية
        const currentQuantity = parseInt(cleanedItem.current_quantity) || 0;

        // تحديث الكمية الحالية في المخزون
        if (currentQuantity > 0) {
          // تحديث المخزون يتم تلقائيًا في backend
          console.log('تم تحديث المخزون تلقائيًا في backend بكمية:', currentQuantity);
        }

        // تم نقل منطق إضافة الكمية الحالية إلى backend، لذا لا داعي لاستدعاء updateInventory هنا
        // await database.updateInventory(result.id || result._id, {
        //   current_quantity: currentQuantity,
        //   last_updated: new Date().toISOString()
        // });

        // مزامنة المخزون بعد إضافة الصنف (بدون إظهار مؤشر التحميل أو الإشعارات)
        try {
          console.log('مزامنة المخزون بعد إضافة الصنف...');
          await syncAllInventory();
          console.log('تمت مزامنة المخزون بعد إضافة الصنف بنجاح');
        } catch (syncError) {
          console.error('خطأ في مزامنة المخزون بعد إضافة الصنف:', syncError);

          // إعادة تحميل المخزون من قاعدة البيانات في حالة فشل المزامنة
          const updatedInventory = await database.getInventory();
          setInventory(updatedInventory);
        }

        // تم إزالة إضافة معاملة افتتاحية بناءً على الكمية الابتدائية

        return result;
      }
    } catch (error) {
      console.error('خطأ في إضافة/تحديث الصنف:', error);
      throw error;
    }
  };

  // مرجع للتحكم في تكرار مزامنة المخزون
  const syncInventoryDebounceRef = useRef({
    isRunning: false,
    lastSyncTime: 0,
    pendingPromise: null
  });

  // مزامنة المخزون تلقائياً (بدون خيارات للمزامنة اليدوية)
  const syncAllInventory = async (options = {}) => {
    const {
      showLoadingIndicator = false,  // عدم إظهار مؤشر التحميل افتراضياً
      showNotifications = false,     // عدم إظهار الإشعارات افتراضياً
      debounceTime = 2000           // وقت التأخير بين المزامنات المتتالية (2 ثانية)
    } = options;

    const now = Date.now();
    const syncRef = syncInventoryDebounceRef.current;

    // إذا كانت هناك مزامنة جارية، أعد الوعد الحالي
    if (syncRef.isRunning) {
      console.log('هناك مزامنة جارية بالفعل، سيتم إعادة الوعد الحالي');
      return syncRef.pendingPromise;
    }

    // إذا كان الوقت منذ آخر مزامنة أقل من وقت التأخير، أعد الوعد الحالي
    if ((now - syncRef.lastSyncTime < debounceTime) && syncRef.pendingPromise) {
      console.log(`تم طلب المزامنة قبل ${now - syncRef.lastSyncTime}ms، سيتم إعادة الوعد الحالي`);
      return syncRef.pendingPromise;
    }

    // إنشاء وعد جديد للمزامنة
    syncRef.isRunning = true;
    syncRef.lastSyncTime = now;

    // إنشاء وظيفة المزامنة
    const performSync = async () => {
      try {
        console.log('بدء مزامنة المخزون تلقائياً...');

        // تعيين حالة التحميل إذا تم طلب ذلك
        if (showLoadingIndicator) {
          setLoading(true);
        }

        // التحقق من وجود window.api قبل استدعاء وظيفة المزامنة
        if (typeof window === 'undefined' || !window.api) {
          console.error('window.api غير متوفر. محاولة إعادة تحميل الصفحة...');

          // محاولة إعادة تحميل الصفحة بعد فترة قصيرة
          setTimeout(() => {
            try {
              window.location.reload();
            } catch (reloadError) {
              console.error('فشل في إعادة تحميل الصفحة:', reloadError);
            }
          }, 2000);

          throw new Error('فشل في مزامنة المخزون بسبب عدم توفر window.api');
        }

        // محاولة استدعاء وظيفة مزامنة المخزون في قاعدة البيانات مع محاولات متعددة
        let result;
        let attempts = 0;
        const maxAttempts = 3;

        while (attempts < maxAttempts) {
          attempts++;
          try {
            console.log(`محاولة مزامنة المخزون (${attempts}/${maxAttempts})...`);
            result = await database.syncAllInventory();
            console.log('نتيجة مزامنة المخزون:', result);
            break; // الخروج من الحلقة إذا نجحت المزامنة
          } catch (syncError) {
            console.error(`خطأ في محاولة المزامنة ${attempts}/${maxAttempts}:`, syncError);

            if (attempts >= maxAttempts) {
              throw syncError; // إعادة رمي الخطأ إذا فشلت جميع المحاولات
            }

            // انتظار قبل المحاولة التالية
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }

        // إعادة تحميل المخزون من قاعدة البيانات
        let updatedInventory;
        try {
          updatedInventory = await database.getInventory();
          setInventory(updatedInventory);
          console.log('تم تحديث المخزون بنجاح:', updatedInventory.length, 'عنصر');
        } catch (inventoryError) {
          console.error('خطأ في تحميل المخزون المحدث:', inventoryError);
        }

        // إعادة تحميل الأصناف من قاعدة البيانات
        let updatedItems;
        try {
          updatedItems = await database.getItems();
          setItems(updatedItems);
          console.log('تم تحديث الأصناف بنجاح:', updatedItems.length, 'صنف');
        } catch (itemsError) {
          console.error('خطأ في تحميل الأصناف المحدثة:', itemsError);
        }

        // إنهاء حالة التحميل إذا تم طلب ذلك
        if (showLoadingIndicator) {
          setLoading(false);
        }

        // إظهار إشعار بنجاح المزامنة إذا تم طلب ذلك
        if (showNotifications) {
          showNotification('تمت مزامنة المخزون بنجاح', 'success');
        }

        // تحديث حالة المزامنة
        syncRef.isRunning = false;

        // تحديث حالة المزامنة في التخزين المحلي
        try {
          localStorage.setItem('lastSyncTime', new Date().toISOString());
          setSyncStatus(prev => ({
            ...prev,
            lastSyncTime: new Date().toISOString(),
            lastSyncStatus: 'success',
            error: null
          }));
        } catch (storageError) {
          console.error('خطأ في تحديث حالة المزامنة في التخزين المحلي:', storageError);
        }

        return result;
      } catch (error) {
        console.error('خطأ في مزامنة المخزون:', error);

        // إنهاء حالة التحميل إذا تم طلب ذلك
        if (showLoadingIndicator) {
          setLoading(false);
        }

        // إظهار إشعار بفشل المزامنة إذا تم طلب ذلك
        if (showNotifications) {
          showNotification(`فشل في مزامنة المخزون: ${error.message}`, 'error');
        }

        // تحديث حالة المزامنة
        syncRef.isRunning = false;

        // تحديث حالة المزامنة في التخزين المحلي
        try {
          setSyncStatus(prev => ({
            ...prev,
            lastSyncStatus: 'error',
            error: error.message
          }));
        } catch (storageError) {
          console.error('خطأ في تحديث حالة المزامنة في التخزين المحلي:', storageError);
        }

        throw error;
      }
    };

    // تخزين الوعد وإعادته
    syncRef.pendingPromise = performSync();
    return syncRef.pendingPromise;
  };

  // تحديث صنف موجود
  const updateItem = async (updatedItem) => {
    try {
      console.log('بدء تحديث الصنف:', updatedItem);

      // التحقق من وجود معاملات بيع مرتبطة بالصنف
      const itemId = updatedItem.id || updatedItem._id;
      const hasSalesTransactions = transactions.some(t => {
        // التحقق من وجود t وحقوله قبل استخدامها
        if (!t) return false;

        // التحقق من معرف الصنف بجميع الطرق المحتملة
        const isItemMatch =
          (t.item_id === itemId) ||
          (t.item_id && typeof t.item_id === 'object' && t.item_id._id === itemId) ||
          (t.item_id && typeof t.item_id === 'object' && t.item_id.id === itemId) ||
          (t.item && t.item._id === itemId) ||
          (t.item && t.item.id === itemId);

        // التحقق من نوع المعاملة
        const isTypeMatch = t.transaction_type === 'sale' || t.transaction_type === 'withdrawal';

        return isItemMatch && isTypeMatch;
      });

      // إذا كان الصنف له معاملات بيع، نتحقق من صلاحيات المستخدم
      if (hasSalesTransactions) {
        // الحصول على دور المستخدم الحالي
        const currentUserRole = localStorage.getItem('currentUserRole');

        // إذا كان المستخدم مشاهدًا، فلا يمكنه تعديل الأصناف
        if (currentUserRole === 'viewer') {
          console.log('المستخدم مشاهد ولا يمكنه تعديل الأصناف');
          throw new Error('ليس لديك صلاحية لتعديل الأصناف. المشاهد يمكنه فقط عرض البيانات.');
        }

        // إذا كان المستخدم موظفًا، نتحقق من صلاحية تعديل الأصناف المحمية
        if (currentUserRole === 'employee') {
          console.log('المستخدم موظف ويحاول تعديل صنف محمي (له معاملات بيع)');

          // التحقق من صلاحية تعديل الأصناف المحمية
          const hasPermission = checkCurrentUserPermission('update_item_with_sales');
          if (!hasPermission) {
            console.log('المستخدم ليس لديه صلاحية تعديل الأصناف المحمية');
            throw new Error('ليس لديك صلاحية لتعديل هذا الصنف لأنه مرتبط بعمليات بيع.');
          }

          console.log('المستخدم لديه صلاحية تعديل الأصناف المحمية');
        }
      } else {
        // إذا لم يكن الصنف له معاملات بيع، نتحقق فقط من أن المستخدم ليس مشاهدًا
        const currentUserRole = localStorage.getItem('currentUserRole');
        if (currentUserRole === 'viewer') {
          console.log('المستخدم مشاهد ولا يمكنه تعديل الأصناف');
          throw new Error('ليس لديك صلاحية لتعديل الأصناف. المشاهد يمكنه فقط عرض البيانات.');
        }
      }

      // تنظيف البيانات وإرسال فقط الحقول المطلوبة للتحديث
      // في قسم الأصناف، نحتاج فقط إلى تحديث الاسم ووحدة القياس

      // استخدام معرف الصنف المستخرج سابقًا

      // إنشاء كائن جديد بدلاً من تعديل الكائن الأصلي
      const cleanedItem = {
        // إضافة الاسم (سواء كان موجودًا أم لا)
        name: updatedItem.name !== undefined && updatedItem.name !== null ? updatedItem.name.trim() : '',

        // إضافة وحدة القياس (سواء كانت موجودة أم لا)
        unit: updatedItem.unit !== undefined && updatedItem.unit !== null ? updatedItem.unit.trim() : ''
      };

      console.log('معرف الصنف للتحديث:', itemId);
      console.log('بيانات التحديث المنظفة:', cleanedItem);

      // لا نحتاج إلى التحقق من وجود تحديثات لأننا سنترك ذلك للخادم
      // الخادم سيستخدم القيم الحالية إذا كانت القيم المرسلة فارغة

      // استدعاء وظيفة تحديث الصنف في قاعدة البيانات
      console.log('جاري تحديث الصنف في قاعدة البيانات:', cleanedItem);

      // التأكد من وجود معرف الصنف
      if (!itemId) {
        console.error('معرف الصنف غير موجود:', updatedItem);
        throw new Error('معرف الصنف غير موجود');
      }

      // تسجيل معلومات تشخيصية إضافية
      console.log('نوع معرف الصنف:', typeof itemId);
      console.log('قيمة معرف الصنف:', itemId);
      console.log('اسم الصنف:', cleanedItem.name);
      console.log('وحدة القياس:', cleanedItem.unit);

      // إنشاء كائن جديد يحتوي على معرف الصنف والبيانات المنظفة
      const itemToUpdate = {
        id: itemId,
        ...cleanedItem
      };

      console.log('الكائن النهائي للتحديث:', itemToUpdate);

      const result = await database.updateItem(itemToUpdate);
      console.log('تم تحديث الصنف في قاعدة البيانات بنجاح:', result);

      // تحديث الصنف في قائمة الأصناف
      setItems(prevItems =>
        prevItems.map(item => (item.id === updatedItem.id || item._id === updatedItem._id) ? result : item)
      );

      // تحديث الصنف في المخزون
      setInventory(prevInventory =>
        prevInventory.map(item => {
          if (item.item_id === updatedItem.id || item.item_id === updatedItem._id) {
            return {
              ...item,
              name: result.name,
              unit: result.unit,
              minimum_quantity: result.minimum_quantity,
              avg_price: result.avg_price,
              selling_price: result.selling_price,
              // Make sure we preserve the current_quantity
              current_quantity: item.current_quantity,
              last_updated: new Date().toISOString()
            };
          }
          return item;
        })
      );

      // بدء مزامنة المخزون في الخلفية (بدون انتظار)
      setTimeout(() => {
        console.log('بدء مزامنة المخزون في الخلفية بعد تحديث الصنف...');
        syncAllInventory({
          showLoadingIndicator: false,
          showNotifications: false
        }).catch(error => {
          console.error('خطأ في مزامنة المخزون في الخلفية بعد تحديث الصنف:', error);
        });
      }, 1000);

      return result;
    } catch (error) {
      console.error('خطأ في تحديث الصنف:', error);
      throw error;
    }
  };

  // حذف صنف
  const deleteItem = async (itemId, forceDelete = false) => {
    console.log('Deleting item with ID:', itemId, 'Force delete:', forceDelete);

    try {
      // التحقق من وجود معاملات بيع مرتبطة بالصنف
      const hasSalesTransactions = transactions.some(t => {
        // التحقق من وجود t وحقوله قبل استخدامها
        if (!t) return false;

        // التحقق من معرف الصنف بجميع الطرق المحتملة
        const isItemMatch =
          (t.item_id === itemId) ||
          (t.item_id && typeof t.item_id === 'object' && t.item_id._id === itemId) ||
          (t.item_id && typeof t.item_id === 'object' && t.item_id.id === itemId) ||
          (t.item && t.item._id === itemId) ||
          (t.item && t.item.id === itemId);

        // التحقق من نوع المعاملة
        const isTypeMatch = t.transaction_type === 'sale' || t.transaction_type === 'withdrawal';

        return isItemMatch && isTypeMatch;
      });

      // إذا كان الصنف له معاملات بيع، نتحقق من صلاحيات المستخدم
      if (hasSalesTransactions) {
        // الحصول على معرف المستخدم الحالي ودوره
        const currentUserRole = localStorage.getItem('currentUserRole');
        console.log('دور المستخدم الحالي:', currentUserRole);

        // إذا كان المستخدم مديرًا، يمكنه حذف الأصناف المحمية
        if (currentUserRole === 'admin' || currentUserRole === 'manager') {
          console.log('المستخدم مدير ويمكنه حذف الأصناف المحمية');
          forceDelete = true;
        } else {
          console.log('المستخدم ليس مديرًا ولا يمكنه حذف الأصناف المحمية');
          throw new Error('لا يمكنك حذف هذا الصنف لأنه مرتبط بعمليات بيع. فقط المدير يمكنه حذف الأصناف المحمية.');
        }
      } else {
        // إذا لم يكن الصنف له معاملات بيع، يمكن لأي مستخدم حذفه (باستثناء المشاهد)
        const currentUserRole = localStorage.getItem('currentUserRole');
        if (currentUserRole === 'viewer') {
          console.log('المستخدم مشاهد ولا يمكنه حذف الأصناف');
          throw new Error('ليس لديك صلاحية لحذف الأصناف. المشاهد يمكنه فقط عرض البيانات.');
        }

        forceDelete = false;
      }

      // حذف الصنف من قاعدة البيانات (استخدام await للانتظار حتى اكتمال العملية)
      const result = await database.deleteItem(itemId, forceDelete);
      console.log('Item deleted from database successfully:', result);

      // حذف الصنف من قائمة الأصناف
      setItems(prevItems => prevItems.filter(item =>
        item.id !== itemId &&
        item._id !== itemId
      ));

      // حذف الصنف من المخزون
      setInventory(prevInventory => prevInventory.filter(item =>
        item.item_id !== itemId &&
        item.id !== itemId &&
        item._id !== itemId
      ));

      // مزامنة المخزون بعد حذف الصنف (بدون إظهار مؤشر التحميل أو الإشعارات)
      try {
        console.log('مزامنة المخزون بعد حذف الصنف...');
        await syncAllInventory();
        console.log('تمت مزامنة المخزون بعد حذف الصنف بنجاح');
      } catch (syncError) {
        console.error('خطأ في مزامنة المخزون بعد حذف الصنف:', syncError);
      }

      // تحديث قائمة المعاملات (إزالة المعاملات المرتبطة بالصنف المحذوف)
      if (forceDelete) {
        // إذا كان الحذف قسري (للمدير فقط)، قم بحذف جميع المعاملات المرتبطة
        setTransactions(prevTransactions => prevTransactions.filter(t => {
          // التحقق من جميع الحالات المحتملة للمعرف
          const isItemTransaction = t && (
            (t.item_id === itemId) ||
            (t.item_id && typeof t.item_id === 'object' && t.item_id._id === itemId) ||
            (t.item_id && typeof t.item_id === 'object' && t.item_id.id === itemId) ||
            (t.item && t.item._id === itemId) ||
            (t.item && t.item.id === itemId)
          );

          // الاحتفاظ بالمعاملات التي ليست مرتبطة بالصنف المحذوف
          return !isItemTransaction;
        }));
      } else {
        // إذا كان الحذف عادي، قم بحذف معاملات الشراء فقط
        setTransactions(prevTransactions => prevTransactions.filter(t => {
          // التحقق من جميع الحالات المحتملة للمعرف
          const isItemTransaction = t && (
            (t.item_id === itemId) ||
            (t.item_id && typeof t.item_id === 'object' && t.item_id._id === itemId) ||
            (t.item_id && typeof t.item_id === 'object' && t.item_id.id === itemId) ||
            (t.item && t.item._id === itemId) ||
            (t.item && t.item.id === itemId)
          );

          // إذا كانت المعاملة مرتبطة بالصنف المحذوف وهي معاملة شراء، قم بحذفها
          if (isItemTransaction && (t.transaction_type === 'purchase' || t.transaction_type === 'receiving')) {
            return false;
          }

          // الاحتفاظ بجميع المعاملات الأخرى
          return true;
        }));
      }

      return true;
    } catch (error) {
      console.error('Error in deleteItem function:', error);
      throw error;
    }
  };

  // إضافة معاملة جديدة (شراء، بيع)
  const addTransaction = async (transaction) => {
    try {
      console.log('استلام طلب إضافة معاملة:', transaction);

      // التحقق من وجود المعاملة بشكل أكثر تفصيلاً
      if (!transaction) {
        console.error('المعاملة غير موجودة (قيمة فارغة)');
        throw new Error('المعاملة غير موجودة أو غير صالحة');
      }

      // التحقق من أن المعاملة كائن وليست قيمة بدائية
      if (typeof transaction !== 'object') {
        console.error('المعاملة ليست كائناً صالحاً:', typeof transaction);
        throw new Error('المعاملة ليست بالتنسيق الصحيح');
      }

      // تحميل البيانات إذا كان المخزون أو الأصناف فارغة
      if (!inventory || inventory.length === 0 || !items || items.length === 0) {
        console.log('المخزون أو قائمة الأصناف فارغة، جاري تحميل البيانات...');
        await loadData();
      }

      console.log('بدء إضافة معاملة جديدة:', transaction);

      // التحقق من وجود معرف الصنف
      if (!transaction.item_id) {
        console.error('معرف الصنف غير موجود في المعاملة:', transaction);
        throw new Error('معرف الصنف غير موجود في المعاملة');
      }

      // تحويل معرف الصنف إلى نص للمقارنة
      const transactionItemId = String(transaction.item_id);
      console.log('معرف الصنف:', transactionItemId, 'نوع المعرف:', typeof transaction.item_id);

      // التحقق من حالة المخزون
      if (!inventory || !Array.isArray(inventory) || inventory.length === 0) {
        console.log('المخزون فارغ أو غير محمل، جاري تحميل المخزون...');
        // يمكن إضافة منطق لتحميل المخزون هنا إذا لزم الأمر
        // على سبيل المثال: await fetchInventory();
      } else {
        console.log('عدد عناصر المخزون:', inventory.length);
      }

      // البحث عن الصنف في المخزون بطريقة أكثر مرونة
      let inventoryItem = null;

      // محاولة البحث باستخدام المعرف المباشر أولاً
      inventoryItem = inventory.find(item => {
        const itemId = String(item.id || '');
        const itemItemId = String(item.item_id || '');
        const item_id = String(item._id || '');

        return (
          itemId === transactionItemId ||
          itemItemId === transactionItemId ||
          item_id === transactionItemId
        );
      });

      // إذا لم يتم العثور على الصنف، نحاول البحث باستخدام الاسم
      if (!inventoryItem && transaction.item_name) {
        console.log('محاولة البحث عن الصنف باستخدام الاسم:', transaction.item_name);
        inventoryItem = inventory.find(item => item.name === transaction.item_name);
      }

      console.log('العنصر الموجود في المخزون:', inventoryItem);

      // إذا لم يتم العثور على الصنف في المخزون، نبحث في قائمة الأصناف
      if (!inventoryItem) {
        console.log('الصنف غير موجود في المخزون. جاري البحث في قائمة الأصناف...');

        // البحث عن الصنف في قائمة الأصناف
        const itemFromItems = items.find(item => {
          const itemId = String(item.id || '');
          const item_id = String(item._id || '');

          return (
            itemId === transactionItemId ||
            item_id === transactionItemId ||
            (transaction.item_name && item.name === transaction.item_name)
          );
        });

        if (itemFromItems) {
          console.log('تم العثور على الصنف في قائمة الأصناف ولكن ليس في المخزون:', itemFromItems);

          // إضافة الصنف إلى المخزون تلقائيًا
          const newInventoryItem = {
            id: itemFromItems.id || itemFromItems._id,
            _id: itemFromItems._id || itemFromItems.id,
            item_id: itemFromItems.id || itemFromItems._id,
            name: itemFromItems.name,
            unit: itemFromItems.unit || 'قطعة',
            current_quantity: 0,
            minimum_quantity: itemFromItems.minimum_quantity || 0,
            avg_price: itemFromItems.avg_price || 0,
            selling_price: itemFromItems.selling_price || 0,
            last_updated: new Date().toISOString()
          };

          console.log('إضافة الصنف إلى المخزون تلقائيًا:', newInventoryItem);

          // إضافة الصنف إلى المخزون
          setInventory(prev => [...prev, newInventoryItem]);

          // استخدام الصنف المضاف للمعاملة
          inventoryItem = newInventoryItem;
        } else {
          console.error('الصنف غير موجود في قائمة الأصناف أو المخزون:', transactionItemId);
          throw new Error('الصنف غير موجود في النظام');
        }
      }

      // التحقق من صحة معاملة البيع أو السحب
      if (transaction.transaction_type === 'sale' || transaction.transaction_type === 'withdrawal') {
        // التحقق من الكمية المتوفرة - لا نسمح بالبيع إلا إذا كانت الكمية 1 أو أكبر
        if (inventoryItem.current_quantity < 1) {
          throw new Error(`الكمية المتوفرة صفر لهذا الصنف`);
        }

        // التحقق من الكمية المطلوبة
        if (transaction.quantity > inventoryItem.current_quantity) {
          throw new Error(`الكمية المطلوبة (${transaction.quantity}) أكبر من الكمية المتوفرة (${inventoryItem.current_quantity})`);
        }

        // حساب الربح (فقط لمعاملات البيع)
        if (transaction.transaction_type === 'sale') {
          const profit = (transaction.price - inventoryItem.avg_price) * transaction.quantity;
          transaction.profit = profit;
        }
      }

      // التحقق من وجود علامة تجاهل تحديث المخزون
      const skipInventoryUpdate = transaction.skip_inventory_update || false;
      console.log(`معاملة ${transaction.transaction_type} مع علامة تجاهل تحديث المخزون:`, skipInventoryUpdate);

      // إعداد بيانات المعاملة للإرسال إلى قاعدة البيانات
      const dbTransaction = {
        ...transaction,
        transaction_date: transaction.transaction_date || new Date().toISOString(),
        user_id: transaction.user_id || 1, // يمكن تغييره لاحقًا عندما نضيف نظام المستخدمين
        user_name: transaction.user_name || 'admin', // يمكن تغييره لاحقًا عندما نضيف نظام المستخدمين
        item_name: inventoryItem.name, // إضافة اسم الصنف للمعاملة
        skip_inventory_update: skipInventoryUpdate // تأكيد إرسال علامة تجاهل تحديث المخزون
      };

      // تأكد من أن معرف الصنف متوافق مع المخزون
      dbTransaction.item_id = inventoryItem.id || inventoryItem._id || inventoryItem.item_id;

      // إضافة المعاملة إلى قاعدة البيانات
      console.log('جاري إضافة المعاملة إلى قاعدة البيانات:', dbTransaction);
      const result = await database.addTransaction(dbTransaction);
      console.log('تم إضافة المعاملة بنجاح:', result);

      // التحقق من أن النتيجة تحتوي على بيانات المعاملة
      if (!result) {
        console.error('النتيجة غير موجودة:', result);
        throw new Error('فشل في إضافة المعاملة: النتيجة غير موجودة');
      }

      // استخراج بيانات المعاملة من النتيجة
      const addedTransaction = result.transaction || result;

      // إضافة المعاملة إلى قائمة المعاملات مباشرة (بدون إعادة تحميل)
      // تأكد من أن المعاملة تحتوي على جميع البيانات المطلوبة
      const completeTransaction = {
        ...addedTransaction,
        id: addedTransaction.id || addedTransaction._id,
        _id: addedTransaction._id || addedTransaction.id,
        item_id: addedTransaction.item_id,
        item_name: addedTransaction.item_name || inventoryItem.name,
        transaction_type: addedTransaction.transaction_type || dbTransaction.transaction_type,
        quantity: addedTransaction.quantity || dbTransaction.quantity,
        price: addedTransaction.price || dbTransaction.price,
        total_price: addedTransaction.total_price || (dbTransaction.price * dbTransaction.quantity),
        transaction_date: addedTransaction.transaction_date || dbTransaction.transaction_date || new Date().toISOString()
      };

      console.log('إضافة المعاملة المكتملة إلى الحالة:', completeTransaction);
      setTransactions(prevTransactions => [completeTransaction, ...prevTransactions]);

      // التحقق مما إذا كان يجب تجاهل تحديث المخزون
      if (skipInventoryUpdate) {
        console.log('تم تجاهل تحديث المخزون بناءً على العلامة skip_inventory_update');
      } else {
        // تحديث المخزون بناءً على نوع المعاملة
        const itemIdToUpdate = inventoryItem.item_id || inventoryItem._id || inventoryItem.id;
        const newQuantity = transaction.transaction_type === 'purchase'
          ? inventoryItem.current_quantity + transaction.quantity
          : inventoryItem.current_quantity - transaction.quantity;

        // تحديث المخزون في الحالة مباشرة (بدون مزامنة)
        setInventory(prevInventory =>
          prevInventory.map(item => {
            if (item.item_id === itemIdToUpdate || item._id === itemIdToUpdate || item.id === itemIdToUpdate) {
              let newAvgPrice = item.avg_price;

              // تحديث متوسط السعر في حالة الشراء
              if (transaction.transaction_type === 'purchase') {
                if (item.current_quantity === 0) {
                  // إذا كانت الكمية صفر، فإن متوسط السعر هو السعر الجديد
                  newAvgPrice = transaction.price;
                } else {
                  // حساب متوسط السعر الجديد
                  const oldTotalValue = item.avg_price * item.current_quantity;
                  const newValue = transaction.price * transaction.quantity;
                  const updatedQuantity = item.current_quantity + transaction.quantity;
                  newAvgPrice = (oldTotalValue + newValue) / updatedQuantity;
                }
              }

              return {
                ...item,
                current_quantity: newQuantity,
                avg_price: transaction.transaction_type === 'purchase' ? newAvgPrice : item.avg_price,
                // تحديث سعر البيع إذا كانت المعاملة شراء وتم تحديد سعر البيع
                selling_price: transaction.transaction_type === 'purchase' && transaction.selling_price ?
                  parseFloat(transaction.selling_price) : item.selling_price,
                // تحديث الحد الأدنى إذا كانت المعاملة شراء وتم تحديد الحد الأدنى
                minimum_quantity: transaction.transaction_type === 'purchase' && transaction.minimum_quantity !== undefined ?
                  parseFloat(transaction.minimum_quantity) : item.minimum_quantity,
                last_updated: new Date().toISOString()
              };
            }
            return item;
          })
        );
      }

      // إذا كانت معاملة بيع وتحتوي على معرف العميل، قم بتحديث سجل المبيعات للعميل
      if (transaction.transaction_type === 'sale' && transaction.customer) {
        try {
          // تحديث سجل مبيعات العميل
          await updateCustomerSalesHistory(transaction.customer, result);
        } catch (error) {
          console.error('خطأ في تحديث سجل مبيعات العميل:', error);
          // لا نريد إيقاف العملية إذا فشل تحديث سجل المبيعات
        }
      }

      return result;
    } catch (error) {
      console.error('خطأ في إضافة المعاملة:', error);
      throw error;
    }
  };

  // تحديث سجل المبيعات للعميل - نظام الفاتورة الواحدة
  const updateCustomerSalesHistory = async (customerId, transaction) => {
    console.log('تحديث سجل مبيعات العميل (نظام الفاتورة الواحدة):', customerId);

    try {
      // البحث عن العميل
      const customer = customers.find(c => c.id === customerId || c._id === customerId);
      if (!customer) {
        console.error('العميل غير موجود:', customerId);
        throw new Error('العميل غير موجود');
      }

      // الحصول على سجل مبيعات العميل من قاعدة البيانات
      const customerSalesHistory = await window.api.invoke('get-customer-sales', customerId);

      // إذا لم يتم العثور على سجل مبيعات، نقوم بإنشاء سجل جديد
      if (!customerSalesHistory || !Array.isArray(customerSalesHistory.sales)) {
        console.log('لم يتم العثور على سجل مبيعات للعميل، جاري إنشاء سجل جديد...');

        // إنشاء سجل مبيعات جديد
        const saleRecord = {
          transaction_id: transaction.id,
          item_id: transaction.item_id,
          item_name: transaction.item_name,
          quantity: transaction.quantity,
          price: transaction.price,
          total_price: transaction.price * transaction.quantity,
          profit: transaction.profit || 0,
          invoice_number: transaction.invoice_number || `INV-CUSTOMER-${customerId}`,
          transaction_date: transaction.transaction_date,
          notes: transaction.notes || ''
        };

        // تحديث العميل في الحالة المحلية
        const updatedCustomer = {
          ...customer,
          sales_history: [saleRecord],
          total_sales: saleRecord.total_price,
          total_profit: saleRecord.profit,
          invoice_number: `INV-CUSTOMER-${customerId}`
        };

        // تحديث حالة العملاء
        setCustomers(prevCustomers =>
          prevCustomers.map(c =>
            (c.id === customerId || c._id === customerId) ? updatedCustomer : c
          )
        );

        console.log('تم تحديث سجل مبيعات العميل بنجاح');
        return updatedCustomer;
      }

      // إضافة المعاملة الجديدة إلى سجل المبيعات
      const saleRecord = {
        transaction_id: transaction.id,
        item_id: transaction.item_id,
        item_name: transaction.item_name,
        quantity: transaction.quantity,
        price: transaction.price,
        total_price: transaction.price * transaction.quantity,
        profit: transaction.profit || 0,
        invoice_number: transaction.invoice_number || `INV-CUSTOMER-${customerId}`,
        transaction_date: transaction.transaction_date,
        notes: transaction.notes || ''
      };

      // تحديث سجل المبيعات
      const updatedSalesHistory = [saleRecord, ...customerSalesHistory.sales];

      // حساب إجمالي المبيعات والربح
      const totalSales = updatedSalesHistory.reduce((sum, sale) => sum + (sale.total_price || 0), 0);
      const totalProfit = updatedSalesHistory.reduce((sum, sale) => sum + (sale.profit || 0), 0);

      // تحديث بيانات العميل
      const updatedCustomer = {
        ...customer,
        sales_history: updatedSalesHistory,
        total_sales: totalSales,
        total_profit: totalProfit,
        invoice_number: `INV-CUSTOMER-${customerId}`
      };

      // تحديث حالة العملاء
      setCustomers(prevCustomers =>
        prevCustomers.map(c =>
          (c.id === customerId || c._id === customerId) ? updatedCustomer : c
        )
      );

      console.log('تم تحديث سجل مبيعات العميل بنجاح');
      return updatedCustomer;
    } catch (error) {
      console.error('خطأ في تحديث سجل مبيعات العميل:', error);
      // لا نريد إيقاف العملية إذا فشل تحديث سجل المبيعات
      return null;
    }
  };

  // تحديث المخزون بناءً على المعاملة (تم دمجها في وظيفة addTransaction)
  // تم الاحتفاظ بالتعليق للتوثيق فقط

  // الحصول على معاملات صنف معين
  const getItemTransactions = (itemId) => {
    return transactions.filter(transaction => {
      if (!transaction) return false;

      return (
        (transaction.item_id === itemId) ||
        (transaction.item_id && typeof transaction.item_id === 'object' && transaction.item_id._id === itemId) ||
        (transaction.item_id && typeof transaction.item_id === 'object' && transaction.item_id.id === itemId) ||
        (transaction.item && transaction.item._id === itemId) ||
        (transaction.item && transaction.item.id === itemId)
      );
    });
  };

  // الحصول على معاملات من نوع معين
  const getTransactionsByType = (type) => {
    return transactions.filter(transaction => transaction && transaction.transaction_type === type);
  };

  // حساب الأرباح
  const calculateProfits = () => {
    const now = new Date();
    const oneYearAgo = new Date(now);
    oneYearAgo.setFullYear(now.getFullYear() - 1);

    const sixMonthsAgo = new Date(now);
    sixMonthsAgo.setMonth(now.getMonth() - 6);

    const threeMonthsAgo = new Date(now);
    threeMonthsAgo.setMonth(now.getMonth() - 3);

    const nineMonthsAgo = new Date(now);
    nineMonthsAgo.setMonth(now.getMonth() - 9);

    // الحصول على معاملات البيع
    const salesTransactions = transactions.filter(t => t && t.transaction_type === 'sale');

    // حساب الربح الربع سنوي
    const quarterlyProfit = salesTransactions
      .filter(t => new Date(t.transaction_date) >= threeMonthsAgo)
      .reduce((total, t) => total + (t.profit || 0), 0);

    // حساب الربح النصف سنوي
    const halfYearlyProfit = salesTransactions
      .filter(t => new Date(t.transaction_date) >= sixMonthsAgo)
      .reduce((total, t) => total + (t.profit || 0), 0);

    // حساب الربح الثلاثة أرباع سنوي
    const threeQuartersProfit = salesTransactions
      .filter(t => new Date(t.transaction_date) >= nineMonthsAgo)
      .reduce((total, t) => total + (t.profit || 0), 0);

    // حساب الربح السنوي
    const yearlyProfit = salesTransactions
      .filter(t => new Date(t.transaction_date) >= oneYearAgo)
      .reduce((total, t) => total + (t.profit || 0), 0);

    // استخدام قيمة الربح من الخزينة إذا كانت متوفرة
    if (cashbox && cashbox.profit_total !== undefined) {
      console.log('استخدام قيمة الربح من الخزينة:', cashbox.profit_total);

      // تحديث قيم الأرباح بناءً على نسبة الربح في الخزينة
      const totalProfitFromTransactions = salesTransactions.reduce((total, t) => total + (t.profit || 0), 0);

      // إذا كان هناك أرباح في المعاملات، نحسب نسبة الربح في الخزينة إلى الربح في المعاملات
      if (totalProfitFromTransactions > 0) {
        // حساب نسبة الربح في الخزينة إلى الربح في المعاملات
        // هذه النسبة تستخدم لتعديل قيم الأرباح للفترات المختلفة
        const profitRatio = cashbox.profit_total / totalProfitFromTransactions;

        // تحديث قيم الأرباح بناءً على النسبة
        // نضرب كل قيمة ربح محسوبة من المعاملات بنسبة الربح لتعكس القيمة الحقيقية في الخزينة
        return {
          quarterly: quarterlyProfit * profitRatio,
          halfYearly: halfYearlyProfit * profitRatio,
          threeQuarters: threeQuartersProfit * profitRatio,
          yearly: cashbox.profit_total // استخدام قيمة الربح الإجمالي من الخزينة
        };
      }

      // إذا لم تكن هناك أرباح في المعاملات، نستخدم قيمة الربح من الخزينة
      // ونقسمها بنسب مختلفة للحصول على قيم الأرباح للفترات المختلفة
      return {
        quarterly: cashbox.profit_total / 4, // تقسيم الربح الإجمالي على 4 للحصول على الربح الربع سنوي
        halfYearly: cashbox.profit_total / 2, // تقسيم الربح الإجمالي على 2 للحصول على الربح النصف سنوي
        threeQuarters: cashbox.profit_total * 0.75, // 75% من الربح الإجمالي
        yearly: cashbox.profit_total // الربح الإجمالي
      };
    }

    // إذا لم تكن هناك قيمة للربح في الخزينة، نستخدم القيم المحسوبة من المعاملات
    return {
      quarterly: quarterlyProfit,
      halfYearly: halfYearlyProfit,
      threeQuarters: threeQuartersProfit,
      yearly: yearlyProfit
    };
  };

  // إضافة مستخدم جديد
  const addUser = async (newUser) => {
    try {
      console.log('بدء إضافة مستخدم جديد:', newUser);

      // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
      const existingUser = users.find(user => user.username === newUser.username);
      if (existingUser) {
        console.error('اسم المستخدم موجود بالفعل:', existingUser);
        throw new Error('اسم المستخدم موجود بالفعل');
      }

      // التحقق من صحة الدور
      if (!['admin', 'employee', 'viewer'].includes(newUser.role)) {
        console.error('دور المستخدم غير صالح:', newUser.role);
        throw new Error('دور المستخدم غير صالح. الأدوار المتاحة: مدير (admin)، موظف (employee)، مشاهد (viewer)');
      }

      // التحقق من وجود كلمة مرور
      if (!newUser.password) {
        console.error('كلمة المرور مطلوبة');
        throw new Error('كلمة المرور مطلوبة');
      }

      // إضافة المستخدم باستخدام قاعدة البيانات
      console.log('جاري إضافة المستخدم إلى قاعدة البيانات...');
      const userData = {
        username: newUser.username,
        password: newUser.password, // سيتم تشفيرها في الخلفية
        full_name: newUser.full_name || '',
        role: newUser.role,
        created_at: new Date().toISOString()
      };

      // استدعاء دالة إضافة المستخدم في قاعدة البيانات
      const addedUser = await database.addUser(userData);
      console.log('تمت إضافة المستخدم بنجاح:', addedUser);

      // إضافة حقل id للتوافق مع الواجهة الأمامية
      const userWithId = {
        ...addedUser,
        id: addedUser._id || addedUser.id
      };

      // تحديث حالة المستخدمين
      setUsers(prevUsers => [...prevUsers, userWithId]);
      console.log('تم تحديث حالة المستخدمين');

      return userWithId;
    } catch (error) {
      console.error('خطأ في إضافة المستخدم:', error);
      throw error;
    }
  };

  // تحديث مستخدم موجود
  const updateUser = async (updatedUser) => {
    try {
      console.log('بدء تحديث المستخدم:', updatedUser);

      // التحقق من عدم وجود مستخدم آخر بنفس اسم المستخدم
      const existingUser = users.find(user =>
        user.username === updatedUser.username &&
        user.id !== updatedUser.id &&
        user._id !== updatedUser._id
      );

      if (existingUser) {
        console.error('اسم المستخدم موجود بالفعل:', existingUser);
        throw new Error('اسم المستخدم موجود بالفعل');
      }

      // التحقق من صحة الدور
      if (!['admin', 'employee', 'viewer'].includes(updatedUser.role)) {
        console.error('دور المستخدم غير صالح:', updatedUser.role);
        throw new Error('دور المستخدم غير صالح. الأدوار المتاحة: مدير (admin)، موظف (employee)، مشاهد (viewer)');
      }

      // إعداد بيانات المستخدم للتحديث
      const userData = {
        _id: updatedUser._id || updatedUser.id,
        username: updatedUser.username,
        full_name: updatedUser.full_name || '',
        role: updatedUser.role
      };

      // إضافة كلمة المرور إذا تم توفيرها
      if (updatedUser.password) {
        userData.password = updatedUser.password;
      }

      // استدعاء دالة تحديث المستخدم في قاعدة البيانات
      console.log('جاري تحديث المستخدم في قاعدة البيانات...');
      const updatedUserFromDB = await database.updateUser(userData);
      console.log('تم تحديث المستخدم بنجاح:', updatedUserFromDB);

      // إضافة حقل id للتوافق مع الواجهة الأمامية
      const userWithId = {
        ...updatedUserFromDB,
        id: updatedUserFromDB._id || updatedUserFromDB.id
      };

      // تحديث حالة المستخدمين
      setUsers(prevUsers =>
        prevUsers.map(user =>
          (user.id === userWithId.id || user._id === userWithId._id) ? userWithId : user
        )
      );
      console.log('تم تحديث حالة المستخدمين');

      return userWithId;
    } catch (error) {
      console.error('خطأ في تحديث المستخدم:', error);
      throw error;
    }
  };

  // حذف مستخدم
  const deleteUser = async (userId) => {
    try {
      console.log('بدء حذف المستخدم بالمعرف:', userId);

      // التحقق من عدم حذف المستخدم الوحيد بدور مدير
      const adminUsers = users.filter(user => user.role === 'admin');
      const userToDelete = users.find(user => user.id === userId || user._id === userId);

      if (!userToDelete) {
        console.error('المستخدم غير موجود:', userId);
        throw new Error('المستخدم غير موجود');
      }

      if (adminUsers.length === 1 && (adminUsers[0].id === userId || adminUsers[0]._id === userId)) {
        console.error('محاولة حذف المستخدم المدير الوحيد');
        throw new Error('لا يمكن حذف المستخدم المدير الوحيد');
      }

      // استدعاء دالة حذف المستخدم في قاعدة البيانات
      console.log('جاري حذف المستخدم من قاعدة البيانات...');
      const result = await database.deleteUser(userToDelete._id || userToDelete.id);
      console.log('تم حذف المستخدم بنجاح:', result);

      // تحديث حالة المستخدمين
      setUsers(prevUsers => prevUsers.filter(user =>
        user.id !== userId && user._id !== userId
      ));
      console.log('تم تحديث حالة المستخدمين');

      return true;
    } catch (error) {
      console.error('خطأ في حذف المستخدم:', error);
      throw error;
    }
  };

  // التحقق من صلاحيات المستخدم
  const checkUserPermission = (userId, action) => {
    // البحث عن المستخدم في قائمة المستخدمين
    const user = users.find(user => user.id === userId || user._id === userId);
    if (!user) {
      console.warn('المستخدم غير موجود للتحقق من الصلاحيات:', userId);
      return false;
    }

    console.log(`التحقق من صلاحية "${action}" للمستخدم ${user.username} (${user.role})`);

    // المدير لديه جميع الصلاحيات دائمًا
    if (user.role === 'admin' || user.role === 'manager') {
      console.log('المستخدم مدير ولديه جميع الصلاحيات');
      return true;
    }

    // تعريف الصلاحيات حسب دور المستخدم
    const permissions = {
      admin: [
        // إدارة المستخدمين
        'manage_users', 'add_user', 'update_user', 'delete_user',
        // إدارة الأصناف
        'add_item', 'update_item', 'update_item_with_sales', 'delete_item', 'delete_item_with_sales',
        // إدارة المعاملات
        'add_purchase', 'add_sale', 'delete_transaction',
        // إدارة العملاء
        'add_customer', 'update_customer', 'delete_customer',
        // إدارة الآلات
        'manage_machines', 'add_machine', 'update_machine', 'delete_machine',
        // إدارة النظام
        'manage_settings', 'backup', 'restore',
        // عرض التقارير
        'view_reports', 'view_inventory', 'view_items', 'view_customers', 'view_transactions',
        // الزكاة
        'manage_zakat', 'calculate_zakat',
        // الطباعة
        'print_reports', 'print_invoices',
        // إدارة الخزينة
        'manage_cashbox', 'add_cashbox_transaction'
      ],
      manager: [
        // إدارة المستخدمين
        'manage_users', 'add_user', 'update_user', 'delete_user',
        // إدارة الأصناف
        'add_item', 'update_item', 'update_item_with_sales', 'delete_item', 'delete_item_with_sales',
        // إدارة المعاملات
        'add_purchase', 'add_sale', 'delete_transaction',
        // إدارة العملاء
        'add_customer', 'update_customer', 'delete_customer',
        // إدارة الآلات
        'manage_machines', 'add_machine', 'update_machine', 'delete_machine',
        // إدارة النظام
        'manage_settings', 'backup', 'restore',
        // عرض التقارير
        'view_reports', 'view_inventory', 'view_items', 'view_customers', 'view_transactions',
        // الزكاة
        'manage_zakat', 'calculate_zakat',
        // الطباعة
        'print_reports', 'print_invoices',
        // إدارة الخزينة
        'manage_cashbox', 'add_cashbox_transaction'
      ],
      employee: [
        // إدارة الأصناف (محدودة)
        'add_item', 'update_item', 'update_item_with_sales', 'delete_item',
        // إدارة المعاملات
        'add_purchase', 'add_sale', 'delete_transaction',
        // إدارة العملاء
        'add_customer', 'update_customer', 'delete_customer',
        // إدارة الآلات
        'manage_machines', 'add_machine', 'update_machine', 'delete_machine',
        // عرض التقارير
        'view_reports', 'view_inventory', 'view_items', 'view_customers', 'view_transactions',
        // الزكاة
        'manage_zakat', 'calculate_zakat',
        // الطباعة
        'print_reports', 'print_invoices',
        // إدارة النظام (محدودة)
        'backup', 'restore',
        // إدارة الخزينة (محدودة)
        'view_cashbox', 'add_cashbox_transaction'
      ],
      viewer: [
        // عرض التقارير فقط
        'view_reports', 'view_inventory', 'view_items', 'view_customers', 'view_transactions',
        'view_cashbox', 'print_reports'
      ]
    };

    // التحقق من وجود الصلاحية في قائمة صلاحيات دور المستخدم
    if (permissions[user.role] && permissions[user.role].includes(action)) {
      console.log(`المستخدم لديه صلاحية "${action}"`);
      return true;
    }

    console.log(`المستخدم ليس لديه صلاحية "${action}"`);
    return false;
  };

  // التحقق من صلاحيات المستخدم الحالي
  const checkCurrentUserPermission = (action) => {
    // الحصول على معرف المستخدم الحالي من التخزين المحلي
    const currentUserId = localStorage.getItem('currentUserId');
    const currentUserRole = localStorage.getItem('currentUserRole');
    const currentUserName = localStorage.getItem('currentUserName');

    if (!currentUserId) {
      console.warn('لا يوجد مستخدم حالي');
      return false;
    }

    console.log(`التحقق من صلاحية "${action}" للمستخدم الحالي:`, {
      id: currentUserId,
      username: currentUserName,
      role: currentUserRole
    });

    // تحسين الأداء: إذا كان المستخدم الحالي مديرًا، فلديه جميع الصلاحيات
    if (currentUserRole === 'admin' || currentUserRole === 'manager') {
      console.log('المستخدم الحالي مدير ولديه جميع الصلاحيات');
      return true;
    }

    // محاولة تحويل المعرف إلى رقم إذا كان ممكنًا
    try {
      // التحقق مما إذا كان المعرف رقمًا أو نصًا
      if (!isNaN(currentUserId) && !currentUserId.includes('-')) {
        return checkUserPermission(parseInt(currentUserId), action);
      } else {
        // إذا كان المعرف نصًا (مثل معرف MongoDB)، استخدمه كما هو
        return checkUserPermission(currentUserId, action);
      }
    } catch (error) {
      console.error('خطأ في التحقق من صلاحيات المستخدم الحالي:', error);
      return false;
    }
  };

  // تحديث إعدادات النظام
  const updateSettings = (newSettings) => {
    console.log('تحديث الإعدادات - الإعدادات الحالية:', settings);
    console.log('تحديث الإعدادات - الإعدادات الجديدة:', newSettings);

    // التحقق من وجود تغييرات في حالة تفعيل الربط
    if (newSettings.integrationEnabled !== undefined &&
        newSettings.integrationEnabled !== settings.integrationEnabled) {
      console.log('تغيير حالة تفعيل الربط من',
        settings.integrationEnabled, 'إلى', newSettings.integrationEnabled);
    }

    // تحديث جميع الإعدادات بما في ذلك اسم المنظومة وشعارها
    const updatedSettings = {
      ...settings,
      ...newSettings
    };

    console.log('الإعدادات المحدثة النهائية:', updatedSettings);

    // حفظ الإعدادات في التخزين المحلي
    try {
      localStorage.setItem('settings', JSON.stringify(updatedSettings));
      console.log('تم حفظ الإعدادات في التخزين المحلي بنجاح');
    } catch (error) {
      console.error('خطأ في حفظ الإعدادات في التخزين المحلي:', error);
    }

    // تحديث حالة الإعدادات
    setSettings(updatedSettings);
    console.log('تم تحديث حالة الإعدادات');

    // تم إزالة التحكم اليدوي في السطوع واستبداله بنظام تلقائي

    return updatedSettings;
  };

  // تطبيق السطوع التلقائي على المنظومة
  // تم تعليق هذه الوظيفة لأنها غير مستخدمة حاليًا
  /*
  const applyBrightness = () => {
    // استخدام الوظيفة المحسنة من ملف brightness.js في الوضع التلقائي
    const validBrightness = applyBrightnessUtil('auto');
    console.log('تم تطبيق مستوى السطوع التلقائي:', validBrightness);
    return validBrightness;
  };
  */



  // اختبار الاتصال بمنظومة المبيعات
  const testIntegrationConnection = async (url, apiKey) => {
    // استخدام المعلمات المرسلة أو القيم المخزنة في الإعدادات
    const integrationUrl = url || settings.integrationUrl;
    const integrationApiKey = apiKey || settings.integrationApiKey;

    console.log('اختبار الاتصال بمنظومة المبيعات:');
    console.log('URL:', integrationUrl);
    console.log('API Key:', integrationApiKey);

    if (!integrationUrl || !integrationApiKey) {
      console.log('بيانات الاتصال غير مكتملة');
      return { success: false, message: 'يرجى إدخال عنوان URL ومفتاح API' };
    }

    try {
      // تحديث حالة المزامنة لإظهار أن الاختبار قيد التقدم
      setSyncStatus(prev => ({
        ...prev,
        testing: true
      }));

      const result = await testConnection(integrationUrl, integrationApiKey);

      console.log('نتيجة اختبار الاتصال:', result);

      // تحديث حالة المزامنة بعد اكتمال الاختبار
      setSyncStatus(prev => ({
        ...prev,
        testing: false,
        lastConnectionTest: {
          success: result.success,
          timestamp: new Date().toISOString()
        }
      }));

      return result;
    } catch (error) {
      console.error('Test connection error:', error);

      // تحديث حالة المزامنة في حالة الخطأ
      setSyncStatus(prev => ({
        ...prev,
        testing: false,
        lastConnectionTest: {
          success: false,
          timestamp: new Date().toISOString(),
          error: error.message
        }
      }));

      return { success: false, message: `حدث خطأ أثناء اختبار الاتصال: ${error.message}` };
    }
  };

  // مزامنة البيانات مع منظومة المبيعات
  const syncWithSalesSystem = async (options = {}) => {
    // استخدام المعلمات المرسلة أو القيم المخزنة في الإعدادات
    const integrationEnabled = options.enabled !== undefined ? options.enabled : settings.integrationEnabled;
    const integrationUrl = options.url || settings.integrationUrl;
    const integrationApiKey = options.apiKey || settings.integrationApiKey;

    // إذا تم تمرير خيار تفعيل المزامنة، قم بتحديث الإعدادات
    if (options.enabled !== undefined) {
      console.log('تم تمرير خيار تفعيل المزامنة:', options.enabled);
      console.log('حالة تفعيل المزامنة الحالية:', settings.integrationEnabled);

      // تحديث الإعدادات بغض النظر عن القيمة الحالية
      console.log('تحديث حالة تفعيل المزامنة من', settings.integrationEnabled, 'إلى', options.enabled);

      // تحديث الإعدادات مباشرة
      const updatedSettings = {
        ...settings,
        integrationEnabled: options.enabled
      };

      // حفظ الإعدادات في التخزين المحلي
      localStorage.setItem('settings', JSON.stringify(updatedSettings));

      // تحديث حالة الإعدادات
      setSettings(updatedSettings);

      console.log('تم تحديث حالة تفعيل المزامنة بنجاح');
    }

    // التحقق من تفعيل المزامنة وتوفر بيانات الاتصال
    if (!integrationEnabled || !integrationUrl || !integrationApiKey) {
      return { success: false, message: 'المزامنة غير مفعلة أو بيانات الاتصال غير مكتملة' };
    }

    // تحديث حالة المزامنة
    setSyncStatus(prev => ({
      ...prev,
      syncing: true,
      error: null
    }));

    try {
      // 1. إرسال بيانات المخزون الحالية إلى منظومة المبيعات
      const inventoryData = inventory.map(item => ({
        item_id: item.item_id,
        name: item.name,
        current_quantity: item.current_quantity,
        selling_price: item.selling_price,
        category: item.category,
        unit: item.unit
      }));

      const syncResult = await syncInventory(
        integrationUrl,
        integrationApiKey,
        inventoryData
      );

      if (!syncResult.success) {
        throw new Error(syncResult.message);
      }

      // 2. الحصول على بيانات المبيعات من منظومة المبيعات
      const salesResult = await getSalesData(
        integrationUrl,
        integrationApiKey,
        syncStatus.lastSyncTime
      );

      if (!salesResult.success) {
        throw new Error(salesResult.message);
      }

      // 3. تحديث المخزون بناءً على بيانات المبيعات
      if (salesResult.sales && salesResult.sales.length > 0) {
        // معالجة كل عملية بيع وإضافتها كمعاملة في النظام
        for (const sale of salesResult.sales) {
          try {
            // التحقق من وجود الصنف في المخزون
            const inventoryItem = inventory.find(item => item.item_id === sale.item_id);

            if (inventoryItem) {
              // إنشاء معاملة بيع جديدة
              const saleTransaction = {
                transaction_type: 'sale',
                item_id: sale.item_id,
                item_name: inventoryItem.name,
                quantity: sale.quantity,
                price: sale.price,
                total_price: sale.price * sale.quantity,
                customer: sale.customer || 'منظومة المبيعات',
                notes: `تمت المزامنة من منظومة المبيعات - معرف المبيعات: ${sale.sale_id}`,
                transaction_date: sale.sale_date,
                external_id: sale.sale_id,
                sync_source: 'sales_system'
              };

              // إضافة المعاملة (ستقوم بتحديث المخزون تلقائيًا)
              addTransaction(saleTransaction);
            }
          } catch (error) {
            console.error(`Error processing sale ${sale.sale_id}:`, error);
            // نستمر في معالجة باقي المبيعات حتى لو فشلت واحدة
          }
        }
      }

      // 4. تحديث حالة المزامنة في منظومة المبيعات
      if (syncResult.data && syncResult.data.sync_id) {
        await updateSyncStatus(
          integrationUrl,
          integrationApiKey,
          syncResult.data.sync_id,
          'completed'
        );
      }

      // 5. تحديث حالة المزامنة المحلية
      const currentTime = new Date().toISOString();
      setSyncStatus({
        syncing: false,
        lastSyncTime: currentTime,
        lastSyncStatus: 'success',
        error: null
      });

      // حفظ آخر وقت للمزامنة في التخزين المحلي
      localStorage.setItem('lastSyncTime', currentTime);

      return { success: true, message: 'تمت المزامنة بنجاح' };
    } catch (error) {
      console.error('Sync error:', error);

      // تحديث حالة المزامنة في حالة الفشل
      setSyncStatus(prev => ({
        ...prev,
        syncing: false,
        lastSyncStatus: 'error',
        error: error.message
      }));

      return { success: false, message: `فشل المزامنة: ${error.message}` };
    }
  };

  // إنشاء خزينة جديدة
  const createCashbox = async (initialBalance) => {
    try {
      console.log('بدء إنشاء خزينة جديدة بالرصيد الافتتاحي:', initialBalance);

      // التحقق من صحة الرصيد الافتتاحي
      if (isNaN(initialBalance) || initialBalance < 0) {
        throw new Error('الرصيد الافتتاحي يجب أن يكون رقمًا موجبًا');
      }

      // إنشاء الخزينة
      const result = await window.api.cashbox.create(Number(initialBalance));

      if (result.success) {
        // تحديث حالة الخزينة
        setCashbox(result.cashbox);
        console.log('تم إنشاء الخزينة بنجاح:', result.cashbox);
        return result.cashbox;
      } else {
        throw new Error('فشل في إنشاء الخزينة');
      }
    } catch (error) {
      console.error('خطأ في إنشاء الخزينة:', error);
      throw error;
    }
  };

  // تحديث رأس المال الافتتاحي للخزينة
  const updateCashboxInitialBalance = async (initialBalance) => {
    try {
      console.log('بدء تحديث رأس المال الافتتاحي للخزينة:', initialBalance);

      // التحقق من صحة الرصيد الافتتاحي
      if (isNaN(initialBalance) || initialBalance < 0) {
        throw new Error('الرصيد الافتتاحي يجب أن يكون رقمًا موجبًا');
      }

      // تحديث رأس المال الافتتاحي
      console.log('جاري استدعاء window.api.cashbox.updateInitialBalance مع القيمة:', Number(initialBalance));
      const result = await window.api.cashbox.updateInitialBalance(Number(initialBalance));
      console.log('نتيجة تحديث رأس المال الافتتاحي:', result);

      if (result && result.success) {
        // تحديث حالة الخزينة
        setCashbox(result.cashbox);
        console.log('تم تحديث رأس المال الافتتاحي بنجاح:', result.cashbox);
        return result;
      } else {
        const errorMessage = result && result.message ? result.message : 'فشل في تحديث رأس المال الافتتاحي';
        console.error('فشل في تحديث رأس المال الافتتاحي:', errorMessage);
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error('خطأ في تحديث رأس المال الافتتاحي:', error);
      throw error;
    }
  };

  // إضافة معاملة للخزينة
  const addCashboxTransaction = async (transaction) => {
    try {
      console.log('بدء إضافة معاملة للخزينة:', transaction);

      // التحقق من صحة المعاملة
      if (!transaction.type || !['income', 'expense'].includes(transaction.type)) {
        throw new Error('نوع المعاملة غير صحيح، يجب أن يكون "income" أو "expense"');
      }

      if (!transaction.amount || isNaN(transaction.amount) || transaction.amount <= 0) {
        throw new Error('مبلغ المعاملة غير صحيح، يجب أن يكون رقمًا موجبًا');
      }

      if (!transaction.source) {
        throw new Error('مصدر المعاملة مطلوب');
      }

      // إضافة المعاملة
      const result = await window.api.cashbox.addTransaction(transaction);

      if (result.success) {
        // تحديث حالة الخزينة
        setCashbox(result.cashbox);

        // تحديث قائمة المعاملات
        setCashboxTransactions(prevTransactions => [result.transaction, ...prevTransactions]);

        console.log('تم إضافة المعاملة بنجاح:', result);
        return result;
      } else {
        throw new Error('فشل في إضافة المعاملة');
      }
    } catch (error) {
      console.error('خطأ في إضافة معاملة للخزينة:', error);
      throw error;
    }
  };

  // الحصول على معاملات الخزينة
  const getCashboxTransactions = async (filters = {}) => {
    try {
      console.log('جاري الحصول على معاملات الخزينة مع التصفية:', filters);

      // الحصول على المعاملات
      const transactions = await window.api.cashbox.getTransactions(filters);

      // تحديث حالة المعاملات
      setCashboxTransactions(transactions);

      return transactions;
    } catch (error) {
      console.error('خطأ في الحصول على معاملات الخزينة:', error);
      throw error;
    }
  };

  // إضافة عميل جديد
  const addCustomer = async (newCustomer) => {
    try {
      console.log('بدء إضافة عميل جديد:', newCustomer);

      // التحقق من صحة بيانات العميل
      if (!newCustomer || !newCustomer.name) {
        console.error('بيانات العميل غير صالحة:', newCustomer);
        throw new Error('بيانات العميل غير صالحة أو الاسم مفقود');
      }

      // تنظيف اسم العميل
      const customerName = String(newCustomer.name).trim().toLowerCase();

      // التحقق مما إذا كان العميل موجود مسبقًا - مع تحسين التحقق
      console.log('التحقق من وجود العميل مسبقًا...');
      console.log('عدد العملاء الحاليين:', customers.length);

      // طباعة أسماء العملاء الحاليين للتشخيص
      if (customers.length > 0) {
        console.log('أسماء العملاء الحاليين:');
        customers.forEach((c, index) => {
          console.log(`${index + 1}. ${c.name} (${c.id || c._id})`);
        });
      }

      // التحقق من وجود العميل بطريقة أكثر دقة
      const existingCustomer = customers.find(customer => {
        if (!customer || !customer.name) return false;
        return String(customer.name).trim().toLowerCase() === customerName;
      });

      if (existingCustomer) {
        console.error('العميل موجود بالفعل:', existingCustomer);
        throw new Error('العميل موجود بالفعل');
      }

      // التحقق من نوع العميل
      if (!['regular', 'sub', 'normal'].includes(newCustomer.customer_type)) {
        console.log('نوع العميل غير صالح، استخدام النوع العادي كقيمة افتراضية');
        newCustomer.customer_type = 'normal'; // استخدام النوع العادي كقيمة افتراضية
      }

      // التحقق من وجود العميل الدائم إذا كان العميل فرعي
      if (newCustomer.customer_type === 'sub' && newCustomer.parent_id) {
        console.log('التحقق من وجود العميل الدائم للعميل الفرعي. معرف العميل الدائم:', newCustomer.parent_id);
        console.log('قائمة العملاء الحالية:', customers);

        // تحويل parent_id إلى نص للمقارنة المتسقة
        const parentIdStr = String(newCustomer.parent_id);

        // البحث عن العميل الدائم بطريقة أكثر شمولية
        const parentCustomer = customers.find(c =>
          (c.id && String(c.id) === parentIdStr) ||
          (c._id && String(c._id) === parentIdStr)
        );

        if (!parentCustomer) {
          console.error('العميل الدائم غير موجود. معرف العميل الدائم:', newCustomer.parent_id);
          console.error('أنواع العملاء المتاحين:', customers.map(c => ({ id: c.id, _id: c._id, name: c.name, type: c.customer_type })));

          // التحقق مما إذا كان العميل الدائم موجود في قاعدة البيانات مباشرة
          try {
            const dbParentCustomer = await DatabaseAdapter.getCustomerById(parentIdStr);
            if (dbParentCustomer) {
              console.log('تم العثور على العميل الدائم في قاعدة البيانات:', dbParentCustomer);

              // التحقق من نوع العميل الدائم
              if (dbParentCustomer.customer_type !== 'regular') {
                console.error('العميل المحدد ليس عميل دائم:', dbParentCustomer);
                throw new Error('العميل المحدد ليس عميل دائم');
              }

              // إذا وصلنا إلى هنا، فإن العميل الدائم موجود وصالح
              console.log('العميل الدائم موجود وصالح');
            } else {
              console.error('العميل الدائم غير موجود في قاعدة البيانات');
              throw new Error('العميل الدائم غير موجود');
            }
          } catch (dbError) {
            console.error('خطأ في البحث عن العميل الدائم في قاعدة البيانات:', dbError);
            throw new Error('العميل الدائم غير موجود');
          }
        } else if (parentCustomer.customer_type !== 'regular') {
          console.error('العميل المحدد ليس عميل دائم:', parentCustomer);
          throw new Error('العميل المحدد ليس عميل دائم');
        } else {
          console.log('تم التحقق من وجود العميل الدائم بنجاح:', parentCustomer.name);
        }
      }

      // التحقق من وجود window.api
      if (typeof window === 'undefined' || !window.api) {
        console.error('window.api غير متوفر. محاولة إعادة تحميل الصفحة...');

        // إظهار رسالة للمستخدم
        alert('حدث خطأ في الاتصال بقاعدة البيانات. سيتم إعادة تحميل الصفحة للمحاولة مرة أخرى.');

        // إعادة تحميل الصفحة بعد ثانية واحدة
        setTimeout(() => {
          window.location.reload();
        }, 1000);

        throw new Error('فشل في إضافة العميل بسبب عدم توفر window.api');
      }

      // إضافة العميل باستخدام قاعدة البيانات
      console.log('جاري إضافة العميل إلى قاعدة البيانات...');
      const customerData = {
        ...newCustomer,
        name: String(newCustomer.name).trim(), // تنظيف الاسم
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        // إضافة حقول افتراضية
        sales_history: [],
        total_sales: 0,
        total_profit: 0,
        balance: 0
      };

      // محاولة إضافة العميل باستخدام قاعدة البيانات
      let customer;
      try {
        customer = await database.addCustomer(customerData);
      } catch (dbError) {
        console.error('خطأ في إضافة العميل إلى قاعدة البيانات:', dbError);

        // التحقق مما إذا كان الخطأ متعلق بـ window.api
        if (dbError.message && dbError.message.includes('window.api')) {
          console.log('محاولة إعادة تهيئة window.api...');

          // إظهار رسالة للمستخدم
          alert('حدث خطأ في الاتصال بقاعدة البيانات. سيتم إعادة تحميل الصفحة للمحاولة مرة أخرى.');

          // إعادة تحميل الصفحة بعد ثانية واحدة
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        }

        throw dbError;
      }

      console.log('تمت إضافة العميل بنجاح:', customer);

      // التأكد من وجود حقل id للتوافق مع الواجهة الأمامية
      const customerWithId = {
        ...customer,
        id: customer._id || customer.id,
        // التأكد من وجود الحقول الضرورية
        sales_history: customer.sales_history || [],
        total_sales: customer.total_sales || 0,
        total_profit: customer.total_profit || 0,
        balance: customer.balance || 0
      };

      // تحديث حالة العملاء
      setCustomers(prevCustomers => {
        // التحقق مرة أخرى من عدم وجود العميل في القائمة قبل الإضافة
        const isDuplicate = prevCustomers.some(c =>
          (c.id === customerWithId.id || c._id === customerWithId._id) ||
          (c.name && customerWithId.name &&
           String(c.name).trim().toLowerCase() === String(customerWithId.name).trim().toLowerCase())
        );

        if (isDuplicate) {
          console.warn('تم اكتشاف عميل مكرر أثناء تحديث الحالة، لن تتم إضافته مرة أخرى');
          return prevCustomers;
        }

        console.log('إضافة العميل إلى قائمة العملاء');
        return [...prevCustomers, customerWithId];
      });

      console.log('تم تحديث حالة العملاء');

      // إعادة تحميل جميع العملاء من قاعدة البيانات لضمان تحديث القائمة بشكل فوري
      console.log('جاري إعادة تحميل العملاء من قاعدة البيانات بعد إضافة عميل جديد...');
      try {
        const refreshedCustomers = await database.getCustomers();
        if (Array.isArray(refreshedCustomers)) {
          console.log('تم إعادة تحميل العملاء بنجاح:', refreshedCustomers.length);
          setCustomers(refreshedCustomers);
        }
      } catch (refreshError) {
        console.error('خطأ في إعادة تحميل العملاء:', refreshError);
      }

      return customerWithId;
    } catch (error) {
      console.error('خطأ في إضافة العميل:', error);
      throw error;
    }
  };

  // تحديث عميل موجود
  const updateCustomer = async (updatedCustomer) => {
    try {
      console.log('بدء تحديث العميل:', updatedCustomer);

      // استخدام _id أو id حسب المتوفر
      const customerId = updatedCustomer._id || updatedCustomer.id;

      // التحقق من وجود العميل
      const customerIndex = customers.findIndex(c => c.id === customerId || c._id === customerId);
      if (customerIndex === -1) {
        console.error('العميل غير موجود:', customerId);
        throw new Error('العميل غير موجود');
      }

      // التحقق من نوع العميل
      if (!['regular', 'sub', 'normal'].includes(updatedCustomer.customer_type)) {
        console.log('نوع العميل غير صالح، استخدام النوع العادي كقيمة افتراضية');
        updatedCustomer.customer_type = 'normal'; // استخدام النوع العادي كقيمة افتراضية
      }

      // التحقق من وجود العميل الدائم إذا كان العميل فرعي
      if (updatedCustomer.customer_type === 'sub' && updatedCustomer.parent_id) {
        const parentCustomer = customers.find(c => c.id === updatedCustomer.parent_id || c._id === updatedCustomer.parent_id);
        if (!parentCustomer) {
          console.error('العميل الدائم غير موجود:', updatedCustomer.parent_id);
          throw new Error('العميل الدائم غير موجود');
        }
        if (parentCustomer.customer_type !== 'regular') {
          console.error('العميل المحدد ليس عميل دائم:', parentCustomer);
          throw new Error('العميل المحدد ليس عميل دائم');
        }
      }

      // تحديث العميل باستخدام قاعدة البيانات
      console.log('جاري تحديث العميل في قاعدة البيانات...');
      const customerData = {
        ...updatedCustomer,
        _id: customerId, // التأكد من وجود _id للتوافق مع قاعدة البيانات
        updated_at: new Date().toISOString()
      };

      const customer = await database.updateCustomer(customerData);
      console.log('تم تحديث العميل بنجاح:', customer);

      // التأكد من وجود حقل id للتوافق مع الواجهة الأمامية
      const customerWithId = {
        ...customer,
        id: customer._id || customer.id
      };

      // تحديث حالة العملاء
      setCustomers(prevCustomers =>
        prevCustomers.map(c => (c.id === customerWithId.id || c._id === customerWithId._id) ? customerWithId : c)
      );
      console.log('تم تحديث حالة العملاء');

      return customerWithId;
    } catch (error) {
      console.error('خطأ في تحديث العميل:', error);
      throw error;
    }
  };

  // حذف عميل
  const deleteCustomer = async (id) => {
    try {
      console.log('بدء حذف العميل مع المعرف:', id);

      // التحقق من وجود العميل
      const customer = customers.find(c => c.id === id || c._id === id);
      if (!customer) {
        console.error('العميل غير موجود:', id);
        throw new Error('العميل غير موجود');
      }

      // حذف العميل باستخدام قاعدة البيانات مباشرة
      // نترك التحقق من وجود عملاء فرعيين ومعاملات مرتبطة لوظيفة حذف العميل في قاعدة البيانات
      console.log('جاري حذف العميل من قاعدة البيانات...');
      const customerId = customer._id || id;

      try {
        // الحصول على دور المستخدم الحالي من التخزين المحلي
        const currentUserRole = localStorage.getItem('currentUserRole') || 'employee';
        console.log('حذف العميل بواسطة مستخدم بدور:', currentUserRole);

        // استدعاء وظيفة حذف العميل مع تمرير دور المستخدم
        const result = await database.deleteCustomer(customerId, currentUserRole);

        // التحقق من نجاح العملية
        if (!result || !result.success) {
          // التحقق من صلاحيات المستخدم
          if (result && result.error && result.error.includes('ليس لديك صلاحية')) {
            console.error('خطأ في صلاحيات المستخدم:', result.error);
            throw new Error(result.error);
          }

          console.error('فشل في حذف العميل:', result ? result.error : 'سبب غير معروف');
          throw new Error(result && result.error ? result.error : 'فشل في حذف العميل');
        }

        console.log('تم حذف العميل بنجاح:', result);

        // تحديث حالة العملاء فقط إذا نجحت عملية الحذف - بشكل فوري
        setCustomers(prevCustomers => prevCustomers.filter(c =>
          c.id !== id && c._id !== id &&
          c.id !== customerId && c._id !== customerId
        ));
        console.log('تم تحديث حالة العملاء بشكل فوري');

        // إعادة تحميل العملاء من قاعدة البيانات لضمان تحديث القائمة
        try {
          console.log('جاري إعادة تحميل العملاء من قاعدة البيانات بعد الحذف...');

          // استخدام Promise.all لضمان اكتمال جميع العمليات
          await Promise.all([
            // تأخير قصير لضمان اكتمال عملية الحذف في قاعدة البيانات
            new Promise(resolve => setTimeout(resolve, 500)),

            // استدعاء وظيفة الحصول على العملاء
            database.getCustomers().then(refreshedCustomers => {
              if (Array.isArray(refreshedCustomers)) {
                console.log('تم إعادة تحميل العملاء بنجاح:', refreshedCustomers.length);
                setCustomers(refreshedCustomers);
              }
            })
          ]);
        } catch (refreshError) {
          console.error('خطأ في إعادة تحميل العملاء بعد الحذف:', refreshError);
          // لا نريد إيقاف العملية إذا فشل إعادة التحميل
        }

        return {
          success: true,
          id,
          _id: customerId,
          customerName: customer.name
        };
      } catch (dbError) {
        console.error('خطأ في حذف العميل من قاعدة البيانات:', dbError);
        throw dbError;
      }
    } catch (error) {
      console.error('خطأ في حذف العميل:', error);
      throw error;
    }
  };

  // الحصول على العملاء حسب النوع
  const getCustomersByType = (customerType) => {
    return customers.filter(c => c.customer_type === customerType);
  };

  // الحصول على العملاء الفرعيين لعميل دائم
  const getSubCustomers = (parentId) => {
    if (!parentId) return [];

    // تحويل parentId إلى نص للمقارنة المتسقة
    const parentIdStr = String(parentId);

    // البحث عن العملاء الفرعيين بمراعاة أن parent_id قد يكون رقمًا أو نصًا
    return customers.filter(c =>
      c.parent_id === parentId ||
      (c.parent_id && String(c.parent_id) === parentIdStr)
    );
  };

  // الحصول على سجل مبيعات العميل
  const getCustomerSalesHistory = async (customerId) => {
    console.log('بدء الحصول على سجل مبيعات العميل:', customerId);
    console.log('نوع معرف العميل:', typeof customerId);
    console.log('قيمة معرف العميل:', customerId);

    try {
      // التحقق من صحة معرف العميل
      if (!customerId) {
        console.error('معرف العميل غير صالح:', customerId);
        throw new Error('معرف العميل غير صالح');
      }

      // البحث عن العميل في الحالة المحلية
      const customer = customers.find(c => c.id === customerId || c._id === customerId);
      if (!customer) {
        console.warn('العميل غير موجود في الحالة المحلية:', customerId);
        console.log('قائمة العملاء المتاحة:', customers.map(c => ({ id: c.id, name: c.name })));
      } else {
        console.log('تم العثور على العميل في الحالة المحلية:', customer.name);
        console.log('بيانات العميل:', customer);
      }

      // التحقق من وجود واجهة API
      if (!window.api) {
        console.error('واجهة API غير متوفرة');
        throw new Error('واجهة API غير متوفرة');
      }

      // التحقق من وجود واجهة API للعملاء
      if (!window.api.customers) {
        console.error('واجهة API للعملاء غير متوفرة');
        throw new Error('واجهة API للعملاء غير متوفرة');
      }

      // التحقق من وجود وظيفة getSalesHistory
      if (!window.api.customers.getSalesHistory) {
        console.error('وظيفة getSalesHistory غير متوفرة');
        throw new Error('وظيفة getSalesHistory غير متوفرة');
      }

      // استخدام API لجلب مبيعات العميل من قاعدة البيانات
      console.log('جاري استدعاء API للحصول على سجل مبيعات العميل:', customerId);

      try {
        // استخدام واجهة API المخصصة للعملاء
        console.log('استدعاء window.api.customers.getSalesHistory مع المعرف:', customerId);
        const result = await window.api.customers.getSalesHistory(customerId);
        console.log('تم استلام نتيجة سجل مبيعات العميل من API:', result);

        // التحقق من صحة النتيجة
        if (!result) {
          console.error('لم يتم استلام بيانات من API');
          throw new Error('لم يتم استلام بيانات من قاعدة البيانات');
        }

        // التحقق من وجود مبيعات
        if (!result.sales || result.sales.length === 0) {
          console.warn('لا توجد مبيعات للعميل في النتيجة المستلمة');
        } else {
          console.log(`تم استلام ${result.sales.length} معاملة بيع للعميل`);
        }

        // التحقق من وجود مبيعات مجمعة
        if (!result.groupedSales || result.groupedSales.length === 0) {
          console.warn('لا توجد مبيعات مجمعة في النتيجة المستلمة');
        } else {
          console.log(`تم استلام ${result.groupedSales.length} فاتورة مجمعة للعميل`);
        }

        // إرجاع النتيجة كما هي
        return result;
      } catch (apiError) {
        console.error('خطأ في استدعاء API للحصول على سجل مبيعات العميل:', apiError);

        // إعادة رمي الخطأ للمعالجة في الطبقة العليا
        throw apiError;
      }
    } catch (error) {
      console.error('خطأ في الحصول على سجل مبيعات العميل:', error);

      // البحث عن العميل مرة أخرى للتأكد
      const customerInfo = customers.find(c => c.id === customerId || c._id === customerId);

      // إنشاء كائن نتيجة افتراضي في حالة الخطأ
      const defaultResult = {
        customer: {
          name: customerInfo ? customerInfo.name : 'عميل غير معروف',
          id: customerId
        },
        sales: [],
        groupedSales: [],
        totalSales: 0,
        totalProfit: 0,
        count: 0,
        invoiceCount: 0
      };

      console.log('إرجاع نتيجة افتراضية بسبب الخطأ:', defaultResult);
      return defaultResult;
    }
  };

  // تم إزالة وظائف الموردين

  // تم إزالة وظائف الموردين



  // إضافة آلة جديدة
  const addMachine = async (newMachine) => {
    try {
      // إضافة الآلة باستخدام قاعدة البيانات
      const machine = await database.addMachine(newMachine);

      // تحديث حالة الآلات
      setMachines(prevMachines => [...prevMachines, machine]);

      return machine;
    } catch (error) {
      console.error('Error adding machine:', error);
      throw error;
    }
  };

  // تحديث آلة موجودة
  const updateMachine = async (updatedMachine) => {
    try {
      // تحديث الآلة باستخدام قاعدة البيانات
      const machine = await database.updateMachine(updatedMachine);

      // تحديث حالة الآلات
      setMachines(prevMachines =>
        prevMachines.map(m => m.id === machine.id ? machine : m)
      );

      return machine;
    } catch (error) {
      console.error('Error updating machine:', error);
      throw error;
    }
  };

  // حذف آلة
  const deleteMachine = async (id) => {
    try {
      // حذف الآلة باستخدام قاعدة البيانات
      await database.deleteMachine(id);

      // تحديث حالة الآلات
      setMachines(prevMachines => prevMachines.filter(m => m.id !== id));

      return { id };
    } catch (error) {
      console.error('Error deleting machine:', error);
      throw error;
    }
  };

  // تشغيل صوت الإشعار
  const playNotificationSound = () => {
    try {
      // إنشاء عنصر صوتي
      const audio = new Audio();

      // تعيين مصدر الصوت حسب نوع الإشعار
      audio.src = process.env.PUBLIC_URL + '/assets/sounds/notification.mp3';

      // تعيين مستوى الصوت
      audio.volume = 0.5;

      // تشغيل الصوت
      audio.play().catch(error => {
        console.log('لا يمكن تشغيل صوت الإشعار:', error);
      });
    } catch (error) {
      console.log('خطأ في تشغيل صوت الإشعار:', error);
    }
  };

  // إظهار إشعار
  const showNotification = (message, type = 'info') => {
    const newNotification = {
      id: Date.now(),
      message,
      type,
      time: new Date().toLocaleTimeString(),
      read: false
    };

    setNotifications(prev => [newNotification, ...prev]);

    // تشغيل صوت الإشعار
    playNotificationSound();

    // إزالة الإشعار تلقائيًا بعد 5 ثوانٍ
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== newNotification.id));
    }, 5000);

    return newNotification;
  };

  // تعليم إشعار كمقروء
  const markNotificationAsRead = (notificationId) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === notificationId
          ? { ...notification, read: true }
          : notification
      )
    );
  };

  // تعليم جميع الإشعارات كمقروءة
  const markAllNotificationsAsRead = () => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, read: true }))
    );
  };

  // إفراغ قاعدة البيانات
  const clearDatabase = async (options = {}) => {
    try {
      console.log('بدء إفراغ قاعدة البيانات مع الخيارات:', options);

      // استدعاء وظيفة إفراغ قاعدة البيانات
      const result = await database.clearDatabase(options);

      // إعادة تعيين الحالات المحلية
      setItems([]);
      setInventory([]);
      setTransactions([]);
      setCustomers([]);
      // تم إزالة setSuppliers
      setMachines([]);

      // الاحتفاظ بالمستخدمين إذا لم يتم تحديدهم في الخيارات
      if (options.collections && options.collections.includes('users')) {
        setUsers([]);
      }

      console.log('تم إفراغ قاعدة البيانات بنجاح:', result);
      return result;
    } catch (error) {
      console.error('خطأ في إفراغ قاعدة البيانات:', error);
      throw error;
    }
  };

  // القيمة التي سيتم توفيرها للمكونات
  const value = {
    // البيانات
    items,
    inventory,
    transactions,
    users,
    customers,
    // تم إزالة الموردين
    machines,
    cashbox,
    cashboxTransactions,
    notifications,
    loading,

    // وظائف تحديث البيانات
    setInventory, // إضافة دالة تحديث المخزون

    // وظائف الأصناف
    addItem,
    updateItem,
    deleteItem,
    loadItems,

    // وظائف المعاملات
    addTransaction,
    getItemTransactions,
    getTransactionsByType,

    // وظائف الأرباح
    calculateProfits,

    // وظائف المستخدمين
    addUser,
    updateUser,
    deleteUser,
    checkUserPermission,
    checkCurrentUserPermission,

    // وظائف العملاء
    addCustomer,
    updateCustomer,
    deleteCustomer,
    getCustomersByType,
    getSubCustomers,
    getCustomerSalesHistory,

    // تم إزالة وظائف الموردين

    // وظائف الآلات والمعدات
    addMachine,
    updateMachine,
    deleteMachine,

    // إعدادات النظام
    settings,

    // تحديث إعدادات النظام
    updateSettings,

    // وظائف المزامنة مع منظومة المبيعات
    testIntegrationConnection,
    syncWithSalesSystem,
    syncStatus,

    // وظائف مزامنة المخزون
    syncAllInventory,

    // وظائف الخزينة
    createCashbox,
    updateCashboxInitialBalance,
    addCashboxTransaction,
    getCashboxTransactions,

    // وظائف الإشعارات
    showNotification,
    markNotificationAsRead,
    markAllNotificationsAsRead,

    // وظائف قاعدة البيانات
    clearDatabase
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};

// هوك مخصص لاستخدام سياق التطبيق
export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }

  // تخزين الإعدادات في متغير عام للوصول إليها من خارج مكونات React
  if (typeof window !== 'undefined' && context.settings) {
    window.appSettings = context.settings;
  }

  return context;
};
