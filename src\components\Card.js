import React, { forwardRef } from 'react';
import './Card.css';

/**
 * مكون البطاقة
 * يوفر تصميمًا موحدًا للبطاقات في التطبيق
 * يستخدم forwardRef لتمرير المراجع
 */
const Card = forwardRef(({
  children,
  title,
  subtitle,
  icon,
  actions,
  className = '',
  headerClassName = '',
  bodyClassName = '',
  footerClassName = '',
  noPadding = false,
  noShadow = false,
  noHover = false,
  ...props
}, ref) => {
  return (
    <div
      className={`app-card ${noShadow ? 'no-shadow' : ''} ${noHover ? 'no-hover' : ''} ${className}`}
      ref={ref}
      {...props}
    >
      {(title || icon || actions) && (
        <div className={`app-card-header ${headerClassName}`}>
          <div className="app-card-header-left">
            {icon && <div className="app-card-icon">{icon}</div>}
            <div className="app-card-titles">
              {title && <h3 className="app-card-title">{title}</h3>}
              {subtitle && <div className="app-card-subtitle">{subtitle}</div>}
            </div>
          </div>
          {actions && <div className="app-card-actions">{actions}</div>}
        </div>
      )}

      <div className={`app-card-body ${noPadding ? 'no-padding' : ''} ${bodyClassName}`}>
        {children}
      </div>

      {props.footer && (
        <div className={`app-card-footer ${footerClassName}`}>
          {props.footer}
        </div>
      )}
    </div>
  );
});

// إضافة displayName للمكون
Card.displayName = 'Card';

export default Card;
