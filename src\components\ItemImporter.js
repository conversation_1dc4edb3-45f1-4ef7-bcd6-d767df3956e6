import React, { useState } from 'react';
import { FaFileExcel, FaUpload, FaDownload, FaCheck, FaTimes } from 'react-icons/fa';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';

/**
 * مكون لاستيراد وتصدير الأصناف من وإلى ملف إكسل
 * @param {Object} props - خصائص المكون
 * @param {Function} props.onImport - دالة تُستدعى عند استيراد الأصناف
 * @param {Array} props.existingItems - قائمة الأصناف الموجودة حالياً
 */
const ItemImporter = ({ onImport, existingItems = [] }) => {
  // Variable no utilizada - considere eliminarla o usarla
  const [importStatus, setImportStatus] = useState({ status: null, message: '' });
  // Variable no utilizada - considere eliminarla o usarla
  const [importedItems, setImportedItems] = useState([]);
  // Variable no utilizada - considere eliminarla o usarla
  const [showPreview, setShowPreview] = useState(false);

  /**
   * معالجة تحميل ملف إكسل
   * @param {Event} e - حدث تغيير الملف
   */
  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // التحقق من نوع الملف
    if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
      setImportStatus({
        status: 'error',
        message: 'يرجى تحميل ملف إكسل بامتداد .xlsx أو .xls'
      });
      return;
    }

    const reader = new FileReader();
    reader.onload = (event) => {
      try {
        const data = new Uint8Array(event.target.result);
        const workbook = XLSX.read(data, { type: 'array' });

        // استخراج البيانات من الورقة الأولى
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet);

        if (jsonData.length === 0) {
          setImportStatus({
            status: 'error',
            message: 'الملف لا يحتوي على بيانات'
          });
          return;
        }

        // التحقق من وجود الأعمدة المطلوبة - فقط اسم الصنف إلزامي
        const requiredColumns = ['name'];
        const firstRow = jsonData[0];

        // تحويل أسماء الأعمدة إلى أحرف صغيرة للمقارنة
        const normalizedFirstRow = {};
        Object.keys(firstRow).forEach(key => {
          normalizedFirstRow[key.toLowerCase()] = firstRow[key];
        });

        // التحقق من وجود عمود الاسم بغض النظر عن حالة الأحرف
        const missingColumns = requiredColumns.filter(col => {
          return !Object.keys(normalizedFirstRow).some(key => key.toLowerCase() === col.toLowerCase());
        });

        if (missingColumns.length > 0) {
          setImportStatus({
            status: 'error',
            message: `الملف لا يحتوي على الأعمدة المطلوبة: ${missingColumns.join(', ')}`
          });
          return;
        }

        console.log('تم التحقق من وجود الأعمدة المطلوبة بنجاح');

        // تنسيق البيانات مع مراعاة حالة الأحرف والقيم الفارغة
        const items = jsonData.map(row => {
          // تحويل أسماء الحقول إلى أحرف صغيرة للمقارنة
          const normalizedRow = {};
          Object.keys(row).forEach(key => {
            normalizedRow[key.toLowerCase()] = row[key];
          });

          // الحصول على قيمة الحقل بغض النظر عن حالة الأحرف
          const getFieldValue = (fieldName, defaultValue = '') => {
            const key = Object.keys(normalizedRow).find(k => k.toLowerCase() === fieldName.toLowerCase());
            return key !== undefined ? normalizedRow[key] : defaultValue;
          };

          // إنشاء كائن الصنف مع التحقق من القيم
          return {
            name: getFieldValue('name', '').toString().trim(),
            description: getFieldValue('description', ''),
            category: getFieldValue('category', ''),
            unit: getFieldValue('unit', 'قطعة'),
            minimum_quantity: parseFloat(getFieldValue('minimum_quantity', '0')) || 0,
            avg_price: parseFloat(getFieldValue('avg_price', '0')) || 0,
            selling_price: parseFloat(getFieldValue('selling_price', '0')) || 0,
            location: getFieldValue('location', ''),
            current_quantity: parseFloat(getFieldValue('current_quantity', '0')) ||
                             parseFloat(getFieldValue('initial_quantity', '0')) || 0
          };
        });

        // تصفية الأصناف التي ليس لها اسم
        const validItems = items.filter(item => item.name && item.name.trim() !== '');

        console.log(`تم تنسيق ${validItems.length} صنف صالح من أصل ${items.length}`);

        // استخدام الأصناف الصالحة فقط
        setImportedItems(validItems);
        setShowPreview(true);
        setImportStatus({
          status: 'success',
          message: `تم تحليل ${validItems.length} صنف صالح بنجاح. يرجى مراجعة البيانات قبل الاستيراد.`
        });
      } catch (error) {
        console.error('Error parsing Excel file:', error);
        setImportStatus({
          status: 'error',
          message: `حدث خطأ أثناء تحليل الملف: ${error.message}`
        });
      }
    };

    reader.readAsArrayBuffer(file);
  };

  /**
   * تأكيد استيراد الأصناف
   */
  const confirmImport = () => {
    if (importedItems.length === 0) return;

    onImport(importedItems);
    setImportStatus({
      status: 'success',
      message: `تم استيراد ${importedItems.length} صنف بنجاح.`
    });
    setShowPreview(false);
    setImportedItems([]);
  };

  /**
   * إلغاء استيراد الأصناف
   */
  const cancelImport = () => {
    setShowPreview(false);
    setImportedItems([]);
    setImportStatus({ status: null, message: '' });
  };

  /**
   * تنزيل قالب إكسل فارغ
   */
  const downloadTemplate = () => {
    // إنشاء بيانات القالب (فارغة)
    const templateData = [
      {
        name: '',
        description: '',
        category: '',
        unit: 'باكو',
        minimum_quantity: 0,
        avg_price: 0,
        selling_price: 0,
        location: '',
        current_quantity: 0
      }
    ];

    // إنشاء ورقة عمل جديدة
    const worksheet = XLSX.utils.json_to_sheet(templateData);

    // إنشاء مصنف عمل جديد
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'الأصناف');

    // تحويل المصنف إلى ملف
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

    // تنزيل الملف
    saveAs(blob, 'items_template.xlsx');
  };

  /**
   * تصدير الأصناف الحالية إلى ملف إكسل
   */
  const exportItems = () => {
    if (existingItems.length === 0) {
      setImportStatus({
        status: 'error',
        message: 'لا توجد أصناف للتصدير'
      });
      return;
    }

    // تنسيق البيانات للتصدير
    const exportData = existingItems.map(item => ({
      name: item.name || '',
      description: item.description || '',
      category: item.category || '',
      unit: item.unit || '',
      minimum_quantity: item.minimum_quantity || 0,
      avg_price: item.avg_price || 0,
      selling_price: item.selling_price || 0,
      location: item.location || '',
      current_quantity: item.current_quantity || 0
    }));

    // إنشاء ورقة عمل جديدة
    const worksheet = XLSX.utils.json_to_sheet(exportData);

    // إنشاء مصنف عمل جديد
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'الأصناف');

    // تحويل المصنف إلى ملف
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

    // تنزيل الملف
    saveAs(blob, `items_export_${new Date().toISOString().split('T')[0]}.xlsx`);

    setImportStatus({
      status: 'success',
      message: `تم تصدير ${exportData.length} صنف بنجاح.`
    });
  };

  return (
    <div className="item-importer">
      {/* رسالة الحالة */}
      {importStatus.status && (
        <div className={`alert alert-${importStatus.status === 'success' ? 'success' : 'danger'} mb-3`}>
          {importStatus.status === 'success' ? <FaCheck className="ml-1" /> : <FaTimes className="ml-1" />}
          {importStatus.message}
        </div>
      )}

      {/* معاينة الأصناف المستوردة */}
      {showPreview && (
        <div className="import-preview mb-3">
          <h4 className="mb-3">معاينة الأصناف ({importedItems.length})</h4>
          <div className="table-responsive">
            <table className="table table-bordered table-striped">
              <thead>
                <tr>
                  <th>#</th>
                  <th>الاسم</th>
                  <th>الوصف</th>
                  <th>التصنيف</th>
                  <th>وحدة القياس</th>
                  <th>الحد الأدنى</th>
                  <th>متوسط السعر</th>
                  <th>سعر البيع</th>
                  <th>الموقع</th>
                  <th>الكمية الحالية</th>
                </tr>
              </thead>
              <tbody>
                {importedItems.map((item, index) => (
                  <tr key={index}>
                    <td>{index + 1}</td>
                    <td>{item.name}</td>
                    <td>{item.description || '-'}</td>
                    <td>{item.category || '-'}</td>
                    <td>{item.unit || '-'}</td>
                    <td>{item.minimum_quantity}</td>
                    <td>{item.avg_price} د.ل</td>
                    <td>{item.selling_price} د.ل</td>
                    <td>{item.location || '-'}</td>
                    <td>{item.current_quantity}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div className="d-flex justify-content-end mt-3">
            <button
              type="button"
              className="btn btn-secondary ml-2"
              onClick={cancelImport}
            >
              <FaTimes className="ml-1" />
              إلغاء
            </button>
            <button
              type="button"
              className="btn btn-primary"
              onClick={confirmImport}
            >
              <FaCheck className="ml-1" />
              تأكيد الاستيراد
            </button>
          </div>
        </div>
      )}

      {/* أزرار الاستيراد والتصدير */}
      {!showPreview && (
        <div className="d-flex flex-wrap">
          <div className="mr-2 mb-2">
            <label className="btn btn-primary">
              <FaUpload className="ml-1" />
              استيراد من إكسل
              <input
                type="file"
                accept=".xlsx, .xls"
                style={{ display: 'none' }}
                onChange={handleFileUpload}
              />
            </label>
          </div>

          <button
            type="button"
            className="btn btn-success mr-2 mb-2"
            onClick={exportItems}
          >
            <FaDownload className="ml-1" />
            تصدير الأصناف
          </button>

          <button
            type="button"
            className="btn btn-info mb-2"
            onClick={downloadTemplate}
          >
            <FaFileExcel className="ml-1" />
            تنزيل قالب فارغ
          </button>
        </div>
      )}
    </div>
  );
};

export default ItemImporter;
