/**
 * إضافة عمود payment_status إلى جدول transactions
 *
 * هذا الملف يضيف عمود payment_status إلى جدول transactions إذا لم يكن موجودًا
 */

const { logSystem, logError } = require('./error-handler');
const DatabaseManager = require('./database-singleton');

/**
 * إضافة عمود payment_status إلى جدول transactions
 * @returns {Object} - نتيجة العملية
 */
function addPaymentStatusColumn() {
  try {
    logSystem('بدء إضافة عمود payment_status إلى جدول transactions', 'info');

    // الحصول على اتصال قاعدة البيانات
    const dbManager = DatabaseManager.getInstance();
    const db = dbManager.getConnection();

    if (!db) {
      throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
    }

    // التحقق من وجود عمود payment_status
    const tableInfoQuery = db.prepare("PRAGMA table_info(transactions)");
    const columns = tableInfoQuery.all();

    // البحث عن عمود payment_status
    const paymentStatusColumn = columns.find(col => col.name === 'payment_status');

    if (!paymentStatusColumn) {
      console.log('عمود payment_status غير موجود، جاري إضافته...');

      // إضافة العمود
      db.prepare("ALTER TABLE transactions ADD COLUMN payment_status TEXT DEFAULT 'paid'").run();

      console.log('تم إضافة عمود payment_status بنجاح');
      logSystem('تم إضافة عمود payment_status إلى جدول المعاملات', 'info');

      return {
        success: true,
        message: 'تم إضافة عمود payment_status بنجاح'
      };
    } else {
      console.log('عمود payment_status موجود بالفعل');

      return {
        success: true,
        message: 'عمود payment_status موجود بالفعل'
      };
    }
  } catch (error) {
    console.error('خطأ في إضافة عمود payment_status:', error);
    logError(error, 'addPaymentStatusColumn');

    return {
      success: false,
      message: `خطأ في إضافة عمود payment_status: ${error.message}`
    };
  }
}

module.exports = {
  addPaymentStatusColumn
};
