/**
 * تنسيق المبلغ كعملة
 * @param {number} amount - المبلغ المراد تنسيقه
 * @param {Object} options - خيارات التنسيق
 * @param {boolean} options.showSymbol - ما إذا كان سيتم عرض رمز العملة
 * @param {number} options.decimalPlaces - عدد المنازل العشرية
 * @param {string} options.currencySymbol - رمز العملة
 * @param {boolean} options.useThousandSeparator - استخدام فاصل الآلاف
 * @returns {string} - المبلغ المنسق
 */
export const formatCurrency = (amount, options = {}) => {
  const {
    showSymbol = true,
    decimalPlaces = 0, // تغيير القيمة الافتراضية إلى 0
    currencySymbol = 'د ل',
    useThousandSeparator = false
  } = options;

  // التعامل مع القيم الفارغة أو غير الصالحة
  if (amount === null || amount === undefined || isNaN(amount)) {
    return showSymbol ? `0 ${currencySymbol}` : '0';
  }

  // التأكد من أن المبلغ رقم
  let numAmount;
  try {
    numAmount = typeof amount === 'string' ? parseFloat(amount) : Number(amount);

    // التحقق مرة أخرى من أن القيمة رقم صالح
    if (isNaN(numAmount)) {
      console.warn('تم تمرير قيمة غير صالحة إلى formatCurrency:', amount);
      return showSymbol ? `0 ${currencySymbol}` : '0';
    }
  } catch (error) {
    console.error('خطأ في تحويل القيمة إلى رقم:', error, amount);
    return showSymbol ? `0 ${currencySymbol}` : '0';
  }

  // تنسيق الرقم
  try {
    let formattedAmount;
    // تخزين إشارة الرقم
    const isNegative = numAmount < 0;
    // استخدام القيمة المطلقة للرقم
    const absAmount = Math.abs(numAmount);

    if (useThousandSeparator) {
      // استخدام تنسيق مع فواصل للآلاف - استخدام تنسيق إنجليزي بدلاً من عربي
      formattedAmount = new Intl.NumberFormat('en-US', {
        minimumFractionDigits: decimalPlaces,
        maximumFractionDigits: decimalPlaces
      }).format(absAmount);
    } else {
      // استخدام التنسيق البسيط بدون فواصل
      formattedAmount = absAmount.toFixed(decimalPlaces);
    }

    // إضافة رمز العملة وإشارة السالب إذا كان الرقم سالبًا
    if (showSymbol) {
      return isNegative ? `${formattedAmount} ${currencySymbol}-` : `${formattedAmount} ${currencySymbol}`;
    } else {
      return isNegative ? `-${formattedAmount}` : formattedAmount;
    }
  } catch (error) {
    console.error('خطأ في تنسيق الرقم:', error, numAmount);
    return showSymbol ? `${numAmount} ${currencySymbol}` : String(numAmount);
  }
};

/**
 * تنسيق المبلغ كعملة (واجهة متوافقة مع الإصدار القديم)
 * @param {number} amount - المبلغ المراد تنسيقه
 * @param {boolean} showSymbol - ما إذا كان سيتم عرض رمز العملة
 * @returns {string} - المبلغ المنسق
 */
export const formatCurrencyLegacy = (amount, showSymbol = true) => {
  return formatCurrency(amount, { showSymbol });
};

/**
 * تحليل نص العملة إلى رقم
 * @param {string} currencyString - نص العملة
 * @returns {number} - الرقم
 */
export const parseCurrency = (currencyString) => {
  if (!currencyString) return 0;

  // إزالة رمز العملة وأي أحرف غير رقمية باستثناء النقطة العشرية
  const numericString = currencyString.replace(/[^\d.-]/g, '');

  return parseFloat(numericString) || 0;
};

// للتوافق مع الإصدار القديم
export default formatCurrencyLegacy;
