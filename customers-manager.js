
/**
 * وحدة إدارة العملاء الجديدة
 * تقوم هذه الوحدة بإدارة جميع عمليات العملاء بشكل موحد
 */

// استيراد وحدة معالجة الأخطاء
const errorHandler = require('./error-handler');
const { logError, logSystem } = errorHandler;

const DatabaseManager = require('./database-singleton');

// مرجع لقاعدة البيانات (سيتم الحصول عليه من مدير قاعدة البيانات)
let db = null;

// ذاكرة تخزين مؤقت للعملاء
let customersCache = [];

/**
 * تهيئة وحدة إدارة العملاء
 */
function initialize() {
  try {
    logSystem('جاري تهيئة وحدة إدارة العملاء الجديدة...', 'info');

    // الحصول على اتصال قاعدة البيانات من مدير قاعدة البيانات
    const dbManager = DatabaseManager.getInstance();
    db = dbManager.getConnection();

    if (!db) {
      throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
    }

    // تحميل العملاء في الذاكرة المؤقتة
    try {
      customersCache = getAllCustomers();
      logSystem(`تم تحميل ${customersCache.length} عميل في الذاكرة المؤقتة`, 'info');
    } catch (cacheError) {
      logError(cacheError, 'initialize - cache loading');
      logSystem('فشل في تحميل العملاء في الذاكرة المؤقتة، سيتم استخدام قاعدة البيانات مباشرة', 'warning');
    }

    logSystem('تم تهيئة وحدة إدارة العملاء الجديدة بنجاح', 'info');
    return true;
  } catch (error) {
    logError(error, 'initialize - new-customers-manager');
    return false;
  }
}

/**
 * الحصول على جميع العملاء
 * @param {boolean} bypassCache - تجاوز الذاكرة المؤقتة
 * @returns {Array} - قائمة العملاء
 */
function getAllCustomers(bypassCache = false) {
  try {
    // استخدام الذاكرة المؤقتة إذا كانت متوفرة وليس هناك طلب لتجاوزها
    if (!bypassCache && customersCache && customersCache.length > 0) {
      // تحقق من عدد العملاء في التخزين المؤقت
      logSystem(`استخدام الذاكرة المؤقتة للحصول على ${customersCache.length} عميل`, 'info');

      // إذا كان هناك عميل واحد فقط، نتجاهل التخزين المؤقت ونجلب البيانات من قاعدة البيانات
      if (customersCache.length <= 1) {
        logSystem('عدد العملاء في الذاكرة المؤقتة قليل جداً، تجاهل الذاكرة المؤقتة وجلب البيانات من قاعدة البيانات', 'info');
        // مسح الذاكرة المؤقتة
        customersCache = [];
      } else {
        return [...customersCache];
      }
    }

    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    const stmt = db.prepare(`
      SELECT c.id, c.name, c.contact_person, c.phone, c.email, c.address,
             c.customer_type, c.parent_id, c.credit_limit, c.balance,
             c.created_at,
             p.name as parent_name
      FROM customers c
      LEFT JOIN customers p ON c.parent_id = p.id
      ORDER BY c.name
    `);

    const customers = stmt.all();

    // تحديث الذاكرة المؤقتة
    if (customers && customers.length > 0) {
      customersCache = [...customers];
      logSystem(`تم تحديث الذاكرة المؤقتة بـ ${customers.length} عميل`, 'info');
    }

    return customers;
  } catch (error) {
    logError(error, 'getAllCustomers');
    return [];
  }
}

/**
 * الحصول على عميل بواسطة المعرف
 * @param {number} customerId - معرف العميل
 * @returns {Object|null} - بيانات العميل
 */
function getCustomerById(customerId) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    const numericCustomerId = Number(customerId);
    if (isNaN(numericCustomerId) || numericCustomerId <= 0) {
      throw new Error(`معرف العميل غير صالح: ${customerId}`);
    }

    const stmt = db.prepare(`
      SELECT c.id, c.name, c.contact_person, c.phone, c.email, c.address,
             c.customer_type, c.parent_id, c.credit_limit, c.balance,
             c.created_at,
             p.name as parent_name
      FROM customers c
      LEFT JOIN customers p ON c.parent_id = p.id
      WHERE c.id = ?
    `);

    const customer = stmt.get(numericCustomerId);

    // إذا تم العثور على العميل، نتحقق من وجوده في الذاكرة المؤقتة
    if (customer) {
      // البحث عن العميل في الذاكرة المؤقتة
      const cachedCustomerIndex = customersCache.findIndex(c =>
        c.id === customer.id ||
        String(c.id) === String(customer.id)
      );

      if (cachedCustomerIndex !== -1) {
        // تحديث العميل في الذاكرة المؤقتة
        customersCache[cachedCustomerIndex] = customer;
        logSystem(`تم تحديث العميل في الذاكرة المؤقتة: ${customer.name} (${customer.id})`, 'info');
      } else {
        // إضافة العميل إلى الذاكرة المؤقتة
        customersCache.push(customer);
        logSystem(`تم إضافة العميل إلى الذاكرة المؤقتة: ${customer.name} (${customer.id})`, 'info');
      }
    }

    return customer || null;
  } catch (error) {
    logError(error, 'getCustomerById');
    return null;
  }
}

/**
 * البحث عن العملاء
 * @param {string} searchTerm - مصطلح البحث
 * @returns {Array} - قائمة العملاء المطابقة
 */
function searchCustomers(searchTerm) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    if (!searchTerm || searchTerm.trim() === '') {
      return getAllCustomers();
    }

    const stmt = db.prepare(`
      SELECT c.id, c.name, c.contact_person, c.phone, c.email, c.address,
             c.customer_type, c.parent_id, c.credit_limit, c.balance,
             c.created_at,
             p.name as parent_name
      FROM customers c
      LEFT JOIN customers p ON c.parent_id = p.id
      WHERE c.name LIKE ? OR c.contact_person LIKE ? OR c.phone LIKE ?
      ORDER BY c.name
    `);

    const searchPattern = `%${searchTerm}%`;
    const customers = stmt.all(searchPattern, searchPattern, searchPattern);
    return customers;
  } catch (error) {
    logError(error, 'searchCustomers');
    return [];
  }
}

/**
 * الحصول على العملاء حسب النوع
 * @param {string} customerType - نوع العميل
 * @param {boolean} bypassCache - تجاوز الذاكرة المؤقتة
 * @returns {Array} - قائمة العملاء
 */
function getCustomersByType(customerType, bypassCache = false) {
  try {
    // استخدام الذاكرة المؤقتة إذا كانت متوفرة وليس هناك طلب لتجاوزها
    if (!bypassCache && customersCache && customersCache.length > 0) {
      logSystem(`استخدام الذاكرة المؤقتة للحصول على العملاء من نوع: ${customerType}`, 'info');
      const filteredCustomers = customersCache.filter(c => c && c.customer_type === customerType);
      logSystem(`تم العثور على ${filteredCustomers.length} عميل من نوع ${customerType} في الذاكرة المؤقتة`, 'info');
      return filteredCustomers;
    }

    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    const stmt = db.prepare(`
      SELECT c.id, c.name, c.contact_person, c.phone, c.email, c.address,
             c.customer_type, c.parent_id, c.credit_limit, c.balance,
             c.created_at,
             p.name as parent_name
      FROM customers c
      LEFT JOIN customers p ON c.parent_id = p.id
      WHERE c.customer_type = ?
      ORDER BY c.name
    `);

    const customers = stmt.all(customerType);
    return customers;
  } catch (error) {
    logError(error, 'getCustomersByType');
    return [];
  }
}

/**
 * الحصول على العملاء الفرعيين لعميل معين
 * @param {number} parentId - معرف العميل الرئيسي
 * @returns {Array} - قائمة العملاء الفرعيين
 */
function getSubCustomers(parentId) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    const numericParentId = Number(parentId);
    if (isNaN(numericParentId) || numericParentId <= 0) {
      throw new Error(`معرف العميل الرئيسي غير صالح: ${parentId}`);
    }

    const stmt = db.prepare(`
      SELECT c.id, c.name, c.contact_person, c.phone, c.email, c.address,
             c.customer_type, c.parent_id, c.credit_limit, c.balance,
             c.created_at,
             p.name as parent_name
      FROM customers c
      LEFT JOIN customers p ON c.parent_id = p.id
      WHERE c.parent_id = ?
      ORDER BY c.name
    `);

    const customers = stmt.all(numericParentId);
    return customers;
  } catch (error) {
    logError(error, 'getSubCustomers');
    return [];
  }
}

/**
 * إضافة عميل جديد
 * @param {Object} customer - بيانات العميل
 * @returns {Object} - نتيجة العملية
 */
function addCustomer(customer) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // التحقق من صحة البيانات
    if (!customer.name || customer.name.trim() === '') {
      return { success: false, error: 'اسم العميل مطلوب' };
    }

    // بدء معاملة قاعدة البيانات
    return db.transaction(() => {
      // التحقق من عدم وجود عميل بنفس الاسم
      const checkStmt = db.prepare('SELECT COUNT(*) as count FROM customers WHERE name = ?');
      const { count } = checkStmt.get(customer.name);

      if (count > 0) {
        throw new Error('يوجد عميل بنفس الاسم بالفعل');
      }

      // التحقق من وجود العميل الرئيسي إذا تم تحديده
      if (customer.parent_id) {
        // تحويل parent_id إلى رقم
        const parentId = Number(customer.parent_id);
        if (isNaN(parentId)) {
          throw new Error(`معرف العميل الرئيسي غير صالح: ${customer.parent_id}`);
        }

        // البحث عن العميل الرئيسي
        const checkParentStmt = db.prepare('SELECT id FROM customers WHERE id = ?');
        const parent = checkParentStmt.get(parentId);

        if (!parent) {
          // محاولة البحث عن العميل الرئيسي باستخدام معرف نصي
          const checkParentByStrStmt = db.prepare('SELECT id FROM customers WHERE id = ?');
          const parentByStr = checkParentByStrStmt.get(String(customer.parent_id));

          if (!parentByStr) {
            throw new Error(`العميل الرئيسي غير موجود: ${customer.parent_id}`);
          }

          // تحديث parent_id إلى المعرف الرقمي
          customer.parent_id = parentByStr.id;
        } else {
          // تحديث parent_id إلى المعرف الرقمي
          customer.parent_id = parent.id;
        }
      }

      // إضافة العميل
      const now = new Date().toISOString();
      const insertStmt = db.prepare(`
        INSERT INTO customers (
          name, contact_person, phone, email, address,
          customer_type, parent_id, credit_limit, balance,
          created_at
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      const result = insertStmt.run(
        customer.name,
        customer.contact_person || '',
        customer.phone || '',
        customer.email || '',
        customer.address || '',
        customer.customer_type || 'normal',
        customer.parent_id || null,
        0, // credit_limit (قيمة افتراضية)
        0, // balance (قيمة افتراضية)
        now
      );

      const customerId = result.lastInsertRowid;

      // الحصول على العميل المضاف
      const getCustomerStmt = db.prepare(`
        SELECT c.id, c.name, c.contact_person, c.phone, c.email, c.address,
               c.customer_type, c.parent_id, c.credit_limit, c.balance,
               c.created_at,
               p.name as parent_name
        FROM customers c
        LEFT JOIN customers p ON c.parent_id = p.id
        WHERE c.id = ?
      `);
      const newCustomer = getCustomerStmt.get(customerId);

      // تحديث الذاكرة المؤقتة بعد إضافة العميل
      if (newCustomer) {
        // إذا كان العميل المضاف هو عميل فرعي أو دائم، نقوم بتحديث الذاكرة المؤقتة بالكامل
        if (newCustomer.customer_type === 'sub' || newCustomer.customer_type === 'regular') {
          logSystem('العميل المضاف هو عميل فرعي أو دائم، تحديث الذاكرة المؤقتة بالكامل', 'info');

          // جلب جميع العملاء من قاعدة البيانات
          const refreshStmt = db.prepare(`
            SELECT c.id, c.name, c.contact_person, c.phone, c.email, c.address,
                   c.customer_type, c.parent_id, c.credit_limit, c.balance,
                   c.created_at,
                   p.name as parent_name
            FROM customers c
            LEFT JOIN customers p ON c.parent_id = p.id
            ORDER BY c.name
          `);

          const refreshedCustomers = refreshStmt.all();

          if (refreshedCustomers && refreshedCustomers.length > 0) {
            logSystem(`تم تحديث الذاكرة المؤقتة بـ ${refreshedCustomers.length} عميل بعد إضافة عميل جديد`, 'info');
            customersCache = [...refreshedCustomers];
          }
        } else {
          // إذا كان العميل عادي، نضيفه فقط إلى الذاكرة المؤقتة
          logSystem('إضافة العميل الجديد إلى الذاكرة المؤقتة', 'info');
          customersCache.push(newCustomer);
        }
      }

      return {
        success: true,
        customer: newCustomer
      };
    })();
  } catch (error) {
    logError(error, 'addCustomer');
    return { success: false, error: error.message };
  }
}

/**
 * تحديث عميل موجود
 * @param {number} customerId - معرف العميل
 * @param {Object} updates - التحديثات المطلوبة
 * @param {string} userRole - دور المستخدم الحالي
 * @returns {Object} - نتيجة العملية
 */
function updateCustomer(customerId, updates, userRole) {
  try {
    logSystem(`محاولة تحديث العميل بالمعرف: ${customerId} بواسطة مستخدم بدور: ${userRole}`, 'info');
    logSystem(`بيانات التحديث: ${JSON.stringify(updates)}`, 'info');

    // التحقق من صلاحيات المستخدم
    if (userRole === 'viewer') {
      logSystem(`رفض تحديث العميل: المستخدم بدور مشاهد لا يملك الصلاحية`, 'warning');
      return {
        success: false,
        error: 'ليس لديك صلاحية لتعديل العملاء. المشاهد يمكنه فقط عرض البيانات.'
      };
    }

    if (!db) {
      const errorMsg = 'قاعدة البيانات غير مهيأة';
      logSystem(errorMsg, 'error');
      return { success: false, error: errorMsg };
    }

    // التحويل إلى رقم
    const numericCustomerId = Number(customerId);
    if (isNaN(numericCustomerId) || numericCustomerId <= 0) {
      const errorMsg = `معرف العميل غير صالح: ${customerId}`;
      logSystem(errorMsg, 'error');
      return { success: false, error: errorMsg };
    }

    // التحقق من وجود تحديثات
    if (!updates) {
      const errorMsg = 'لم يتم تحديد أي تحديثات';
      logSystem(errorMsg, 'error');
      return { success: false, error: errorMsg };
    }

    // تنظيف البيانات وإزالة الحقول غير الضرورية
    const cleanedUpdates = { ...updates };

    // إزالة الحقول التي لا تنتمي إلى جدول العملاء
    delete cleanedUpdates._id;  // نستخدم id فقط في قاعدة البيانات

    // تسجيل البيانات المنظفة
    logSystem(`البيانات المنظفة للتحديث: ${JSON.stringify(cleanedUpdates)}`, 'info');

    // التحقق من وجود العميل قبل بدء المعاملة
    const checkCustomerStmt = db.prepare('SELECT * FROM customers WHERE id = ?');
    const customer = checkCustomerStmt.get(numericCustomerId);

    if (!customer) {
      const errorMsg = `العميل غير موجود: ${numericCustomerId}`;
      logSystem(errorMsg, 'error');
      return { success: false, error: errorMsg };
    }

    logSystem(`تم العثور على العميل: ${customer.name} (${numericCustomerId})`, 'info');

    // بدء معاملة قاعدة البيانات
    try {
      const result = db.transaction(() => {
        // التحقق من عدم وجود عميل آخر بنفس الاسم
        if (updates.name && updates.name !== customer.name) {
          const checkNameStmt = db.prepare('SELECT COUNT(*) as count FROM customers WHERE name = ? AND id != ?');
          const { count } = checkNameStmt.get(updates.name, numericCustomerId);

          if (count > 0) {
            throw new Error('يوجد عميل آخر بنفس الاسم بالفعل');
          }
        }

        // التحقق من وجود العميل الرئيسي إذا تم تحديده
        if (updates.parent_id && updates.parent_id !== customer.parent_id) {
          // التأكد من أن العميل لا يمكن أن يكون عميلًا رئيسيًا لنفسه
          if (updates.parent_id === numericCustomerId || String(updates.parent_id) === String(numericCustomerId)) {
            throw new Error('لا يمكن أن يكون العميل عميلًا رئيسيًا لنفسه');
          }

          // تحويل parent_id إلى رقم
          let parentId;
          try {
            parentId = Number(updates.parent_id);
          } catch (e) {
            throw new Error(`معرف العميل الرئيسي غير صالح: ${updates.parent_id}`);
          }

          if (isNaN(parentId)) {
            throw new Error(`معرف العميل الرئيسي غير صالح: ${updates.parent_id}`);
          }

          // البحث عن العميل الرئيسي
          const checkParentStmt = db.prepare('SELECT id FROM customers WHERE id = ?');
          const parent = checkParentStmt.get(parentId);

          if (!parent) {
            // محاولة البحث عن العميل الرئيسي باستخدام معرف نصي
            const checkParentByStrStmt = db.prepare('SELECT id FROM customers WHERE id = ?');
            const parentByStr = checkParentByStrStmt.get(String(updates.parent_id));

            if (!parentByStr) {
              throw new Error(`العميل الرئيسي غير موجود: ${updates.parent_id}`);
            }

            // تحديث parent_id إلى المعرف الرقمي
            updates.parent_id = parentByStr.id;
          } else {
            // تحديث parent_id إلى المعرف الرقمي
            updates.parent_id = parent.id;
          }
        }

        // تحديث العميل
        let updateFields = [];
        let updateParams = [];

        // تسجيل بيانات العميل الحالية للمقارنة
        logSystem(`بيانات العميل الحالية: ${JSON.stringify(customer)}`, 'info');

        // تبسيط عملية التحديث - استخدام الحقول المحددة فقط
        const fieldsToUpdate = [
          'name', 'phone', 'email', 'address', 'customer_type', 'parent_id'
        ];

        // تحديث الحقول المحددة فقط
        fieldsToUpdate.forEach(field => {
          if (cleanedUpdates[field] !== undefined) {
            let newValue = cleanedUpdates[field];

            // معالجة القيم الخاصة
            if (field === 'parent_id') {
              if (newValue === '' || newValue === null || newValue === undefined) {
                newValue = null;
              } else {
                newValue = Number(newValue);
                if (isNaN(newValue)) newValue = null;
              }
            }

            // تنظيف القيم النصية
            if (typeof newValue === 'string') {
              newValue = newValue.trim();
            }

            // إضافة الحقل إلى قائمة التحديث
            updateFields.push(`${field} = ?`);
            updateParams.push(newValue);
            hasChanges = true;

            logSystem(`إضافة الحقل ${field} للتحديث: ${JSON.stringify(newValue)}`, 'info');
          }
        });

        // تم تعديل هذا الشرط لأننا نقوم بإضافة جميع الحقول إلى قائمة التحديث بغض النظر عن المقارنة
        if (updateFields.length === 0) {
          logSystem(`لم يتم العثور على أي حقول للتحديث`, 'info');
          return {
            success: true,
            customer: customer,
            message: 'لم يتم إجراء أي تغييرات على بيانات العميل'
          };
        }

        // إضافة معرف العميل كمعلمة أخيرة
        updateParams.push(numericCustomerId);

        // إنشاء استعلام التحديث
        const updateQuery = `
          UPDATE customers
          SET ${updateFields.join(', ')}
          WHERE id = ?
        `;

        logSystem(`استعلام التحديث: ${updateQuery}`, 'info');
        logSystem(`معلمات التحديث: ${JSON.stringify(updateParams)}`, 'info');

        // تنفيذ استعلام التحديث
        let updateResult;
        try {
          logSystem(`جاري تنفيذ استعلام التحديث: ${updateQuery}`, 'info');
          logSystem(`معلمات التحديث: ${JSON.stringify(updateParams)}`, 'info');

          const updateStmt = db.prepare(updateQuery);
          updateResult = updateStmt.run(...updateParams);

          logSystem(`نتيجة التحديث: ${JSON.stringify(updateResult)}`, 'info');

          // التحقق من نجاح التحديث
          if (!updateResult) {
            throw new Error('فشل في تنفيذ استعلام التحديث: لم يتم استلام نتيجة');
          }
        } catch (sqlError) {
          logError(sqlError, 'updateCustomer - SQL error');
          throw new Error(`فشل في تنفيذ استعلام التحديث: ${sqlError.message}`);
        }

        // التحقق من نجاح التحديث
        if (!updateResult || updateResult.changes === 0) {
          throw new Error(`فشل في تحديث العميل: لم يتم تحديث أي سجل`);
        }

        // الحصول على العميل المحدث
        const getCustomerStmt = db.prepare(`
          SELECT c.id, c.name, c.contact_person, c.phone, c.email, c.address,
                 c.customer_type, c.parent_id, c.credit_limit, c.balance,
                 c.created_at,
                 p.name as parent_name
          FROM customers c
          LEFT JOIN customers p ON c.parent_id = p.id
          WHERE c.id = ?
        `);
        const updatedCustomer = getCustomerStmt.get(numericCustomerId);

        if (!updatedCustomer) {
          throw new Error(`فشل في الحصول على العميل المحدث`);
        }

        logSystem(`تم تحديث العميل بنجاح: ${updatedCustomer.name} (${updatedCustomer.id})`, 'info');

        // تحديث الذاكرة المؤقتة بعد تحديث العميل
        if (updatedCustomer) {
          // إذا كان العميل المحدث هو عميل فرعي أو دائم، نقوم بتحديث الذاكرة المؤقتة بالكامل
          if (updatedCustomer.customer_type === 'sub' || updatedCustomer.customer_type === 'regular') {
            logSystem('العميل المحدث هو عميل فرعي أو دائم، تحديث الذاكرة المؤقتة بالكامل', 'info');

            // جلب جميع العملاء من قاعدة البيانات
            const refreshStmt = db.prepare(`
              SELECT c.id, c.name, c.contact_person, c.phone, c.email, c.address,
                     c.customer_type, c.parent_id, c.credit_limit, c.balance,
                     c.created_at,
                     p.name as parent_name
              FROM customers c
              LEFT JOIN customers p ON c.parent_id = p.id
              ORDER BY c.name
            `);

            const refreshedCustomers = refreshStmt.all();

            if (refreshedCustomers && refreshedCustomers.length > 0) {
              logSystem(`تم تحديث الذاكرة المؤقتة بـ ${refreshedCustomers.length} عميل بعد تحديث عميل`, 'info');
              customersCache = [...refreshedCustomers];
            }
          } else {
            // إذا كان العميل عادي، نحدثه فقط في الذاكرة المؤقتة
            logSystem('تحديث العميل في الذاكرة المؤقتة', 'info');

            // البحث عن العميل في الذاكرة المؤقتة وتحديثه
            const customerIndex = customersCache.findIndex(c =>
              c.id === updatedCustomer.id ||
              String(c.id) === String(updatedCustomer.id)
            );

            if (customerIndex !== -1) {
              customersCache[customerIndex] = updatedCustomer;
            } else {
              // إذا لم يتم العثور على العميل، نضيفه إلى الذاكرة المؤقتة
              customersCache.push(updatedCustomer);
            }
          }
        }

        return {
          success: true,
          customer: updatedCustomer
        };
      })();

      return result;
    } catch (transactionError) {
      logError(transactionError, 'updateCustomer - transaction');
      return {
        success: false,
        error: transactionError.message,
        customerId: numericCustomerId
      };
    }
  } catch (error) {
    logError(error, 'updateCustomer');
    return { success: false, error: error.message };
  }
}

/**
 * حذف عميل
 * @param {number} customerId - معرف العميل
 * @param {string} userRole - دور المستخدم الحالي
 * @returns {Object} - نتيجة العملية
 */
async function deleteCustomer(customerId, userRole) {
  try {
    logSystem(`محاولة حذف العميل بالمعرف: ${customerId} بواسطة مستخدم بدور: ${userRole}`, 'info');

    // التحقق من صلاحيات المستخدم
    if (userRole === 'viewer' || userRole === 'employee') {
      logSystem(`رفض حذف العميل: المستخدم بدور ${userRole} لا يملك الصلاحية`, 'warning');
      return {
        success: false,
        error: `ليس لديك صلاحية لحذف العملاء. المستخدم بدور ${userRole} لا يمكنه حذف العملاء.`
      };
    }

    if (!db) {
      logSystem('قاعدة البيانات غير مهيأة في deleteCustomer', 'error');
      return { success: false, error: 'قاعدة البيانات غير مهيأة' };
    }

    // التحويل إلى رقم
    const numericCustomerId = Number(customerId);
    if (isNaN(numericCustomerId) || numericCustomerId <= 0) {
      logSystem(`معرف العميل غير صالح: ${customerId}`, 'error');
      return { success: false, error: `معرف العميل غير صالح: ${customerId}` };
    }

    logSystem(`بدء عملية حذف العميل بالمعرف: ${numericCustomerId}`, 'info');

    // بدء معاملة قاعدة البيانات
    try {
      const result = db.transaction(() => {
        // التحقق من وجود العميل
        const checkCustomerStmt = db.prepare('SELECT * FROM customers WHERE id = ?');
        const customer = checkCustomerStmt.get(numericCustomerId);

        if (!customer) {
          logSystem(`العميل غير موجود: ${numericCustomerId}`, 'error');
          throw new Error(`العميل غير موجود: ${numericCustomerId}`);
        }

        logSystem(`تم العثور على العميل: ${customer.name} (${numericCustomerId})`, 'info');

        // التحقق من عدم وجود عملاء فرعيين
        const checkSubCustomersStmt = db.prepare('SELECT COUNT(*) as count FROM customers WHERE parent_id = ?');
        const { count: subCustomersCount } = checkSubCustomersStmt.get(numericCustomerId);

        if (subCustomersCount > 0) {
          logSystem(`لا يمكن حذف العميل لأنه يحتوي على ${subCustomersCount} عميل فرعي`, 'warning');
          throw new Error(`لا يمكن حذف العميل لأنه يحتوي على ${subCustomersCount} عميل فرعي. يجب حذف العملاء الفرعيين أولاً.`);
        }

        // التحقق من عدم وجود معاملات مرتبطة بالعميل
        const checkTransactionsStmt = db.prepare('SELECT COUNT(*) as count FROM transactions WHERE customer_id = ?');
        const { count: transactionsCount } = checkTransactionsStmt.get(numericCustomerId);

        if (transactionsCount > 0) {
          logSystem(`لا يمكن حذف العميل لأنه مرتبط بـ ${transactionsCount} معاملة`, 'warning');
          throw new Error(`لا يمكن حذف العميل لأنه مرتبط بـ ${transactionsCount} معاملة.`);
        }

        logSystem(`جاري حذف سجل مبيعات العميل...`, 'info');

        // التحقق من وجود جدول customer_sales_history
        try {
          // التحقق من وجود الجدول
          const tableCheckStmt = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='customer_sales_history'");
          const tableExists = tableCheckStmt.get();

          if (tableExists) {
            // حذف سجل مبيعات العميل
            const deleteSalesHistoryStmt = db.prepare('DELETE FROM customer_sales_history WHERE customer_id = ?');
            const salesHistoryResult = deleteSalesHistoryStmt.run(numericCustomerId);
            logSystem(`تم حذف ${salesHistoryResult.changes} سجل من سجل مبيعات العميل`, 'info');
          } else {
            logSystem(`جدول customer_sales_history غير موجود، تخطي عملية حذف سجل المبيعات`, 'warning');
          }
        } catch (historyError) {
          logSystem(`خطأ أثناء محاولة حذف سجل مبيعات العميل: ${historyError.message}`, 'warning');
          // لا نرمي استثناء هنا، نستمر في عملية حذف العميل
        }

        logSystem(`جاري حذف العميل...`, 'info');

        // حذف العميل
        const deleteCustomerStmt = db.prepare('DELETE FROM customers WHERE id = ?');
        const deleteResult = deleteCustomerStmt.run(numericCustomerId);

        // التحقق من نجاح عملية الحذف
        if (deleteResult.changes === 0) {
          logSystem(`فشل في حذف العميل: لم يتم تحديث أي سجل`, 'error');
          throw new Error(`فشل في حذف العميل: لم يتم تحديث أي سجل`);
        }

        logSystem(`تم حذف العميل بنجاح: ${numericCustomerId} (${customer.name})`, 'info');

        // تحديث الذاكرة المؤقتة بعد حذف العميل
        if (customersCache && customersCache.length > 0) {
          // حذف العميل من الذاكرة المؤقتة
          customersCache = customersCache.filter(c =>
            c.id !== numericCustomerId &&
            String(c.id) !== String(numericCustomerId)
          );

          logSystem(`تم تحديث الذاكرة المؤقتة بعد حذف العميل، عدد العملاء المتبقي: ${customersCache.length}`, 'info');
        }

        return {
          success: true,
          customerId: numericCustomerId,
          customerName: customer.name
        };
      })();

      return result;
    } catch (transactionError) {
      logError(transactionError, 'deleteCustomer - transaction');
      return {
        success: false,
        error: transactionError.message,
        customerId: numericCustomerId
      };
    }
  } catch (error) {
    logError(error, 'deleteCustomer');
    return {
      success: false,
      error: error.message,
      customerId: customerId
    };
  }
}

/**
 * الحصول على سجل مبيعات العميل
 * @param {number} customerId - معرف العميل
 * @returns {Object} - سجل المبيعات مع معلومات العميل
 */
function getCustomerSalesHistory(customerId) {
  try {
    logSystem(`بدء استعلام سجل مبيعات العميل: ${customerId}`, 'info');
    logSystem(`نوع معرف العميل: ${typeof customerId}`, 'info');
    logSystem(`قيمة معرف العميل: ${customerId}`, 'info');

    if (!db) {
      logSystem('قاعدة البيانات غير مهيأة في getCustomerSalesHistory', 'error');
      throw new Error('قاعدة البيانات غير مهيأة');
    }

    // التحقق من حالة قاعدة البيانات
    try {
      const testStmt = db.prepare('SELECT 1');
      const testResult = testStmt.get();
      logSystem(`اختبار اتصال قاعدة البيانات: ${JSON.stringify(testResult)}`, 'info');
    } catch (dbTestError) {
      logSystem(`فشل اختبار اتصال قاعدة البيانات: ${dbTestError.message}`, 'error');
      throw new Error(`فشل اختبار اتصال قاعدة البيانات: ${dbTestError.message}`);
    }

    // التحويل إلى رقم
    const numericCustomerId = Number(customerId);
    if (isNaN(numericCustomerId) || numericCustomerId <= 0) {
      logSystem(`معرف العميل غير صالح: ${customerId}`, 'error');
      throw new Error(`معرف العميل غير صالح: ${customerId}`);
    }

    logSystem(`تم تحويل معرف العميل إلى رقم: ${numericCustomerId}`, 'info');

    // الحصول على معلومات العميل
    logSystem(`جاري البحث عن العميل بالمعرف: ${numericCustomerId}`, 'info');

    let customer;
    try {
      const customerStmt = db.prepare(`
        SELECT c.id, c.name, c.contact_person, c.phone, c.email, c.address,
               c.customer_type, c.parent_id, c.credit_limit, c.balance,
               c.created_at,
               p.name as parent_name
        FROM customers c
        LEFT JOIN customers p ON c.parent_id = p.id
        WHERE c.id = ?
      `);

      customer = customerStmt.get(numericCustomerId);

      logSystem(`نتيجة البحث عن العميل: ${customer ? JSON.stringify(customer) : 'لم يتم العثور على العميل'}`, 'info');
    } catch (customerQueryError) {
      logSystem(`خطأ في استعلام العميل: ${customerQueryError.message}`, 'error');
      throw new Error(`خطأ في استعلام العميل: ${customerQueryError.message}`);
    }

    if (!customer) {
      logSystem(`العميل غير موجود: ${numericCustomerId}`, 'error');
      return {
        customer: { name: 'عميل غير معروف', id: numericCustomerId },
        sales: [],
        groupedSales: [],
        totalSales: 0,
        totalProfit: 0,
        count: 0,
        invoiceCount: 0
      };
    }

    logSystem(`تم العثور على العميل: ${customer.name} (${numericCustomerId})`, 'info');

    // الحصول على سجل مبيعات العميل - استخدام استعلام بسيط وواضح
    logSystem(`جاري البحث عن معاملات البيع للعميل: ${numericCustomerId}`, 'info');

    let sales = [];
    try {
      const salesStmt = db.prepare(`
        SELECT
          t.id,
          t.item_id,
          t.quantity,
          t.price,
          t.total_price,
          t.profit,
          t.transaction_date,
          t.notes,
          t.invoice_number,
          i.name as item_name,
          i.unit as item_unit
        FROM transactions t
        LEFT JOIN items i ON t.item_id = i.id
        WHERE t.customer_id = ? AND t.transaction_type = 'sale'
        ORDER BY t.transaction_date DESC
      `);

      // استخدام المعرف الرقمي فقط للبحث
      sales = salesStmt.all(numericCustomerId);

      logSystem(`تم العثور على ${sales.length} معاملة بيع للعميل ${customer.name} باستخدام الاستعلام الأساسي`, 'info');

      if (sales.length > 0) {
        logSystem(`عينة من المعاملات: ${JSON.stringify(sales.slice(0, 2))}`, 'info');
      }
    } catch (salesQueryError) {
      logSystem(`خطأ في استعلام معاملات البيع: ${salesQueryError.message}`, 'error');
      throw new Error(`خطأ في استعلام معاملات البيع: ${salesQueryError.message}`);
    }

    logSystem(`تم العثور على ${sales.length} معاملة بيع للعميل ${customer.name}`, 'info');

    // إذا لم يتم العثور على مبيعات، نحاول البحث بطريقة أخرى
    if (sales.length === 0) {
      logSystem(`محاولة البحث عن المبيعات بطريقة بديلة...`, 'info');

      try {
        // استعلام بديل يبحث في حقل customer أيضًا
        const altSalesStmt = db.prepare(`
          SELECT
            t.id,
            t.item_id,
            t.quantity,
            t.price,
            t.total_price,
            t.profit,
            t.transaction_date,
            t.notes,
            t.invoice_number,
            i.name as item_name,
            i.unit as item_unit
          FROM transactions t
          LEFT JOIN items i ON t.item_id = i.id
          WHERE (t.customer_id = ? OR t.customer = ?) AND t.transaction_type = 'sale'
          ORDER BY t.transaction_date DESC
        `);

        const customerIdStr = numericCustomerId.toString();
        logSystem(`استخدام معرف العميل كنص: ${customerIdStr}`, 'info');

        const altSales = altSalesStmt.all(numericCustomerId, customerIdStr);

        logSystem(`تم العثور على ${altSales.length} معاملة بيع باستخدام الطريقة البديلة`, 'info');

        if (altSales.length > 0) {
          logSystem(`عينة من المعاملات البديلة: ${JSON.stringify(altSales.slice(0, 2))}`, 'info');
        }

        // استخدام النتائج البديلة إذا وجدت
        if (altSales.length > 0) {
          sales.push(...altSales);
          logSystem(`تم دمج النتائج، إجمالي المعاملات: ${sales.length}`, 'info');
        }
      } catch (altSalesQueryError) {
        logSystem(`خطأ في استعلام معاملات البيع البديل: ${altSalesQueryError.message}`, 'error');
        // لا نرمي استثناء هنا، نستمر في العمل مع المعاملات الموجودة (إن وجدت)
      }

      // محاولة ثالثة: البحث في جميع المعاملات
      if (sales.length === 0) {
        logSystem(`محاولة البحث عن المبيعات بطريقة ثالثة (جميع المعاملات)...`, 'info');

        try {
          // استعلام يجلب جميع معاملات البيع
          const allSalesStmt = db.prepare(`
            SELECT
              t.id,
              t.item_id,
              t.quantity,
              t.price,
              t.total_price,
              t.profit,
              t.transaction_date,
              t.notes,
              t.invoice_number,
              t.customer_id,
              t.customer,
              i.name as item_name,
              i.unit as item_unit
            FROM transactions t
            LEFT JOIN items i ON t.item_id = i.id
            WHERE t.transaction_type = 'sale'
            ORDER BY t.transaction_date DESC
            LIMIT 100
          `);

          const allSales = allSalesStmt.all();

          logSystem(`تم العثور على ${allSales.length} معاملة بيع في النظام`, 'info');

          if (allSales.length > 0) {
            logSystem(`عينة من جميع المعاملات: ${JSON.stringify(allSales.slice(0, 2))}`, 'info');

            // البحث عن معاملات العميل يدويًا
            const customerSales = allSales.filter(sale => {
              const saleCustomerId = sale.customer_id;
              const saleCustomer = sale.customer;

              return (
                saleCustomerId === numericCustomerId ||
                saleCustomerId === numericCustomerId.toString() ||
                (typeof saleCustomer === 'string' && saleCustomer.includes(numericCustomerId.toString())) ||
                (typeof saleCustomer === 'string' && saleCustomer.includes(customer.name))
              );
            });

            logSystem(`تم العثور على ${customerSales.length} معاملة بيع للعميل باستخدام الطريقة الثالثة`, 'info');

            if (customerSales.length > 0) {
              sales.push(...customerSales);
              logSystem(`تم دمج النتائج، إجمالي المعاملات: ${sales.length}`, 'info');
            }
          }
        } catch (allSalesQueryError) {
          logSystem(`خطأ في استعلام جميع معاملات البيع: ${allSalesQueryError.message}`, 'error');
          // لا نرمي استثناء هنا، نستمر في العمل مع المعاملات الموجودة (إن وجدت)
        }
      }
    }

    // حساب إجمالي المبيعات والربح
    let totalSales = 0;
    let totalProfit = 0;

    // إزالة المعاملات المكررة
    const uniqueSales = [];
    const seenIds = new Set();

    sales.forEach(sale => {
      const saleId = sale.id || sale._id;
      if (!seenIds.has(saleId)) {
        seenIds.add(saleId);
        uniqueSales.push(sale);
      }
    });

    logSystem(`تم إزالة المعاملات المكررة: ${sales.length} -> ${uniqueSales.length}`, 'info');

    // استخدام المعاملات الفريدة
    sales = uniqueSales;

    // حساب الإجماليات
    sales.forEach(sale => {
      const saleTotal = Number(sale.total_price) || 0;
      const saleProfit = Number(sale.profit) || 0;

      totalSales += saleTotal;
      totalProfit += saleProfit;

      // إضافة معلومات إضافية للتشخيص
      logSystem(`معاملة #${sale.id}: الصنف=${sale.item_name}, الكمية=${sale.quantity}, السعر=${sale.price}, الإجمالي=${saleTotal}, الربح=${saleProfit}`, 'info');
    });

    logSystem(`إجمالي المبيعات: ${totalSales}, إجمالي الربح: ${totalProfit}`, 'info');

    // تجميع المبيعات في فاتورة واحدة
    const mainInvoiceNumber = `H${numericCustomerId.toString().padStart(5, '0')}`;
    logSystem(`إنشاء فاتورة مجمعة برقم: ${mainInvoiceNumber}`, 'info');

    // إنشاء فاتورة واحدة تحتوي على جميع المبيعات
    const mainInvoice = {
      invoice_number: mainInvoiceNumber,
      items: sales,
      transaction_date: sales.length > 0 ? sales[0].transaction_date : new Date().toISOString(),
      notes: `فاتورة مجمعة لجميع مشتريات العميل ${customer.name}`
    };

    // التحقق من صحة الفاتورة
    if (!mainInvoice.items || !Array.isArray(mainInvoice.items)) {
      logSystem(`خطأ: الأصناف في الفاتورة ليست مصفوفة`, 'error');
      mainInvoice.items = [];
    }

    // وضع الفاتورة في مصفوفة
    const groupedSales = [mainInvoice];

    logSystem(`تم تجميع ${sales.length} معاملة في فاتورة واحدة: ${mainInvoiceNumber}`, 'info');
    logSystem(`بيانات الفاتورة المجمعة: ${JSON.stringify({
      invoice_number: mainInvoice.invoice_number,
      items_count: mainInvoice.items.length,
      transaction_date: mainInvoice.transaction_date,
      notes: mainInvoice.notes
    })}`, 'info');

    // معالجة مشكلة ترميز النص العربي
    if (customer && customer.name && typeof customer.name === 'string') {
      // التحقق مما إذا كان هناك رموز غريبة في الاسم
      if (customer.name.includes('╪') || customer.name.includes('┘') || customer.name.includes('▒')) {
        logSystem(`تم اكتشاف مشكلة في ترميز اسم العميل: ${customer.name}`, 'warning');
        // محاولة إصلاح الاسم أو استخدام اسم افتراضي
        customer.name = `عميل ${numericCustomerId}`;
        logSystem(`تم تعيين اسم افتراضي للعميل: ${customer.name}`, 'info');
      }
    }

    // معالجة مشكلة ترميز النص العربي في المبيعات
    if (Array.isArray(sales)) {
      sales.forEach(sale => {
        if (sale.item_name && typeof sale.item_name === 'string') {
          if (sale.item_name.includes('╪') || sale.item_name.includes('┘') || sale.item_name.includes('▒')) {
            logSystem(`تم اكتشاف مشكلة في ترميز اسم الصنف: ${sale.item_name}`, 'warning');
            sale.item_name = `صنف ${sale.item_id || 'غير معروف'}`;
          }
        }
      });
    }

    // إنشاء كائن النتيجة
    const result = {
      customer: customer,
      sales: sales,
      groupedSales: groupedSales,
      totalSales: totalSales,
      totalProfit: totalProfit,
      count: sales.length,
      invoiceCount: groupedSales.length
    };

    // تسجيل النتيجة للتشخيص
    logSystem(`إرجاع نتيجة سجل مبيعات العميل: ${result.count} معاملة، ${result.invoiceCount} فاتورة، إجمالي المبيعات: ${result.totalSales}، إجمالي الربح: ${result.totalProfit}`, 'info');

    // التحقق من صحة النتيجة
    if (!result.customer) {
      logSystem(`خطأ: العميل غير موجود في النتيجة`, 'error');
      result.customer = { name: 'عميل غير معروف', id: numericCustomerId };
    }

    if (!result.sales || !Array.isArray(result.sales)) {
      logSystem(`خطأ: المبيعات غير موجودة أو ليست مصفوفة في النتيجة`, 'error');
      result.sales = [];
      result.count = 0;
    }

    if (!result.groupedSales || !Array.isArray(result.groupedSales)) {
      logSystem(`خطأ: المبيعات المجمعة غير موجودة أو ليست مصفوفة في النتيجة`, 'error');
      result.groupedSales = [];
      result.invoiceCount = 0;
    }

    // إذا كانت المبيعات فارغة، تسجيل رسالة تحذير
    if (sales.length === 0) {
      logSystem(`تحذير: لا توجد مبيعات للعميل ${customer.name} (${numericCustomerId})`, 'warning');
    }

    // تسجيل النتيجة النهائية
    try {
      logSystem(`النتيجة النهائية: ${JSON.stringify({
        customer_id: result.customer.id,
        customer_name: result.customer.name,
        sales_count: result.count,
        invoice_count: result.invoiceCount,
        total_sales: result.totalSales,
        total_profit: result.totalProfit
      })}`, 'info');

      // تسجيل النتيجة كاملة للتشخيص
      logSystem(`نتيجة الاستدعاء: ${JSON.stringify(result, null, 2)}`, 'info');
    } catch (jsonError) {
      logSystem(`خطأ في تحويل النتيجة إلى JSON: ${jsonError.message}`, 'error');
    }

    return result;
  } catch (error) {
    logError(error, 'getCustomerSalesHistory');

    // تسجيل معلومات إضافية للتشخيص
    logSystem(`حدث خطأ أثناء جلب سجل مبيعات العميل: ${customerId}`, 'error');
    logSystem(`رسالة الخطأ: ${error.message}`, 'error');
    logSystem(`مكدس الخطأ: ${error.stack}`, 'error');

    // إنشاء كائن نتيجة افتراضي
    const defaultResult = {
      customer: { name: 'عميل غير معروف', id: customerId },
      sales: [],
      groupedSales: [],
      totalSales: 0,
      totalProfit: 0,
      count: 0,
      invoiceCount: 0
    };

    // تسجيل النتيجة الافتراضية
    logSystem(`إرجاع نتيجة افتراضية لسجل مبيعات العميل بسبب الخطأ`, 'warning');

    // محاولة إنشاء نتيجة أفضل إذا كان لدينا معلومات العميل
    try {
      if (db) {
        const customerStmt = db.prepare('SELECT * FROM customers WHERE id = ?');
        const customer = customerStmt.get(Number(customerId));

        if (customer) {
          logSystem(`تم العثور على العميل في معالجة الخطأ: ${customer.name} (${customer.id})`, 'info');
          defaultResult.customer = customer;
        }
      }
    } catch (fallbackError) {
      logSystem(`خطأ في محاولة استرداد معلومات العميل في معالجة الخطأ: ${fallbackError.message}`, 'error');
    }

    // تسجيل النتيجة النهائية
    logSystem(`النتيجة الافتراضية النهائية: ${JSON.stringify({
      customer_id: defaultResult.customer.id,
      customer_name: defaultResult.customer.name,
      sales_count: 0,
      invoice_count: 0,
      total_sales: 0,
      total_profit: 0
    })}`, 'info');

    return defaultResult;
  }
}

/**
 * الحصول على معلومات الصنف للبيع
 * @param {number} itemId - معرف الصنف
 * @returns {Object} - معلومات الصنف للبيع
 */
function getItemInfoForSale(itemId) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    const numericItemId = Number(itemId);
    if (isNaN(numericItemId) || numericItemId <= 0) {
      throw new Error(`معرف الصنف غير صالح: ${itemId}`);
    }

    // الحصول على معلومات الصنف من المخزون
    const inventoryStmt = db.prepare(`
      SELECT i.id, i.name, i.unit, inv.current_quantity, inv.selling_price, inv.minimum_quantity
      FROM items i
      LEFT JOIN inventory inv ON i.id = inv.item_id
      WHERE i.id = ?
    `);

    const item = inventoryStmt.get(numericItemId);

    if (!item) {
      logError(new Error(`الصنف غير موجود: ${numericItemId}`), 'getItemInfoForSale');
      return null;
    }

    // التحقق من توفر الكمية
    if (item.current_quantity <= 0) {
      logSystem(`الكمية المتوفرة من الصنف ${item.name} غير كافية للبيع (${item.current_quantity})`, 'warning');
      return {
        ...item,
        available_for_sale: false,
        message: 'الكمية المتوفرة غير كافية للبيع'
      };
    }

    // التحقق من وجود سعر بيع
    if (!item.selling_price || item.selling_price <= 0) {
      logSystem(`سعر البيع للصنف ${item.name} غير محدد (${item.selling_price})`, 'warning');

      // محاولة الحصول على سعر البيع من آخر معاملة شراء
      const lastPurchaseStmt = db.prepare(`
        SELECT price
        FROM transactions
        WHERE item_id = ? AND transaction_type = 'purchase'
        ORDER BY transaction_date DESC
        LIMIT 1
      `);

      const lastPurchase = lastPurchaseStmt.get(numericItemId);

      if (lastPurchase && lastPurchase.price > 0) {
        // هامش ربح 20%
        const calculatedPrice = parseFloat(lastPurchase.price) * 1.2;

        return {
          ...item,
          selling_price: calculatedPrice,
          available_for_sale: true,
          price_source: 'calculated'
        };
      }

      return {
        ...item,
        available_for_sale: false,
        message: 'سعر البيع غير محدد'
      };
    }

    return {
      ...item,
      available_for_sale: true
    };
  } catch (error) {
    logError(error, 'getItemInfoForSale');
    return null;
  }
}

/**
 * حذف عميل
 * @param {number} customerId - معرف العميل
 * @param {string} userRole - دور المستخدم الحالي
 * @returns {Object} - نتيجة العملية
 */
async function deleteCustomer(customerId, userRole) {
  try {
    logSystem(`محاولة حذف العميل بالمعرف: ${customerId} بواسطة مستخدم بدور: ${userRole}`, 'info');

    // التحقق من صلاحيات المستخدم
    if (userRole === 'viewer' || userRole === 'employee') {
      logSystem(`رفض حذف العميل: المستخدم بدور ${userRole} لا يملك الصلاحية`, 'warning');
      return {
        success: false,
        error: `ليس لديك صلاحية لحذف العملاء. المستخدم بدور ${userRole} لا يمكنه حذف العملاء.`
      };
    }

    if (!db) {
      logSystem('قاعدة البيانات غير مهيأة في deleteCustomer', 'error');
      return { success: false, error: 'قاعدة البيانات غير مهيأة' };
    }

    // التحويل إلى رقم
    const numericCustomerId = Number(customerId);
    if (isNaN(numericCustomerId) || numericCustomerId <= 0) {
      logSystem(`معرف العميل غير صالح: ${customerId}`, 'error');
      return { success: false, error: `معرف العميل غير صالح: ${customerId}` };
    }

    logSystem(`بدء عملية حذف العميل بالمعرف: ${numericCustomerId}`, 'info');

    // بدء معاملة قاعدة البيانات
    try {
      const result = db.transaction(() => {
        // التحقق من وجود العميل
        const checkCustomerStmt = db.prepare('SELECT * FROM customers WHERE id = ?');
        const customer = checkCustomerStmt.get(numericCustomerId);

        if (!customer) {
          logSystem(`العميل غير موجود: ${numericCustomerId}`, 'error');
          throw new Error(`العميل غير موجود: ${numericCustomerId}`);
        }

        logSystem(`تم العثور على العميل: ${customer.name} (${numericCustomerId})`, 'info');

        // التحقق من عدم وجود عملاء فرعيين
        const checkSubCustomersStmt = db.prepare('SELECT COUNT(*) as count FROM customers WHERE parent_id = ?');
        const { count: subCustomersCount } = checkSubCustomersStmt.get(numericCustomerId);

        if (subCustomersCount > 0) {
          logSystem(`لا يمكن حذف العميل لأنه يحتوي على ${subCustomersCount} عميل فرعي`, 'warning');
          throw new Error(`لا يمكن حذف العميل لأنه يحتوي على ${subCustomersCount} عميل فرعي. يجب حذف العملاء الفرعيين أولاً.`);
        }

        // التحقق من عدم وجود معاملات مرتبطة بالعميل
        const checkTransactionsStmt = db.prepare('SELECT COUNT(*) as count FROM transactions WHERE customer_id = ?');
        const { count: transactionsCount } = checkTransactionsStmt.get(numericCustomerId);

        if (transactionsCount > 0) {
          logSystem(`لا يمكن حذف العميل لأنه مرتبط بـ ${transactionsCount} معاملة`, 'warning');
          throw new Error(`لا يمكن حذف العميل لأنه مرتبط بـ ${transactionsCount} معاملة.`);
        }

        logSystem(`جاري حذف سجل مبيعات العميل...`, 'info');

        // التحقق من وجود جدول customer_sales_history
        try {
          // التحقق من وجود الجدول
          const tableCheckStmt = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='customer_sales_history'");
          const tableExists = tableCheckStmt.get();

          if (tableExists) {
            // حذف سجل مبيعات العميل
            const deleteSalesHistoryStmt = db.prepare('DELETE FROM customer_sales_history WHERE customer_id = ?');
            const salesHistoryResult = deleteSalesHistoryStmt.run(numericCustomerId);
            logSystem(`تم حذف ${salesHistoryResult.changes} سجل من سجل مبيعات العميل`, 'info');
          } else {
            logSystem(`جدول customer_sales_history غير موجود، تخطي عملية حذف سجل المبيعات`, 'warning');
          }
        } catch (historyError) {
          logSystem(`خطأ أثناء محاولة حذف سجل مبيعات العميل: ${historyError.message}`, 'warning');
          // لا نرمي استثناء هنا، نستمر في عملية حذف العميل
        }

        logSystem(`جاري حذف العميل...`, 'info');

        // حذف العميل
        const deleteCustomerStmt = db.prepare('DELETE FROM customers WHERE id = ?');
        const deleteResult = deleteCustomerStmt.run(numericCustomerId);

        // التحقق من نجاح عملية الحذف
        if (deleteResult.changes === 0) {
          logSystem(`فشل في حذف العميل: لم يتم تحديث أي سجل`, 'error');
          throw new Error(`فشل في حذف العميل: لم يتم تحديث أي سجل`);
        }

        logSystem(`تم حذف العميل بنجاح: ${numericCustomerId} (${customer.name})`, 'info');

        // تحديث الذاكرة المؤقتة بعد حذف العميل
        if (customersCache && customersCache.length > 0) {
          // حذف العميل من الذاكرة المؤقتة
          customersCache = customersCache.filter(c =>
            c.id !== numericCustomerId &&
            String(c.id) !== String(numericCustomerId)
          );

          logSystem(`تم تحديث الذاكرة المؤقتة بعد حذف العميل، عدد العملاء المتبقي: ${customersCache.length}`, 'info');

          // تحديث الذاكرة المؤقتة العالمية إذا كانت موجودة
          if (global && global.customersCache) {
            global.customersCache = [...customersCache];
            logSystem(`تم تحديث الذاكرة المؤقتة العالمية بعد حذف العميل`, 'info');
          }
        }

        return {
          success: true,
          customerId: numericCustomerId,
          customerName: customer.name
        };
      })();

      return result;
    } catch (transactionError) {
      logError(transactionError, 'deleteCustomer - transaction');
      return {
        success: false,
        error: transactionError.message,
        customerId: numericCustomerId
      };
    }
  } catch (error) {
    logError(error, 'deleteCustomer');
    return {
      success: false,
      error: error.message,
      customerId: customerId
    };
  }
}

/**
 * إنشاء فاتورة فرعية من فاتورة رئيسية
 * @param {string} parentInvoiceNumber - رقم الفاتورة الرئيسية
 * @param {number} customerId - معرف العميل
 * @param {Array} selectedItems - الأصناف المختارة للفاتورة الفرعية
 * @returns {Object} - نتيجة العملية
 */
function createSubInvoice(parentInvoiceNumber, customerId, selectedItems) {
  try {
    logSystem(`بدء إنشاء فاتورة فرعية من الفاتورة الرئيسية: ${parentInvoiceNumber} للعميل: ${customerId}`, 'info');

    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // التحقق من صحة البيانات
    if (!parentInvoiceNumber || parentInvoiceNumber.trim() === '') {
      return { success: false, error: 'رقم الفاتورة الرئيسية مطلوب' };
    }

    const numericCustomerId = Number(customerId);
    if (isNaN(numericCustomerId) || numericCustomerId <= 0) {
      return { success: false, error: `معرف العميل غير صالح: ${customerId}` };
    }

    if (!selectedItems || !Array.isArray(selectedItems) || selectedItems.length === 0) {
      return { success: false, error: 'يجب تحديد صنف واحد على الأقل للفاتورة الفرعية' };
    }

    // بدء معاملة قاعدة البيانات
    return db.transaction(() => {
      // التحقق من وجود العميل
      const checkCustomerStmt = db.prepare('SELECT * FROM customers WHERE id = ?');
      const customer = checkCustomerStmt.get(numericCustomerId);

      if (!customer) {
        throw new Error(`العميل غير موجود: ${numericCustomerId}`);
      }

      // التحقق من وجود الفاتورة الرئيسية
      const checkInvoiceStmt = db.prepare('SELECT COUNT(*) as count FROM transactions WHERE invoice_number = ?');
      const { count } = checkInvoiceStmt.get(parentInvoiceNumber);

      if (count === 0) {
        throw new Error(`الفاتورة الرئيسية غير موجودة: ${parentInvoiceNumber}`);
      }

      // إنشاء رقم فاتورة فرعية مطابق للفاتورة الرئيسية مع تغيير الحرف الأول فقط
      let subInvoiceNumber;

      // التحقق من أن الفاتورة الرئيسية تبدأ بـ H
      if (parentInvoiceNumber.startsWith('H')) {
        // استبدال الحرف H بالحرف F مع الحفاظ على باقي الرقم
        subInvoiceNumber = 'F' + parentInvoiceNumber.substring(1);
      } else {
        // إذا لم تبدأ الفاتورة الرئيسية بـ H، نستخدم الطريقة القديمة
        subInvoiceNumber = `F${new Date().getTime().toString().slice(-5).padStart(5, '0')}`;
        logSystem(`تحذير: الفاتورة الرئيسية لا تبدأ بـ H: ${parentInvoiceNumber}، تم إنشاء رقم فاتورة فرعية بالطريقة القديمة: ${subInvoiceNumber}`, 'warning');
      }

      logSystem(`تم إنشاء رقم فاتورة فرعية: ${subInvoiceNumber} من الفاتورة الرئيسية: ${parentInvoiceNumber}`, 'info');

      // استيراد وظيفة إنشاء معرف فريد للمعاملة
      const { generateTransactionId } = require('./unified-transaction-manager');

      // إنشاء معاملات جديدة للفاتورة الفرعية
      const insertTransactionStmt = db.prepare(`
        INSERT INTO transactions (
          transaction_id, item_id, customer_id, quantity, price, total_price, profit,
          transaction_type, transaction_date, notes, invoice_number, parent_invoice_number
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, 'sale', ?, ?, ?, ?)
      `);

      const now = new Date().toISOString();
      let totalAmount = 0;
      let totalProfit = 0;

      // إضافة كل صنف مختار إلى الفاتورة الفرعية
      for (const item of selectedItems) {
        const itemId = item.item_id;
        const quantity = Number(item.quantity) || 0;
        const price = Number(item.price) || 0;
        const totalPrice = quantity * price;
        const profit = Number(item.profit) || 0;

        // التحقق من صحة البيانات
        if (!itemId || isNaN(quantity) || quantity <= 0 || isNaN(price) || price < 0) {
          continue; // تخطي الأصناف غير الصالحة
        }

        // إنشاء معرف فريد للمعاملة
        const transactionId = generateTransactionId('sale');
        logSystem(`تم إنشاء معرف فريد للمعاملة: ${transactionId}`, 'info');

        // إضافة المعاملة
        const result = insertTransactionStmt.run(
          transactionId,
          itemId,
          numericCustomerId,
          quantity,
          price,
          totalPrice,
          profit,
          now,
          `فاتورة فرعية من الفاتورة ${parentInvoiceNumber}`,
          subInvoiceNumber,
          parentInvoiceNumber
        );

        if (result.changes > 0) {
          totalAmount += totalPrice;
          totalProfit += profit;
        }
      }

      // الحصول على المعاملات المضافة
      const getTransactionsStmt = db.prepare(`
        SELECT
          t.id,
          t.item_id,
          t.quantity,
          t.price,
          t.total_price,
          t.profit,
          t.transaction_date,
          t.notes,
          t.invoice_number,
          t.parent_invoice_number,
          i.name as item_name,
          i.unit as item_unit
        FROM transactions t
        LEFT JOIN items i ON t.item_id = i.id
        WHERE t.invoice_number = ?
        ORDER BY t.id
      `);

      const transactions = getTransactionsStmt.all(subInvoiceNumber);

      if (transactions.length === 0) {
        throw new Error('فشل في إنشاء الفاتورة الفرعية: لم يتم إضافة أي معاملات');
      }

      return {
        success: true,
        subInvoice: {
          invoice_number: subInvoiceNumber,
          parent_invoice_number: parentInvoiceNumber,
          customer_id: numericCustomerId,
          customer_name: customer.name,
          transaction_date: now,
          items: transactions,
          total_amount: totalAmount,
          total_profit: totalProfit,
          count: transactions.length
        }
      };
    })();
  } catch (error) {
    logError(error, 'createSubInvoice');
    return { success: false, error: error.message };
  }
}

/**
 * تحديث قائمة العملاء في الذاكرة المؤقتة
 * @param {Array} customers - قائمة العملاء الجديدة
 * @returns {Object} - نتيجة العملية
 */
function updateCustomersInMemory(customers) {
  try {
    if (!Array.isArray(customers)) {
      return {
        success: false,
        error: 'قائمة العملاء غير صالحة'
      };
    }

    logSystem(`تحديث قائمة العملاء في الذاكرة المؤقتة: ${customers.length} عميل`, 'info');

    // تحقق من عدد العملاء
    if (customers.length <= 1) {
      logSystem('عدد العملاء في القائمة الجديدة قليل جداً، جلب البيانات من قاعدة البيانات بدلاً من ذلك', 'info');

      // جلب جميع العملاء من قاعدة البيانات
      try {
        if (db) {
          const stmt = db.prepare(`
            SELECT c.id, c.name, c.contact_person, c.phone, c.email, c.address,
                   c.customer_type, c.parent_id, c.credit_limit, c.balance,
                   c.created_at,
                   p.name as parent_name
            FROM customers c
            LEFT JOIN customers p ON c.parent_id = p.id
            ORDER BY c.name
          `);

          const refreshedCustomers = stmt.all();

          if (refreshedCustomers && refreshedCustomers.length > 0) {
            logSystem(`تم جلب ${refreshedCustomers.length} عميل من قاعدة البيانات`, 'info');

            // تحديث الذاكرة المؤقتة
            customersCache = [...refreshedCustomers];

            // تحديث المتغير العالمي أيضًا
            if (global) {
              global.customersCache = [...refreshedCustomers];
            }

            return {
              success: true,
              count: refreshedCustomers.length,
              message: 'تم تحديث قائمة العملاء من قاعدة البيانات'
            };
          }
        }
      } catch (dbError) {
        logError(dbError, 'updateCustomersInMemory - database refresh');
        logSystem('فشل في جلب العملاء من قاعدة البيانات، استخدام القائمة المقدمة', 'warning');
      }
    }

    // تحديث الذاكرة المؤقتة
    customersCache = [...customers];

    // تحديث المتغير العالمي أيضًا
    if (global) {
      global.customersCache = [...customers];
    }

    logSystem(`تم تحديث قائمة العملاء في الذاكرة المؤقتة بنجاح`, 'info');

    return {
      success: true,
      count: customers.length
    };
  } catch (error) {
    logError(error, 'updateCustomersInMemory');
    return {
      success: false,
      error: error.message || 'حدث خطأ أثناء تحديث قائمة العملاء في الذاكرة المؤقتة'
    };
  }
}

/**
 * تحديث سياق العملاء في الذاكرة العامة
 * @param {Array} customers - قائمة العملاء المحدثة
 * @returns {Object} - نتيجة العملية
 */
function updateCustomersContext(customers) {
  try {
    logSystem(`بدء تحديث سياق العملاء في الذاكرة العامة...`, 'info');

    if (!Array.isArray(customers)) {
      logSystem(`قائمة العملاء ليست مصفوفة`, 'error');
      return { success: false, error: 'قائمة العملاء ليست مصفوفة' };
    }

    // تحديث الذاكرة المؤقتة
    customersCache = [...customers];

    // تحديث المتغير العالمي أيضًا
    if (global) {
      global.customersCache = [...customers];
    }

    logSystem(`تم تحديث سياق العملاء في الذاكرة العامة بنجاح، عدد العملاء: ${customers.length}`, 'info');

    return {
      success: true,
      count: customers.length
    };
  } catch (error) {
    logError(error, 'updateCustomersContext');
    return {
      success: false,
      error: error.message || 'حدث خطأ أثناء تحديث سياق العملاء في الذاكرة العامة'
    };
  }
}

// تصدير الوظائف
module.exports = {
  initialize,
  getAllCustomers,
  getCustomerById,
  searchCustomers,
  getCustomersByType,
  getSubCustomers,
  addCustomer,
  updateCustomer,
  deleteCustomer,
  getCustomerSalesHistory,
  getItemInfoForSale,
  createSubInvoice,
  updateCustomersInMemory,
  updateCustomersContext
};
