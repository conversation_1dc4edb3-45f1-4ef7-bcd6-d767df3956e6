/* أنماط مكون النافذة المنبثقة */
.app-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1050;
  backdrop-filter: blur(3px);
  animation: app-modal-backdrop-fade 0.2s ease-out;
}

@keyframes app-modal-backdrop-fade {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.app-modal-container {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: app-modal-in 0.3s ease-out;
  width: 90%;
  max-width: 600px;
}

@keyframes app-modal-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* أحجام النوافذ المنبثقة */
.app-modal-sm {
  max-width: 400px;
}

.app-modal-md {
  max-width: 600px;
}

.app-modal-lg {
  max-width: 800px;
}

.app-modal-xl {
  max-width: 1000px;
}

.app-modal-full {
  max-width: 90%;
  width: 90%;
  height: 90vh;
}

/* رأس النافذة */
.app-modal-header {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.02);
}

.app-modal-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-dark);
}

.app-modal-close {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: var(--text-light);
  transition: color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  border-radius: 50%;
}

.app-modal-close:hover {
  color: var(--danger-color);
  background-color: rgba(0, 0, 0, 0.05);
}

/* محتوى النافذة */
.app-modal-body {
  padding: var(--spacing-lg);
  overflow-y: auto;
  flex: 1;
  color: var(--text-dark);
}

/* تذييل النافذة */
.app-modal-footer {
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  background-color: rgba(0, 0, 0, 0.01);
}

/* تنسيقات للشاشات الصغيرة */
@media (max-width: 768px) {
  .app-modal-container {
    width: 95%;
    max-height: 95vh;
  }
  
  .app-modal-header {
    padding: var(--spacing-sm) var(--spacing-md);
  }
  
  .app-modal-body {
    padding: var(--spacing-md);
  }
  
  .app-modal-footer {
    padding: var(--spacing-sm) var(--spacing-md);
    flex-wrap: wrap;
  }
  
  .app-modal-footer button {
    flex: 1;
    min-width: 120px;
  }
}
