.error-boundary {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  padding: 20px;
  text-align: center;
}

.error-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 30px;
  max-width: 600px;
  width: 100%;
}

.error-icon {
  font-size: 48px;
  color: #e74c3c;
  margin-bottom: 20px;
}

.error-boundary h2 {
  margin-bottom: 15px;
  color: #333;
}

.error-boundary p {
  margin-bottom: 20px;
  color: #666;
}

.error-details {
  margin: 20px 0;
  text-align: left;
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  overflow: auto;
  max-height: 200px;
}

.error-details h3 {
  margin-bottom: 10px;
  font-size: 16px;
  color: #333;
}

.error-details pre {
  font-family: monospace;
  font-size: 12px;
  white-space: pre-wrap;
  color: #e74c3c;
}

.error-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
}

.error-actions button {
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s;
}

.error-actions .btn-primary {
  background-color: #3498db;
  color: white;
  border: none;
}

.error-actions .btn-primary:hover {
  background-color: #2980b9;
}

.error-actions .btn-secondary {
  background-color: #ecf0f1;
  color: #34495e;
  border: 1px solid #bdc3c7;
}

.error-actions .btn-secondary:hover {
  background-color: #dfe6e9;
}
