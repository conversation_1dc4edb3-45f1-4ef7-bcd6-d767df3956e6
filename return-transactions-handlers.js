/**
 * معالجات عمليات الإرجاع
 * يقوم بتسجيل معالجات عمليات الإرجاع في نظام IPC
 */

const { ipcMain } = require('electron');
const { logError, logSystem } = require('./error-handler');
const returnTransactionsManager = require('./return-transactions-manager');

/**
 * تسجيل معالجات عمليات الإرجاع
 */
function registerReturnTransactionsHandlers() {
  try {
    logSystem('تسجيل معالجات عمليات الإرجاع في return-transactions-handlers.js', 'info');

    // معالج إنشاء عملية إرجاع جديدة
    try {
      // التحقق من وجود المعالج قبل تسجيله
      if (!ipcMain.eventNames().includes('create-return-transaction')) {
        ipcMain.handle('create-return-transaction', async (event, returnData) => {
          try {
            logSystem(`إنشاء عملية إرجاع جديدة للصنف ${returnData.item_id} والعميل ${returnData.customer_id}`, 'info');
            const result = await returnTransactionsManager.createReturnTransaction(returnData);
            return { success: true, ...result };
          } catch (error) {
            logError(error, 'create-return-transaction');
            return { success: false, error: error.message };
          }
        });
      } else {
        logSystem('معالج create-return-transaction مسجل بالفعل، تم تجاوزه', 'info');
      }
    } catch (error) {
      logError(error, 'تسجيل معالج create-return-transaction');
    }

    // معالج التحقق من حالة نظام الإرجاع
    try {
      // التحقق من وجود المعالج قبل تسجيله
      if (!ipcMain.eventNames().includes('check-return-system-status')) {
        ipcMain.handle('check-return-system-status', async () => {
          try {
            logSystem('التحقق من حالة نظام الإرجاع', 'info');
            const status = returnTransactionsManager.checkReturnSystemStatus();
            return { success: true, status };
          } catch (error) {
            logError(error, 'check-return-system-status');
            return { success: false, status: null, error: error.message };
          }
        });
      } else {
        logSystem('معالج check-return-system-status مسجل بالفعل، تم تجاوزه', 'info');
      }
    } catch (error) {
      logError(error, 'تسجيل معالج check-return-system-status');
    }

    // معالج إصلاح نظام الإرجاع
    try {
      // التحقق من وجود المعالج قبل تسجيله
      if (!ipcMain.eventNames().includes('repair-return-system')) {
        ipcMain.handle('repair-return-system', async () => {
          try {
            logSystem('إصلاح نظام الإرجاع', 'info');
            const result = returnTransactionsManager.repairReturnSystem();
            return { success: result.success, message: result.message, repairs: result.repairs };
          } catch (error) {
            logError(error, 'repair-return-system');
            return { success: false, message: error.message, repairs: [] };
          }
        });
      } else {
        logSystem('معالج repair-return-system مسجل بالفعل، تم تجاوزه', 'info');
      }
    } catch (error) {
      logError(error, 'تسجيل معالج repair-return-system');
    }

    // معالج التحقق من توفر نظام الإرجاع الجديد
    try {
      // التحقق من وجود المعالج قبل تسجيله
      if (!ipcMain.eventNames().includes('check-return-system-availability')) {
        ipcMain.handle('check-return-system-availability', async () => {
          try {
            logSystem('التحقق من توفر نظام الإرجاع الجديد', 'info');
            const status = returnTransactionsManager.checkReturnSystemStatus();
            return {
              success: true,
              useNewSystem: status.isReady,
              status
            };
          } catch (error) {
            logError(error, 'check-return-system-availability');
            return {
              success: false,
              useNewSystem: false,
              error: error.message
            };
          }
        });
      } else {
        logSystem('معالج check-return-system-availability مسجل بالفعل، تم تجاوزه', 'info');
      }
    } catch (error) {
      logError(error, 'تسجيل معالج check-return-system-availability');
    }

    // معالج التحقق من حالة المخزون بعد عملية الإرجاع
    try {
      // التحقق من وجود المعالج قبل تسجيله
      if (!ipcMain.eventNames().includes('verify-inventory-after-return')) {
        ipcMain.handle('verify-inventory-after-return', async (event, { itemId }) => {
          try {
            logSystem(`التحقق من حالة المخزون بعد عملية الإرجاع للصنف ${itemId}`, 'info');
            const result = await returnTransactionsManager.verifyInventoryAfterReturn(itemId);
            return { success: true, ...result };
          } catch (error) {
            logError(error, 'verify-inventory-after-return');
            return { success: false, error: error.message };
          }
        });
      } else {
        logSystem('معالج verify-inventory-after-return مسجل بالفعل، تم تجاوزه', 'info');
      }
    } catch (error) {
      logError(error, 'تسجيل معالج verify-inventory-after-return');
    }

    // معالج الحصول على الكمية المتاحة للإرجاع
    try {
      // التحقق من وجود المعالج قبل تسجيله
      if (!ipcMain.eventNames().includes('get-available-quantity-for-return')) {
        ipcMain.handle('get-available-quantity-for-return', async (event, { itemId, customerId, originalTransactionId }) => {
          try {
            logSystem(`الحصول على الكمية المتاحة للإرجاع للصنف ${itemId} والعميل ${customerId}`, 'info');
            const availableQuantity = await returnTransactionsManager.getAvailableQuantityForReturn(
              itemId,
              customerId,
              originalTransactionId
            );
            return { success: true, availableQuantity };
          } catch (error) {
            logError(error, 'get-available-quantity-for-return');
            return { success: false, availableQuantity: 0, error: error.message };
          }
        });
      } else {
        logSystem('معالج get-available-quantity-for-return مسجل بالفعل، تم تجاوزه', 'info');
      }
    } catch (error) {
      logError(error, 'تسجيل معالج get-available-quantity-for-return');
    }

    // معالج الحصول على تقرير عمليات الإرجاع
    try {
      // التحقق من وجود المعالج قبل تسجيله
      if (!ipcMain.eventNames().includes('get-return-transactions-report')) {
        ipcMain.handle('get-return-transactions-report', async (event, filters) => {
          try {
            logSystem(`الحصول على تقرير عمليات الإرجاع مع الفلاتر: ${JSON.stringify(filters || {})}`, 'info');
            const returnTransactions = returnTransactionsManager.getAllReturnTransactions(filters);

            // حساب إحصائيات الإرجاعات
            const stats = {
              totalReturns: returnTransactions.length,
              totalReturnAmount: returnTransactions.reduce((sum, t) => sum + (t.total_price || 0), 0),
              totalReturnQuantity: returnTransactions.reduce((sum, t) => sum + (t.quantity || 0), 0),
              averageReturnAmount: returnTransactions.length > 0
                ? returnTransactions.reduce((sum, t) => sum + (t.total_price || 0), 0) / returnTransactions.length
                : 0
            };

            return {
              success: true,
              returnTransactions,
              stats
            };
          } catch (error) {
            logError(error, 'get-return-transactions-report');
            return {
              success: false,
              returnTransactions: [],
              stats: { totalReturns: 0, totalReturnAmount: 0, totalReturnQuantity: 0, averageReturnAmount: 0 },
              error: error.message
            };
          }
        });
      } else {
        logSystem('معالج get-return-transactions-report مسجل بالفعل، تم تجاوزه', 'info');
      }
    } catch (error) {
      logError(error, 'تسجيل معالج get-return-transactions-report');
    }

    // معالج الحصول على عمليات الإرجاع للعميل
    try {
      // التحقق من وجود المعالج قبل تسجيله
      if (!ipcMain.eventNames().includes('get-customer-return-transactions')) {
        ipcMain.handle('get-customer-return-transactions', async (event, customerId) => {
          try {
            logSystem(`الحصول على عمليات الإرجاع للعميل: ${customerId}`, 'info');
            const returnTransactions = returnTransactionsManager.getCustomerReturnTransactions(customerId);
            return { success: true, returnTransactions };
          } catch (error) {
            logError(error, 'get-customer-return-transactions');
            return { success: false, returnTransactions: [], error: error.message };
          }
        });
      } else {
        logSystem('معالج get-customer-return-transactions مسجل بالفعل، تم تجاوزه', 'info');
      }
    } catch (error) {
      logError(error, 'تسجيل معالج get-customer-return-transactions');
    }

    logSystem('تم تسجيل معالجات عمليات الإرجاع بنجاح في return-transactions-handlers.js', 'info');
  } catch (error) {
    logError(error, 'registerReturnTransactionsHandlers');
    throw error;
  }
}

module.exports = {
  registerReturnTransactionsHandlers
};
