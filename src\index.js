import React from 'react';
import ReactDOM from 'react-dom';
import { <PERSON>h<PERSON>outer as Router } from 'react-router-dom';
import App from './App';
import { AuthProvider } from './context/AuthContext';
import { InventoryProvider } from './context/InventoryContext';
import { ThemeProvider } from './context/ThemeContext';
// استيراد وتسجيل مكتبات PDF
import './utils/pdfUtils';
import './utils/pdfLibrarySetup';
import './utils/arabicPdfSupport';
import './index.css';

// إضافة وظيفة عالمية لتحديث بيانات العميل
// سيتم تعيين هذه الوظيفة في AppContext عند تهيئة السياق
window.updateCustomerData = null;

ReactDOM.render(
  <React.StrictMode>
    <Router
      future={{
        v7_startTransition: true,
        v7_relativeSplatPath: true
      }}
    >
      <ThemeProvider>
        <AuthProvider>
          <App />
        </AuthProvider>
      </ThemeProvider>
    </Router>
  </React.StrictMode>,
  document.getElementById('root')
);
