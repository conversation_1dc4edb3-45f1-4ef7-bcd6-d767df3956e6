/* تصميم جديد لنافذة سجل مبيعات العميل */

:root {
  --primary-color: #3b82f6;
  --primary-dark: #1d4ed8;
  --primary-light: #93c5fd;
  --secondary-color: #10b981;
  --secondary-dark: #059669;
  --secondary-light: #6ee7b7;
  --accent-color: #f59e0b;
  --accent-dark: #d97706;
  --accent-light: #fcd34d;
  --danger-color: #ef4444;
  --danger-dark: #dc2626;
  --danger-light: #fca5a5;
  --dark-color: #1f2937;
  --light-color: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 1rem;
  --border-radius-xl: 1.5rem;
  --border-radius-full: 9999px;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  --font-family: 'Cairo', 'Tajawal', sans-serif;
}

/* تأثيرات الحركة */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes shimmer {
  0% { background-position: -1000px 0; }
  100% { background-position: 1000px 0; }
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

/* تنسيق النافذة المنبثقة */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s var(--transition-normal);
}

.modern-history-container {
  width: 95%;
  max-width: 1300px;
  max-height: 90vh;
  background-color: var(--light-color);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-xl);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: slideInUp 0.4s var(--transition-normal);
  position: relative;
}

/* رأس النافذة */
.modern-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.modern-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 50%, rgba(255, 255, 255, 0.2), transparent 70%);
  pointer-events: none;
}

.modern-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.modern-header-icon {
  background-color: rgba(255, 255, 255, 0.2);
  width: 3rem;
  height: 3rem;
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  box-shadow: var(--shadow-md);
}

.modern-close-btn {
  background-color: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.modern-close-btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: rotate(90deg);
}

/* جسم النافذة */
.modern-body {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;
}

/* شريط التنقل */
.modern-tabs {
  display: flex;
  background-color: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  margin-bottom: 1.5rem;
  overflow: hidden;
  position: relative;
}

.modern-tab {
  flex: 1;
  padding: 1rem;
  text-align: center;
  background: transparent;
  border: none;
  color: var(--gray-600);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.modern-tab.active {
  color: var(--primary-color);
}

.modern-tab-indicator {
  position: absolute;
  bottom: 0;
  height: 3px;
  background-color: var(--primary-color);
  transition: all var(--transition-normal);
}

.modern-tab:hover {
  background-color: var(--gray-100);
}

.modern-tab.active::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background-color: var(--primary-color);
}

/* بطاقات الإحصائيات */
.modern-stats {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.modern-stat-card {
  background-color: white;
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-md);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all var(--transition-normal);
}

.modern-stat-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.modern-stat-icon {
  width: 3rem;
  height: 3rem;
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
}

.sales-icon {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

.returns-icon {
  background: linear-gradient(135deg, var(--accent-color), var(--accent-dark));
}

.invoices-icon {
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
}

.profit-icon {
  background: linear-gradient(135deg, var(--primary-dark), var(--secondary-dark));
}

.modern-stat-content {
  flex: 1;
}

.modern-stat-title {
  color: var(--gray-500);
  font-size: 0.9rem;
  margin: 0 0 0.25rem 0;
}

.modern-stat-value {
  color: var(--gray-900);
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}

/* فلاتر البحث */
.modern-filters {
  background-color: white;
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-md);
  margin-bottom: 1.5rem;
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
}

.modern-search {
  flex: 1;
  min-width: 200px;
  position: relative;
}

.modern-search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-400);
}

.modern-search input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius-lg);
  font-size: 1rem;
  transition: all var(--transition-normal);
}

.modern-search input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.modern-date-filters {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.modern-date-filter {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modern-date-filter label {
  color: var(--gray-600);
  font-size: 0.9rem;
}

.modern-date-filter input {
  padding: 0.75rem;
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius-lg);
  font-size: 0.9rem;
  transition: all var(--transition-normal);
}

.modern-date-filter input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.modern-sort-btn {
  background-color: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius-lg);
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--gray-600);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.modern-sort-btn:hover {
  background-color: var(--gray-100);
  color: var(--primary-color);
}

/* جداول البيانات */
.modern-data-container {
  background-color: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  margin-bottom: 1.5rem;
}

.modern-section-title {
  padding: 1.25rem 1.5rem;
  margin: 0;
  font-size: 1.2rem;
  color: var(--gray-800);
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.modern-section-title svg {
  color: var(--primary-color);
}

.modern-table-container {
  overflow-x: auto;
  padding: 0.5rem;
}

.modern-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.modern-table th,
.modern-table td {
  padding: 1rem;
  text-align: right;
}

.modern-table th {
  background-color: var(--gray-100);
  color: var(--gray-700);
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 10;
}

.modern-table th:first-child {
  border-top-right-radius: var(--border-radius-md);
}

.modern-table th:last-child {
  border-top-left-radius: var(--border-radius-md);
}

.modern-table tbody tr {
  transition: all var(--transition-normal);
  border-bottom: 1px solid var(--gray-200);
}

.modern-table tbody tr:last-child {
  border-bottom: none;
}

.modern-table tbody tr:hover {
  background-color: var(--gray-100);
}

.modern-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.35rem 0.75rem;
  border-radius: var(--border-radius-full);
  font-size: 0.85rem;
  font-weight: 500;
}

.modern-badge-sale {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--primary-color);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.modern-badge-return {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--accent-color);
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.modern-badge-main-invoice {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--primary-color);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.modern-badge-sub-invoice {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--secondary-color);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.modern-return-row {
  background-color: rgba(245, 158, 11, 0.05);
  position: relative;
}

.modern-return-row::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 4px;
  background-color: var(--accent-color);
}

.modern-return-row:hover {
  background-color: rgba(245, 158, 11, 0.1) !important;
}

.modern-sub-invoice-row {
  background-color: rgba(16, 185, 129, 0.05);
  position: relative;
}

.modern-sub-invoice-row::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 4px;
  background-color: var(--secondary-color);
}

.modern-sub-invoice-row:hover {
  background-color: rgba(16, 185, 129, 0.1) !important;
}

.modern-main-invoice-row {
  background-color: rgba(59, 130, 246, 0.05);
  position: relative;
}

.modern-main-invoice-row::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 4px;
  background-color: var(--primary-color);
}

.modern-main-invoice-row:hover {
  background-color: rgba(59, 130, 246, 0.1) !important;
}

/* بطاقات الفواتير */
.modern-invoice-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
  padding: 1.5rem;
}

.modern-invoice-card {
  background-color: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: all var(--transition-normal);
  border: 1px solid var(--gray-200);
}

.modern-invoice-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.modern-invoice-header {
  padding: 1.25rem;
  background-color: var(--gray-100);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--gray-200);
}

.modern-invoice-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.modern-invoice-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  color: white;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

.modern-invoice-title h5 {
  margin: 0;
  font-size: 1rem;
  color: var(--gray-800);
}

.modern-invoice-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.modern-invoice-date {
  color: var(--gray-600);
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modern-print-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.modern-print-btn:hover {
  background-color: var(--primary-dark);
}

.modern-invoice-body {
  padding: 1.25rem;
}

.modern-invoice-items {
  width: 100%;
  border-collapse: collapse;
}

.modern-invoice-items th,
.modern-invoice-items td {
  padding: 0.75rem;
  text-align: right;
  border-bottom: 1px solid var(--gray-200);
}

.modern-invoice-items th {
  background-color: var(--gray-100);
  color: var(--gray-700);
  font-weight: 600;
}

.modern-invoice-items tbody tr:last-child td {
  border-bottom: none;
}

.modern-invoice-items tfoot {
  font-weight: 600;
}

.modern-invoice-items tfoot td {
  border-top: 2px solid var(--gray-300);
  background-color: var(--gray-100);
}

/* حالات خاصة */
.modern-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1.5rem;
  text-align: center;
}

.modern-empty-icon {
  font-size: 3rem;
  color: var(--gray-400);
  margin-bottom: 1.5rem;
  animation: float 3s ease-in-out infinite;
}

.modern-empty-title {
  font-size: 1.5rem;
  color: var(--gray-700);
  margin: 0 0 0.75rem 0;
}

.modern-empty-description {
  color: var(--gray-500);
  max-width: 400px;
  margin: 0 auto;
}

.modern-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1.5rem;
}

.modern-spinner {
  width: 3rem;
  height: 3rem;
  border: 0.25rem solid var(--gray-200);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1.5rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.modern-loading-text {
  color: var(--gray-600);
  font-size: 1.1rem;
}

.modern-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1.5rem;
  text-align: center;
}

.modern-error-icon {
  font-size: 3rem;
  color: var(--danger-color);
  margin-bottom: 1.5rem;
}

.modern-error-title {
  font-size: 1.5rem;
  color: var(--danger-dark);
  margin: 0 0 0.75rem 0;
}

.modern-error-description {
  color: var(--gray-600);
  max-width: 400px;
  margin: 0 auto;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .modern-header {
    padding: 1rem;
  }

  .modern-header h3 {
    font-size: 1.2rem;
  }

  .modern-header-icon {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1rem;
  }

  .modern-body {
    padding: 1rem;
  }

  .modern-tabs {
    flex-wrap: nowrap;
    overflow-x: auto;
    padding-bottom: 5px;
  }

  .modern-tab {
    padding: 0.75rem;
    font-size: 0.9rem;
    white-space: nowrap;
  }

  .modern-stats {
    grid-template-columns: repeat(auto-fill, minmax(100%, 1fr));
  }

  .modern-filters {
    flex-direction: column;
    align-items: stretch;
  }

  .modern-date-filters {
    flex-direction: column;
    width: 100%;
  }

  .modern-date-filter {
    width: 100%;
  }

  .modern-invoice-list {
    grid-template-columns: 1fr;
  }

  .modern-invoice-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .modern-invoice-meta {
    width: 100%;
    justify-content: space-between;
  }
}
