/* نمط نموذج الإرجاع المحسن */
.enhanced-return-form {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
  direction: rtl;
}

.enhanced-return-form .form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
  flex-wrap: wrap;
  gap: 10px;
}

.enhanced-return-form .form-header h3 {
  margin: 0;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 10px;
}

.enhanced-return-form .customer-info {
  background-color: #f8f9fa;
  padding: 8px 15px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.enhanced-return-form .system-actions {
  display: flex;
  gap: 10px;
}

.enhanced-return-form .customer-info .label {
  font-weight: bold;
  color: #6c757d;
}

.enhanced-return-form .customer-info .value {
  font-weight: bold;
  color: #2c3e50;
}

.enhanced-return-form .form-section {
  margin-bottom: 25px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.enhanced-return-form .form-section h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #495057;
  font-size: 1.1rem;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 8px;
}

.enhanced-return-form .form-row {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.enhanced-return-form .form-group {
  flex: 1;
  margin-bottom: 15px;
}

.enhanced-return-form label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #495057;
}

.enhanced-return-form input,
.enhanced-return-form textarea,
.enhanced-return-form select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 1rem;
}

.enhanced-return-form input:focus,
.enhanced-return-form textarea:focus,
.enhanced-return-form select:focus {
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.enhanced-return-form input[readonly] {
  background-color: #e9ecef;
  cursor: not-allowed;
}

.enhanced-return-form .item-selection {
  background-color: #fff;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.enhanced-return-form .item-search {
  margin-bottom: 15px;
}

.enhanced-return-form .selected-item-details {
  background-color: #f0f7ff;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #cce5ff;
  margin-top: 15px;
}

.enhanced-return-form .add-item-btn {
  margin-top: 10px;
  display: flex;
  align-items: center;
  gap: 5px;
  justify-content: center;
}

.enhanced-return-form .return-items-table {
  overflow-x: auto;
}

.enhanced-return-form table {
  width: 100%;
  border-collapse: collapse;
}

.enhanced-return-form th,
.enhanced-return-form td {
  padding: 10px;
  text-align: center;
  border: 1px solid #dee2e6;
}

.enhanced-return-form th {
  background-color: #f8f9fa;
  font-weight: 600;
}

.enhanced-return-form tbody tr:nth-child(even) {
  background-color: #f8f9fa;
}

.enhanced-return-form tbody tr:hover {
  background-color: #e9ecef;
}

.enhanced-return-form tfoot {
  font-weight: bold;
}

.enhanced-return-form tfoot td {
  background-color: #e9ecef;
}

.enhanced-return-form .text-left {
  text-align: left;
}

.enhanced-return-form .form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.enhanced-return-form .btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  display: flex;
  align-items: center;
  gap: 5px;
}

.enhanced-return-form .btn-primary {
  background-color: #007bff;
  color: white;
}

.enhanced-return-form .btn-primary:hover {
  background-color: #0069d9;
}

.enhanced-return-form .btn-success {
  background-color: #28a745;
  color: white;
}

.enhanced-return-form .btn-success:hover {
  background-color: #218838;
}

.enhanced-return-form .btn-secondary {
  background-color: #6c757d;
  color: white;
}

.enhanced-return-form .btn-secondary:hover {
  background-color: #5a6268;
}

.enhanced-return-form .btn-danger {
  background-color: #dc3545;
  color: white;
}

.enhanced-return-form .btn-danger:hover {
  background-color: #c82333;
}

.enhanced-return-form .btn-warning {
  background-color: #ffc107;
  color: #212529;
}

.enhanced-return-form .btn-warning:hover {
  background-color: #e0a800;
}

.enhanced-return-form .btn-sm {
  padding: 4px 8px;
  font-size: 0.875rem;
}

.enhanced-return-form .btn:disabled {
  opacity: 0.65;
  cursor: not-allowed;
}

.enhanced-return-form .alert {
  padding: 12px 15px;
  margin-bottom: 20px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.enhanced-return-form .alert-danger {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.enhanced-return-form .alert-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.enhanced-return-form .alert-warning {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeeba;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
  .enhanced-return-form .form-row {
    flex-direction: column;
    gap: 10px;
  }

  .enhanced-return-form .form-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .enhanced-return-form .customer-info {
    width: 100%;
  }
}
