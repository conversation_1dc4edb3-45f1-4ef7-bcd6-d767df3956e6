import { useState, useEffect, useRef } from 'react';
import {
  FaHistory,
  FaShoppingCart,
  FaMoneyBillWave,
  FaChartLine,
  FaCalendarAlt,
  FaBoxOpen,
  FaTimes,
  FaPrint,
  FaPhone,
  FaReceipt,
  FaExclamationTriangle,
  FaFileInvoice,
  FaCheck,
  FaCheckSquare,
  FaSquare,
  FaUndo,
  FaFilter,
  FaSortAmountDown,
  FaSortAmountUp,
  FaSearch,
  FaFileAlt,
  FaFileContract,
  FaInfoCircle,
  FaTag,
  FaClipboardList,
  FaExchangeAlt,
  FaArrowLeft,
  FaArrowRight,
  FaCalendarCheck,
  FaUserAlt
} from 'react-icons/fa';
import { useApp } from '../context/AppContext';
import '../assets/css/modern-customer-history.css';

// مكون لعرض سجل عمليات العميل بتصميم جديد
const EnhancedCustomerSalesHistory = ({ customerId, onClose }) => {
  const { getCustomerSalesHistory } = useApp();
  const [salesData, setSalesData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // بيانات مفصلة حسب نوع العملية
  const [salesTransactions, setSalesTransactions] = useState([]);
  const [returnTransactions, setReturnTransactions] = useState([]);
  const [mainInvoices, setMainInvoices] = useState([]);
  const [subInvoices, setSubInvoices] = useState([]);

  // حالة التصفية والفرز
  const [activeTab, setActiveTab] = useState('all');
  const [dateFilter, setDateFilter] = useState({ startDate: '', endDate: '' });
  const [sortOrder, setSortOrder] = useState('desc');
  const [searchTerm, setSearchTerm] = useState('');

  // مراجع للتأثيرات الحركية
  const tabIndicatorRef = useRef(null);
  const tabsRef = useRef(null);

  // تحديث مؤشر التبويب النشط
  useEffect(() => {
    if (tabIndicatorRef.current && tabsRef.current) {
      const activeTabElement = tabsRef.current.querySelector(`.modern-tab[data-tab="${activeTab}"]`);
      if (activeTabElement) {
        const { offsetLeft, offsetWidth } = activeTabElement;
        tabIndicatorRef.current.style.left = `${offsetLeft}px`;
        tabIndicatorRef.current.style.width = `${offsetWidth}px`;
      }
    }
  }, [activeTab]);

  // تحميل بيانات العمليات
  useEffect(() => {
    const loadSalesHistory = async () => {
      try {
        setLoading(true);
        console.log('جاري تحميل سجل العمليات للعميل:', customerId);

        // التحقق من وجود وظيفة getCustomerSalesHistory
        if (typeof getCustomerSalesHistory !== 'function') {
          console.error('وظيفة getCustomerSalesHistory غير متوفرة');
          setError('وظيفة getCustomerSalesHistory غير متوفرة');
          setLoading(false);
          return;
        }

        // التحقق من صحة معرف العميل
        if (!customerId) {
          console.error('معرف العميل غير صالح');
          setError('معرف العميل غير صالح');
          setLoading(false);
          return;
        }

        // الحصول على بيانات العمليات
        const data = await getCustomerSalesHistory(customerId);
        console.log('تم استلام بيانات العمليات:', data);
        setSalesData(data);

        // تصنيف العمليات حسب النوع
        // التحقق من وجود البيانات في الهيكل الصحيح (sales أو salesHistory)
        const salesArray = data.sales || data.salesHistory || [];

        if (Array.isArray(salesArray)) {
          console.log('تم العثور على مصفوفة المبيعات بطول:', salesArray.length);

          // عمليات البيع
          const sales = salesArray.filter(item => item.transaction_type === 'sale');
          console.log('عدد عمليات البيع:', sales.length);
          setSalesTransactions(sales);

          // استخدام البيانات المتاحة للحصول على عمليات الإرجاع
          try {
            console.log('استخدام البيانات المتاحة للحصول على عمليات الإرجاع');

            // البحث عن عمليات الإرجاع في البيانات المتاحة
            const returns = salesArray.filter(item =>
              item.transaction_type === 'return' ||
              (item.notes && item.notes.includes('مرتجع')) ||
              (item.quantity < 0) // الكميات السالبة قد تشير إلى عمليات إرجاع
            );

            console.log('عدد عمليات الإرجاع من البيانات المتاحة:', returns.length);

            // تنسيق البيانات للتأكد من أنها تتبع نفس الهيكل
            const formattedReturns = returns.map(returnItem => ({
              ...returnItem,
              transaction_type: 'return', // التأكد من تعيين نوع المعاملة
              dataType: 'return' // إضافة حقل للتمييز في واجهة المستخدم
            }));

            setReturnTransactions(formattedReturns);

            // محاولة الحصول على عمليات الإرجاع من الجدول الجديد كمحاولة ثانية
            try {
              if (window.api && typeof window.api.invoke === 'function') {
                console.log('محاولة الحصول على عمليات الإرجاع من الجدول الجديد كمحاولة ثانية');
                const returnTransactionsResult = await window.api.invoke('get-all-return-transactions', { customer_id: customerId });

                if (returnTransactionsResult && returnTransactionsResult.success && Array.isArray(returnTransactionsResult.returnTransactions)) {
                  console.log('تم الحصول على عمليات الإرجاع من الجدول الجديد:', returnTransactionsResult.returnTransactions);

                  // تحويل بيانات عمليات الإرجاع لتتوافق مع هيكل البيانات المتوقع
                  const newFormattedReturns = returnTransactionsResult.returnTransactions
                    .filter(item => item.customer_id === parseInt(customerId))
                    .map(returnItem => ({
                      id: returnItem.id,
                      return_id: returnItem.return_id,
                      item_id: returnItem.item_id,
                      item_name: returnItem.item_name,
                      quantity: returnItem.quantity,
                      price: returnItem.price,
                      total_price: returnItem.total_price,
                      customer_id: returnItem.customer_id,
                      invoice_number: returnItem.invoice_number,
                      notes: returnItem.notes,
                      transaction_date: returnItem.return_date,
                      transaction_type: 'return',
                      dataType: 'return',
                      original_transaction_id: returnItem.original_transaction_id,
                      inventory_updated: returnItem.inventory_updated
                    }));

                  if (newFormattedReturns.length > 0) {
                    console.log('استخدام بيانات الإرجاع من الجدول الجديد بدلاً من البيانات القديمة');
                    setReturnTransactions(newFormattedReturns);
                  }
                }
              }
            } catch (secondAttemptError) {
              console.log('فشلت المحاولة الثانية للحصول على بيانات الإرجاع:', secondAttemptError);
              // الاستمرار باستخدام البيانات التي تم الحصول عليها سابقاً
            }
          } catch (returnError) {
            console.error('خطأ في الحصول على عمليات الإرجاع:', returnError);
            // استخدام مصفوفة فارغة في حالة الخطأ
            setReturnTransactions([]);
          }

          // تجميع الفواتير الرئيسية والفرعية بطريقة محسنة
          const invoiceGroups = {};
          const subInvoicesList = [];
          const mainInvoicesList = [];
          const processedInvoices = new Set(); // لتتبع الفواتير التي تمت معالجتها

          // أولاً: تحديد الفواتير الفرعية
          salesArray.forEach(transaction => {
            const invoiceNumber = transaction.invoice_number || '';
            const parentInvoiceNumber = transaction.parent_invoice_number || '';
            const notes = transaction.notes || '';

            // تحديد ما إذا كانت فاتورة فرعية بناءً على وجود parent_invoice_number
            // أو وجود كلمة "فرعية" في الملاحظات
            const isSubInvoice =
              (parentInvoiceNumber && parentInvoiceNumber !== invoiceNumber) ||
              notes.includes('فرعية') ||
              notes.includes('فرعي') ||
              (invoiceNumber && invoiceNumber.startsWith('F')); // الفواتير الفرعية غالباً ما تبدأ بحرف F

            if (isSubInvoice) {
              // تجنب تكرار الفواتير الفرعية
              if (!subInvoicesList.some(inv => inv.invoice_number === invoiceNumber)) {
                const actualParentInvoice = parentInvoiceNumber ||
                  (notes.match(/H\d+/) ? notes.match(/H\d+/)[0] : 'غير محدد');

                subInvoicesList.push({
                  invoice_number: invoiceNumber,
                  parent_invoice_number: actualParentInvoice,
                  items: [transaction],
                  transaction_date: transaction.transaction_date,
                  total_amount: Number(transaction.total_price) || 0,
                  total_profit: Number(transaction.profit) || 0,
                  is_sub_invoice: true // علامة لتمييز الفواتير الفرعية
                });

                // إضافة رقم الفاتورة إلى القائمة المعالجة
                processedInvoices.add(invoiceNumber);
              } else {
                // إضافة المعاملة إلى الفاتورة الفرعية الموجودة
                const subInvoice = subInvoicesList.find(inv => inv.invoice_number === invoiceNumber);
                subInvoice.items.push(transaction);
                subInvoice.total_amount += Number(transaction.total_price) || 0;
                subInvoice.total_profit += Number(transaction.profit) || 0;
              }
            }
          });

          // ثانياً: تحديد الفواتير الرئيسية (التي ليست فرعية)
          salesArray.forEach(transaction => {
            const invoiceNumber = transaction.invoice_number || '';
            const notes = transaction.notes || '';

            // تجاهل المعاملات التي تنتمي إلى فواتير فرعية تم معالجتها بالفعل
            if (processedInvoices.has(invoiceNumber)) {
              return;
            }

            // تحديد ما إذا كانت فاتورة رئيسية
            const isMainInvoice =
              !processedInvoices.has(invoiceNumber) &&
              (invoiceNumber.startsWith('H') || // الفواتير الرئيسية غالباً ما تبدأ بحرف H
               (!invoiceNumber.startsWith('F') && !notes.includes('فرعية') && !notes.includes('فرعي')));

            if (isMainInvoice) {
              if (!invoiceGroups[invoiceNumber]) {
                invoiceGroups[invoiceNumber] = {
                  invoice_number: invoiceNumber,
                  items: [transaction],
                  transaction_date: transaction.transaction_date,
                  total_amount: Number(transaction.total_price) || 0,
                  total_profit: Number(transaction.profit) || 0,
                  is_main_invoice: true // علامة لتمييز الفواتير الرئيسية
                };
              } else {
                // إضافة المعاملة إلى الفاتورة الرئيسية الموجودة
                invoiceGroups[invoiceNumber].items.push(transaction);
                invoiceGroups[invoiceNumber].total_amount += Number(transaction.total_price) || 0;
                invoiceGroups[invoiceNumber].total_profit += Number(transaction.profit) || 0;
              }
            }
          });

          // تحويل الفواتير الرئيسية إلى مصفوفة
          Object.values(invoiceGroups).forEach(invoice => {
            // التأكد من أن الفاتورة ليست فرعية
            if (!subInvoicesList.some(subInv => subInv.invoice_number === invoice.invoice_number)) {
              mainInvoicesList.push(invoice);
            }
          });

          console.log('عدد الفواتير الرئيسية بعد التحسين:', mainInvoicesList.length);
          console.log('عدد الفواتير الفرعية بعد التحسين:', subInvoicesList.length);
          setMainInvoices(mainInvoicesList);
          setSubInvoices(subInvoicesList);
        } else {
          console.error('لم يتم العثور على مصفوفة المبيعات في البيانات المرجعة:', data);
        }

        setLoading(false);
      } catch (error) {
        console.error('خطأ في تحميل سجل العمليات:', error);
        setError(error.message || 'حدث خطأ أثناء تحميل سجل العمليات');
        setLoading(false);
      }
    };

    loadSalesHistory();
  }, [customerId, getCustomerSalesHistory]);

  // تنسيق التاريخ بشكل رقمي وبالأرقام الإفرنجية
  const formatDate = (dateString) => {
    if (!dateString) return '-';
    try {
      const date = new Date(dateString);

      // استخدام تنسيق رقمي للتاريخ (YYYY-MM-DD HH:MM)
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');

      // إرجاع التاريخ بتنسيق رقمي
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    } catch (error) {
      console.error('خطأ في تنسيق التاريخ:', error);
      return dateString;
    }
  };

  // تطبيق التصفية على البيانات
  const getFilteredData = () => {
    let filteredData = [];

    // تحديد البيانات حسب التبويب النشط
    switch (activeTab) {
      case 'returns':
        filteredData = [...returnTransactions];
        break;
      case 'mainInvoices':
        filteredData = [...mainInvoices];
        break;
      case 'subInvoices':
        filteredData = [...subInvoices];
        break;
      default:
        // عرض جميع العمليات (بدون المبيعات)
        filteredData = [
          ...returnTransactions.map(item => ({ ...item, dataType: 'return' }))
        ];

        // إضافة عناصر من الفواتير الرئيسية والفرعية للعرض في تبويب "جميع العمليات"
        mainInvoices.forEach(invoice => {
          invoice.items.forEach(item => {
            filteredData.push({
              ...item,
              dataType: 'main_invoice',
              invoice_number: invoice.invoice_number
            });
          });
        });

        subInvoices.forEach(invoice => {
          invoice.items.forEach(item => {
            filteredData.push({
              ...item,
              dataType: 'sub_invoice',
              invoice_number: invoice.invoice_number,
              parent_invoice_number: invoice.parent_invoice_number
            });
          });
        });
        break;
    }

    // تطبيق فلتر التاريخ
    if (dateFilter.startDate) {
      filteredData = filteredData.filter(item =>
        new Date(item.transaction_date) >= new Date(dateFilter.startDate)
      );
    }

    if (dateFilter.endDate) {
      filteredData = filteredData.filter(item =>
        new Date(item.transaction_date) <= new Date(dateFilter.endDate)
      );
    }

    // تطبيق فلتر البحث
    if (searchTerm) {
      filteredData = filteredData.filter(item =>
        (item.item_name && item.item_name.includes(searchTerm)) ||
        (item.invoice_number && item.invoice_number.includes(searchTerm))
      );
    }

    // تطبيق الترتيب
    filteredData.sort((a, b) => {
      const dateA = new Date(a.transaction_date);
      const dateB = new Date(b.transaction_date);
      return sortOrder === 'desc' ? dateB - dateA : dateA - dateB;
    });

    return filteredData;
  };

  // طباعة الفاتورة
  const printInvoice = (invoice) => {
    try {
      console.log('طباعة الفاتورة من سجل العمليات:', invoice);

      // استخدام وظيفة الطباعة المحسنة من ملف printInvoice
      const printInvoiceUtil = require('../utils/printInvoice').default;

      if (typeof printInvoiceUtil !== 'function') {
        console.error('وظيفة طباعة الفاتورة غير متوفرة');
        alert('وظيفة طباعة الفاتورة غير متوفرة. سيتم استخدام الطريقة البديلة.');
        useFallbackPrintMethod(invoice);
        return;
      }

      // تحويل بيانات الفاتورة إلى الشكل المطلوب
      const saleData = {
        invoice_number: invoice.invoice_number || '',
        transaction_date: invoice.transaction_date || new Date().toISOString(),
        items: invoice.items || [],
        total_price: invoice.total_amount || 0
      };

      // بيانات العميل
      const customerData = {
        name: customer?.name || 'عميل',
        phone: customer?.phone || ''
      };

      // إعدادات الشركة
      const settings = {
        systemName: window.appSettings?.companyName || 'شركة اتش قروب',
        address: window.appSettings?.companyAddress || 'للتصميم و تصنيع الأثاث والديكورات',
        logoUrl: window.appSettings?.logoUrl || ''
      };

      // طباعة الفاتورة
      printInvoiceUtil(saleData, customerData, settings);

      console.log('تم إرسال الفاتورة للطباعة بنجاح');
    } catch (error) {
      console.error('خطأ في طباعة الفاتورة:', error);
      alert('حدث خطأ أثناء محاولة طباعة الفاتورة. سيتم استخدام الطريقة البديلة.');
      useFallbackPrintMethod(invoice);
    }
  };

  // طريقة بديلة للطباعة في حالة فشل الطريقة الرئيسية
  const useFallbackPrintMethod = (invoice) => {
    console.log('استخدام الطريقة البديلة لطباعة الفاتورة:', invoice);

    // إنشاء نافذة طباعة جديدة
    const printWindow = window.open('', '_blank', 'width=800,height=600');

    // التحقق من إنشاء النافذة بنجاح
    if (!printWindow) {
      alert('يرجى السماح بالنوافذ المنبثقة لطباعة الفاتورة');
      return;
    }

    // إنشاء محتوى HTML للطباعة
    const printContent = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>فاتورة مبيعات</title>
        <style>
          body {
            font-family: Arial, Tahoma, sans-serif;
            margin: 0;
            padding: 20px;
            direction: rtl;
          }
          .invoice-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
          }
          .invoice-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
          }
          .invoice-number {
            font-size: 18px;
            color: #555;
            margin-bottom: 5px;
          }
          .invoice-date {
            font-size: 14px;
            color: #777;
          }
          .customer-info {
            margin-bottom: 30px;
          }
          .customer-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
          }
          .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
          }
          .invoice-table th, .invoice-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: right;
          }
          .invoice-table th {
            background-color: #f5f5f5;
            font-weight: bold;
          }
          .invoice-table tr:nth-child(even) {
            background-color: #f9f9f9;
          }
          .invoice-total {
            text-align: left;
            margin-top: 20px;
            font-size: 18px;
            font-weight: bold;
          }
          .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 14px;
            color: #777;
            border-top: 1px solid #eee;
            padding-top: 20px;
          }
          @media print {
            body {
              padding: 0;
              margin: 0;
            }
            .no-print {
              display: none;
            }
            .page-break {
              page-break-before: always;
            }
          }
        </style>
      </head>
      <body>
        <div class="invoice-header">
          <div class="invoice-title">فاتورة مبيعات</div>
          <div class="invoice-number">رقم الفاتورة: ${invoice.invoice_number || 'غير محدد'}</div>
          <div class="invoice-date">التاريخ: ${formatDate(invoice.transaction_date) || new Date().toLocaleDateString('ar-EG')}</div>
        </div>

        <div class="customer-info">
          <div class="customer-name">العميل: ${customer.name || 'غير محدد'}</div>
          ${customer.phone ? `<div>رقم الهاتف: ${customer.phone}</div>` : ''}
          ${customer.address ? `<div>العنوان: ${customer.address}</div>` : ''}
        </div>

        <table class="invoice-table">
          <thead>
            <tr>
              <th>#</th>
              <th>الصنف</th>
              <th>الكمية</th>
              <th>السعر</th>
              <th>الإجمالي</th>
            </tr>
          </thead>
          <tbody>
            ${invoice.items.map((item, index) => `
              <tr>
                <td>${index + 1}</td>
                <td>${item.item_name || 'غير محدد'}</td>
                <td>${item.quantity}</td>
                <td>${Number(item.price).toFixed(2)}</td>
                <td>${Number(item.total_price).toFixed(2)}</td>
              </tr>
            `).join('')}
          </tbody>
          <tfoot>
            <tr>
              <td colspan="4" style="text-align: left; font-weight: bold;">الإجمالي:</td>
              <td style="font-weight: bold;">${Number(invoice.total_amount || 0).toFixed(2)}</td>
            </tr>
          </tfoot>
        </table>

        <div class="footer">
          <p>شكراً لتعاملكم معنا</p>
        </div>

        <div class="no-print" style="text-align: center; margin-top: 30px;">
          <button onclick="window.print()" style="padding: 10px 20px; background-color: #1a3a5f; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;">
            طباعة الفاتورة
          </button>
        </div>
      </body>
      </html>
    `;

    // كتابة المحتوى في نافذة الطباعة
    printWindow.document.open();
    printWindow.document.documentElement.innerHTML = printContent;
    printWindow.document.close();

    // الانتظار حتى يتم تحميل المحتوى ثم طباعته
    printWindow.onload = function() {
      // تأخير قصير للتأكد من تحميل الأنماط
      setTimeout(() => {
        printWindow.focus();
        printWindow.print();
      }, 500);
    };
  };

  if (loading) {
    return (
      <div className="modal-backdrop">
        <div className="modern-history-container">
          <div className="modern-header">
            <h3>
              <span className="modern-header-icon"><FaHistory /></span>
              سجل عمليات العميل
            </h3>
            <button className="modern-close-btn" onClick={onClose}>
              <FaTimes />
            </button>
          </div>
          <div className="modern-body">
            <div className="modern-loading">
              <div className="modern-spinner"></div>
              <p className="modern-loading-text">جاري تحميل البيانات...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="modal-backdrop">
        <div className="modern-history-container">
          <div className="modern-header">
            <h3>
              <span className="modern-header-icon"><FaHistory /></span>
              سجل عمليات العميل
            </h3>
            <button className="modern-close-btn" onClick={onClose}>
              <FaTimes />
            </button>
          </div>
          <div className="modern-body">
            <div className="modern-error">
              <div className="modern-error-icon">
                <FaExclamationTriangle />
              </div>
              <h3 className="modern-error-title">حدث خطأ</h3>
              <p className="modern-error-description">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!salesData || !salesData.customer) {
    return (
      <div className="modal-backdrop">
        <div className="modern-history-container">
          <div className="modern-header">
            <h3>
              <span className="modern-header-icon"><FaHistory /></span>
              سجل عمليات العميل
            </h3>
            <button className="modern-close-btn" onClick={onClose}>
              <FaTimes />
            </button>
          </div>
          <div className="modern-body">
            <div className="modern-empty-state">
              <div className="modern-empty-icon">
                <FaShoppingCart />
              </div>
              <h3 className="modern-empty-title">لا توجد بيانات متاحة</h3>
              <p className="modern-empty-description">لا توجد بيانات متاحة لهذا العميل في الوقت الحالي</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const { customer, totalSales, totalProfit } = salesData;
  const filteredData = getFilteredData();

  return (
    <div className="modal-backdrop">
      <div className="modern-history-container">
        <div className="modern-header">
          <h3>
            <span className="modern-header-icon"><FaHistory /></span>
            سجل عمليات العميل: {customer.name}
          </h3>
          <button className="modern-close-btn" onClick={onClose} title="إغلاق">
            <FaTimes />
          </button>
        </div>
        <div className="modern-body">
          {/* بطاقات الإحصائيات */}
          <div className="modern-stats">
            <div className="modern-stat-card">
              <div className="modern-stat-icon returns-icon">
                <FaUndo />
              </div>
              <div className="modern-stat-content">
                <h5 className="modern-stat-title">عدد المرتجعات</h5>
                <p className="modern-stat-value" dir="ltr">{returnTransactions.length}</p>
              </div>
            </div>

            <div className="modern-stat-card">
              <div className="modern-stat-icon invoices-icon">
                <FaFileAlt />
              </div>
              <div className="modern-stat-content">
                <h5 className="modern-stat-title">الفواتير الرئيسية</h5>
                <p className="modern-stat-value" dir="ltr">{mainInvoices.length}</p>
              </div>
            </div>

            <div className="modern-stat-card">
              <div className="modern-stat-icon invoices-icon">
                <FaFileContract />
              </div>
              <div className="modern-stat-content">
                <h5 className="modern-stat-title">الفواتير الفرعية</h5>
                <p className="modern-stat-value" dir="ltr">{subInvoices.length}</p>
              </div>
            </div>

            <div className="modern-stat-card">
              <div className="modern-stat-icon sales-icon">
                <FaMoneyBillWave />
              </div>
              <div className="modern-stat-content">
                <h5 className="modern-stat-title">إجمالي المبيعات</h5>
                <p className="modern-stat-value" dir="ltr">{totalSales?.toFixed(2) || '0.00'}</p>
              </div>
            </div>

            <div className="modern-stat-card">
              <div className="modern-stat-icon profit-icon">
                <FaChartLine />
              </div>
              <div className="modern-stat-content">
                <h5 className="modern-stat-title">إجمالي الأرباح</h5>
                <p className="modern-stat-value" dir="ltr">{totalProfit?.toFixed(2) || '0.00'}</p>
              </div>
            </div>
          </div>

          {/* شريط التنقل */}
          <div className="modern-tabs" ref={tabsRef}>
            <button
              className={`modern-tab ${activeTab === 'all' ? 'active' : ''}`}
              onClick={() => setActiveTab('all')}
              data-tab="all"
            >
              <FaHistory /> جميع العمليات
            </button>
            <button
              className={`modern-tab ${activeTab === 'returns' ? 'active' : ''}`}
              onClick={() => setActiveTab('returns')}
              data-tab="returns"
            >
              <FaUndo /> المرتجعات
            </button>
            <button
              className={`modern-tab ${activeTab === 'mainInvoices' ? 'active' : ''}`}
              onClick={() => setActiveTab('mainInvoices')}
              data-tab="mainInvoices"
            >
              <FaFileAlt /> الفواتير الرئيسية
            </button>
            <button
              className={`modern-tab ${activeTab === 'subInvoices' ? 'active' : ''}`}
              onClick={() => setActiveTab('subInvoices')}
              data-tab="subInvoices"
            >
              <FaFileContract /> الفواتير الفرعية
            </button>
            <div className="modern-tab-indicator" ref={tabIndicatorRef}></div>
          </div>

          {/* فلاتر البحث */}
          <div className="modern-filters">
            <div className="modern-search">
              <FaSearch className="modern-search-icon" />
              <input
                type="text"
                placeholder="بحث عن صنف أو رقم فاتورة..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <div className="modern-date-filters">
              <div className="modern-date-filter">
                <label><FaCalendarAlt /> من:</label>
                <input
                  type="date"
                  value={dateFilter.startDate}
                  onChange={(e) => setDateFilter({...dateFilter, startDate: e.target.value})}
                />
              </div>
              <div className="modern-date-filter">
                <label><FaCalendarAlt /> إلى:</label>
                <input
                  type="date"
                  value={dateFilter.endDate}
                  onChange={(e) => setDateFilter({...dateFilter, endDate: e.target.value})}
                />
              </div>
            </div>

            <button
              className="modern-sort-btn"
              onClick={() => setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc')}
              title={sortOrder === 'desc' ? 'ترتيب تصاعدي' : 'ترتيب تنازلي'}
            >
              {sortOrder === 'desc' ? <FaSortAmountDown /> : <FaSortAmountUp />}
            </button>
          </div>

          {/* عرض البيانات */}
          <div className="modern-data-container">
            {filteredData.length === 0 ? (
              <div className="modern-empty-state">
                <div className="modern-empty-icon">
                  <FaExclamationTriangle />
                </div>
                <h3 className="modern-empty-title">لا توجد بيانات</h3>
                <p className="modern-empty-description">لا توجد بيانات متطابقة مع معايير البحث الحالية</p>
              </div>
            ) : (
              <>
                {activeTab === 'all' && (
                  <div>
                    <h4 className="modern-section-title">
                      <FaHistory /> جميع العمليات
                    </h4>
                    <div className="modern-table-container">
                      <table className="modern-table">
                        <thead>
                          <tr>
                            <th>#</th>
                            <th>نوع العملية</th>
                            <th>الصنف</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>الإجمالي</th>
                            <th>رقم الفاتورة</th>
                            <th>التاريخ</th>
                          </tr>
                        </thead>
                        <tbody>
                          {filteredData.map((item, index) => (
                            <tr
                              key={`transaction-${item.id || index}`}
                              className={
                                item.dataType === 'return'
                                  ? 'modern-return-row'
                                  : item.dataType === 'sub_invoice'
                                    ? 'modern-sub-invoice-row'
                                    : item.dataType === 'main_invoice'
                                      ? 'modern-main-invoice-row'
                                      : ''
                              }
                            >
                              <td dir="ltr">{index + 1}</td>
                              <td>
                                {item.dataType === 'return' ? (
                                  <span className="modern-badge modern-badge-return"><FaUndo /> مرتجع</span>
                                ) : item.dataType === 'main_invoice' ? (
                                  <span className="modern-badge modern-badge-main-invoice"><FaFileAlt /> فاتورة رئيسية</span>
                                ) : item.dataType === 'sub_invoice' ? (
                                  <span className="modern-badge modern-badge-sub-invoice"><FaFileContract /> فاتورة فرعية</span>
                                ) : (
                                  <span className="modern-badge modern-badge-sale"><FaShoppingCart /> مبيعات</span>
                                )}
                              </td>
                              <td>{item.item_name || 'غير محدد'}</td>
                              <td dir="ltr">{item.quantity}</td>
                              <td dir="ltr">{Number(item.price).toFixed(2)}</td>
                              <td dir="ltr">{Number(item.total_price).toFixed(2)}</td>
                              <td dir="ltr">{item.invoice_number || '-'}</td>
                              <td dir="ltr">{formatDate(item.transaction_date)}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}



                {activeTab === 'returns' && (
                  <div>
                    <h4 className="modern-section-title">
                      <FaUndo /> المرتجعات
                    </h4>
                    <div className="modern-table-container">
                      <table className="modern-table">
                        <thead>
                          <tr>
                            <th>#</th>
                            <th>الصنف</th>
                            <th>الكمية المرتجعة</th>
                            <th>السعر</th>
                            <th>الإجمالي</th>
                            <th>رقم الفاتورة</th>
                            <th>تاريخ الإرجاع</th>
                          </tr>
                        </thead>
                        <tbody>
                          {filteredData.map((item, index) => (
                            <tr key={`return-${item.id || index}`}>
                              <td dir="ltr">{index + 1}</td>
                              <td>{item.item_name || 'غير محدد'}</td>
                              <td dir="ltr">{item.quantity}</td>
                              <td dir="ltr">{Number(item.price).toFixed(2)}</td>
                              <td dir="ltr">{Number(item.total_price).toFixed(2)}</td>
                              <td dir="ltr">{item.invoice_number || '-'}</td>
                              <td dir="ltr">{formatDate(item.transaction_date)}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}

                {activeTab === 'mainInvoices' && (
                  <div>
                    <h4 className="modern-section-title">
                      <FaFileAlt /> الفواتير الرئيسية
                    </h4>
                    <div className="modern-invoice-list">
                      {filteredData.map((invoice, index) => (
                        <div key={`main-invoice-${invoice.invoice_number || index}`} className="modern-invoice-card">
                          <div className="modern-invoice-header">
                            <div className="modern-invoice-title">
                              <div className="modern-invoice-icon">
                                <FaFileAlt />
                              </div>
                              <h5>فاتورة رقم: {invoice.invoice_number || `فاتورة-${index + 1}`}</h5>
                            </div>
                            <div className="modern-invoice-meta">
                              <span className="modern-invoice-date">
                                <FaCalendarAlt /> <span dir="ltr">{formatDate(invoice.transaction_date)}</span>
                              </span>
                              <button className="modern-print-btn" onClick={() => printInvoice(invoice)} title="طباعة الفاتورة">
                                <FaPrint /> طباعة
                              </button>
                            </div>
                          </div>
                          <div className="modern-invoice-body">
                            <table className="modern-invoice-items">
                              <thead>
                                <tr>
                                  <th>#</th>
                                  <th>الصنف</th>
                                  <th>الكمية</th>
                                  <th>السعر</th>
                                  <th>الإجمالي</th>
                                </tr>
                              </thead>
                              <tbody>
                                {invoice.items.map((item, itemIndex) => (
                                  <tr key={`main-invoice-item-${item.id || itemIndex}`}>
                                    <td dir="ltr">{itemIndex + 1}</td>
                                    <td>{item.item_name || 'غير محدد'}</td>
                                    <td dir="ltr">{item.quantity}</td>
                                    <td dir="ltr">{Number(item.price).toFixed(2)}</td>
                                    <td dir="ltr">{Number(item.total_price).toFixed(2)}</td>
                                  </tr>
                                ))}
                              </tbody>
                              <tfoot>
                                <tr>
                                  <td colSpan="4" style={{ textAlign: 'left' }}>الإجمالي:</td>
                                  <td dir="ltr">{Number(invoice.total_amount).toFixed(2)}</td>
                                </tr>
                              </tfoot>
                            </table>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {activeTab === 'subInvoices' && (
                  <div>
                    <h4 className="modern-section-title">
                      <FaFileContract /> الفواتير الفرعية
                    </h4>
                    <div className="modern-invoice-list">
                      {filteredData.map((invoice, index) => (
                        <div key={`sub-invoice-${invoice.invoice_number || index}`} className="modern-invoice-card">
                          <div className="modern-invoice-header">
                            <div className="modern-invoice-title">
                              <div className="modern-invoice-icon">
                                <FaFileContract />
                              </div>
                              <h5>
                                فاتورة فرعية رقم: {invoice.invoice_number || `فاتورة-فرعية-${index + 1}`}
                                <span className="parent-invoice-badge">
                                  تابعة للفاتورة: {invoice.parent_invoice_number}
                                </span>
                              </h5>
                            </div>
                            <div className="modern-invoice-meta">
                              <span className="modern-invoice-date">
                                <FaCalendarAlt /> <span dir="ltr">{formatDate(invoice.transaction_date)}</span>
                              </span>
                              <button className="modern-print-btn" onClick={() => printInvoice(invoice)} title="طباعة الفاتورة">
                                <FaPrint /> طباعة
                              </button>
                            </div>
                          </div>
                          <div className="modern-invoice-body">
                            <table className="modern-invoice-items">
                              <thead>
                                <tr>
                                  <th>#</th>
                                  <th>الصنف</th>
                                  <th>الكمية</th>
                                  <th>السعر</th>
                                  <th>الإجمالي</th>
                                </tr>
                              </thead>
                              <tbody>
                                {invoice.items.map((item, itemIndex) => (
                                  <tr key={`sub-invoice-item-${item.id || itemIndex}`}>
                                    <td dir="ltr">{itemIndex + 1}</td>
                                    <td>{item.item_name || 'غير محدد'}</td>
                                    <td dir="ltr">{item.quantity}</td>
                                    <td dir="ltr">{Number(item.price).toFixed(2)}</td>
                                    <td dir="ltr">{Number(item.total_price).toFixed(2)}</td>
                                  </tr>
                                ))}
                              </tbody>
                              <tfoot>
                                <tr>
                                  <td colSpan="4" style={{ textAlign: 'left' }}>الإجمالي:</td>
                                  <td dir="ltr">{Number(invoice.total_amount).toFixed(2)}</td>
                                </tr>
                              </tfoot>
                            </table>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedCustomerSalesHistory;