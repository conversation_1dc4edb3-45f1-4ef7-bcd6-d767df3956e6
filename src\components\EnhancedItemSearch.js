import { useState, useEffect, useRef } from 'react';
import { FaSearch, FaTimes, FaExclamationTriangle, FaRulerHorizontal, FaBoxOpen, FaMoneyBillWave } from 'react-icons/fa';
import './EnhancedItemSearch.css';

/**
 * مكون البحث المحسن عن الأصناف
 * @param {Function} onSelect - دالة تُستدعى عند اختيار صنف
 * @param {Function} onClose - دالة تُستدعى عند إغلاق النافذة
 */
const EnhancedItemSearch = ({ onSelect, onClose }) => {
  const [items, setItems] = useState([]);
  const [filteredItems, setFilteredItems] = useState([]);
  const [selectedItem, setSelectedItem] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const searchInputRef = useRef(null);

  // تحميل الأصناف من قاعدة البيانات عند فتح النافذة
  useEffect(() => {
    const loadItems = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log('بدء تحميل بيانات الأصناف والمخزون...');

        // جلب قائمة الأصناف أولاً
        const itemsList = await window.api.invoke('get-all-items');
        console.log(`تم جلب ${itemsList.length} صنف من قاعدة البيانات`);

        if (!itemsList || itemsList.length === 0) {
          setItems([]);
          setFilteredItems([]);
          setError('لم يتم العثور على أصناف في قاعدة البيانات');
          return;
        }

        // جلب المخزون مع تجاوز التخزين المؤقت للحصول على أحدث البيانات
        const inventoryList = await window.api.invoke('get-inventory', true);
        console.log(`تم جلب ${inventoryList.length} عنصر من المخزون`);

        // إنشاء خريطة للمخزون للوصول السريع
        const inventoryMap = new Map();
        inventoryList.forEach(item => {
          if (item && item.item_id) {
            inventoryMap.set(item.item_id, item);
          } else if (item && item.id) {
            inventoryMap.set(item.id, item);
          }
        });

        console.log(`تم إنشاء خريطة المخزون بـ ${inventoryMap.size} عنصر`);

        // دمج بيانات الأصناف مع بيانات المخزون
        const mergedItems = [];

        for (const item of itemsList) {
          try {
            // البحث عن معلومات المخزون للصنف
            const inventoryItem = inventoryMap.get(item.id);

            // جلب معلومات المخزون مباشرة من قاعدة البيانات
            console.log(`جلب معلومات المخزون مباشرة للصنف: ${item.name} (${item.id})`);

            try {
              // استخدام معلمة bypassCache=true لتجاوز التخزين المؤقت
              const directInventoryItem = await window.api.invoke('get-inventory-for-item', item.id, true);

              if (directInventoryItem) {
                console.log(`تم جلب معلومات المخزون مباشرة للصنف ${item.name}:`);
                console.log(`- الكمية المتوفرة: ${directInventoryItem.current_quantity}`);
                console.log(`- سعر البيع: ${directInventoryItem.selling_price}`);

                mergedItems.push({
                  id: item.id,
                  name: item.name || directInventoryItem.name || 'صنف بدون اسم',
                  unit: item.unit || directInventoryItem.unit || 'قطعة',
                  current_quantity: typeof directInventoryItem.current_quantity === 'number' ? directInventoryItem.current_quantity : 0,
                  selling_price: typeof directInventoryItem.selling_price === 'number' ? directInventoryItem.selling_price : 0,
                  minimum_quantity: typeof directInventoryItem.minimum_quantity === 'number' ? directInventoryItem.minimum_quantity : 0
                });

                continue; // انتقل إلى العنصر التالي
              }
            } catch (innerError) {
              console.error(`خطأ في جلب معلومات المخزون مباشرة للصنف ${item.name}:`, innerError);
            }

            // استخدام معلومات المخزون إذا وجدت، وإلا استخدام قيم افتراضية
            mergedItems.push({
              id: item.id,
              name: item.name || 'صنف بدون اسم',
              unit: item.unit || 'قطعة',
              current_quantity: inventoryItem && typeof inventoryItem.current_quantity === 'number' ? inventoryItem.current_quantity : 0,
              selling_price: inventoryItem && typeof inventoryItem.selling_price === 'number' ? inventoryItem.selling_price : 0,
              minimum_quantity: inventoryItem && typeof inventoryItem.minimum_quantity === 'number' ? inventoryItem.minimum_quantity : 0
            });
          } catch (itemError) {
            console.error(`خطأ في معالجة الصنف ${item.name}:`, itemError);
          }
        }

        console.log(`تم دمج بيانات ${mergedItems.length} صنف مع المخزون بنجاح`);

        // عرض عينة من البيانات للتشخيص
        if (mergedItems.length > 0) {
          const sampleItems = mergedItems.slice(0, Math.min(3, mergedItems.length));
          sampleItems.forEach(item => {
            console.log(`عينة: ${item.name} - الكمية=${item.current_quantity}, السعر=${item.selling_price}`);
          });
        }

        // ترتيب الأصناف بحيث تظهر الأصناف ذات الكمية المتوفرة وسعر البيع أولاً
        const sortedItems = [...mergedItems].sort((a, b) => {
          // الأصناف ذات الكمية المتوفرة وسعر البيع أولاً
          const aValid = a.current_quantity > 0 && a.selling_price > 0;
          const bValid = b.current_quantity > 0 && b.selling_price > 0;

          if (aValid && !bValid) return -1;
          if (!aValid && bValid) return 1;

          // ثم ترتيب حسب الاسم
          return a.name.localeCompare(b.name);
        });

        setItems(sortedItems);
        setFilteredItems(sortedItems);
      } catch (error) {
        console.error('خطأ في تحميل الأصناف:', error);
        setError('حدث خطأ أثناء تحميل الأصناف. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };

    loadItems();

    // تركيز حقل البحث عند فتح النافذة
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);

  // تصفية الأصناف بناءً على مصطلح البحث
  useEffect(() => {
    if (!items || items.length === 0) {
      setFilteredItems([]);
      return;
    }

    if (searchTerm.trim() === '') {
      setFilteredItems(items);
    } else {
      const filtered = items.filter(item =>
        (item.name && item.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (item.id && item.id.toString().includes(searchTerm))
      );
      setFilteredItems(filtered);
    }
  }, [items, searchTerm]);

  // معالجة تغيير مصطلح البحث
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  // معالجة اختيار صنف
  const handleItemClick = (item) => {
    setSelectedItem(item);
  };

  // معالجة تأكيد الاختيار
  const handleConfirm = async () => {
    if (selectedItem) {
      try {
        console.log('تأكيد اختيار الصنف:', selectedItem);

        // استخدام الوظيفة الجديدة للحصول على معلومات الصنف للبيع
        console.log(`جلب معلومات الصنف للبيع: ${selectedItem.id} (${selectedItem.name})`);

        const itemInfo = await window.api.customers.getItemInfoForSale(selectedItem.id);

        if (itemInfo) {
          console.log('تم استلام معلومات الصنف للبيع:');
          console.log('- المعرف:', itemInfo.id);
          console.log('- الاسم:', itemInfo.name);
          console.log('- الكمية المتوفرة:', itemInfo.current_quantity);
          console.log('- سعر البيع:', itemInfo.selling_price);
          console.log('- متاح للبيع:', itemInfo.available_for_sale);

          if (itemInfo.message) {
            console.log('- رسالة:', itemInfo.message);
          }

          // إنشاء كائن الصنف المحدث مع البيانات من قاعدة البيانات
          const updatedItem = {
            id: itemInfo.id,
            name: itemInfo.name || selectedItem.name || 'صنف بدون اسم',
            unit: itemInfo.unit || selectedItem.unit || 'قطعة',
            current_quantity: typeof itemInfo.current_quantity === 'number' ? itemInfo.current_quantity : 0,
            selling_price: typeof itemInfo.selling_price === 'number' ? itemInfo.selling_price : 0,
            minimum_quantity: typeof itemInfo.minimum_quantity === 'number' ? itemInfo.minimum_quantity : 0,
            available_for_sale: itemInfo.available_for_sale,
            message: itemInfo.message
          };

          // إرسال البيانات المحدثة إلى صفحة البيع
          onSelect(updatedItem);
          return;
        }
      } catch (error) {
        console.error('خطأ في الحصول على معلومات الصنف للبيع:', error);
      }

      // في حالة فشل جلب البيانات المحدثة، استخدم البيانات المحلية
      // مع إضافة علامة تحذير
      onSelect({
        ...selectedItem,
        available_for_sale: selectedItem.current_quantity > 0,
        message: 'تم استخدام بيانات محلية، قد لا تكون محدثة'
      });
    }
  };

  // معالجة الضغط على مفتاح
  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      onClose();
    } else if (e.key === 'Enter' && selectedItem) {
      handleConfirm();
    }
  };

  // تحديد لون الكمية بناءً على قيمتها
  const getQuantityColorClass = (quantity, minimumQuantity = 0) => {
    if (quantity <= 0) return 'out';
    if (minimumQuantity > 0 && quantity <= minimumQuantity) return 'low';
    return '';
  };

  return (
    <div className="item-search-popup" onKeyDown={handleKeyDown}>
      <div className="item-search-container">
        <div className="item-search-header">
          <h3>البحث عن صنف</h3>
          <button className="item-search-close" onClick={onClose}>
            <FaTimes />
          </button>
        </div>

        <div className="item-search-body">
          <div className="item-search-input">
            <FaSearch className="search-icon" />
            <input
              ref={searchInputRef}
              type="text"
              placeholder="اكتب اسم الصنف أو الرقم..."
              value={searchTerm}
              onChange={handleSearchChange}
            />
          </div>

          {loading ? (
            <div style={{ textAlign: 'center', padding: '20px', color: 'var(--text-light, #777)' }}>
              جاري تحميل الأصناف...
            </div>
          ) : error ? (
            <div style={{ textAlign: 'center', padding: '20px', color: 'var(--danger-color, #e74c3c)' }}>
              <FaExclamationTriangle style={{ marginLeft: '5px' }} />
              {error}
            </div>
          ) : filteredItems.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '20px', color: 'var(--text-light, #777)' }}>
              {items.length === 0 ? 'لا توجد أصناف متاحة' : 'لا توجد نتائج مطابقة للبحث'}
            </div>
          ) : (
            <div className="item-search-results">
              {filteredItems.map(item => (
                <div
                  key={item.id}
                  className={`item-card ${selectedItem && selectedItem.id === item.id ? 'selected' : ''}`}
                  onClick={() => handleItemClick(item)}
                  onDoubleClick={handleConfirm}
                >
                  <div className="item-name">{item.name || 'صنف بدون اسم'}</div>
                  <div className="item-details">
                    <div className="item-detail">
                      <span className="item-detail-label">
                        <FaRulerHorizontal style={{ marginLeft: '5px' }} />
                        الوحدة:
                      </span>
                      <span className="item-detail-value">{item.unit || 'غير محدد'}</span>
                    </div>
                    <div className="item-detail">
                      <span className="item-detail-label">
                        <FaBoxOpen style={{ marginLeft: '5px' }} />
                        الكمية المتوفرة:
                      </span>
                      <span
                        className={`item-detail-value item-quantity ${getQuantityColorClass(item.current_quantity, item.minimum_quantity)}`}
                        title={`الكمية المتوفرة: ${item.current_quantity || 0}`}
                      >
                        <strong>{item.current_quantity || 0}</strong>
                        {item.current_quantity <= 0 && (
                          <FaExclamationTriangle style={{ marginRight: '5px', color: '#e74c3c' }} title="الكمية غير متوفرة" />
                        )}
                      </span>
                    </div>
                    <div className="item-detail">
                      <span className="item-detail-label">
                        <FaMoneyBillWave style={{ marginLeft: '5px' }} />
                        سعر البيع:
                      </span>
                      <span
                        className="item-detail-value"
                        title={`سعر البيع: ${item.selling_price ? item.selling_price.toFixed(2) : '0.00'}`}
                      >
                        <strong>{item.selling_price ? item.selling_price.toFixed(2) : '0.00'}</strong>
                        {(item.selling_price <= 0) && (
                          <FaExclamationTriangle style={{ marginRight: '5px', color: '#e74c3c' }} title="سعر البيع غير محدد" />
                        )}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="item-search-footer">
          <button
            className="item-search-btn item-search-btn-secondary"
            onClick={onClose}
          >
            إلغاء
          </button>
          <button
            className="item-search-btn item-search-btn-primary"
            onClick={handleConfirm}
            disabled={!selectedItem}
          >
            اختيار
          </button>
        </div>
      </div>
    </div>
  );
};

export default EnhancedItemSearch;