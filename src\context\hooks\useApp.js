import { useContext } from 'react';
import { InventoryContext } from '../providers/InventoryProvider';
import { TransactionsContext } from '../providers/TransactionsProvider';
import { UsersContext } from '../providers/UsersProvider';
import { CustomersContext } from '../providers/CustomersProvider';
import { MachinesContext } from '../providers/MachinesProvider';
import { CashboxContext } from '../providers/CashboxProvider';
import { NotificationsContext } from '../providers/NotificationsProvider';
import { SettingsContext } from '../providers/SettingsProvider';
import { SyncContext } from '../providers/SyncProvider';

/**
 * هوك مخصص لاستخدام سياق التطبيق
 * 
 * يقوم هذا الهوك بدمج جميع السياقات المختلفة في كائن واحد لسهولة الاستخدام
 * 
 * @returns {Object} كائن يحتوي على جميع البيانات والوظائف من السياقات المختلفة
 */
export const useApp = () => {
  // الحصول على قيم السياقات المختلفة
  const inventory = useContext(InventoryContext);
  const transactions = useContext(TransactionsContext);
  const users = useContext(UsersContext);
  const customers = useContext(CustomersContext);
  const machines = useContext(MachinesContext);
  const cashbox = useContext(CashboxContext);
  const notifications = useContext(NotificationsContext);
  const settings = useContext(SettingsContext);
  const sync = useContext(SyncContext);
  
  // التحقق من وجود جميع السياقات
  if (!inventory || !transactions || !users || !customers || 
      !machines || !cashbox || !notifications || !settings || !sync) {
    throw new Error('useApp must be used within all required providers');
  }
  
  // دمج جميع القيم في كائن واحد
  const context = {
    ...inventory,
    ...transactions,
    ...users,
    ...customers,
    ...machines,
    ...cashbox,
    ...notifications,
    ...settings,
    ...sync
  };
  
  // تخزين الإعدادات في متغير عام
  if (typeof window !== 'undefined' && context.settings) {
    window.appSettings = context.settings;
  }
  
  return context;
};

export default useApp;
