/* تنسيق سجل مبيعات العميل */

/* تنسيق النافذة المنبثقة */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-lg);
  width: 90%;
  max-width: 1200px;
  max-height: 90vh;
  overflow-y: auto;
  animation: modal-in 0.3s ease-out;
  position: relative;
}

.modal-header {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.3rem;
  color: var(--text-dark);
  display: flex;
  align-items: center;
}

.modal-body {
  padding: var(--spacing-lg);
}

.modal-footer {
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  color: var(--text-light);
  cursor: pointer;
  transition: color 0.2s;
}

.close-btn:hover {
  color: var(--danger-color);
}

/* تنسيق ملخص المبيعات */
.sales-summary .card {
  transition: transform 0.2s, box-shadow 0.2s;
  border: none;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-md);
}

.sales-summary .card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.sales-summary .card-title {
  font-size: 1rem;
  color: var(--text-dark);
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: center;
}

.sales-summary .card-text {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
}

/* تنسيق زر عرض سجل المبيعات */
.btn-info {
  background-color: var(--info-color);
  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-info:hover {
  background-color: #8e44ad;
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

/* تنسيق الجدول */
.table-container {
  overflow-x: auto;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
}

/* تنسيق الرسائل */
.alert {
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  margin-bottom: var(--spacing-md);
}

.alert-danger {
  background-color: rgba(var(--danger-rgb), 0.1);
  color: var(--danger-color);
  border: 1px solid rgba(var(--danger-rgb), 0.2);
}

.alert-warning {
  background-color: rgba(var(--warning-rgb), 0.1);
  color: var(--warning-color);
  border: 1px solid rgba(var(--warning-rgb), 0.2);
}

.alert-info {
  background-color: rgba(var(--info-rgb), 0.1);
  color: var(--info-color);
  border: 1px solid rgba(var(--info-rgb), 0.2);
}

.text-center {
  text-align: center;
}

.text-success {
  color: var(--success-color);
}

/* تأثيرات الحركة */
@keyframes modal-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تنسيق الأزرار */
.btn {
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-secondary:hover {
  background-color: #7f8c8d;
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

/* تنسيق المؤشر الدوار */
.spinner-border {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  border: 0.25rem solid var(--primary-color);
  border-right-color: transparent;
  border-radius: 50%;
  animation: spinner 0.75s linear infinite;
}

@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.mt-3 {
  margin-top: 1rem;
}

.mb-3 {
  margin-bottom: 1rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.col-md-4 {
  flex: 0 0 33.333333%;
  max-width: 33.333333%;
  padding-right: 15px;
  padding-left: 15px;
}

@media (max-width: 768px) {
  .col-md-4 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}
