/* أنماط محسنة لسجل عمليات العميل - تصميم جديد */

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-backdrop {
  backdrop-filter: blur(8px);
  background-color: rgba(0, 0, 0, 0.65);
  transition: all 0.4s ease;
}

.enhanced-sales-history-container {
  max-width: 95%;
  width: 1200px;
  max-height: 90vh;
  overflow-y: auto;
  background-color: #f8f9fa;
  border-radius: 20px;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.25), 0 10px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: modalSlideIn 0.5s cubic-bezier(0.165, 0.84, 0.44, 1) forwards;
  position: relative;
  overflow: hidden;
}

/* تحسين رأس النافذة */
.modal-header {
  background: linear-gradient(135deg, #1e5799, #2989d8, #207cca, #1e5799);
  background-size: 300% 300%;
  color: white;
  padding: 22px 28px;
  border-radius: 20px 20px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: none;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  position: relative;
  animation: gradientAnimation 8s ease infinite;
}

@keyframes gradientAnimation {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.modal-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.modal-header h3 {
  margin: 0;
  font-size: 1.6rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 15px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.header-icon {
  background-color: rgba(255, 255, 255, 0.25);
  width: 45px;
  height: 45px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.3rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.header-icon::before {
  content: '';
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  transform: rotate(45deg);
  transition: all 0.5s ease;
}

.header-icon:hover::before {
  transform: rotate(90deg);
}

.header-text {
  color: white;
  position: relative;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  border-radius: 50%;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.close-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.close-btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: rotate(90deg) scale(1.1);
}

.close-btn:hover::after {
  opacity: 1;
}

/* تحسين جسم النافذة */
.modal-body {
  padding: 30px;
  overflow-y: auto;
  background-color: #f8f9fa;
  background-image:
    radial-gradient(circle at 10% 20%, rgba(230, 240, 255, 0.5) 0%, transparent 20%),
    radial-gradient(circle at 90% 80%, rgba(230, 240, 255, 0.5) 0%, transparent 20%);
}

/* تحسين ملخص العمليات */
.sales-summary {
  background-color: #fff;
  border-radius: 16px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05), 0 5px 10px rgba(0, 0, 0, 0.02);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.sales-summary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(to right, #1e5799, #2989d8, #207cca, #1e5799);
  background-size: 300% 100%;
  animation: gradientAnimation 8s ease infinite;
}

.summary-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: space-between;
}

.summary-card {
  flex: 1;
  min-width: 160px;
  background-color: #fff;
  border-radius: 16px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.03), 0 3px 8px rgba(0, 0, 0, 0.02);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid rgba(0, 0, 0, 0.03);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6px;
  height: 100%;
  background: linear-gradient(to bottom, #1e5799, #2989d8);
  z-index: -1;
}

.summary-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0));
  transform: translateX(-100%) rotate(45deg);
  transition: transform 0.7s ease;
  z-index: -1;
}

.summary-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1), 0 8px 15px rgba(0, 0, 0, 0.05);
}

.summary-card:hover::after {
  transform: translateX(100%) rotate(45deg);
}

.card-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1e5799, #2989d8);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.3rem;
  color: white;
  margin-right: 18px;
  transition: all 0.4s ease;
  box-shadow: 0 5px 15px rgba(30, 87, 153, 0.2);
}

.summary-card:hover .card-icon {
  transform: scale(1.15) rotate(15deg);
  box-shadow: 0 8px 20px rgba(30, 87, 153, 0.3);
}

.card-content {
  flex: 1;
}

.card-content h5 {
  margin: 0 0 8px 0;
  font-size: 1rem;
  color: #555;
  font-weight: 600;
}

.card-content p {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #1e5799;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* تحسين أدوات التصفية */
.filter-controls {
  background-color: #fff;
  border-radius: 16px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05), 0 5px 10px rgba(0, 0, 0, 0.02);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.filter-controls::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(to right, #2989d8, #7db9e8, #2989d8);
  background-size: 200% 100%;
  animation: gradientAnimation 5s ease infinite;
}

.tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding-bottom: 20px;
}

.tab-btn {
  background-color: #f5f7fa;
  border: none;
  border-radius: 12px;
  padding: 12px 18px;
  font-size: 0.95rem;
  color: #555;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
  position: relative;
  overflow: hidden;
}

.tab-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tab-btn:hover {
  background-color: #e9f0f8;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.tab-btn:hover::after {
  opacity: 1;
}

.tab-btn.active {
  background: linear-gradient(135deg, #1e5799, #2989d8);
  color: white;
  box-shadow: 0 5px 15px rgba(30, 87, 153, 0.2);
}

.tab-btn.active:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(30, 87, 153, 0.3);
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
}

.search-box {
  position: relative;
  flex: 1;
  min-width: 250px;
}

.search-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #7db9e8;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.search-box input {
  width: 100%;
  padding: 14px 18px 14px 45px;
  border: 2px solid #e9f0f8;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background-color: #f5f7fa;
  color: #333;
}

.search-box input:focus {
  border-color: #2989d8;
  box-shadow: 0 0 0 4px rgba(41, 137, 216, 0.1);
  outline: none;
  background-color: #fff;
}

.search-box input:focus + .search-icon {
  color: #1e5799;
}

.date-filters {
  display: flex;
  gap: 15px;
}

.date-filter {
  display: flex;
  align-items: center;
  gap: 10px;
}

.date-filter label {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #444;
  font-size: 0.95rem;
  font-weight: 500;
}

.date-filter input {
  padding: 12px 15px;
  border: 2px solid #e9f0f8;
  border-radius: 12px;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  background-color: #f5f7fa;
  color: #333;
}

.date-filter input:focus {
  border-color: #2989d8;
  box-shadow: 0 0 0 4px rgba(41, 137, 216, 0.1);
  outline: none;
  background-color: #fff;
}

.sort-btn {
  background: linear-gradient(135deg, #f5f7fa, #e9f0f8);
  border: none;
  border-radius: 12px;
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  color: #2989d8;
  font-size: 1.1rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03);
}

.sort-btn:hover {
  background: linear-gradient(135deg, #e9f0f8, #d9e6f2);
  transform: translateY(-5px) rotate(5deg);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  color: #1e5799;
}

/* تحسين عرض البيانات */
.data-container {
  background-color: #fff;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05), 0 5px 10px rgba(0, 0, 0, 0.02);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.data-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(to right, #1e5799, #7db9e8, #1e5799);
  background-size: 200% 100%;
  animation: gradientAnimation 8s ease infinite;
}

.section-title {
  margin-top: 0;
  margin-bottom: 25px;
  font-size: 1.3rem;
  color: #1e5799;
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
  padding-bottom: 12px;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(to right, #1e5799, #7db9e8);
  border-radius: 3px;
}

/* تحسين الجداول */
.data-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 30px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.data-table th,
.data-table td {
  padding: 18px;
  text-align: right;
  transition: all 0.3s ease;
}

.data-table th {
  background: linear-gradient(to bottom, #f5f7fa, #e9f0f8);
  font-weight: 600;
  color: #1e5799;
  border-bottom: 2px solid rgba(41, 137, 216, 0.1);
  position: relative;
}

.data-table th::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(41, 137, 216, 0.2), transparent);
}

.data-table tbody tr {
  border-bottom: 1px solid rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
  background-color: #fff;
}

.data-table tbody tr:hover {
  background-color: #f0f7ff;
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.03);
}

.data-table tbody tr:last-child {
  border-bottom: none;
}

.data-table tbody td {
  border-right: 1px solid rgba(0, 0, 0, 0.03);
}

.data-table tbody td:last-child {
  border-right: none;
}

/* تحسين بطاقات الفواتير */
.invoice-card {
  background-color: #fff;
  border-radius: 16px;
  margin-bottom: 30px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05), 0 5px 10px rgba(0, 0, 0, 0.02);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid rgba(0, 0, 0, 0.03);
  position: relative;
}

.invoice-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 6px;
  background: linear-gradient(to bottom, #1e5799, #2989d8);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.invoice-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1), 0 8px 15px rgba(0, 0, 0, 0.05);
}

.invoice-card:hover::before {
  opacity: 1;
}

.invoice-header {
  padding: 22px 25px;
  background: linear-gradient(to bottom, #f5f7fa, #e9f0f8);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.03);
  position: relative;
}

.invoice-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(41, 137, 216, 0.2), transparent);
}

.invoice-title {
  display: flex;
  align-items: center;
  gap: 15px;
}

.invoice-icon {
  font-size: 1.4rem;
  color: #1e5799;
  background-color: rgba(41, 137, 216, 0.1);
  width: 45px;
  height: 45px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(41, 137, 216, 0.1);
}

.invoice-card:hover .invoice-icon {
  transform: scale(1.1) rotate(10deg);
  background-color: rgba(41, 137, 216, 0.15);
}

.invoice-title h5 {
  margin: 0;
  font-size: 1.2rem;
  color: #1e5799;
  font-weight: 600;
}

.invoice-meta {
  display: flex;
  align-items: center;
  gap: 18px;
}

.invoice-date {
  color: #555;
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.print-btn {
  background: linear-gradient(135deg, #1e5799, #2989d8);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 10px 18px;
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  font-size: 0.95rem;
  font-weight: 500;
  box-shadow: 0 5px 15px rgba(30, 87, 153, 0.2);
  position: relative;
  overflow: hidden;
}

.print-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.print-btn:hover {
  background: linear-gradient(135deg, #1a4c80, #2377b9);
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(30, 87, 153, 0.3);
}

.print-btn:hover::after {
  opacity: 1;
}

.invoice-body {
  padding: 25px;
}

.invoice-items-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.03);
}

.invoice-items-table th,
.invoice-items-table td {
  padding: 15px;
  text-align: right;
  border-bottom: 1px solid rgba(0, 0, 0, 0.03);
}

.invoice-items-table th {
  background: linear-gradient(to bottom, #f5f7fa, #e9f0f8);
  font-weight: 600;
  color: #1e5799;
  position: relative;
}

.invoice-items-table th::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(41, 137, 216, 0.2), transparent);
}

.invoice-items-table tbody tr {
  transition: all 0.3s ease;
  background-color: #fff;
}

.invoice-items-table tbody tr:hover {
  background-color: #f0f7ff;
  transform: translateY(-2px);
}

.invoice-items-table tfoot tr {
  font-weight: 600;
  background-color: #f5f7fa;
}

.invoice-items-table tfoot td {
  border-top: 2px solid rgba(41, 137, 216, 0.1);
}

/* تحسين حالة عدم وجود بيانات */
.empty-state {
  text-align: center;
  padding: 60px 30px;
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05), 0 5px 10px rgba(0, 0, 0, 0.02);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.empty-state::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(to right, #1e5799, #7db9e8, #1e5799);
  background-size: 200% 100%;
  animation: gradientAnimation 8s ease infinite;
}

.empty-icon {
  font-size: 4rem;
  color: #7db9e8;
  margin-bottom: 25px;
  display: inline-block;
  animation: floatAnimation 3s ease-in-out infinite;
}

@keyframes floatAnimation {
  0% { transform: translateY(0); }
  50% { transform: translateY(-15px); }
  100% { transform: translateY(0); }
}

.empty-title {
  font-size: 1.8rem;
  color: #1e5799;
  margin-bottom: 15px;
  font-weight: 600;
}

.empty-description {
  color: #555;
  max-width: 450px;
  margin: 0 auto;
  font-size: 1.1rem;
  line-height: 1.6;
}

/* تحسين حالة التحميل */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 30px;
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05), 0 5px 10px rgba(0, 0, 0, 0.02);
}

.spinner {
  width: 60px;
  height: 60px;
  border: 6px solid rgba(41, 137, 216, 0.1);
  border-top: 6px solid #1e5799;
  border-radius: 50%;
  animation: spin 1.2s cubic-bezier(0.5, 0.1, 0.5, 0.9) infinite;
  margin-bottom: 25px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spinner p {
  color: #1e5799;
  font-size: 1.2rem;
  font-weight: 500;
  margin: 0;
}

/* تحسين حالة الخطأ */
.error-state {
  text-align: center;
  padding: 60px 30px;
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05), 0 5px 10px rgba(0, 0, 0, 0.02);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.error-state::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(to right, #e74c3c, #f39c12, #e74c3c);
  background-size: 200% 100%;
  animation: gradientAnimation 8s ease infinite;
}

.error-icon {
  font-size: 4rem;
  color: #e74c3c;
  margin-bottom: 25px;
  display: inline-block;
  animation: shakeAnimation 5s ease-in-out infinite;
}

@keyframes shakeAnimation {
  0%, 100% { transform: rotate(0); }
  2%, 6% { transform: rotate(-5deg); }
  4%, 8% { transform: rotate(5deg); }
  10% { transform: rotate(0); }
}

.error-title {
  font-size: 1.8rem;
  color: #e74c3c;
  margin-bottom: 15px;
  font-weight: 600;
}

.error-description {
  color: #555;
  max-width: 450px;
  margin: 0 auto;
  font-size: 1.1rem;
  line-height: 1.6;
}

/* تحسين الشارات */
.badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 30px;
  font-size: 0.85rem;
  font-weight: 500;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.sale-badge {
  background: linear-gradient(135deg, rgba(41, 137, 216, 0.1), rgba(41, 137, 216, 0.2));
  color: #2989d8;
  border: 1px solid rgba(41, 137, 216, 0.2);
}

.return-badge {
  background: linear-gradient(135deg, rgba(243, 156, 18, 0.1), rgba(243, 156, 18, 0.2));
  color: #f39c12;
  border: 1px solid rgba(243, 156, 18, 0.2);
}

/* تحسين الصفوف */
.return-row {
  background-color: rgba(243, 156, 18, 0.05);
  position: relative;
}

.return-row::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 4px;
  background-color: #f39c12;
  opacity: 0.5;
}

.return-row:hover {
  background-color: rgba(243, 156, 18, 0.1) !important;
}

.return-row:hover::before {
  opacity: 1;
}

/* تحسين الفواتير الفرعية */
.sub-invoice-card {
  border-right: 6px solid #2989d8;
  background: linear-gradient(to right, rgba(41, 137, 216, 0.03), transparent 20%);
}

.parent-invoice-badge {
  background: linear-gradient(135deg, rgba(41, 137, 216, 0.1), rgba(41, 137, 216, 0.2));
  color: #2989d8;
  padding: 6px 12px;
  border-radius: 30px;
  font-size: 0.85rem;
  font-weight: 500;
  margin-right: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(41, 137, 216, 0.2);
  transition: all 0.3s ease;
}

.parent-invoice-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .modal-container {
    width: 95%;
    max-height: 95vh;
  }

  .modal-header {
    padding: 18px;
  }

  .modal-header h3 {
    font-size: 1.3rem;
  }

  .header-icon {
    width: 40px;
    height: 40px;
    font-size: 1.1rem;
  }

  .close-btn {
    width: 40px;
    height: 40px;
  }

  .modal-body {
    padding: 20px;
  }

  .summary-cards {
    flex-direction: column;
  }

  .summary-card {
    width: 100%;
    margin-bottom: 15px;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .date-filters {
    flex-direction: column;
    gap: 10px;
  }

  .tabs {
    overflow-x: auto;
    padding-bottom: 15px;
    flex-wrap: nowrap;
    justify-content: flex-start;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .tabs::-webkit-scrollbar {
    display: none;
  }

  .tab-btn {
    padding: 10px 15px;
    font-size: 0.9rem;
    white-space: nowrap;
  }

  .data-table {
    display: block;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .invoice-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .invoice-meta {
    width: 100%;
    justify-content: space-between;
  }
}
