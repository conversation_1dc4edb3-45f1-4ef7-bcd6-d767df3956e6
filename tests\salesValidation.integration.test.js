/**
 * اختبارات التكامل لنظام التحقق من المبيعات
 * تتحقق هذه الاختبارات من صحة عمل آلية منع البيع الجزئي
 */

// محاكاة window.api للاختبار
global.window = {
  api: {
    customers: {
      getItemInfoForSale: jest.fn()
    },
    inventory: {
      getItem: jest.fn()
    }
  }
};

const { 
  validateInventoryAvailability, 
  generateDetailedErrorMessage, 
  generateShortErrorMessage 
} = require('../src/utils/inventoryValidator');

describe('Sales Validation Integration Tests', () => {
  beforeEach(() => {
    // إعادة تعيين جميع المحاكيات قبل كل اختبار
    jest.clearAllMocks();
  });

  describe('Multi-Item Sales Validation', () => {
    test('should prevent partial sales when some items are unavailable', async () => {
      // محاكاة سيناريو: 3 أصناف، واحد متوفر، واحد بكمية قليلة، وواحد غير موجود
      window.api.customers.getItemInfoForSale
        .mockResolvedValueOnce({
          id: 1,
          name: 'صنف متوفر',
          current_quantity: 100,
          available_for_sale: true
        })
        .mockResolvedValueOnce({
          id: 2,
          name: 'صنف بكمية قليلة',
          current_quantity: 5,
          available_for_sale: true
        })
        .mockResolvedValueOnce(null); // صنف غير موجود

      const saleItems = [
        { item_id: 1, item_name: 'صنف متوفر', quantity: 50 },
        { item_id: 2, item_name: 'صنف بكمية قليلة', quantity: 10 },
        { item_id: 3, item_name: 'صنف غير موجود', quantity: 20 }
      ];

      const result = await validateInventoryAvailability(saleItems);

      // يجب أن تفشل العملية بالكامل
      expect(result.isValid).toBe(false);
      expect(result.validItems).toHaveLength(1); // صنف واحد فقط متوفر
      expect(result.insufficientItems).toHaveLength(1); // صنف واحد بكمية قليلة
      expect(result.unavailableItems).toHaveLength(1); // صنف واحد غير موجود

      // التحقق من تفاصيل الأصناف غير المتوفرة
      expect(result.insufficientItems[0]).toMatchObject({
        item_id: 2,
        item_name: 'صنف بكمية قليلة',
        requested_quantity: 10,
        available_quantity: 5,
        shortage: 5
      });

      expect(result.unavailableItems[0]).toMatchObject({
        item_id: 3,
        item_name: 'صنف غير موجود',
        requested_quantity: 20,
        available_quantity: 0
      });

      // التحقق من رسائل الخطأ
      const detailedError = generateDetailedErrorMessage(result);
      expect(detailedError).toContain('لا يمكن إتمام عملية البيع');
      expect(detailedError).toContain('صنف بكمية قليلة');
      expect(detailedError).toContain('صنف غير موجود');

      const shortError = generateShortErrorMessage(result);
      expect(shortError).toContain('1 صنف غير متوفر');
      expect(shortError).toContain('1 صنف بكمية غير كافية');
    });

    test('should allow sales when all items are available', async () => {
      // محاكاة سيناريو: جميع الأصناف متوفرة بالكميات المطلوبة
      window.api.customers.getItemInfoForSale
        .mockResolvedValueOnce({
          id: 1,
          name: 'صنف أول',
          current_quantity: 100,
          available_for_sale: true
        })
        .mockResolvedValueOnce({
          id: 2,
          name: 'صنف ثاني',
          current_quantity: 50,
          available_for_sale: true
        })
        .mockResolvedValueOnce({
          id: 3,
          name: 'صنف ثالث',
          current_quantity: 200,
          available_for_sale: true
        });

      const saleItems = [
        { item_id: 1, item_name: 'صنف أول', quantity: 30 },
        { item_id: 2, item_name: 'صنف ثاني', quantity: 20 },
        { item_id: 3, item_name: 'صنف ثالث', quantity: 100 }
      ];

      const result = await validateInventoryAvailability(saleItems);

      // يجب أن تنجح العملية
      expect(result.isValid).toBe(true);
      expect(result.validItems).toHaveLength(3);
      expect(result.insufficientItems).toHaveLength(0);
      expect(result.unavailableItems).toHaveLength(0);
      expect(result.errors).toHaveLength(0);

      // التحقق من عدم وجود رسائل خطأ
      const detailedError = generateDetailedErrorMessage(result);
      expect(detailedError).toBe('');

      const shortError = generateShortErrorMessage(result);
      expect(shortError).toBe('');
    });

    test('should handle items not available for sale', async () => {
      // محاكاة سيناريو: صنف غير متاح للبيع
      window.api.customers.getItemInfoForSale
        .mockResolvedValueOnce({
          id: 1,
          name: 'صنف متوفر',
          current_quantity: 100,
          available_for_sale: true
        })
        .mockResolvedValueOnce({
          id: 2,
          name: 'صنف غير متاح للبيع',
          current_quantity: 50,
          available_for_sale: false,
          message: 'هذا الصنف محجوز ولا يمكن بيعه'
        });

      const saleItems = [
        { item_id: 1, item_name: 'صنف متوفر', quantity: 30 },
        { item_id: 2, item_name: 'صنف غير متاح للبيع', quantity: 20 }
      ];

      const result = await validateInventoryAvailability(saleItems);

      // يجب أن تفشل العملية
      expect(result.isValid).toBe(false);
      expect(result.validItems).toHaveLength(1);
      expect(result.unavailableItems).toHaveLength(1);

      // التحقق من رسالة الخطأ المخصصة
      expect(result.unavailableItems[0].error).toBe('هذا الصنف محجوز ولا يمكن بيعه');
    });

    test('should handle duplicate items by summing quantities', async () => {
      // محاكاة سيناريو: نفس الصنف مكرر في القائمة
      window.api.customers.getItemInfoForSale
        .mockResolvedValue({
          id: 1,
          name: 'صنف مكرر',
          current_quantity: 100,
          available_for_sale: true
        });

      const saleItems = [
        { item_id: 1, item_name: 'صنف مكرر', quantity: 30 },
        { item_id: 1, item_name: 'صنف مكرر', quantity: 40 },
        { item_id: 1, item_name: 'صنف مكرر', quantity: 50 }
      ];

      const result = await validateInventoryAvailability(saleItems);

      // يجب أن تفشل العملية لأن المجموع (120) أكبر من المتوفر (100)
      expect(result.isValid).toBe(false);
      expect(result.insufficientItems).toHaveLength(1);

      // التحقق من أن الكمية المطلوبة هي مجموع الكميات
      expect(result.insufficientItems[0].requested_quantity).toBe(120);
      expect(result.insufficientItems[0].available_quantity).toBe(100);
      expect(result.insufficientItems[0].shortage).toBe(20);
    });

    test('should handle API errors gracefully', async () => {
      // محاكاة خطأ في API
      window.api.customers.getItemInfoForSale
        .mockResolvedValueOnce({
          id: 1,
          name: 'صنف متوفر',
          current_quantity: 100,
          available_for_sale: true
        })
        .mockRejectedValueOnce(new Error('خطأ في الشبكة'));

      const saleItems = [
        { item_id: 1, item_name: 'صنف متوفر', quantity: 30 },
        { item_id: 2, item_name: 'صنف بخطأ', quantity: 20 }
      ];

      const result = await validateInventoryAvailability(saleItems);

      // يجب أن تفشل العملية بسبب الخطأ
      expect(result.isValid).toBe(false);
      expect(result.validItems).toHaveLength(1);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('Edge Cases', () => {
    test('should handle empty items list', async () => {
      const result = await validateInventoryAvailability([]);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('لا توجد أصناف للتحقق من توفرها');
    });

    test('should handle invalid item data', async () => {
      const saleItems = [
        { item_id: null, item_name: 'صنف بدون معرف', quantity: 10 },
        { item_id: 2, item_name: 'صنف بكمية سالبة', quantity: -5 },
        { item_id: 3, item_name: 'صنف بكمية صفر', quantity: 0 }
      ];

      const result = await validateInventoryAvailability(saleItems);

      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    test('should handle zero quantities correctly', async () => {
      window.api.customers.getItemInfoForSale
        .mockResolvedValue({
          id: 1,
          name: 'صنف اختبار',
          current_quantity: 0,
          available_for_sale: true
        });

      const saleItems = [
        { item_id: 1, item_name: 'صنف اختبار', quantity: 1 }
      ];

      const result = await validateInventoryAvailability(saleItems);

      expect(result.isValid).toBe(false);
      expect(result.insufficientItems).toHaveLength(1);
      expect(result.insufficientItems[0].available_quantity).toBe(0);
    });
  });

  describe('Performance Tests', () => {
    test('should handle large number of items efficiently', async () => {
      // إنشاء قائمة كبيرة من الأصناف
      const largeItemsList = Array.from({ length: 100 }, (_, i) => ({
        item_id: i + 1,
        item_name: `صنف ${i + 1}`,
        quantity: 10
      }));

      // محاكاة استجابة API لجميع الأصناف
      window.api.customers.getItemInfoForSale.mockImplementation((itemId) => 
        Promise.resolve({
          id: itemId,
          name: `صنف ${itemId}`,
          current_quantity: 50,
          available_for_sale: true
        })
      );

      const startTime = Date.now();
      const result = await validateInventoryAvailability(largeItemsList);
      const endTime = Date.now();

      // يجب أن تكتمل العملية في وقت معقول (أقل من 5 ثوان)
      expect(endTime - startTime).toBeLessThan(5000);
      expect(result.isValid).toBe(true);
      expect(result.validItems).toHaveLength(100);
    });
  });
});
