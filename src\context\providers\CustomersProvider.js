import React, { createContext, useState, useEffect, useContext } from 'react';
import * as database from '../../utils/database';
import { NotificationsContext } from './NotificationsProvider';

// إنشاء سياق العملاء
export const CustomersContext = createContext();

/**
 * مزود سياق العملاء
 *
 * يوفر هذا المكون وظائف إدارة العملاء
 *
 * @param {Object} props - خصائص المكون
 * @param {React.ReactNode} props.children - المكونات الفرعية
 * @returns {React.ReactElement} مزود سياق العملاء
 */
export const CustomersProvider = ({ children }) => {
  // استخدام سياق الإشعارات
  const { showNotification } = useContext(NotificationsContext);

  // حالة العملاء
  const [customers, setCustomers] = useState([]);

  // تحميل العملاء عند بدء التطبيق
  useEffect(() => {
    const loadCustomers = async () => {
      try {
        console.log('جاري تحميل العملاء...');
        const customersData = await database.getCustomers();
        console.log('تم استلام بيانات العملاء:', customersData);

        if (Array.isArray(customersData)) {
          setCustomers(customersData);
          console.log('تم تحميل العملاء بنجاح:', customersData.length);
        } else {
          console.error('بيانات العملاء ليست مصفوفة:', customersData);
          setCustomers([]);
        }
      } catch (error) {
        console.error('خطأ في تحميل العملاء:', error);
        setCustomers([]);
      }
    };

    loadCustomers();

    // تعيين وظيفة تحديث بيانات العميل العالمية
    window.updateCustomerData = (customerData) => {
      if (!customerData || !customerData.id) {
        console.error('بيانات العميل غير صالحة للتحديث:', customerData);
        return false;
      }

      console.log('تحديث بيانات العميل من خلال الوظيفة العالمية:', customerData);

      // تحديث العميل في قائمة العملاء
      setCustomers(prevCustomers => {
        return prevCustomers.map(customer => {
          if (customer.id === customerData.id || customer._id === customerData.id) {
            console.log('تحديث بيانات العميل في القائمة:', customerData);
            return { ...customer, ...customerData };
          }
          return customer;
        });
      });

      return true;
    };

    // تنظيف الوظيفة عند إلغاء تحميل المكون
    return () => {
      window.updateCustomerData = null;
    };
  }, []);

  // تم استبدال دالة validateSubCustomer بمعالجة مباشرة في دالتي addCustomer و updateCustomer

  // إضافة عميل جديد
  const addCustomer = async (newCustomer) => {
    try {
      console.log('بدء إضافة عميل جديد:', newCustomer);
      console.log('نوع العميل الجديد:', newCustomer.customer_type);

      // التحقق من عدم وجود عميل بنفس الاسم
      const existingCustomer = customers.find(customer =>
        customer && customer.name && newCustomer && newCustomer.name &&
        customer.name.toLowerCase() === newCustomer.name.toLowerCase()
      );

      if (existingCustomer) {
        console.error('اسم العميل موجود بالفعل:', existingCustomer);
        throw new Error('اسم العميل موجود بالفعل');
      }

      // التحقق من صحة نوع العميل
      if (!['normal', 'regular', 'sub'].includes(newCustomer.customer_type)) {
        console.log('نوع العميل غير صالح، استخدام النوع العادي كقيمة افتراضية');
        newCustomer.customer_type = 'normal';
      } else {
        console.log('نوع العميل صالح:', newCustomer.customer_type);
      }

      // معالجة خاصة للعميل الفرعي
      if (newCustomer.customer_type === 'sub') {
        console.log('معالجة العميل الفرعي...');

        // التحقق من وجود parent_id
        if (!newCustomer.parent_id) {
          console.error('لم يتم تحديد العميل الدائم للعميل الفرعي');
          throw new Error('يجب تحديد العميل الدائم للعميل الفرعي');
        }

        // استخراج قيمة parent_id بغض النظر عن نوعها
        let parentId = newCustomer.parent_id;

        // إذا كان parent_id كائن، نستخرج المعرف منه
        if (typeof parentId === 'object' && parentId !== null) {
          console.log('العميل الدائم المحدد هو كائن:', parentId);

          if (parentId.id) {
            parentId = parentId.id;
          } else if (parentId._id) {
            parentId = parentId._id;
          } else {
            console.error('العميل الدائم المحدد ككائن لا يحتوي على معرف (id أو _id)');
            throw new Error('صيغة معرف العميل الدائم غير صحيحة');
          }
        }

        console.log('معرف العميل الدائم بعد المعالجة:', parentId);

        // تحويل المعرف إلى رقم
        const parentIdNum = Number(parentId);
        console.log('معرف العميل الدائم كرقم:', parentIdNum);

        // تحديث parent_id في بيانات العميل الفرعي
        newCustomer.parent_id = parentIdNum;
        console.log('تم تحديث parent_id في بيانات العميل الفرعي:', newCustomer.parent_id);

        // التحقق من وجود العميل الدائم في قاعدة البيانات (للتسجيل فقط)
        try {
          if (window.api && window.api.customers && typeof window.api.customers.getById === 'function') {
            const parentCustomer = await window.api.customers.getById(parentIdNum);
            if (parentCustomer) {
              console.log('تم العثور على العميل الدائم في قاعدة البيانات:', parentCustomer);
              if (parentCustomer.customer_type !== 'regular') {
                console.warn('العميل المحدد ليس عميل دائم، ولكن سنستمر في العملية:', parentCustomer.customer_type);
              }
            } else {
              console.warn('لم يتم العثور على العميل الدائم في قاعدة البيانات، ولكن سنستمر في العملية');
            }
          }
        } catch (error) {
          console.warn('خطأ في البحث عن العميل الدائم، ولكن سنستمر في العملية:', error);
        }
      }

      // إضافة العميل باستخدام قاعدة البيانات
      console.log('جاري إضافة العميل إلى قاعدة البيانات...');

      // تم معالجة العميل الفرعي بالفعل في الخطوة السابقة

      const customerData = {
        name: newCustomer.name,
        phone: newCustomer.phone || '',
        email: newCustomer.email || '',
        address: newCustomer.address || '',
        customer_type: newCustomer.customer_type || 'normal',
        parent_id: newCustomer.parent_id || null,
        contact_person: newCustomer.contact_person || '',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      console.log('بيانات العميل المرسلة إلى قاعدة البيانات:', customerData);
      console.log('نوع العميل المرسل إلى قاعدة البيانات:', customerData.customer_type);

      // استدعاء دالة إضافة العميل في قاعدة البيانات
      const addedCustomer = await database.addCustomer(customerData);
      console.log('استجابة إضافة العميل من قاعدة البيانات:', addedCustomer);

      if (!addedCustomer || addedCustomer.error) {
        console.error('خطأ في إضافة العميل إلى قاعدة البيانات:', addedCustomer ? addedCustomer.error : 'لا توجد استجابة');
        throw new Error(addedCustomer && addedCustomer.error ? addedCustomer.error : 'فشل في إضافة العميل إلى قاعدة البيانات');
      }

      if (addedCustomer.success === false) {
        console.error('فشل في إضافة العميل إلى قاعدة البيانات:', addedCustomer.error);
        throw new Error(addedCustomer.error || 'فشل في إضافة العميل إلى قاعدة البيانات');
      }

      // التحقق من وجود العميل المضاف
      const customerFromResponse = addedCustomer.customer || addedCustomer;
      console.log('العميل المضاف من الاستجابة:', customerFromResponse);

      if (!customerFromResponse) {
        console.error('لم يتم العثور على العميل المضاف في الاستجابة');
        throw new Error('لم يتم العثور على العميل المضاف في الاستجابة');
      }

      // التحقق من نوع العميل المضاف
      console.log('نوع العميل المضاف:', customerFromResponse.customer_type);

      // إضافة حقل id للتوافق مع الواجهة الأمامية
      const customerWithId = {
        ...customerFromResponse,
        id: customerFromResponse._id || customerFromResponse.id
      };

      console.log('العميل المضاف مع معرف:', customerWithId);

      // تحديث حالة العملاء
      if (customerWithId.customer_type === 'sub') {
        // إذا كان العميل فرعي، نقوم بتحديث قائمة العملاء بالكامل من قاعدة البيانات
        console.log('العميل المضاف هو عميل فرعي، جاري تحديث قائمة العملاء بالكامل...');
        try {
          // الحصول على قائمة العملاء المحدثة من قاعدة البيانات
          const refreshedCustomers = await database.getCustomers(true);
          if (Array.isArray(refreshedCustomers) && refreshedCustomers.length > 0) {
            console.log('تم تحديث قائمة العملاء من قاعدة البيانات:', refreshedCustomers.length);
            setCustomers(refreshedCustomers);

            // تحديث سياق العملاء في الذاكرة العامة
            if (window.api && window.api.customers && typeof window.api.customers.updateCustomersContext === 'function') {
              try {
                console.log('تحديث سياق العملاء في الذاكرة العامة بعد إضافة عميل فرعي...');
                window.api.customers.updateCustomersContext(refreshedCustomers);
                console.log('تم تحديث سياق العملاء في الذاكرة العامة بنجاح');
              } catch (contextError) {
                console.error('خطأ في تحديث سياق العملاء في الذاكرة العامة:', contextError);
              }
            }
          } else {
            // إذا فشل تحديث القائمة بالكامل، نضيف العميل الجديد فقط
            setCustomers(prevCustomers => {
              const newCustomers = [...prevCustomers, customerWithId];
              console.log('قائمة العملاء الجديدة (تحديث جزئي):', newCustomers.length);
              return newCustomers;
            });
          }
        } catch (refreshError) {
          console.error('خطأ في تحديث قائمة العملاء من قاعدة البيانات:', refreshError);
          // إذا فشل التحديث، نضيف العميل الجديد فقط
          setCustomers(prevCustomers => {
            const newCustomers = [...prevCustomers, customerWithId];
            console.log('قائمة العملاء الجديدة (تحديث جزئي بعد خطأ):', newCustomers.length);
            return newCustomers;
          });
        }
      } else {
        // إذا كان العميل عادي أو دائم، نقوم أيضاً بتحديث قائمة العملاء بالكامل من قاعدة البيانات
        console.log('العميل المضاف هو عميل عادي أو دائم، جاري تحديث قائمة العملاء بالكامل...');
        try {
          // الحصول على قائمة العملاء المحدثة من قاعدة البيانات
          const refreshedCustomers = await database.getCustomers(true);
          if (Array.isArray(refreshedCustomers) && refreshedCustomers.length > 0) {
            console.log('تم تحديث قائمة العملاء من قاعدة البيانات:', refreshedCustomers.length);
            setCustomers(refreshedCustomers);

            // تحديث سياق العملاء في الذاكرة العامة
            if (window.api && window.api.customers && typeof window.api.customers.updateCustomersContext === 'function') {
              try {
                console.log('تحديث سياق العملاء في الذاكرة العامة بعد إضافة عميل عادي أو دائم...');
                window.api.customers.updateCustomersContext(refreshedCustomers);
                console.log('تم تحديث سياق العملاء في الذاكرة العامة بنجاح');
              } catch (contextError) {
                console.error('خطأ في تحديث سياق العملاء في الذاكرة العامة:', contextError);
              }
            }
          } else {
            // إذا فشل تحديث القائمة بالكامل، نضيف العميل الجديد فقط
            setCustomers(prevCustomers => {
              const newCustomers = [...prevCustomers, customerWithId];
              console.log('قائمة العملاء الجديدة (تحديث جزئي):', newCustomers.length);
              console.log('عدد العملاء الدائمين في القائمة الجديدة:', newCustomers.filter(c => c && c.customer_type === 'regular').length);
              return newCustomers;
            });
          }
        } catch (refreshError) {
          console.error('خطأ في تحديث قائمة العملاء من قاعدة البيانات:', refreshError);
          // إذا فشل التحديث، نضيف العميل الجديد فقط
          setCustomers(prevCustomers => {
            const newCustomers = [...prevCustomers, customerWithId];
            console.log('قائمة العملاء الجديدة (تحديث جزئي بعد خطأ):', newCustomers.length);
            console.log('عدد العملاء الدائمين في القائمة الجديدة:', newCustomers.filter(c => c && c.customer_type === 'regular').length);
            return newCustomers;
          });
        }
      }
      console.log('تم تحديث حالة العملاء');

      // إظهار إشعار بنجاح إضافة العميل
      showNotification(`تمت إضافة العميل ${customerWithId.name} بنجاح`, 'success');

      return customerWithId;
    } catch (error) {
      console.error('خطأ في إضافة العميل:', error);

      // إظهار إشعار بفشل إضافة العميل
      showNotification(`فشل في إضافة العميل: ${error.message}`, 'error');

      throw error;
    }
  };

  // تحديث عميل موجود
  const updateCustomer = async (updatedCustomer) => {
    try {
      console.log('بدء تحديث العميل:', updatedCustomer);

      // التحقق من عدم وجود عميل آخر بنفس الاسم
      const existingCustomer = customers.find(customer =>
        customer && customer.name && updatedCustomer && updatedCustomer.name &&
        customer.name.toLowerCase() === updatedCustomer.name.toLowerCase() &&
        customer.id !== updatedCustomer.id &&
        customer._id !== updatedCustomer._id
      );

      if (existingCustomer) {
        console.error('اسم العميل موجود بالفعل:', existingCustomer);
        throw new Error('اسم العميل موجود بالفعل');
      }

      // التحقق من صحة نوع العميل
      if (!['normal', 'regular', 'sub'].includes(updatedCustomer.customer_type)) {
        console.log('نوع العميل غير صالح، استخدام النوع العادي كقيمة افتراضية');
        updatedCustomer.customer_type = 'normal';
      }

      // معالجة خاصة للعميل الفرعي
      if (updatedCustomer.customer_type === 'sub') {
        console.log('معالجة العميل الفرعي...');

        // التحقق من وجود parent_id
        if (!updatedCustomer.parent_id) {
          console.error('لم يتم تحديد العميل الدائم للعميل الفرعي');
          throw new Error('يجب تحديد العميل الدائم للعميل الفرعي');
        }

        // استخراج قيمة parent_id بغض النظر عن نوعها
        let parentId = updatedCustomer.parent_id;

        // إذا كان parent_id كائن، نستخرج المعرف منه
        if (typeof parentId === 'object' && parentId !== null) {
          console.log('العميل الدائم المحدد هو كائن:', parentId);

          if (parentId.id) {
            parentId = parentId.id;
          } else if (parentId._id) {
            parentId = parentId._id;
          } else {
            console.error('العميل الدائم المحدد ككائن لا يحتوي على معرف (id أو _id)');
            throw new Error('صيغة معرف العميل الدائم غير صحيحة');
          }
        }

        console.log('معرف العميل الدائم بعد المعالجة:', parentId);

        // تحويل المعرف إلى رقم
        const parentIdNum = Number(parentId);
        console.log('معرف العميل الدائم كرقم:', parentIdNum);

        // تحديث parent_id في بيانات العميل الفرعي
        updatedCustomer.parent_id = parentIdNum;
        console.log('تم تحديث parent_id في بيانات العميل الفرعي:', updatedCustomer.parent_id);

        // التحقق من وجود العميل الدائم في قاعدة البيانات (للتسجيل فقط)
        try {
          if (window.api && window.api.customers && typeof window.api.customers.getById === 'function') {
            const parentCustomer = await window.api.customers.getById(parentIdNum);
            if (parentCustomer) {
              console.log('تم العثور على العميل الدائم في قاعدة البيانات:', parentCustomer);
              if (parentCustomer.customer_type !== 'regular') {
                console.warn('العميل المحدد ليس عميل دائم، ولكن سنستمر في العملية:', parentCustomer.customer_type);
              }
            } else {
              console.warn('لم يتم العثور على العميل الدائم في قاعدة البيانات، ولكن سنستمر في العملية');
            }
          }
        } catch (error) {
          console.warn('خطأ في البحث عن العميل الدائم، ولكن سنستمر في العملية:', error);
        }
      }

      // تم معالجة العميل الفرعي بالفعل في الخطوة السابقة

      // إعداد بيانات العميل للتحديث
      const customerData = {
        _id: updatedCustomer._id || updatedCustomer.id,
        id: updatedCustomer._id || updatedCustomer.id,
        name: updatedCustomer.name,
        phone: updatedCustomer.phone || '',
        email: updatedCustomer.email || '',
        address: updatedCustomer.address || '',
        customer_type: updatedCustomer.customer_type || 'normal',
        parent_id: updatedCustomer.parent_id || null,
        contact_person: updatedCustomer.contact_person || '',
        updated_at: new Date().toISOString()
      };

      // استدعاء دالة تحديث العميل في قاعدة البيانات
      console.log('جاري تحديث العميل في قاعدة البيانات...');
      const updatedCustomerFromDB = await database.updateCustomer(customerData);
      console.log('تم تحديث العميل بنجاح:', updatedCustomerFromDB);

      // إضافة حقل id للتوافق مع الواجهة الأمامية
      const customerWithId = {
        ...updatedCustomerFromDB,
        id: updatedCustomerFromDB._id || updatedCustomerFromDB.id
      };

      // تحديث حالة العملاء
      if (customerWithId.customer_type === 'sub') {
        // إذا كان العميل فرعي، نقوم بتحديث قائمة العملاء بالكامل من قاعدة البيانات
        console.log('العميل المحدث هو عميل فرعي، جاري تحديث قائمة العملاء بالكامل...');
        try {
          // الحصول على قائمة العملاء المحدثة من قاعدة البيانات
          const refreshedCustomers = await database.getCustomers(true);
          if (Array.isArray(refreshedCustomers) && refreshedCustomers.length > 0) {
            console.log('تم تحديث قائمة العملاء من قاعدة البيانات:', refreshedCustomers.length);
            setCustomers(refreshedCustomers);

            // تحديث سياق العملاء في الذاكرة العامة
            if (window.api && window.api.customers && typeof window.api.customers.updateCustomersContext === 'function') {
              try {
                console.log('تحديث سياق العملاء في الذاكرة العامة بعد تحديث عميل فرعي...');
                window.api.customers.updateCustomersContext(refreshedCustomers);
                console.log('تم تحديث سياق العملاء في الذاكرة العامة بنجاح');
              } catch (contextError) {
                console.error('خطأ في تحديث سياق العملاء في الذاكرة العامة:', contextError);
              }
            }
          } else {
            // إذا فشل تحديث القائمة بالكامل، نحدث العميل فقط
            setCustomers(prevCustomers =>
              prevCustomers.map(customer =>
                (customer.id === customerWithId.id || customer._id === customerWithId._id) ? customerWithId : customer
              )
            );
          }
        } catch (refreshError) {
          console.error('خطأ في تحديث قائمة العملاء من قاعدة البيانات:', refreshError);
          // إذا فشل التحديث، نحدث العميل فقط
          setCustomers(prevCustomers =>
            prevCustomers.map(customer =>
              (customer.id === customerWithId.id || customer._id === customerWithId._id) ? customerWithId : customer
            )
          );
        }
      } else {
        // إذا كان العميل عادي أو دائم، نقوم أيضاً بتحديث قائمة العملاء بالكامل من قاعدة البيانات
        console.log('العميل المحدث هو عميل عادي أو دائم، جاري تحديث قائمة العملاء بالكامل...');
        try {
          // الحصول على قائمة العملاء المحدثة من قاعدة البيانات
          const refreshedCustomers = await database.getCustomers(true);
          if (Array.isArray(refreshedCustomers) && refreshedCustomers.length > 0) {
            console.log('تم تحديث قائمة العملاء من قاعدة البيانات:', refreshedCustomers.length);
            setCustomers(refreshedCustomers);

            // تحديث سياق العملاء في الذاكرة العامة
            if (window.api && window.api.customers && typeof window.api.customers.updateCustomersContext === 'function') {
              try {
                console.log('تحديث سياق العملاء في الذاكرة العامة بعد تحديث عميل عادي أو دائم...');
                window.api.customers.updateCustomersContext(refreshedCustomers);
                console.log('تم تحديث سياق العملاء في الذاكرة العامة بنجاح');
              } catch (contextError) {
                console.error('خطأ في تحديث سياق العملاء في الذاكرة العامة:', contextError);
              }
            }
          } else {
            // إذا فشل تحديث القائمة بالكامل، نحدث العميل فقط
            setCustomers(prevCustomers =>
              prevCustomers.map(customer =>
                (customer.id === customerWithId.id || customer._id === customerWithId._id) ? customerWithId : customer
              )
            );
          }
        } catch (refreshError) {
          console.error('خطأ في تحديث قائمة العملاء من قاعدة البيانات:', refreshError);
          // إذا فشل التحديث، نحدث العميل فقط
          setCustomers(prevCustomers =>
            prevCustomers.map(customer =>
              (customer.id === customerWithId.id || customer._id === customerWithId._id) ? customerWithId : customer
            )
          );
        }
      }
      console.log('تم تحديث حالة العملاء');

      // إظهار إشعار بنجاح تحديث العميل
      showNotification(`تم تحديث بيانات العميل ${customerWithId.name} بنجاح`, 'success');

      return customerWithId;
    } catch (error) {
      console.error('خطأ في تحديث العميل:', error);

      // إظهار إشعار بفشل تحديث العميل
      showNotification(`فشل في تحديث العميل: ${error.message}`, 'error');

      throw error;
    }
  };

  // حذف عميل
  const deleteCustomer = async (customerId) => {
    try {
      console.log('بدء حذف العميل بالمعرف:', customerId);

      // البحث عن العميل
      const customerToDelete = customers.find(customer => customer.id === customerId || customer._id === customerId);

      if (!customerToDelete) {
        console.error('العميل غير موجود:', customerId);
        throw new Error('العميل غير موجود');
      }

      // التحقق من وجود عملاء فرعيين
      const hasSubCustomers = customers.some(customer =>
        customer.parent_id === customerId ||
        customer.parent_id === customerToDelete._id
      );

      if (hasSubCustomers) {
        console.error('لا يمكن حذف العميل لأنه يحتوي على عملاء فرعيين');
        throw new Error('لا يمكن حذف العميل لأنه يحتوي على عملاء فرعيين. قم بحذف العملاء الفرعيين أولاً.');
      }

      // استدعاء دالة حذف العميل في قاعدة البيانات
      console.log('جاري حذف العميل من قاعدة البيانات...');
      const result = await database.deleteCustomer(customerToDelete._id || customerToDelete.id);
      console.log('تم حذف العميل بنجاح:', result);

      // تحديث حالة العملاء
      setCustomers(prevCustomers => {
        const updatedCustomers = prevCustomers.filter(customer =>
          customer.id !== customerId && customer._id !== customerId
        );
        console.log('تم تحديث حالة العملاء، عدد العملاء المتبقي:', updatedCustomers.length);

        // تحديث سياق العملاء في الذاكرة العامة
        if (window.api && window.api.customers && typeof window.api.customers.updateCustomersContext === 'function') {
          try {
            console.log('تحديث سياق العملاء في الذاكرة العامة بعد حذف العميل...');
            window.api.customers.updateCustomersContext(updatedCustomers);
            console.log('تم تحديث سياق العملاء في الذاكرة العامة بنجاح بعد الحذف');
          } catch (contextError) {
            console.error('خطأ في تحديث سياق العملاء في الذاكرة العامة بعد الحذف:', contextError);
          }
        }

        return updatedCustomers;
      });
      console.log('تم تحديث حالة العملاء');

      // إظهار إشعار بنجاح حذف العميل
      showNotification(`تم حذف العميل ${customerToDelete.name} بنجاح`, 'success');

      return true;
    } catch (error) {
      console.error('خطأ في حذف العميل:', error);

      // إظهار إشعار بفشل حذف العميل
      showNotification(`فشل في حذف العميل: ${error.message}`, 'error');

      throw error;
    }
  };

  // الحصول على العملاء حسب النوع
  const getCustomersByType = (type) => {
    return customers.filter(customer => customer && customer.customer_type === type);
  };

  // الحصول على العملاء الفرعيين
  const getSubCustomers = (parentId) => {
    return customers.filter(customer =>
      customer && (
        customer.parent_id === parentId ||
        (customer.parent_id && (customer.parent_id._id === parentId || customer.parent_id.id === parentId))
      )
    );
  };

  // الحصول على سجل مبيعات العميل
  const getCustomerSalesHistory = async (customerId) => {
    try {
      console.log('بدء الحصول على سجل مبيعات العميل:', customerId);

      // البحث عن العميل
      const customer = customers.find(c => c.id === customerId || c._id === customerId);
      if (!customer) {
        console.error('العميل غير موجود:', customerId);
        throw new Error('العميل غير موجود');
      }

      // التحقق من وجود واجهة API
      if (!window.api) {
        console.error('واجهة API غير متوفرة');
        throw new Error('واجهة API غير متوفرة');
      }

      // التحقق من وجود واجهة API للعملاء
      if (!window.api.customers) {
        console.error('واجهة API للعملاء غير متوفرة');
        throw new Error('واجهة API للعملاء غير متوفرة');
      }

      // التحقق من وجود وظيفة getSalesHistory
      if (!window.api.customers.getSalesHistory) {
        console.error('وظيفة getSalesHistory غير متوفرة');
        throw new Error('وظيفة getSalesHistory غير متوفرة');
      }

      // استخدام API لجلب مبيعات العميل من قاعدة البيانات
      console.log('جاري استدعاء API للحصول على سجل مبيعات العميل:', customerId);

      try {
        // استخدام واجهة API المخصصة للعملاء
        console.log('استدعاء window.api.customers.getSalesHistory مع المعرف:', customerId);
        const result = await window.api.customers.getSalesHistory(customerId);
        console.log('تم استلام نتيجة سجل مبيعات العميل من API:', result);

        // التحقق من صحة النتيجة
        if (!result) {
          console.error('لم يتم استلام بيانات من API');
          throw new Error('لم يتم استلام بيانات من قاعدة البيانات');
        }

        // إرجاع النتيجة كما هي
        return result;
      } catch (apiError) {
        console.error('خطأ في استدعاء API للحصول على سجل مبيعات العميل:', apiError);

        // إعادة رمي الخطأ للمعالجة في الطبقة العليا
        throw apiError;
      }
    } catch (error) {
      console.error('خطأ في الحصول على سجل مبيعات العميل:', error);

      // البحث عن العميل مرة أخرى للتأكد
      const customerInfo = customers.find(c => c.id === customerId || c._id === customerId);

      // إنشاء كائن نتيجة افتراضي في حالة الخطأ
      const defaultResult = {
        customer: {
          name: customerInfo ? customerInfo.name : 'عميل غير معروف',
          id: customerId
        },
        sales: [],
        groupedSales: [],
        totalSales: 0,
        totalProfit: 0,
        count: 0,
        invoiceCount: 0
      };

      console.log('إرجاع نتيجة افتراضية بسبب الخطأ:', defaultResult);
      return defaultResult;
    }
  };

  // القيمة التي سيتم توفيرها للمكونات
  const value = {
    customers,
    addCustomer,
    updateCustomer,
    deleteCustomer,
    getCustomersByType,
    getSubCustomers,
    getCustomerSalesHistory
  };

  return (
    <CustomersContext.Provider value={value}>
      {children}
    </CustomersContext.Provider>
  );
};

export default CustomersProvider;
