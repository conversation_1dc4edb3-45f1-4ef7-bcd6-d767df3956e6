/**
 * وحدة معالجة الأخطاء - تقوم بتوحيد طريقة التعامل مع الأخطاء في النظام
 * وتسجيلها بشكل منظم
 */

const fs = require('fs');
const path = require('path');

// مسار ملف السجل - استخدام مسار محدد
const LOG_DIR = path.join(__dirname, 'logs');
const ERROR_LOG_FILE = path.join(LOG_DIR, 'errors.log');
const SYSTEM_LOG_FILE = path.join(LOG_DIR, 'system.log');

// التأكد من وجود مجلد السجلات
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
}

/**
 * تسجيل خطأ في ملف السجل
 * @param {Error|string} error - الخطأ المراد تسجيله
 * @param {string} source - مصدر الخطأ
 * @param {Object} additionalInfo - معلومات إضافية عن الخطأ
 */
function logError(error, source = 'unknown', additionalInfo = {}) {
  try {
    const timestamp = new Date().toISOString();
    const errorMessage = error instanceof Error ? error.message : error;
    const errorStack = error instanceof Error ? error.stack : '';

    const logEntry = {
      timestamp,
      source,
      message: errorMessage,
      stack: errorStack,
      ...additionalInfo
    };

    const logString = JSON.stringify(logEntry) + '\n';

    fs.appendFileSync(ERROR_LOG_FILE, logString);

    console.error(`[${timestamp}] [${source}] Error: ${errorMessage}`);
    if (errorStack) {
      console.error(errorStack);
    }
  } catch (logError) {
    console.error('Failed to log error:', logError);
  }
}

/**
 * تسجيل معلومات النظام في ملف السجل
 * @param {string} message - الرسالة المراد تسجيلها
 * @param {string} level - مستوى السجل (info, warning, debug)
 * @param {Object} additionalInfo - معلومات إضافية
 */
function logSystem(message, level = 'info', additionalInfo = {}) {
  try {
    const timestamp = new Date().toISOString();

    const logEntry = {
      timestamp,
      level,
      message,
      ...additionalInfo
    };

    const logString = JSON.stringify(logEntry) + '\n';

    fs.appendFileSync(SYSTEM_LOG_FILE, logString);

    switch (level) {
      case 'warning':
        console.warn(`[${timestamp}] [${level}] ${message}`);
        break;
      case 'debug':
        console.debug(`[${timestamp}] [${level}] ${message}`);
        break;
      default:
        console.log(`[${timestamp}] [${level}] ${message}`);
    }
  } catch (logError) {
    console.error('Failed to log system message:', logError);
  }
}

/**
 * معالجة الخطأ وإرجاع استجابة موحدة
 * @param {Error|string} error - الخطأ المراد معالجته
 * @param {string} source - مصدر الخطأ
 * @param {Object} additionalInfo - معلومات إضافية عن الخطأ
 * @returns {Object} - استجابة موحدة
 */
function handleError(error, source = 'unknown', additionalInfo = {}) {
  // تسجيل الخطأ
  logError(error, source, additionalInfo);

  // إنشاء استجابة موحدة
  const errorMessage = error instanceof Error ? error.message : error;

  return {
    success: false,
    error: {
      message: errorMessage,
      source,
      timestamp: new Date().toISOString()
    }
  };
}

/**
 * تنظيف ملفات السجل القديمة
 * يحتفظ بسجلات آخر 30 يوم فقط
 */
function cleanupLogs() {
  try {
    logSystem('بدء تنظيف ملفات السجل القديمة', 'info');

    // الحصول على قائمة ملفات السجل
    const files = fs.readdirSync(LOG_DIR);

    // تاريخ قبل 30 يوم
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    let deletedCount = 0;

    // فحص كل ملف
    for (const file of files) {
      const filePath = path.join(LOG_DIR, file);

      // الحصول على تاريخ تعديل الملف
      const stats = fs.statSync(filePath);
      const fileDate = new Date(stats.mtime);

      // حذف الملفات القديمة
      if (fileDate < thirtyDaysAgo) {
        fs.unlinkSync(filePath);
        deletedCount++;
      }
    }

    logSystem(`تم حذف ${deletedCount} ملف سجل قديم`, 'info');
  } catch (error) {
    console.error('خطأ في تنظيف ملفات السجل:', error);
  }
}

// تنظيف السجلات القديمة عند بدء التشغيل
cleanupLogs();

module.exports = {
  logError,
  logSystem,
  handleError,
  cleanupLogs
};
