/**
 * وحدة مساعدة لدعم اللغة العربية في ملفات PDF
 * تقوم بتسجيل الخطوط الافتراضية في ويندوز في مكتبة jsPDF
 */

import { jsPDF } from 'jspdf';

/**
 * إعداد مستند PDF لدعم اللغة العربية
 * @param {jsPDF} doc - كائن jsPDF
 */
export function setupArabicSupport(doc) {
  try {
    // تعيين اتجاه النص من اليمين إلى اليسار
    doc.setR2L(true);

    // تعيين الخط الافتراضي - استخدام خط يدعم اللغة العربية
    // استخدام خط Helvetica لأنه متوفر افتراضيًا في معظم الأنظمة
    doc.setFont('helvetica');

    console.log('تم إعداد دعم اللغة العربية بنجاح');
    return true;
  } catch (error) {
    console.error('خطأ في إعداد دعم اللغة العربية:', error);
    return false;
  }
}

/**
 * تحويل النص العربي إلى تنسيق يمكن عرضه في PDF
 * @param {string} text - النص العربي
 * @returns {string} - النص المعالج
 */
export function processArabicText(text) {
  if (!text) return '';

  // تحويل النص العربي إلى تنسيق يمكن عرضه في PDF
  // نستخدم طريقة أكثر تعقيدًا لمعالجة النص العربي

  // تحويل الأرقام العربية إلى أرقام إنجليزية
  text = convertArabicDigitsToEnglish(text);

  // عكس النص مع الحفاظ على الأرقام والرموز الخاصة في مكانها
  const arabicPattern = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+/g;

  return text.replace(arabicPattern, function(match) {
    return match.split('').reverse().join('');
  });
}

/**
 * إضافة نص عربي إلى مستند PDF
 * @param {jsPDF} doc - كائن jsPDF
 * @param {string} text - النص العربي
 * @param {number} x - الإحداثي الأفقي
 * @param {number} y - الإحداثي الرأسي
 * @param {Object} options - خيارات إضافية
 */
export function addArabicText(doc, text, x, y, options = {}) {
  try {
    // معالجة النص العربي - تحويل الأرقام العربية إلى إنجليزية يتم داخل processArabicText
    const processedText = processArabicText(text);

    // تعيين اتجاه النص من اليمين إلى اليسار
    const mergedOptions = { ...options, align: options.align || 'right' };

    // إضافة النص إلى المستند
    doc.text(processedText, x, y, mergedOptions);
  } catch (error) {
    console.error('خطأ في إضافة نص عربي:', error);
  }
}

/**
 * تحويل الأرقام العربية إلى أرقام إنجليزية
 * @param {string} text - النص الذي يحتوي على أرقام عربية
 * @returns {string} - النص بعد تحويل الأرقام إلى إنجليزية
 */
export function convertArabicDigitsToEnglish(text) {
  if (!text) return '';

  const arabicDigits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
  const englishDigits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

  return text.replace(/[٠-٩]/g, match => {
    return englishDigits[arabicDigits.indexOf(match)];
  });
}

/**
 * تحويل الأرقام الإنجليزية إلى أرقام عربية
 * @param {string} text - النص الذي يحتوي على أرقام إنجليزية
 * @returns {string} - النص بعد تحويل الأرقام إلى عربية
 */
export function convertEnglishDigitsToArabic(text) {
  if (!text) return '';

  const arabicDigits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
  const englishDigits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

  return text.replace(/[0-9]/g, match => {
    return arabicDigits[englishDigits.indexOf(match)];
  });
}

export default {
  setupArabicSupport,
  processArabicText,
  addArabicText,
  convertArabicDigitsToEnglish,
  convertEnglishDigitsToArabic
};
