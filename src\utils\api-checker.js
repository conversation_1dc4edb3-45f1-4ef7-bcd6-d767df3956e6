/**
 * أداة للتحقق من توفر window.api وتشخيص المشاكل المتعلقة به
 */

// التحقق من وجود window.api
export function checkApiAvailability() {
  console.log('جاري التحقق من توفر window.api...');
  
  if (typeof window === 'undefined') {
    console.error('خطأ: window غير معرف!');
    return {
      available: false,
      error: 'window غير معرف',
      details: 'هذا يحدث عادة عند تشغيل الكود في بيئة Node.js وليس في المتصفح.'
    };
  }
  
  if (!window.api) {
    console.error('خطأ: window.api غير متوفر!');
    return {
      available: false,
      error: 'window.api غير متوفر',
      details: 'لم يتم تحميل preload.js بشكل صحيح أو لم يتم تعريف window.api في contextBridge.'
    };
  }
  
  console.log('window.api متوفر!');
  
  // التحقق من وجود الوظائف الأساسية
  const apiMethods = {
    invoke: typeof window.api.invoke === 'function',
    customers: typeof window.api.customers === 'object',
    items: typeof window.api.items === 'object',
    inventory: typeof window.api.inventory === 'object',
    transactions: typeof window.api.transactions === 'object',
    users: typeof window.api.users === 'object',
    machines: typeof window.api.machines === 'object',
    database: typeof window.api.database === 'object'
  };
  
  const missingMethods = Object.keys(apiMethods).filter(key => !apiMethods[key]);
  
  if (missingMethods.length > 0) {
    console.warn('تحذير: بعض وظائف window.api غير متوفرة:', missingMethods);
    return {
      available: true,
      warning: 'بعض وظائف window.api غير متوفرة',
      missingMethods,
      details: 'يجب التحقق من preload.js للتأكد من تعريف جميع الوظائف المطلوبة.'
    };
  }
  
  return {
    available: true,
    methods: Object.keys(apiMethods),
    details: 'window.api متوفر وجميع الوظائف الأساسية موجودة.'
  };
}

// اختبار اتصال window.api
export async function testApiConnection() {
  console.log('جاري اختبار اتصال window.api...');
  
  const availability = checkApiAvailability();
  
  if (!availability.available) {
    return {
      success: false,
      error: availability.error,
      details: availability.details
    };
  }
  
  try {
    // اختبار وظيفة invoke
    const testResult = await window.api.invoke('test-channel', { test: true });
    console.log('نتيجة اختبار window.api.invoke:', testResult);
    
    return {
      success: true,
      result: testResult,
      details: 'تم اختبار اتصال window.api بنجاح.'
    };
  } catch (error) {
    console.error('خطأ في اختبار اتصال window.api:', error);
    return {
      success: false,
      error: error.message,
      details: 'فشل في اختبار اتصال window.api. تحقق من وجود معالج للقناة test-channel في main.js.'
    };
  }
}

// إصلاح مشاكل window.api
export function troubleshootApi() {
  console.log('جاري تشخيص مشاكل window.api...');
  
  const availability = checkApiAvailability();
  
  if (availability.available) {
    console.log('window.api متوفر، لا توجد مشاكل أساسية.');
    return {
      needsFix: false,
      message: 'window.api متوفر، لا توجد مشاكل أساسية.',
      details: availability
    };
  }
  
  // محاولة إصلاح المشكلة
  console.warn('محاولة إصلاح مشكلة window.api...');
  
  // التحقق من وجود window.api بعد فترة قصيرة
  return new Promise(resolve => {
    setTimeout(() => {
      const newAvailability = checkApiAvailability();
      
      if (newAvailability.available) {
        console.log('تم إصلاح مشكلة window.api بنجاح!');
        resolve({
          needsFix: false,
          fixed: true,
          message: 'تم إصلاح مشكلة window.api بنجاح!',
          details: newAvailability
        });
      } else {
        console.error('فشل في إصلاح مشكلة window.api.');
        resolve({
          needsFix: true,
          fixed: false,
          message: 'فشل في إصلاح مشكلة window.api.',
          details: newAvailability,
          recommendations: [
            'تأكد من تحميل preload.js بشكل صحيح في main.js',
            'تأكد من استخدام contextBridge.exposeInMainWorld بشكل صحيح في preload.js',
            'تأكد من تعريف window.api في preload.js',
            'تأكد من عدم وجود أخطاء في preload.js',
            'حاول إعادة تشغيل التطبيق'
          ]
        });
      }
    }, 1000);
  });
}

// تصدير الوظائف
export default {
  checkApiAvailability,
  testApiConnection,
  troubleshootApi
};
