/**
 * وظائف مساعدة للتعامل مع التواريخ
 */

/**
 * تنسيق التاريخ إلى صيغة مقروءة
 * @param {string|Date} date - التاريخ المراد تنسيقه
 * @param {boolean} includeTime - ما إذا كان يجب تضمين الوقت في التنسيق
 * @returns {string} - التاريخ المنسق
 */
export const formatDate = (date, includeTime = false) => {
  if (!date) return 'غير متوفر';
  
  try {
    const dateObj = new Date(date);
    
    // التحقق من صحة التاريخ
    if (isNaN(dateObj.getTime())) {
      return 'تاريخ غير صالح';
    }
    
    // تنسيق التاريخ بالأرقام الإنجليزية
    const day = dateObj.getDate().toString().padStart(2, '0');
    const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
    const year = dateObj.getFullYear();
    
    let formattedDate = `${day}/${month}/${year}`;
    
    // إضافة الوقت إذا كان مطلوبًا
    if (includeTime) {
      const hours = dateObj.getHours().toString().padStart(2, '0');
      const minutes = dateObj.getMinutes().toString().padStart(2, '0');
      formattedDate += ` ${hours}:${minutes}`;
    }
    
    return formattedDate;
  } catch (error) {
    console.error('خطأ في تنسيق التاريخ:', error);
    return 'خطأ في التاريخ';
  }
};

/**
 * الحصول على تاريخ اليوم بتنسيق ISO
 * @returns {string} - تاريخ اليوم بتنسيق ISO
 */
export const getTodayISODate = () => {
  const today = new Date();
  return today.toISOString().split('T')[0];
};

/**
 * الحصول على تاريخ بداية الشهر الحالي بتنسيق ISO
 * @returns {string} - تاريخ بداية الشهر الحالي بتنسيق ISO
 */
export const getFirstDayOfCurrentMonth = () => {
  const today = new Date();
  const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
  return firstDay.toISOString().split('T')[0];
};

/**
 * الحصول على تاريخ بداية الشهر السابق بتنسيق ISO
 * @returns {string} - تاريخ بداية الشهر السابق بتنسيق ISO
 */
export const getFirstDayOfPreviousMonth = () => {
  const today = new Date();
  const firstDay = new Date(today.getFullYear(), today.getMonth() - 1, 1);
  return firstDay.toISOString().split('T')[0];
};

/**
 * الحصول على تاريخ بداية السنة الحالية بتنسيق ISO
 * @returns {string} - تاريخ بداية السنة الحالية بتنسيق ISO
 */
export const getFirstDayOfCurrentYear = () => {
  const today = new Date();
  const firstDay = new Date(today.getFullYear(), 0, 1);
  return firstDay.toISOString().split('T')[0];
};

/**
 * حساب الفرق بين تاريخين بالأيام
 * @param {string|Date} date1 - التاريخ الأول
 * @param {string|Date} date2 - التاريخ الثاني
 * @returns {number} - الفرق بالأيام
 */
export const getDaysDifference = (date1, date2) => {
  const d1 = new Date(date1);
  const d2 = new Date(date2);
  
  // التحقق من صحة التواريخ
  if (isNaN(d1.getTime()) || isNaN(d2.getTime())) {
    return null;
  }
  
  // حساب الفرق بالمللي ثانية وتحويله إلى أيام
  const diffTime = Math.abs(d2 - d1);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return diffDays;
};

/**
 * التحقق مما إذا كان التاريخ ضمن نطاق زمني محدد
 * @param {string|Date} date - التاريخ المراد التحقق منه
 * @param {string|Date} startDate - تاريخ البداية
 * @param {string|Date} endDate - تاريخ النهاية
 * @returns {boolean} - ما إذا كان التاريخ ضمن النطاق
 */
export const isDateInRange = (date, startDate, endDate) => {
  const d = new Date(date);
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  // التحقق من صحة التواريخ
  if (isNaN(d.getTime()) || isNaN(start.getTime()) || isNaN(end.getTime())) {
    return false;
  }
  
  return d >= start && d <= end;
};

/**
 * الحصول على اسم الشهر بالعربية
 * @param {number} monthIndex - رقم الشهر (0-11)
 * @returns {string} - اسم الشهر بالعربية
 */
export const getArabicMonthName = (monthIndex) => {
  const months = [
    'يناير', 'فبراير', 'مارس', 'إبريل', 'مايو', 'يونيو',
    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
  ];
  
  return months[monthIndex];
};

/**
 * الحصول على اسم اليوم بالعربية
 * @param {number} dayIndex - رقم اليوم (0-6)
 * @returns {string} - اسم اليوم بالعربية
 */
export const getArabicDayName = (dayIndex) => {
  const days = [
    'الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'
  ];
  
  return days[dayIndex];
};
