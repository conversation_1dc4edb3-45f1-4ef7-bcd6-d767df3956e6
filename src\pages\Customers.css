/* أنماط صفحة العملاء */
.customers-page {
  padding: 20px;
}

/* أنماط التنبيهات */
.alert {
  padding: 15px 20px;
  margin-bottom: 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  min-width: 300px;
  max-width: 80%;
  animation: alertFadeIn 0.3s ease-in-out;
}

@keyframes alertFadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -20px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}

.alert-success {
  background-color: rgba(40, 167, 69, 0.1);
  color: #198754;
  border-left: 4px solid #198754;
}

.alert-danger {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border-left: 4px solid #dc3545;
}

.alert-warning {
  background-color: rgba(255, 193, 7, 0.1);
  color: #ffc107;
  border-left: 4px solid #ffc107;
}

.alert-info {
  background-color: rgba(13, 202, 240, 0.1);
  color: #0dcaf0;
  border-left: 4px solid #0dcaf0;
}

.customers-header {
  margin-bottom: 30px;
}

.customers-header h1 {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 10px;
}

.customers-header p {
  font-size: 1rem;
  color: var(--text-light);
}

/* أنماط فلترة العملاء */
.customers-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
}

.filter-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.filter-tab {
  padding: 8px 16px;
  border-radius: var(--border-radius-md);
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: var(--bg-light);
  color: var(--text-dark);
  border: 1px solid var(--border-color);
}

.filter-tab:hover {
  background-color: rgba(var(--primary-rgb), 0.05);
}

.filter-tab.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.search-container {
  position: relative;
  flex: 1;
  min-width: 250px;
}

.search-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-light);
}

.search-input {
  width: 100%;
  padding: 10px 15px 10px 40px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  font-size: 0.95rem;
}

.search-input:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.parent-filter {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 10px;
}

.parent-filter-label {
  font-weight: 500;
  color: var(--text-dark);
}

.parent-filter-select {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: 0.95rem;
  min-width: 200px;
}

.parent-filter-select:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

/* أنماط جدول العملاء */
.customers-table-container {
  margin-bottom: 30px;
  overflow-x: auto;
}

.customers-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  table-layout: fixed;
}

.customers-table th {
  background-color: var(--bg-light);
  color: var(--text-dark);
  font-weight: 600;
  padding: 12px 15px;
  text-align: right;
  border-bottom: 2px solid var(--border-color);
}

.customers-table td {
  padding: 12px 15px;
  border-bottom: 1px solid var(--border-color);
  vertical-align: middle;
}

.customers-table tr:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.customers-table .th-small {
  width: 50px;
}

.customers-table .th-actions {
  width: 150px;
  text-align: center;
}

.customers-table .th-icon {
  margin-left: 5px;
  color: var(--primary-color);
}

.customer-name {
  font-weight: 600;
  color: var(--text-dark);
  display: flex;
  align-items: center;
  gap: 8px;
}

.customer-name-with-toggle {
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
  padding: 5px;
  border-radius: 4px;
}

.customer-name-with-toggle:hover {
  background-color: rgba(var(--primary-rgb), 0.05);
}

.toggle-icon {
  font-size: 0.8rem;
  color: var(--primary-color);
  transition: transform 0.3s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: rgba(var(--primary-rgb), 0.1);
}

.toggle-icon.expanded {
  transform: rotate(90deg);
  background-color: rgba(var(--primary-rgb), 0.2);
}

.sub-customers-count {
  font-size: 0.8rem;
  color: var(--text-light);
  margin-right: 5px;
  background-color: rgba(var(--info-rgb), 0.1);
  padding: 2px 6px;
  border-radius: 10px;
}

.has-sub-customers {
  border-bottom: none;
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.empty-table {
  text-align: center;
  padding: 40px !important;
}

/* أنماط العملاء الفرعيين */
.sub-customer-row {
  background-color: rgba(0, 123, 255, 0.05);
}

.customer-type-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
  gap: 5px;
}

.customer-type-badge.regular {
  background-color: rgba(0, 123, 255, 0.1);
  color: #0056b3;
}

.customer-type-badge.sub {
  background-color: rgba(108, 117, 125, 0.1);
  color: #495057;
}

.customer-type-badge.normal {
  background-color: rgba(40, 167, 69, 0.1);
  color: #198754;
}

.parent-customer-name {
  display: inline-flex;
  align-items: center;
  color: #0056b3;
  font-weight: 500;
}

.contact-info {
  display: flex;
  align-items: center;
  gap: 5px;
}

.customer-balance {
  display: flex;
  align-items: center;
  gap: 5px;
}

.customer-balance.positive {
  color: #198754;
}

.customer-balance.negative {
  color: #dc3545;
}

.customer-actions {
  display: flex;
  justify-content: center;
  gap: 5px;
}

.button-group {
  display: flex;
  gap: 10px;
}

.customer-type-badge {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
}

.customer-type-badge.regular {
  background-color: rgba(var(--primary-rgb), 0.1);
  color: var(--primary-color);
}

.customer-type-badge.sub {
  background-color: rgba(var(--info-rgb), 0.1);
  color: var(--info-color);
}

.customer-type-badge.normal {
  background-color: rgba(var(--secondary-rgb), 0.1);
  color: var(--secondary-color);
}

.parent-customer-name {
  color: var(--primary-color);
  font-weight: 500;
}

.contact-info {
  display: flex;
  align-items: center;
  gap: 5px;
  color: var(--text-light);
}

.customer-balance {
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 5px;
}

.customer-balance.positive {
  color: var(--success-color);
}

.customer-balance.negative {
  color: var(--danger-color);
}

.customer-actions {
  display: flex;
  gap: 5px;
}

/* أنماط صفوف العملاء الفرعيين */
.sub-customer-row {
  background-color: rgba(var(--info-rgb), 0.05);
  animation: fadeIn 0.3s ease-in-out;
  border-right: 3px solid var(--info-color);
}

.sub-customer-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sub-customer-indicator {
  display: inline-block;
  color: var(--info-color);
  font-weight: bold;
  margin-left: 5px;
}

.no-sub-customers-row {
  background-color: rgba(var(--info-rgb), 0.02);
  animation: fadeIn 0.3s ease-in-out;
}

.no-sub-customers-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 15px;
  color: var(--text-light);
  font-style: italic;
}

.no-sub-customers-message svg {
  color: var(--info-color);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* أنماط نافذة إضافة/تعديل العميل */
.customer-form {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group.full-width {
  grid-column: span 2;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-dark);
}

.form-control {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: 0.95rem;
}

.form-control:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.form-text {
  font-size: 0.8rem;
  color: var(--text-light);
  margin-top: 5px;
}

.form-actions {
  grid-column: span 2;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 10px;
}

.required {
  color: var(--danger-color);
  margin-right: 3px;
}

/* أنماط أزرار نوع العميل */
.customer-type-buttons {
  display: flex;
  gap: 10px;
  margin-top: 5px;
}

.customer-type-button {
  flex: 1;
  padding: 10px 15px;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  background-color: #fff;
  color: var(--text-dark);
  font-weight: 500;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.customer-type-button:hover {
  border-color: var(--primary-color);
  background-color: rgba(var(--primary-rgb), 0.05);
}

.customer-type-button.active {
  border-color: var(--primary-color);
  background-color: var(--primary-color);
  color: white;
}

.button-icon {
  font-size: 1rem;
}

/* أنماط نافذة سجل المبيعات */
.sales-history-modal {
  max-width: 900px;
}

.sales-history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.sales-history-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-dark);
}

.sales-history-customer {
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 5px;
}

.sales-history-summary {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.summary-item {
  flex: 1;
  padding: 15px;
  border-radius: var(--border-radius-md);
  background-color: var(--bg-light);
  text-align: center;
}

.summary-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 5px;
}

.summary-label {
  font-size: 0.9rem;
  color: var(--text-light);
}

/* أنماط نافذة البيع للعميل */
.customer-sales-modal {
  max-width: 1000px;
}

.modal-sales-container {
  padding: 0;
}

.modal-sales-grid {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 20px;
}

.modal-sales-sidebar {
  padding: 20px;
  background-color: var(--bg-light);
  border-radius: var(--border-radius-sm);
}

.modal-sales-main {
  padding: 20px;
}

/* أنماط مكون الإكمال التلقائي في نافذة البيع للعميل */
.customers-sales-autocomplete .item-autocomplete-input {
  width: 100%;
  padding: 10px 40px 10px 15px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: 0.95rem;
  transition: all 0.2s;
}

.customers-sales-autocomplete .item-autocomplete-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-light);
}

.customers-sales-autocomplete .item-autocomplete-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.customers-sales-autocomplete .item-autocomplete-suggestions-container-open {
  border-radius: var(--border-radius-sm);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color);
  z-index: 1100;
}

.customers-sales-autocomplete .item-autocomplete-suggestion-highlighted {
  background-color: rgba(var(--primary-rgb), 0.05);
}

.customers-sales-autocomplete .item-suggestion-name {
  font-weight: 600;
  color: var(--text-dark);
}

.customers-sales-autocomplete .item-quantity.out {
  color: var(--danger-color);
}

.customers-sales-autocomplete .item-quantity.low {
  color: var(--warning-color);
}

.invoice-summary {
  margin-top: 30px;
  padding: 15px;
  background-color: white;
  border-radius: var(--border-radius-sm);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.invoice-summary h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--text-dark);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 10px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 0.95rem;
}

.summary-item.total {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--primary-color);
  border-top: 1px solid var(--border-color);
  padding-top: 10px;
  margin-top: 10px;
}

.form-section {
  margin-bottom: 25px;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-icon {
  color: var(--primary-color);
}

.item-add-form {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr auto;
  gap: 15px;
  align-items: end;
}

.sale-items-table {
  margin-top: 15px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  overflow: hidden;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th {
  background-color: var(--bg-light);
  padding: 12px 15px;
  text-align: right;
  font-weight: 600;
  color: var(--text-dark);
  border-bottom: 1px solid var(--border-color);
}

.table td {
  padding: 12px 15px;
  border-bottom: 1px solid var(--border-color);
  text-align: right;
}

.table td:nth-child(3),
.table td:nth-child(4),
.table td:nth-child(5),
.table td:nth-child(6) {
  text-align: center;
}

.table th:nth-child(3),
.table th:nth-child(4),
.table th:nth-child(5),
.table th:nth-child(6) {
  text-align: center;
}

.text-center {
  text-align: center;
}

/* أنماط البحث مع الاقتراحات */
.search-autocomplete {
  position: relative;
  width: 100%;
}

.autocomplete-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  max-height: 250px;
  overflow-y: auto;
  background-color: white;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.autocomplete-item {
  padding: 10px 15px;
  cursor: pointer;
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.2s;
}

.autocomplete-item:last-child {
  border-bottom: none;
}

.autocomplete-item:hover {
  background-color: rgba(var(--primary-rgb), 0.05);
}

.item-name {
  font-weight: 600;
  margin-bottom: 5px;
}

.item-details {
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
  color: var(--text-light);
}

/* تنسيقات للشاشات الصغيرة */
@media (max-width: 992px) {
  .customers-filters {
    flex-direction: column;
  }

  .filter-tabs {
    width: 100%;
    overflow-x: auto;
    padding-bottom: 10px;
  }

  .search-container {
    width: 100%;
  }

  .customer-form {
    grid-template-columns: 1fr;
  }

  .form-group.full-width {
    grid-column: span 1;
  }

  .form-actions {
    grid-column: span 1;
  }

  /* تنسيقات نافذة البيع للشاشات المتوسطة */
  .modal-sales-grid {
    grid-template-columns: 1fr;
  }

  .item-add-form {
    grid-template-columns: 1fr 1fr;
    gap: 10px;
  }

  .item-select {
    grid-column: span 2;
  }
}

@media (max-width: 768px) {
  .customers-header h1 {
    font-size: 1.5rem;
  }

  .customers-header p {
    font-size: 0.9rem;
  }

  .sales-history-summary {
    flex-direction: column;
    gap: 10px;
  }

  /* تنسيقات نافذة البيع للشاشات الصغيرة */
  .item-add-form {
    grid-template-columns: 1fr;
  }

  .item-select, .item-quantity, .item-price {
    grid-column: span 1;
  }

  .table {
    font-size: 0.9rem;
  }

  .table th, .table td {
    padding: 8px;
  }
}
