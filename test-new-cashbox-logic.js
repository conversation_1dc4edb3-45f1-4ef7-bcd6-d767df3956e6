/**
 * اختبار المنطق الجديد للخزينة
 * يتحقق من تطبيق المعادلات الصحيحة:
 * - الرصيد الحالي = الرصيد الافتتاحي (ثابت)
 * - إجمالي الأرباح = المبيعات - المشتريات - مصاريف النقل
 */

const path = require('path');
const fs = require('fs');

// تحديد مسار قاعدة البيانات
const dbPath = path.join(require('os').homedir(), 'AppData', 'Roaming', 'warehouse-management-system', 'wms-database', 'warehouse.db');

console.log('🧪 اختبار المنطق الجديد للخزينة...');
console.log(`📁 مسار قاعدة البيانات: ${dbPath}`);

// التحقق من وجود قاعدة البيانات
if (!fs.existsSync(dbPath)) {
  console.error('❌ قاعدة البيانات غير موجودة!');
  process.exit(1);
}

// استيراد المكتبات
const Database = require('better-sqlite3');

async function testNewCashboxLogic() {
  let db;
  
  try {
    // الاتصال بقاعدة البيانات
    db = new Database(dbPath);
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // قراءة الخزينة الحالية
    const cashbox = db.prepare('SELECT * FROM cashbox LIMIT 1').get();
    
    if (!cashbox) {
      console.log('❌ لا توجد خزينة في قاعدة البيانات');
      return;
    }

    console.log('\n📊 بيانات الخزينة الحالية:');
    console.log(`   - الرصيد الافتتاحي: ${cashbox.initial_balance}`);
    console.log(`   - الرصيد الحالي: ${cashbox.current_balance}`);
    console.log(`   - إجمالي المبيعات: ${cashbox.sales_total}`);
    console.log(`   - إجمالي المشتريات: ${cashbox.purchases_total}`);
    console.log(`   - إجمالي مصاريف النقل: ${cashbox.transport_total || 0}`);
    console.log(`   - إجمالي الأرباح: ${cashbox.profit_total}`);

    // التحقق من صحة المعادلات
    console.log('\n🧮 التحقق من صحة المعادلات:');
    
    // المعادلة 1: الرصيد الحالي = الرصيد الافتتاحي
    const equation1Valid = cashbox.current_balance === cashbox.initial_balance;
    console.log(`   1. الرصيد الحالي (${cashbox.current_balance}) = الرصيد الافتتاحي (${cashbox.initial_balance}) ${equation1Valid ? '✅' : '❌'}`);
    
    // المعادلة 2: إجمالي الأرباح = المبيعات - المشتريات - مصاريف النقل
    const expectedProfit = (cashbox.sales_total || 0) - (cashbox.purchases_total || 0) - (cashbox.transport_total || 0);
    const equation2Valid = Math.abs((cashbox.profit_total || 0) - expectedProfit) < 0.01;
    console.log(`   2. إجمالي الأرباح (${cashbox.profit_total}) = ${cashbox.sales_total || 0} - ${cashbox.purchases_total || 0} - ${cashbox.transport_total || 0} = ${expectedProfit} ${equation2Valid ? '✅' : '❌'}`);

    // اختبار تطبيق المنطق الجديد
    console.log('\n🔧 اختبار تطبيق المنطق الجديد...');
    
    // استيراد الدوال المحدثة
    const cashboxUtils = require('./src/utils/cashboxUtils');
    
    // محاكاة عملية بيع
    console.log('\n📈 محاكاة عملية بيع بقيمة 1000...');
    const saleTransaction = {
      type: 'sale',
      amount: 1000,
      profit: 200
    };
    
    const updatedCashboxAfterSale = cashboxUtils.updateCashboxAfterTransaction(cashbox, saleTransaction);
    console.log('   - الرصيد الحالي بعد البيع:', updatedCashboxAfterSale.current_balance);
    console.log('   - إجمالي المبيعات بعد البيع:', updatedCashboxAfterSale.sales_total);
    console.log('   - إجمالي الأرباح بعد البيع:', updatedCashboxAfterSale.profit_total);
    
    // التحقق من صحة النتائج
    const saleEquation1Valid = updatedCashboxAfterSale.current_balance === cashbox.initial_balance;
    const expectedProfitAfterSale = updatedCashboxAfterSale.sales_total - updatedCashboxAfterSale.purchases_total - (updatedCashboxAfterSale.transport_total || 0);
    const saleEquation2Valid = Math.abs(updatedCashboxAfterSale.profit_total - expectedProfitAfterSale) < 0.01;
    
    console.log(`   - المعادلة 1 صحيحة: ${saleEquation1Valid ? '✅' : '❌'}`);
    console.log(`   - المعادلة 2 صحيحة: ${saleEquation2Valid ? '✅' : '❌'}`);

    // محاكاة عملية شراء
    console.log('\n📉 محاكاة عملية شراء بقيمة 500...');
    const purchaseTransaction = {
      type: 'purchase',
      amount: 500,
      profit: 0
    };
    
    const updatedCashboxAfterPurchase = cashboxUtils.updateCashboxAfterTransaction(updatedCashboxAfterSale, purchaseTransaction);
    console.log('   - الرصيد الحالي بعد الشراء:', updatedCashboxAfterPurchase.current_balance);
    console.log('   - إجمالي المشتريات بعد الشراء:', updatedCashboxAfterPurchase.purchases_total);
    console.log('   - إجمالي الأرباح بعد الشراء:', updatedCashboxAfterPurchase.profit_total);
    
    // التحقق من صحة النتائج
    const purchaseEquation1Valid = updatedCashboxAfterPurchase.current_balance === cashbox.initial_balance;
    const expectedProfitAfterPurchase = updatedCashboxAfterPurchase.sales_total - updatedCashboxAfterPurchase.purchases_total - (updatedCashboxAfterPurchase.transport_total || 0);
    const purchaseEquation2Valid = Math.abs(updatedCashboxAfterPurchase.profit_total - expectedProfitAfterPurchase) < 0.01;
    
    console.log(`   - المعادلة 1 صحيحة: ${purchaseEquation1Valid ? '✅' : '❌'}`);
    console.log(`   - المعادلة 2 صحيحة: ${purchaseEquation2Valid ? '✅' : '❌'}`);

    // النتيجة النهائية
    console.log('\n🎯 النتيجة النهائية:');
    const allTestsPassed = equation1Valid && equation2Valid && saleEquation1Valid && saleEquation2Valid && purchaseEquation1Valid && purchaseEquation2Valid;
    
    if (allTestsPassed) {
      console.log('🎉 جميع الاختبارات نجحت! المنطق الجديد للخزينة يعمل بشكل صحيح.');
    } else {
      console.log('❌ بعض الاختبارات فشلت. هناك حاجة لمراجعة المنطق.');
    }

    return {
      success: allTestsPassed,
      currentState: {
        equation1Valid,
        equation2Valid
      },
      simulationResults: {
        saleEquation1Valid,
        saleEquation2Valid,
        purchaseEquation1Valid,
        purchaseEquation2Valid
      }
    };

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error);
    return { success: false, error: error.message };
  } finally {
    if (db) {
      db.close();
      console.log('🔒 تم إغلاق الاتصال بقاعدة البيانات');
    }
  }
}

// تشغيل الاختبار
testNewCashboxLogic()
  .then(result => {
    console.log('\n📋 نتيجة الاختبار:', result);
    console.log('\n🏁 انتهى الاختبار');
  })
  .catch(error => {
    console.error('❌ خطأ في تشغيل الاختبار:', error);
  });
