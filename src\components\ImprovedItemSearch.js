import React, { useState, useEffect, useRef } from 'react';
import { FaSearch, FaTimes, FaBoxOpen, FaMoneyBillWave, FaRulerHorizontal, FaExclamationTriangle } from 'react-icons/fa';
import '../../assets/css/item-search-popup.css';

/**
 * مكون نافذة منبثقة محسنة للبحث عن الأصناف
 * @param {Object} props - خصائص المكون
 * @param {Function} props.onSelect - دالة تُستدعى عند اختيار صنف
 * @param {Function} props.onClose - دالة تُستدعى عند إغلاق النافذة
 * @returns {JSX.Element}
 */
const ImprovedItemSearch = ({ onSelect, onClose }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [items, setItems] = useState([]);
  const [filteredItems, setFilteredItems] = useState([]);
  const [selectedItem, setSelectedItem] = useState(null);
  const searchInputRef = useRef(null);

  // تحميل الأصناف من قاعدة البيانات عند فتح النافذة
  useEffect(() => {
    const loadItems = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log('بدء تحميل بيانات الأصناف والمخزون...');

        // جلب قائمة الأصناف أولاً
        const itemsList = await window.api.invoke('get-all-items');
        console.log(`تم جلب ${itemsList.length} صنف من قاعدة البيانات`);

        if (!itemsList || itemsList.length === 0) {
          setItems([]);
          setFilteredItems([]);
          setError('لم يتم العثور على أصناف في قاعدة البيانات');
          return;
        }

        // جلب المخزون مع تجاوز التخزين المؤقت للحصول على أحدث البيانات
        const inventoryList = await window.api.invoke('get-inventory', true);
        console.log(`تم جلب ${inventoryList.length} عنصر من المخزون`);

        // إنشاء خريطة للمخزون للوصول السريع
        const inventoryMap = new Map();
        inventoryList.forEach(item => {
          if (item && item.item_id) {
            inventoryMap.set(item.item_id, item);
          } else if (item && item.id) {
            inventoryMap.set(item.id, item);
          }
        });

        console.log(`تم إنشاء خريطة المخزون بـ ${inventoryMap.size} عنصر`);

        // دمج بيانات الأصناف مع بيانات المخزون
        const mergedItems = [];

        // استخدام for loop بدلاً من Promise.all لتجنب طلبات متزامنة كثيرة
        for (const item of itemsList) {
          try {
            // البحث عن معلومات المخزون للصنف
            const inventoryItem = inventoryMap.get(item.id);

            // إذا لم يتم العثور على معلومات المخزون أو كانت غير مكتملة، نحاول جلبها مباشرة
            if (!inventoryItem ||
                (typeof inventoryItem.current_quantity !== 'number' || inventoryItem.current_quantity <= 0) ||
                (typeof inventoryItem.selling_price !== 'number' || inventoryItem.selling_price <= 0)) {

              console.log(`جلب معلومات المخزون مباشرة للصنف: ${item.name} (${item.id})`);

              try {
                // استخدام معلمة bypassCache=true لتجاوز التخزين المؤقت
                const directInventoryItem = await window.api.invoke('get-inventory-for-item', item.id, true);

                if (directInventoryItem) {
                  console.log(`تم جلب معلومات المخزون مباشرة للصنف ${item.name}:`);
                  console.log(`- الكمية المتوفرة: ${directInventoryItem.current_quantity}`);
                  console.log(`- سعر البيع: ${directInventoryItem.selling_price}`);

                  mergedItems.push({
                    id: item.id,
                    name: item.name || directInventoryItem.name || 'صنف بدون اسم',
                    unit: item.unit || directInventoryItem.unit || 'قطعة',
                    current_quantity: typeof directInventoryItem.current_quantity === 'number' ? directInventoryItem.current_quantity : 0,
                    selling_price: typeof directInventoryItem.selling_price === 'number' ? directInventoryItem.selling_price : 0,
                    minimum_quantity: typeof directInventoryItem.minimum_quantity === 'number' ? directInventoryItem.minimum_quantity : 0
                  });

                  // تحديث خريطة المخزون بالبيانات الجديدة
                  inventoryMap.set(item.id, directInventoryItem);

                  continue; // انتقل إلى العنصر التالي
                }
              } catch (innerError) {
                console.error(`خطأ في جلب معلومات المخزون مباشرة للصنف ${item.name}:`, innerError);
              }
            }

            // استخدام معلومات المخزون إذا وجدت، وإلا استخدام قيم افتراضية
            mergedItems.push({
              id: item.id,
              name: item.name || 'صنف بدون اسم',
              unit: item.unit || 'قطعة',
              current_quantity: inventoryItem && typeof inventoryItem.current_quantity === 'number' ? inventoryItem.current_quantity : 0,
              selling_price: inventoryItem && typeof inventoryItem.selling_price === 'number' ? inventoryItem.selling_price : 0,
              minimum_quantity: inventoryItem && typeof inventoryItem.minimum_quantity === 'number' ? inventoryItem.minimum_quantity : 0
            });
          } catch (itemError) {
            console.error(`خطأ في معالجة الصنف ${item.name}:`, itemError);
          }
        }

        console.log(`تم دمج بيانات ${mergedItems.length} صنف مع المخزون بنجاح`);

        // عرض عينة من البيانات للتشخيص
        if (mergedItems.length > 0) {
          const sampleItems = mergedItems.slice(0, Math.min(3, mergedItems.length));
          sampleItems.forEach(item => {
            console.log(`عينة: ${item.name} - الكمية=${item.current_quantity}, السعر=${item.selling_price}`);
          });
        }

        // فلترة الأصناف التي لها كمية متوفرة وسعر بيع
        const validItems = mergedItems.filter(item =>
          typeof item.current_quantity === 'number' &&
          typeof item.selling_price === 'number'
        );

        console.log(`تم العثور على ${validItems.length} صنف صالح للبيع من أصل ${mergedItems.length}`);

        // ترتيب الأصناف بحيث تظهر الأصناف ذات الكمية المتوفرة وسعر البيع أولاً
        const sortedItems = [...mergedItems].sort((a, b) => {
          // الأصناف ذات الكمية المتوفرة وسعر البيع أولاً
          const aValid = a.current_quantity > 0 && a.selling_price > 0;
          const bValid = b.current_quantity > 0 && b.selling_price > 0;

          if (aValid && !bValid) return -1;
          if (!aValid && bValid) return 1;

          // ثم ترتيب حسب الاسم
          return a.name.localeCompare(b.name);
        });

        setItems(sortedItems);
        setFilteredItems(sortedItems);
      } catch (error) {
        console.error('خطأ في تحميل الأصناف:', error);
        setError('حدث خطأ أثناء تحميل الأصناف. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };

    loadItems();

    // تركيز حقل البحث عند فتح النافذة
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);

  // تصفية الأصناف بناءً على مصطلح البحث
  useEffect(() => {
    if (!items || items.length === 0) {
      setFilteredItems([]);
      return;
    }

    if (searchTerm.trim() === '') {
      setFilteredItems(items);
    } else {
      const filtered = items.filter(item =>
        (item.name && item.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (item.id && item.id.toString().includes(searchTerm))
      );
      setFilteredItems(filtered);
    }
  }, [items, searchTerm]);

  // معالجة تغيير مصطلح البحث
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  // معالجة اختيار صنف
  const handleItemClick = (item) => {
    setSelectedItem(item);
  };

  // معالجة تأكيد الاختيار
  const handleConfirm = async () => {
    if (selectedItem) {
      try {
        console.log('تأكيد اختيار الصنف:', selectedItem);

        // التحقق من وجود البيانات المطلوبة في الصنف المحدد
        const hasRequiredData =
          selectedItem.id !== undefined &&
          selectedItem.name !== undefined &&
          typeof selectedItem.current_quantity === 'number' &&
          typeof selectedItem.selling_price === 'number';

        // محاولة الحصول على أحدث البيانات من قاعدة البيانات
        try {
          console.log(`جلب أحدث معلومات للصنف ${selectedItem.id} (${selectedItem.name}) من قاعدة البيانات`);

          // استخدام معلمة bypassCache=true لتجاوز التخزين المؤقت
          const freshItem = await window.api.invoke('get-inventory-for-item', selectedItem.id, true);

          if (freshItem) {
            console.log('تم استلام أحدث بيانات الصنف من قاعدة البيانات:');
            console.log('- المعرف:', freshItem.id);
            console.log('- الاسم:', freshItem.name);
            console.log('- الكمية المتوفرة:', freshItem.current_quantity);
            console.log('- سعر البيع:', freshItem.selling_price);

            // إنشاء كائن الصنف المحدث مع البيانات من قاعدة البيانات
            const updatedItem = {
              id: selectedItem.id,
              name: selectedItem.name || freshItem.name || 'صنف بدون اسم',
              unit: selectedItem.unit || freshItem.unit || 'قطعة',
              current_quantity: typeof freshItem.current_quantity === 'number' ? freshItem.current_quantity : 0,
              selling_price: typeof freshItem.selling_price === 'number' ? freshItem.selling_price : 0,
              minimum_quantity: typeof freshItem.minimum_quantity === 'number' ? freshItem.minimum_quantity : 0
            };

            // التحقق من صحة البيانات المحدثة
            if (updatedItem.current_quantity > 0 && updatedItem.selling_price > 0) {
              console.log('تم التحقق من صحة البيانات المحدثة، إرسال البيانات إلى صفحة البيع');

              // تحديث الصنف المحدد في الواجهة
              setSelectedItem(updatedItem);

              // إرسال البيانات المحدثة إلى صفحة البيع
              onSelect(updatedItem);
              return;
            } else {
              console.warn('البيانات المحدثة غير صالحة:');
              console.warn(`- الكمية المتوفرة: ${updatedItem.current_quantity}`);
              console.warn(`- سعر البيع: ${updatedItem.selling_price}`);
            }
          }
        } catch (freshError) {
          console.error('خطأ في جلب أحدث معلومات الصنف:', freshError);
        }

        // إذا كانت البيانات المحلية صالحة، نستخدمها
        if (hasRequiredData && selectedItem.current_quantity > 0 && selectedItem.selling_price > 0) {
          console.log('استخدام البيانات المحلية الصالحة للصنف:');
          console.log('- المعرف:', selectedItem.id);
          console.log('- الاسم:', selectedItem.name);
          console.log('- الكمية المتوفرة:', selectedItem.current_quantity);
          console.log('- سعر البيع:', selectedItem.selling_price);

          onSelect(selectedItem);
          return;
        }

        console.log('البيانات المحلية غير صالحة، محاولة جلب البيانات من قاعدة البيانات بطريقة أخرى');

        // محاولة الحصول على معلومات الصنف من قاعدة البيانات بطريقة أخرى
        const dbItem = await window.api.invoke('get-inventory-item', selectedItem.id, true);

        if (dbItem && dbItem.id) {
          console.log('تم استلام بيانات الصنف من قاعدة البيانات (الطريقة الثانية):', dbItem);

          // إنشاء كائن الصنف المحدث مع البيانات من قاعدة البيانات
          const updatedItem = {
            id: selectedItem.id,
            name: selectedItem.name || dbItem.name || 'صنف بدون اسم',
            unit: selectedItem.unit || dbItem.unit || 'قطعة',
            current_quantity: typeof dbItem.current_quantity === 'number' ? dbItem.current_quantity : 0,
            selling_price: typeof dbItem.selling_price === 'number' ? dbItem.selling_price : 0,
            minimum_quantity: typeof dbItem.minimum_quantity === 'number' ? dbItem.minimum_quantity : 0
          };

          // التحقق من صحة البيانات المحدثة
          if (updatedItem.current_quantity > 0 && updatedItem.selling_price > 0) {
            console.log('تم التحقق من صحة البيانات المحدثة (الطريقة الثانية)، إرسال البيانات إلى صفحة البيع');

            // تحديث الصنف المحدد في الواجهة
            setSelectedItem(updatedItem);

            // إرسال البيانات المحدثة إلى صفحة البيع
            onSelect(updatedItem);
            return;
          }
        }

        // محاولة استخدام طريقة ثالثة للحصول على البيانات
        console.log('محاولة استخدام طريقة ثالثة للحصول على البيانات');

        // محاولة الحصول على معلومات المعاملات للصنف
        try {
          const transactions = await window.api.invoke('get-transactions', { item_id: selectedItem.id });

          if (transactions && transactions.length > 0) {
            console.log(`تم العثور على ${transactions.length} معاملة للصنف ${selectedItem.id}`);

            // البحث عن آخر معاملة شراء
            const purchaseTransactions = transactions.filter(t => t.transaction_type === 'purchase');

            if (purchaseTransactions.length > 0) {
              // ترتيب معاملات الشراء حسب التاريخ (الأحدث أولاً)
              const sortedPurchases = purchaseTransactions.sort((a, b) => {
                const dateA = new Date(a.transaction_date || 0);
                const dateB = new Date(b.transaction_date || 0);
                return dateB - dateA;
              });

              const lastPurchase = sortedPurchases[0];

              console.log('تم العثور على آخر معاملة شراء:', lastPurchase);

              // حساب الكمية المتوفرة من خلال جمع معاملات الشراء وطرح معاملات البيع
              const totalPurchased = purchaseTransactions.reduce((sum, t) => sum + (parseFloat(t.quantity) || 0), 0);

              const totalSold = transactions
                .filter(t => t.transaction_type === 'sale')
                .reduce((sum, t) => sum + (parseFloat(t.quantity) || 0), 0);

              const calculatedQuantity = totalPurchased - totalSold;

              console.log(`حساب الكمية المتوفرة للصنف ${selectedItem.id}:`);
              console.log(`- إجمالي المشتريات: ${totalPurchased}`);
              console.log(`- إجمالي المبيعات: ${totalSold}`);
              console.log(`- الكمية المحسوبة: ${calculatedQuantity}`);

              // استخدام سعر البيع المخزن في المعاملة إذا كان موجوداً
              let sellingPrice = 0;

              if (lastPurchase.selling_price && parseFloat(lastPurchase.selling_price) > 0) {
                sellingPrice = parseFloat(lastPurchase.selling_price);
                console.log(`استخدام سعر البيع المخزن في المعاملة: ${sellingPrice}`);
              } else if (lastPurchase.price && parseFloat(lastPurchase.price) > 0) {
                // هامش ربح 20%
                sellingPrice = parseFloat(lastPurchase.price) * 1.2;
                console.log(`حساب سعر البيع بناءً على سعر الشراء: ${sellingPrice}`);
              }

              // إنشاء كائن الصنف المحدث مع البيانات من المعاملات
              const transactionBasedItem = {
                id: selectedItem.id,
                name: selectedItem.name || 'صنف بدون اسم',
                unit: selectedItem.unit || 'قطعة',
                current_quantity: calculatedQuantity > 0 ? calculatedQuantity : 0,
                selling_price: sellingPrice,
                minimum_quantity: lastPurchase.minimum_quantity ? parseFloat(lastPurchase.minimum_quantity) : 0
              };

              // التحقق من صحة البيانات المحدثة
              if (transactionBasedItem.current_quantity > 0 && transactionBasedItem.selling_price > 0) {
                console.log('تم التحقق من صحة البيانات المحدثة (الطريقة الثالثة)، إرسال البيانات إلى صفحة البيع');

                // تحديث الصنف المحدد في الواجهة
                setSelectedItem(transactionBasedItem);

                // إرسال البيانات المحدثة إلى صفحة البيع
                onSelect(transactionBasedItem);

                // تحديث المخزون في قاعدة البيانات
                try {
                  await window.api.invoke('update-inventory', {
                    itemId: selectedItem.id,
                    updates: {
                      current_quantity: transactionBasedItem.current_quantity,
                      selling_price: transactionBasedItem.selling_price,
                      minimum_quantity: transactionBasedItem.minimum_quantity
                    }
                  });

                  console.log('تم تحديث المخزون في قاعدة البيانات');
                } catch (updateError) {
                  console.error('خطأ في تحديث المخزون:', updateError);
                }

                return;
              }
            }
          }
        } catch (transactionError) {
          console.error('خطأ في جلب معلومات المعاملات:', transactionError);
        }
      } catch (error) {
        console.error('خطأ في الحصول على معلومات الصنف:', error);
      }

      // في حالة فشل جميع المحاولات، نستخدم البيانات المحلية مع تعيين قيم افتراضية للحقول المفقودة
      console.log('استخدام البيانات المحلية للصنف مع تعيين قيم افتراضية للحقول المفقودة');
      const fallbackItem = {
        id: selectedItem.id,
        name: selectedItem.name || 'صنف بدون اسم',
        unit: selectedItem.unit || 'قطعة',
        current_quantity: typeof selectedItem.current_quantity === 'number' ? selectedItem.current_quantity : 0,
        selling_price: typeof selectedItem.selling_price === 'number' ? selectedItem.selling_price : 0,
        minimum_quantity: typeof selectedItem.minimum_quantity === 'number' ? selectedItem.minimum_quantity : 0
      };

      console.log('استخدام البيانات الاحتياطية للصنف:');
      console.log('- المعرف:', fallbackItem.id);
      console.log('- الاسم:', fallbackItem.name);
      console.log('- الكمية المتوفرة:', fallbackItem.current_quantity);
      console.log('- سعر البيع:', fallbackItem.selling_price);

      onSelect(fallbackItem);
    }
  };

  // معالجة الضغط على مفتاح
  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      onClose();
    } else if (e.key === 'Enter' && selectedItem) {
      handleConfirm();
    }
  };

  // تحديد لون الكمية بناءً على قيمتها
  const getQuantityColorClass = (quantity, minimumQuantity = 0) => {
    if (quantity <= 0) return 'out';
    if (minimumQuantity > 0 && quantity <= minimumQuantity) return 'low';
    return '';
  };

  return (
    <div className="item-search-popup" onKeyDown={handleKeyDown}>
      <div className="item-search-container">
        <div className="item-search-header">
          <h3>البحث عن صنف</h3>
          <button className="item-search-close" onClick={onClose}>
            <FaTimes />
          </button>
        </div>

        <div className="item-search-body">
          <div className="item-search-input">
            <FaSearch className="search-icon" />
            <input
              ref={searchInputRef}
              type="text"
              placeholder="اكتب اسم الصنف أو الرقم..."
              value={searchTerm}
              onChange={handleSearchChange}
            />
          </div>

          {loading ? (
            <div style={{ textAlign: 'center', padding: '20px', color: 'var(--text-light, #777)' }}>
              جاري تحميل الأصناف...
            </div>
          ) : error ? (
            <div style={{ textAlign: 'center', padding: '20px', color: 'var(--danger-color, #e74c3c)' }}>
              <FaExclamationTriangle style={{ marginLeft: '5px' }} />
              {error}
            </div>
          ) : filteredItems.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '20px', color: 'var(--text-light, #777)' }}>
              {items.length === 0 ? 'لا توجد أصناف متاحة' : 'لا توجد نتائج مطابقة للبحث'}
            </div>
          ) : (
            <div className="item-search-results">
              {filteredItems.length === 0 ? (
                <div className="no-items-message">
                  <FaExclamationCircle style={{ marginLeft: '5px', color: '#e74c3c' }} />
                  لم يتم العثور على أصناف مطابقة لمصطلح البحث
                </div>
              ) : (
                filteredItems.map(item => (
                  <div
                    key={item.id}
                    className={`item-card ${selectedItem && selectedItem.id === item.id ? 'selected' : ''}`}
                    onClick={() => handleItemClick(item)}
                    onDoubleClick={handleConfirm}
                  >
                    <div className="item-name">{item.name || 'صنف بدون اسم'}</div>
                    <div className="item-details">
                      <div className="item-detail">
                        <span className="item-detail-label">
                          <FaRulerHorizontal style={{ marginLeft: '5px' }} />
                          الوحدة:
                        </span>
                        <span className="item-detail-value">{item.unit || 'غير محدد'}</span>
                      </div>
                      <div className="item-detail">
                        <span className="item-detail-label">
                          <FaBoxOpen style={{ marginLeft: '5px' }} />
                          الكمية المتوفرة:
                        </span>
                        <span
                          className={`item-detail-value item-quantity ${getQuantityColorClass(item.current_quantity, item.minimum_quantity)}`}
                          title={`الكمية المتوفرة: ${item.current_quantity || 0}`}
                        >
                          <strong>{item.current_quantity || 0}</strong>
                          {item.current_quantity <= 0 && (
                            <FaExclamationTriangle style={{ marginRight: '5px', color: '#e74c3c' }} title="الكمية غير متوفرة" />
                          )}
                        </span>
                      </div>
                      <div className="item-detail">
                        <span className="item-detail-label">
                          <FaMoneyBillWave style={{ marginLeft: '5px' }} />
                          سعر البيع:
                        </span>
                        <span
                          className="item-detail-value"
                          title={`سعر البيع: ${item.selling_price ? item.selling_price.toFixed(2) : '0.00'}`}
                        >
                          <strong>{item.selling_price ? item.selling_price.toFixed(2) : '0.00'}</strong>
                          {(item.selling_price <= 0) && (
                            <FaExclamationTriangle style={{ marginRight: '5px', color: '#e74c3c' }} title="سعر البيع غير محدد" />
                          )}
                        </span>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          )}
        </div>

        <div className="item-search-footer">
          <button
            className="item-search-btn item-search-btn-secondary"
            onClick={onClose}
          >
            إلغاء
          </button>
          <button
            className="item-search-btn item-search-btn-primary"
            onClick={handleConfirm}
            disabled={!selectedItem}
          >
            اختيار
          </button>
        </div>
      </div>
    </div>
  );
};

export default ImprovedItemSearch;
