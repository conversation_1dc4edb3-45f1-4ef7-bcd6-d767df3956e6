/**
 * مدير Worker Threads لقاعدة البيانات
 * يستخدم لإدارة Worker Threads وتنفيذ استعلامات قاعدة البيانات في خلفية التطبيق
 */

const { Worker } = require('worker_threads');
const path = require('path');

// إنشاء معرف فريد للرسائل
let messageId = 0;

// تخزين الوعود المعلقة
const pendingPromises = new Map();

// إنشاء Worker Thread
let worker = null;

// حالة تهيئة Worker Thread
let isInitialized = false;
let isInitializing = false;
let initPromise = null;

/**
 * تهيئة Worker Thread
 * @param {String} dbPath - مسار قاعدة البيانات
 * @returns {Promise} - وعد بنتيجة التهيئة
 */
const initialize = (dbPath) => {
  // إذا كان Worker Thread قيد التهيئة، انتظر اكتمال التهيئة
  if (isInitializing) {
    return initPromise;
  }
  
  // إذا كان Worker Thread مهيأ بالفعل، أعد وعدًا محلولًا
  if (isInitialized && worker) {
    return Promise.resolve({ success: true, message: 'Worker Thread مهيأ بالفعل' });
  }
  
  // تعيين حالة التهيئة
  isInitializing = true;
  
  // إنشاء وعد التهيئة
  initPromise = new Promise((resolve, reject) => {
    try {
      console.log('جاري تهيئة Worker Thread لقاعدة البيانات...');
      
      // إنشاء Worker Thread جديد
      worker = new Worker(path.join(__dirname, '../workers/database-worker.js'));
      
      // إضافة مستمع للرسائل
      worker.on('message', (message) => {
        const { id, type, result, error } = message;
        
        // إذا كانت رسالة جاهزية، تعيين حالة التهيئة
        if (type === 'ready') {
          console.log('Worker Thread جاهز للاستخدام');
          isInitialized = true;
          isInitializing = false;
          resolve({ success: true, message: 'تم تهيئة Worker Thread بنجاح' });
          return;
        }
        
        // الحصول على الوعد المعلق
        const promise = pendingPromises.get(id);
        
        if (promise) {
          // حل أو رفض الوعد بناءً على النتيجة
          if (error) {
            promise.reject(new Error(error));
          } else {
            promise.resolve(result);
          }
          
          // إزالة الوعد من القائمة
          pendingPromises.delete(id);
        }
      });
      
      // إضافة مستمع للأخطاء
      worker.on('error', (error) => {
        console.error('خطأ في Worker Thread:', error);
        
        // إذا كان Worker Thread قيد التهيئة، رفض وعد التهيئة
        if (isInitializing) {
          isInitializing = false;
          reject(error);
        }
        
        // رفض جميع الوعود المعلقة
        for (const [id, promise] of pendingPromises.entries()) {
          promise.reject(new Error('حدث خطأ في Worker Thread'));
          pendingPromises.delete(id);
        }
      });
      
      // إضافة مستمع لإنهاء Worker Thread
      worker.on('exit', (code) => {
        console.log(`Worker Thread انتهى بكود: ${code}`);
        
        // إعادة تعيين حالة التهيئة
        isInitialized = false;
        worker = null;
        
        // رفض جميع الوعود المعلقة
        for (const [id, promise] of pendingPromises.entries()) {
          promise.reject(new Error('تم إنهاء Worker Thread'));
          pendingPromises.delete(id);
        }
      });
      
      // تهيئة قاعدة البيانات في Worker Thread
      sendToWorker('initialize', { dbPath })
        .then(() => {
          console.log('تم تهيئة قاعدة البيانات في Worker Thread بنجاح');
        })
        .catch((error) => {
          console.error('خطأ في تهيئة قاعدة البيانات في Worker Thread:', error);
          isInitializing = false;
          reject(error);
        });
    } catch (error) {
      console.error('خطأ في تهيئة Worker Thread:', error);
      isInitializing = false;
      reject(error);
    }
  });
  
  return initPromise;
};

/**
 * إرسال رسالة إلى Worker Thread
 * @param {String} type - نوع العملية
 * @param {Object} data - بيانات العملية
 * @returns {Promise} - وعد بنتيجة العملية
 */
const sendToWorker = (type, data) => {
  // التحقق من وجود Worker Thread
  if (!worker) {
    return Promise.reject(new Error('لم يتم تهيئة Worker Thread'));
  }
  
  // إنشاء معرف فريد للرسالة
  const id = messageId++;
  
  // إنشاء وعد جديد
  return new Promise((resolve, reject) => {
    // تخزين الوعد في القائمة
    pendingPromises.set(id, { resolve, reject });
    
    // إرسال الرسالة إلى Worker Thread
    worker.postMessage({ id, type, data });
  });
};

/**
 * تنفيذ استعلام SQL
 * @param {String} sql - استعلام SQL
 * @param {Array} params - معلمات الاستعلام
 * @param {String} type - نوع الاستعلام (run, get, all, exec)
 * @returns {Promise} - وعد بنتيجة الاستعلام
 */
const executeQuery = (sql, params, type = 'all') => {
  return sendToWorker('executeQuery', { sql, params, type });
};

/**
 * الحصول على الأصناف
 * @returns {Promise<Array>} - وعد بقائمة الأصناف
 */
const getItems = () => {
  return sendToWorker('getItems');
};

/**
 * الحصول على المخزون
 * @returns {Promise<Array>} - وعد بقائمة المخزون
 */
const getInventory = () => {
  return sendToWorker('getInventory');
};

/**
 * الحصول على المعاملات
 * @param {Object} filters - فلاتر التصفية
 * @returns {Promise<Array>} - وعد بقائمة المعاملات
 */
const getTransactions = (filters) => {
  return sendToWorker('getTransactions', filters);
};

/**
 * الحصول على الأصناف مع التحميل التدريجي
 * @param {Object} options - خيارات التحميل التدريجي
 * @returns {Promise<Object>} - وعد بنتيجة التحميل التدريجي
 */
const getItemsPaginated = (options) => {
  return sendToWorker('getItemsPaginated', options);
};

/**
 * الحصول على المعاملات مع التحميل التدريجي
 * @param {Object} options - خيارات التحميل التدريجي
 * @returns {Promise<Object>} - وعد بنتيجة التحميل التدريجي
 */
const getTransactionsPaginated = (options) => {
  return sendToWorker('getTransactionsPaginated', options);
};

/**
 * إضافة صنف جديد
 * @param {Object} item - بيانات الصنف
 * @returns {Promise<Object>} - وعد بالصنف المضاف
 */
const addItem = (item) => {
  return sendToWorker('addItem', item);
};

/**
 * إضافة معاملة جديدة
 * @param {Object} transaction - بيانات المعاملة
 * @returns {Promise<Object>} - وعد بالمعاملة المضافة
 */
const addTransaction = (transaction) => {
  return sendToWorker('addTransaction', transaction);
};

/**
 * إغلاق Worker Thread
 * @returns {Promise} - وعد بنتيجة الإغلاق
 */
const close = () => {
  if (worker) {
    console.log('جاري إغلاق Worker Thread...');
    worker.terminate();
    worker = null;
    isInitialized = false;
  }
  
  return Promise.resolve({ success: true, message: 'تم إغلاق Worker Thread بنجاح' });
};

// تصدير الوظائف
module.exports = {
  initialize,
  executeQuery,
  getItems,
  getInventory,
  getTransactions,
  getItemsPaginated,
  getTransactionsPaginated,
  addItem,
  addTransaction,
  close
};
