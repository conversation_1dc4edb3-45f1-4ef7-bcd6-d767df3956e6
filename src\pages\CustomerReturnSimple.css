/* أنماط واجهة الإرجاع البسيطة */
.customer-return-modal-simple .app-modal-container {
  max-width: 600px;
}

.return-simple-container {
  padding: 15px;
}

.return-header {
  margin-bottom: 20px;
}

.return-item-selection {
  margin-bottom: 20px;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--text-dark);
  display: flex;
  align-items: center;
}

.item-select-container {
  margin-bottom: 15px;
}

.return-item-autocomplete {
  width: 100%;
}

.return-item-details {
  background-color: var(--bg-light);
  border-radius: var(--border-radius-md);
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid var(--border-color);
}

.item-name {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--primary-color);
  text-align: center;
}

.item-quantities {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.quantity-box {
  flex: 1;
  min-width: 120px;
  background-color: #fff;
  border-radius: var(--border-radius-sm);
  padding: 10px;
  margin: 0 5px 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  text-align: center;
}

.quantity-label {
  font-size: 0.85rem;
  color: var(--text-light);
  margin-bottom: 5px;
}

.quantity-value {
  font-size: 1.5rem;
  font-weight: 700;
}

.quantity-value.sold {
  color: var(--primary-color);
}

.quantity-value.returned {
  color: var(--warning-color);
}

.quantity-value.available {
  color: var(--success-color);
}

.return-form {
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: 0.95rem;
}

.form-text {
  font-size: 0.8rem;
  color: var(--text-light);
  margin-top: 5px;
}

.total-amount {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--primary-color);
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-color);
}

.return-actions {
  display: flex;
  justify-content: center;
  margin-top: 15px;
}

.return-items-list {
  margin-top: 30px;
  margin-bottom: 20px;
}

.return-items-table {
  margin-bottom: 20px;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 10px;
  text-align: right;
  border-bottom: 1px solid var(--border-color);
}

.table th {
  font-weight: 600;
  background-color: var(--bg-light);
}

.table tfoot td {
  font-weight: 600;
}

.total-cell {
  font-size: 1.1rem;
  color: var(--primary-color);
}

.text-left {
  text-align: left;
}

.return-submit-form {
  margin-top: 20px;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
  .item-quantities {
    flex-direction: column;
  }
  
  .quantity-box {
    margin: 0 0 10px;
  }
}
