/**
 * نظام الأحداث المركزي
 * يوفر واجهة موحدة لإرسال واستقبال الإشعارات بين العمليات الخلفية وواجهة المستخدم
 */

const { ipcMain, BrowserWindow } = require('electron');
const { logSystem, logError } = require('./error-handler');

// قائمة بجميع أنواع الأحداث المدعومة
const EventTypes = {
  // أحداث المخزون
  INVENTORY_UPDATED: 'inventory-updated',
  INVENTORY_ITEM_ADDED: 'inventory-item-added',
  INVENTORY_ITEM_UPDATED: 'inventory-item-updated',
  INVENTORY_ITEM_DELETED: 'inventory-item-deleted',

  // أحداث الأصناف
  ITEM_ADDED: 'item-added',
  ITEM_UPDATED: 'item-updated',
  ITEM_DELETED: 'item-deleted',

  // أحداث المعاملات
  TRANSACTION_ADDED: 'transaction-added',
  TRANSACTION_UPDATED: 'transaction-updated',
  TRANSACTION_DELETED: 'transaction-deleted',

  // أحداث العملاء
  CUSTOMER_ADDED: 'customer-added',
  CUSTOMER_UPDATED: 'customer-updated',
  CUSTOMER_DELETED: 'customer-deleted',

  // أحداث الخزينة
  CASHBOX_UPDATED: 'cashbox-updated',
  CASHBOX_TRANSACTION_ADDED: 'cashbox-transaction-added',

  // أحداث الأرباح
  PROFITS_UPDATED: 'profits-updated',

  // أحداث عامة
  DATA_CHANGED: 'data-changed',
  REFRESH_NEEDED: 'refresh-needed'
};

// تخزين مؤقت للأحداث لمنع التكرار
const eventCache = new Map();
const EVENT_CACHE_EXPIRY = 2000; // مدة صلاحية التخزين المؤقت للأحداث (2 ثانية)

/**
 * إرسال حدث إلى واجهة المستخدم
 * @param {string} eventType - نوع الحدث
 * @param {Object} data - البيانات المرتبطة بالحدث
 * @param {boolean} ignoreCache - تجاهل التخزين المؤقت وإرسال الحدث بغض النظر عن التكرار
 */
function sendEvent(eventType, data = {}, ignoreCache = false) {
  try {
    // التحقق من وجود النافذة الرئيسية
    if (!global.mainWindow || !global.mainWindow.webContents) {
      logSystem(`لا يمكن إرسال الحدث ${eventType} لأن النافذة الرئيسية غير متوفرة`, 'warning');
      return;
    }

    // إضافة وقت الحدث
    const eventData = {
      ...data,
      timestamp: new Date().toISOString()
    };

    // التحقق من التخزين المؤقت للأحداث لمنع التكرار
    if (eventType === EventTypes.REFRESH_NEEDED && !ignoreCache) {
      const cacheKey = `${eventType}-${eventData.target}`;
      const cachedEvent = eventCache.get(cacheKey);

      if (cachedEvent && (Date.now() - cachedEvent.timestamp < EVENT_CACHE_EXPIRY)) {
        // تجاهل الحدث لأنه تم إرساله مؤخرًا
        return;
      }

      // تخزين الحدث في التخزين المؤقت
      eventCache.set(cacheKey, {
        timestamp: Date.now(),
        data: eventData
      });
    }

    // تسجيل معلومات الحدث (فقط للأحداث المهمة)
    if (eventType !== EventTypes.REFRESH_NEEDED) {
      logSystem(`إرسال حدث ${eventType} مع البيانات: ${JSON.stringify(eventData)}`, 'info');
    }

    // إرسال الحدث إلى واجهة المستخدم
    global.mainWindow.webContents.send(eventType, eventData);

    // إرسال حدث عام للتحديث (فقط للأحداث المهمة)
    if (eventType !== EventTypes.REFRESH_NEEDED && eventType !== EventTypes.DATA_CHANGED) {
      global.mainWindow.webContents.send(EventTypes.DATA_CHANGED, {
        source: eventType,
        data: eventData,
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    logError(error, `sendEvent-${eventType}`);
  }
}

/**
 * تسجيل معالج لاستقبال الأحداث من واجهة المستخدم
 * @param {string} eventType - نوع الحدث
 * @param {Function} handler - دالة المعالجة
 */
function registerHandler(eventType, handler) {
  try {
    ipcMain.handle(eventType, async (event, ...args) => {
      try {
        return await handler(...args);
      } catch (error) {
        logError(error, `handler-${eventType}`);
        throw error;
      }
    });

    logSystem(`تم تسجيل معالج للحدث ${eventType} بنجاح`, 'info');
  } catch (error) {
    logError(error, `registerHandler-${eventType}`);
  }
}

/**
 * إرسال إشعار بتحديث المخزون
 * @param {Object} data - بيانات التحديث
 * @param {boolean} forceRefresh - إجبار تحديث المخزون بغض النظر عن التخزين المؤقت
 */
function notifyInventoryUpdated(data = {}, forceRefresh = false) {
  sendEvent(EventTypes.INVENTORY_UPDATED, data);
  sendEvent(EventTypes.REFRESH_NEEDED, { target: 'inventory' }, forceRefresh);
}

/**
 * إرسال إشعار بإضافة صنف جديد
 * @param {Object} item - بيانات الصنف الجديد
 */
function notifyItemAdded(item) {
  logSystem(`إرسال إشعار بإضافة صنف جديد: ${JSON.stringify(item)}`, 'info');

  // إرسال إشعار بإضافة صنف جديد (مع تجاهل التخزين المؤقت)
  sendEvent(EventTypes.ITEM_ADDED, item, true);

  // إرسال إشعار بالحاجة لتحديث قائمة الأصناف (مع تجاهل التخزين المؤقت)
  sendEvent(EventTypes.REFRESH_NEEDED, {
    target: 'items',
    operation: 'add-item',
    itemId: item.id,
    timestamp: new Date().toISOString()
  }, true);

  // إرسال إشعار بالحاجة لتحديث المخزون (مع تجاهل التخزين المؤقت)
  sendEvent(EventTypes.REFRESH_NEEDED, {
    target: 'inventory',
    operation: 'add-item',
    itemId: item.id,
    timestamp: new Date().toISOString()
  }, true);

  // إرسال إشعار إضافي للتأكد من تحديث واجهة المستخدم بعد فترة قصيرة
  setTimeout(() => {
    sendEvent(EventTypes.REFRESH_NEEDED, {
      target: 'items',
      operation: 'add-item-delayed',
      itemId: item.id,
      timestamp: new Date().toISOString()
    }, true);

    // إرسال إشعار بالحاجة لتحديث المخزون مرة أخرى
    sendEvent(EventTypes.REFRESH_NEEDED, {
      target: 'inventory',
      operation: 'add-item-delayed',
      itemId: item.id,
      timestamp: new Date().toISOString()
    }, true);
  }, 1000);

  // إرسال إشعار إضافي بعد فترة أطول للتأكد من تحديث واجهة المستخدم
  setTimeout(() => {
    sendEvent(EventTypes.REFRESH_NEEDED, {
      target: 'all',
      operation: 'add-item-final',
      itemId: item.id,
      timestamp: new Date().toISOString()
    }, true);
  }, 2000);
}

/**
 * إرسال إشعار بتحديث صنف
 * @param {Object} item - بيانات الصنف المحدث
 */
function notifyItemUpdated(item) {
  sendEvent(EventTypes.ITEM_UPDATED, item);
  sendEvent(EventTypes.REFRESH_NEEDED, { target: 'items' });
}

/**
 * إرسال إشعار بحذف صنف
 * @param {Object} item - بيانات الصنف المحذوف
 */
function notifyItemDeleted(item) {
  sendEvent(EventTypes.ITEM_DELETED, item);
  sendEvent(EventTypes.REFRESH_NEEDED, { target: 'items' });
}

/**
 * إرسال إشعار بإضافة معاملة جديدة
 * @param {Object} transaction - بيانات المعاملة الجديدة
 */
function notifyTransactionAdded(transaction) {
  sendEvent(EventTypes.TRANSACTION_ADDED, transaction);
  sendEvent(EventTypes.REFRESH_NEEDED, { target: 'transactions' });

  // تحديث المخزون لأن المعاملات تؤثر على المخزون (مع تجاهل التخزين المؤقت)
  sendEvent(EventTypes.REFRESH_NEEDED, { target: 'inventory' }, true);
}

/**
 * إرسال إشعار بإضافة عميل جديد
 * @param {Object} customer - بيانات العميل الجديد
 */
function notifyCustomerAdded(customer) {
  sendEvent(EventTypes.CUSTOMER_ADDED, customer);
  sendEvent(EventTypes.REFRESH_NEEDED, { target: 'customers' });
}

/**
 * إرسال إشعار بتحديث عميل
 * @param {Object} customer - بيانات العميل المحدث
 */
function notifyCustomerUpdated(customer) {
  sendEvent(EventTypes.CUSTOMER_UPDATED, customer);
  sendEvent(EventTypes.REFRESH_NEEDED, { target: 'customers' });
}

/**
 * إرسال إشعار بتحديث الخزينة
 * @param {Object} data - بيانات تحديث الخزينة
 */
function notifyCashboxUpdated(data) {
  try {
    console.log(`[CASHBOX-EVENT] إرسال إشعار بتحديث الخزينة:`, data);
    logSystem(`[CASHBOX-EVENT] إرسال إشعار بتحديث الخزينة: ${JSON.stringify(data)}`, 'info');

    // التحقق من وجود البيانات
    if (!data) {
      console.error(`[CASHBOX-EVENT] خطأ: بيانات الإشعار غير موجودة (قيمة فارغة)`);
      logError(new Error('بيانات الإشعار غير موجودة'), 'notifyCashboxUpdated');
      return;
    }

    // تسجيل معلومات إضافية للتشخيص
    console.log(`[CASHBOX-EVENT] معلومات تشخيصية للإشعار:`);
    console.log(`- معرف الخزينة: ${data.id || 'غير محدد'}`);
    console.log(`- نوع المعاملة: ${data.transaction_type || 'غير محدد'}`);
    console.log(`- المبلغ: ${data.amount || 0}`);
    console.log(`- الرصيد الحالي: ${data.current_balance || 'غير محدد'}`);
    console.log(`- إجمالي المبيعات: ${data.sales_total || 'غير محدد'}`);
    console.log(`- إجمالي المشتريات: ${data.purchases_total || 'غير محدد'}`);
    console.log(`- إجمالي المرتجعات: ${data.returns_total || 'غير محدد'}`);
    console.log(`- إجمالي الأرباح: ${data.profit_total || 'غير محدد'}`);
    console.log(`- الربح: ${data.profit || 'غير محدد'}`);
    console.log(`- النجاح: ${data.success || false}`);

    // التحقق من وجود النافذة الرئيسية
    if (!global.mainWindow || !global.mainWindow.webContents) {
      console.error(`[CASHBOX-EVENT] خطأ: النافذة الرئيسية غير متوفرة`);
      logError(new Error('النافذة الرئيسية غير متوفرة'), 'notifyCashboxUpdated');
      return;
    }

    // إرسال إشعار بتحديث الخزينة (مع تجاهل التخزين المؤقت)
    try {
      console.log(`[CASHBOX-EVENT] إرسال إشعار CASHBOX_UPDATED...`);
      sendEvent(EventTypes.CASHBOX_UPDATED, data, true);
      console.log(`[CASHBOX-EVENT] تم إرسال إشعار CASHBOX_UPDATED بنجاح`);
    } catch (sendEventError) {
      console.error(`[CASHBOX-EVENT] خطأ في إرسال إشعار CASHBOX_UPDATED:`, sendEventError);
      logError(sendEventError, 'notifyCashboxUpdated - sendEvent CASHBOX_UPDATED');
      // لا نريد إيقاف العملية إذا فشل إرسال الإشعار
    }

    // إرسال إشعار بالحاجة لتحديث الخزينة (مع تجاهل التخزين المؤقت)
    try {
      console.log(`[CASHBOX-EVENT] إرسال إشعار REFRESH_NEEDED للخزينة...`);

      const refreshData = {
        target: 'cashbox',
        timestamp: new Date().toISOString(),
        transaction_type: data.transaction_type || 'unknown',
        amount: data.amount || 0,
        force_refresh: true,
        // إضافة بيانات الخزينة المحدثة
        current_balance: data.current_balance,
        sales_total: data.sales_total,
        purchases_total: data.purchases_total,
        returns_total: data.returns_total,
        profit_total: data.profit_total
      };

      sendEvent(EventTypes.REFRESH_NEEDED, refreshData, true);
      console.log(`[CASHBOX-EVENT] تم إرسال إشعار REFRESH_NEEDED للخزينة بنجاح`);
    } catch (sendRefreshError) {
      console.error(`[CASHBOX-EVENT] خطأ في إرسال إشعار REFRESH_NEEDED للخزينة:`, sendRefreshError);
      logError(sendRefreshError, 'notifyCashboxUpdated - sendEvent REFRESH_NEEDED');
      // لا نريد إيقاف العملية إذا فشل إرسال الإشعار
    }

    // إرسال إشعار فوري واحد فقط للتحديث
    setTimeout(() => {
      console.log(`[INSTANT-CASHBOX] إرسال إشعار فوري بتحديث الخزينة`);
      sendEvent(EventTypes.REFRESH_NEEDED, {
        target: 'cashbox',
        operation: 'instant-update',
        timestamp: new Date().toISOString(),
        force_refresh: true,
        instant: true,
        // إضافة بيانات الخزينة المحدثة
        id: data.id,
        initial_balance: data.initial_balance,
        current_balance: data.current_balance,
        sales_total: data.sales_total,
        purchases_total: data.purchases_total,
        returns_total: data.returns_total,
        transport_total: data.transport_total,
        profit_total: data.profit_total
      }, true);
    }, 50); // تأخير قصير جداً فقط

    // إرسال إشعار مباشر إلى واجهة المستخدم عبر IPC
    try {
      if (global.mainWindow && global.mainWindow.webContents) {
        console.log(`[CASHBOX-EVENT] إرسال إشعار مباشر إلى واجهة المستخدم عبر IPC...`);

        const directUpdateData = {
          ...data,
          timestamp: new Date().toISOString(),
          force_refresh: true
        };

        global.mainWindow.webContents.send('direct-cashbox-update', directUpdateData);

        // إرسال إشعار خاص لتحديث واجهة المستخدم
        global.mainWindow.webContents.send('cashbox-updated-ui', directUpdateData);

        console.log(`[CASHBOX-EVENT] تم إرسال إشعار مباشر إلى واجهة المستخدم بنجاح`);
      } else {
        console.warn(`[CASHBOX-EVENT] تحذير: لا يمكن إرسال إشعار مباشر لأن النافذة الرئيسية غير متوفرة`);
      }
    } catch (directUpdateError) {
      console.error(`[CASHBOX-EVENT] خطأ في إرسال إشعار مباشر إلى واجهة المستخدم:`, directUpdateError);
      logError(directUpdateError, 'notifyCashboxUpdated - direct update');
      // لا نريد إيقاف العملية إذا فشل إرسال الإشعار المباشر
    }
  } catch (error) {
    console.error(`[CASHBOX-FIX] خطأ في إرسال إشعار تحديث الخزينة:`, error);
    logError(error, 'notifyCashboxUpdated');
  }
}

/**
 * إرسال إشعار بإضافة عملية إرجاع جديدة
 * @param {Object} returnTransaction - بيانات عملية الإرجاع الجديدة
 */
function notifyReturnTransactionAdded(returnTransaction) {
  // استخدام نفس حدث إضافة المعاملة لأن الإرجاع هو نوع من المعاملات
  sendEvent(EventTypes.TRANSACTION_ADDED, {
    ...returnTransaction,
    transaction_type: 'return'
  });

  // تحديث المخزون لأن عمليات الإرجاع تؤثر على المخزون (مع تجاهل التخزين المؤقت)
  sendEvent(EventTypes.REFRESH_NEEDED, { target: 'inventory' }, true);

  // تحديث المعاملات
  sendEvent(EventTypes.REFRESH_NEEDED, { target: 'transactions' });
}

/**
 * إرسال إشعار بتحديث الأرباح - محسن للتحديث التلقائي الفوري
 * @param {Object} data - بيانات تحديث الأرباح
 */
function notifyProfitsUpdated(data = {}) {
  try {
    console.log(`[AUTO-PROFITS-EVENT] إرسال إشعار تحديث الأرباح التلقائي:`, data);
    logSystem(`[AUTO-PROFITS-EVENT] إرسال إشعار تحديث الأرباح التلقائي: ${JSON.stringify(data)}`, 'info');

    // إرسال إشعار بتحديث الأرباح فورياً (مع تجاهل التخزين المؤقت)
    sendEvent(EventTypes.PROFITS_UPDATED, {
      ...data,
      auto_update: true,
      immediate: true,
      timestamp: new Date().toISOString()
    }, true);

    // إرسال إشعار بالحاجة لتحديث التقارير فورياً (مع تجاهل التخزين المؤقت)
    sendEvent(EventTypes.REFRESH_NEEDED, {
      target: 'reports',
      operation: 'auto-update-profits',
      timestamp: new Date().toISOString(),
      force_refresh: true,
      immediate: true,
      auto_update: true,
      ...data
    }, true);

    // إرسال إشعار إضافي بعد فترة قصيرة للتأكد من تحديث واجهة المستخدم
    setTimeout(() => {
      console.log(`[PROFITS-EVENT] إرسال إشعار إضافي بتحديث الأرباح بعد تأخير قصير (500ms)`);
      sendEvent(EventTypes.REFRESH_NEEDED, {
        target: 'reports',
        operation: 'delayed-update-profits-500ms',
        timestamp: new Date().toISOString(),
        force_refresh: true,
        ...data
      }, true);
    }, 500);

    // إرسال إشعار مباشر إلى واجهة المستخدم عبر IPC
    try {
      if (global.mainWindow && global.mainWindow.webContents) {
        console.log(`[PROFITS-EVENT] إرسال إشعار مباشر إلى واجهة المستخدم عبر IPC...`);

        const directUpdateData = {
          ...data,
          timestamp: new Date().toISOString(),
          force_refresh: true
        };

        global.mainWindow.webContents.send('direct-profits-update', directUpdateData);
        console.log(`[PROFITS-EVENT] تم إرسال إشعار مباشر إلى واجهة المستخدم بنجاح`);
      }
    } catch (directUpdateError) {
      console.error(`[PROFITS-EVENT] خطأ في إرسال إشعار مباشر إلى واجهة المستخدم:`, directUpdateError);
      logError(directUpdateError, 'notifyProfitsUpdated - direct update');
    }
  } catch (error) {
    console.error(`[PROFITS-EVENT] خطأ في إرسال إشعار تحديث الأرباح:`, error);
    logError(error, 'notifyProfitsUpdated');
  }
}

// تصدير الدوال والثوابت
module.exports = {
  EventTypes,
  sendEvent,
  registerHandler,
  notifyInventoryUpdated,
  notifyItemAdded,
  notifyItemUpdated,
  notifyItemDeleted,
  notifyTransactionAdded,
  notifyCustomerAdded,
  notifyCustomerUpdated,
  notifyCashboxUpdated,
  notifyReturnTransactionAdded,
  notifyProfitsUpdated
};
