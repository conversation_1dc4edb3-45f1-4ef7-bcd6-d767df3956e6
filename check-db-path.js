/**
 * فحص مسار قاعدة البيانات
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 فحص مسار قاعدة البيانات');
console.log('=' .repeat(40));

// المسارات المحتملة
const possiblePaths = [
  'C:\\Users\\<USER>\\AppData\\Roaming\\warehouse-management-system\\wms-database',
  'C:\\Users\\<USER>\\AppData\\Roaming\\warehouse-management-system\\wms-database.db',
  'C:\\Users\\<USER>\\AppData\\Roaming\\warehouse-management-system\\wms-database.sqlite',
  'C:\\Users\\<USER>\\AppData\\Roaming\\warehouse-management-system\\wms-database.sqlite3',
  'C:\\Users\\<USER>\\AppData\\Roaming\\warehouse-management-system\\database.db',
  'C:\\Users\\<USER>\\AppData\\Roaming\\warehouse-management-system\\database.sqlite'
];

console.log('📂 فحص المسارات المحتملة:');

possiblePaths.forEach((dbPath, index) => {
  try {
    if (fs.existsSync(dbPath)) {
      const stats = fs.statSync(dbPath);
      console.log(`✅ ${index + 1}. موجود: ${dbPath}`);
      console.log(`   الحجم: ${stats.size} بايت`);
      console.log(`   آخر تعديل: ${stats.mtime}`);
    } else {
      console.log(`❌ ${index + 1}. غير موجود: ${dbPath}`);
    }
  } catch (error) {
    console.log(`❌ ${index + 1}. خطأ في فحص: ${dbPath} - ${error.message}`);
  }
});

// فحص المجلد الأساسي
const baseDir = 'C:\\Users\\<USER>\\AppData\\Roaming\\warehouse-management-system';
console.log(`\n📁 فحص محتويات المجلد: ${baseDir}`);

try {
  if (fs.existsSync(baseDir)) {
    const files = fs.readdirSync(baseDir);
    console.log('📋 الملفات الموجودة:');
    files.forEach((file, index) => {
      const filePath = path.join(baseDir, file);
      const stats = fs.statSync(filePath);
      console.log(`   ${index + 1}. ${file} (${stats.size} بايت)`);
    });
  } else {
    console.log('❌ المجلد غير موجود');
  }
} catch (error) {
  console.log(`❌ خطأ في فحص المجلد: ${error.message}`);
}

// فحص بنية قاعدة البيانات
console.log('\n🔍 فحص بنية قاعدة البيانات...');
const sqlite3 = require('sqlite3').verbose();
const dbPath = 'C:\\Users\\<USER>\\AppData\\Roaming\\warehouse-management-system\\wms-database.db';

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ خطأ في الاتصال بقاعدة البيانات:', err.message);
    return;
  }

  console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

  // الحصول على قائمة الجداول
  db.all("SELECT name FROM sqlite_master WHERE type='table'", (err, tables) => {
    if (err) {
      console.error('❌ خطأ في الحصول على قائمة الجداول:', err.message);
      db.close();
      return;
    }

    console.log('\n📋 الجداول الموجودة في قاعدة البيانات:');
    tables.forEach((table, index) => {
      console.log(`   ${index + 1}. ${table.name}`);
    });

    // البحث عن جدول الخزينة أو جداول مالية
    const financialTables = tables.filter(table =>
      table.name.toLowerCase().includes('cashbox') ||
      table.name.toLowerCase().includes('cash') ||
      table.name.toLowerCase().includes('خزينة') ||
      table.name.toLowerCase().includes('financial') ||
      table.name.toLowerCase().includes('profit')
    );

    if (financialTables.length > 0) {
      console.log('\n💰 جداول مالية موجودة:');
      financialTables.forEach(table => {
        console.log(`   - ${table.name}`);
      });
    } else {
      console.log('\n❌ لم يتم العثور على جداول مالية');
    }

    db.close();
  });
});

console.log('\n💡 ملاحظة: تأكد من أن التطبيق قد تم تشغيله مرة واحدة على الأقل لإنشاء قاعدة البيانات');
