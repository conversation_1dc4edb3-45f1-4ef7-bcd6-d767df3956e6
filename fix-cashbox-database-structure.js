/**
 * إصلاح بنية قاعدة البيانات للخزينة
 * 
 * هذا السكريبت يقوم بإصلاح بنية جدول الخزينة وإضافة الحقول المفقودة
 */

const path = require('path');
const Database = require('better-sqlite3');

// مسار قاعدة البيانات
const dbPath = path.join(process.env.APPDATA || process.env.HOME, 'warehouse-management-system', 'wms-database', 'warehouse.db');

console.log('🔧 بدء إصلاح بنية قاعدة البيانات للخزينة...');
console.log(`📁 مسار قاعدة البيانات: ${dbPath}`);

try {
  // فتح قاعدة البيانات
  const db = new Database(dbPath);
  console.log('✅ تم فتح قاعدة البيانات بنجاح');

  // التحقق من وجود جدول الخزينة
  const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='cashbox'").get();
  
  if (!tableExists) {
    console.log('❌ جدول الخزينة غير موجود');
    process.exit(1);
  }

  console.log('✅ جدول الخزينة موجود');

  // الحصول على معلومات الجدول الحالي
  const tableInfo = db.prepare("PRAGMA table_info(cashbox)").all();
  const existingColumns = tableInfo.map(col => col.name);
  
  console.log('📋 الأعمدة الموجودة حالياً:', existingColumns);

  // قائمة الأعمدة المطلوبة
  const requiredColumns = [
    { name: 'id', type: 'INTEGER PRIMARY KEY AUTOINCREMENT', required: true },
    { name: 'initial_balance', type: 'REAL NOT NULL', required: true },
    { name: 'current_balance', type: 'REAL NOT NULL', required: true },
    { name: 'profit_total', type: 'REAL', default: '0' },
    { name: 'sales_total', type: 'REAL', default: '0' },
    { name: 'purchases_total', type: 'REAL', default: '0' },
    { name: 'returns_total', type: 'REAL', default: '0' },
    { name: 'transport_total', type: 'REAL', default: '0' },
    { name: 'created_at', type: 'DATETIME', default: 'CURRENT_TIMESTAMP' },
    { name: 'updated_at', type: 'DATETIME', default: 'CURRENT_TIMESTAMP' }
  ];

  console.log('🔍 التحقق من الأعمدة المطلوبة...');

  let columnsAdded = 0;
  let hasTransportColumn = false;

  // إضافة الأعمدة المفقودة
  for (const column of requiredColumns) {
    if (!existingColumns.includes(column.name)) {
      if (column.required) {
        console.log(`❌ العمود المطلوب ${column.name} مفقود ولا يمكن إضافته تلقائياً`);
        continue;
      }

      try {
        console.log(`➕ إضافة العمود المفقود: ${column.name}`);
        const alterQuery = `ALTER TABLE cashbox ADD COLUMN ${column.name} ${column.type} DEFAULT ${column.default}`;
        db.prepare(alterQuery).run();
        console.log(`✅ تم إضافة العمود: ${column.name}`);
        columnsAdded++;
        
        if (column.name === 'transport_total') {
          hasTransportColumn = true;
        }
      } catch (error) {
        console.error(`❌ خطأ في إضافة العمود ${column.name}:`, error.message);
      }
    } else {
      console.log(`✅ العمود ${column.name} موجود`);
      if (column.name === 'transport_total') {
        hasTransportColumn = true;
      }
    }
  }

  // التحقق من وجود بيانات في الخزينة
  console.log('📊 فحص بيانات الخزينة...');
  const cashboxData = db.prepare('SELECT * FROM cashbox LIMIT 1').get();
  
  if (cashboxData) {
    console.log('✅ تم العثور على بيانات الخزينة:', {
      id: cashboxData.id,
      initial_balance: cashboxData.initial_balance,
      current_balance: cashboxData.current_balance,
      transport_total: cashboxData.transport_total || 'غير محدد'
    });

    // تحديث القيم الافتراضية للأعمدة الجديدة إذا كانت NULL
    if (columnsAdded > 0) {
      console.log('🔄 تحديث القيم الافتراضية...');

      const updateQuery = `
        UPDATE cashbox
        SET
          profit_total = COALESCE(profit_total, 0),
          sales_total = COALESCE(sales_total, 0),
          purchases_total = COALESCE(purchases_total, 0),
          returns_total = COALESCE(returns_total, 0),
          transport_total = COALESCE(transport_total, 0),
          updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;

      const updateResult = db.prepare(updateQuery).run(cashboxData.id);
      console.log(`✅ تم تحديث ${updateResult.changes} سجل`);
    }
  } else {
    console.log('⚠️ لا توجد بيانات في جدول الخزينة');
  }

  // اختبار تحديث عمود transport_total
  if (hasTransportColumn && cashboxData) {
    console.log('🧪 اختبار تحديث عمود transport_total...');
    
    try {
      const testUpdateQuery = `
        UPDATE cashbox 
        SET transport_total = COALESCE(transport_total, 0),
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;
      
      const testResult = db.prepare(testUpdateQuery).run(cashboxData.id);
      console.log(`✅ اختبار تحديث transport_total نجح، عدد الصفوف المتأثرة: ${testResult.changes}`);
    } catch (testError) {
      console.error('❌ فشل اختبار تحديث transport_total:', testError.message);
      return {
        success: false,
        message: `فشل اختبار تحديث transport_total: ${testError.message}`
      };
    }
  }

  // عرض بنية الجدول النهائية
  console.log('📋 بنية الجدول النهائية:');
  const finalTableInfo = db.prepare("PRAGMA table_info(cashbox)").all();
  finalTableInfo.forEach(col => {
    console.log(`   - ${col.name}: ${col.type} ${col.notnull ? 'NOT NULL' : ''} ${col.dflt_value ? `DEFAULT ${col.dflt_value}` : ''}`);
  });

  // إغلاق قاعدة البيانات
  db.close();

  console.log('✅ تم إكمال إصلاح بنية جدول الخزينة بنجاح');
  console.log(`📊 تم إضافة ${columnsAdded} عمود جديد`);
  console.log('💡 يمكنك الآن تحديث الرصيد الافتتاحي من واجهة التطبيق');

} catch (error) {
  console.error('❌ خطأ في إصلاح بنية جدول الخزينة:', error);
  process.exit(1);
}
