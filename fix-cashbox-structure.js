/**
 * إصلاح شامل لبنية جدول الخزينة
 *
 * هذا الملف يقوم بإصلاح مشكلة تحديث الرصيد الافتتاحي عن طريق:
 * 1. التحقق من بنية جدول الخزينة
 * 2. إضافة الأعمدة المفقودة
 * 3. تحديث القيم الافتراضية
 * 4. اختبار تحديث الرصيد الافتتاحي
 */

// استخدام نفس قاعدة البيانات المستخدمة في التطبيق
const DatabaseManager = require('./database-singleton');

console.log('🔧 بدء إصلاح بنية جدول الخزينة...');

try {
  // الحصول على قاعدة البيانات
  const dbManager = DatabaseManager.getInstance();
  const db = dbManager.getConnection();

  if (!db) {
    console.log('❌ قاعدة البيانات غير مهيأة. يرجى تشغيل التطبيق أولاً.');
    process.exit(1);
  }

  console.log('✅ تم فتح قاعدة البيانات بنجاح');

  // التحقق من وجود جدول الخزينة
  const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='cashbox'").get();

  if (!tableExists) {
    console.log('❌ جدول الخزينة غير موجود');
    process.exit(1);
  }

  console.log('✅ جدول الخزينة موجود');

  // الحصول على معلومات الجدول الحالي
  const tableInfo = db.prepare("PRAGMA table_info(cashbox)").all();
  const existingColumns = tableInfo.map(col => col.name);

  console.log('📋 الأعمدة الموجودة حالياً:', existingColumns);

  // قائمة الأعمدة المطلوبة
  const requiredColumns = [
    { name: 'id', type: 'INTEGER PRIMARY KEY AUTOINCREMENT', required: true },
    { name: 'initial_balance', type: 'REAL NOT NULL', required: true },
    { name: 'current_balance', type: 'REAL NOT NULL', required: true },
    { name: 'profit_total', type: 'REAL', default: '0' },
    { name: 'sales_total', type: 'REAL', default: '0' },
    { name: 'purchases_total', type: 'REAL', default: '0' },
    { name: 'returns_total', type: 'REAL', default: '0' },
    { name: 'transport_total', type: 'REAL', default: '0' },
    { name: 'created_at', type: 'DATETIME', default: 'CURRENT_TIMESTAMP' },
    { name: 'updated_at', type: 'DATETIME', default: 'CURRENT_TIMESTAMP' }
  ];

  console.log('🔍 التحقق من الأعمدة المطلوبة...');

  let columnsAdded = 0;

  // إضافة الأعمدة المفقودة
  for (const column of requiredColumns) {
    if (!existingColumns.includes(column.name)) {
      if (column.required) {
        console.log(`❌ العمود المطلوب ${column.name} مفقود ولا يمكن إضافته تلقائياً`);
        continue;
      }

      try {
        console.log(`➕ إضافة العمود المفقود: ${column.name}`);
        const alterQuery = `ALTER TABLE cashbox ADD COLUMN ${column.name} ${column.type} DEFAULT ${column.default}`;
        db.prepare(alterQuery).run();
        console.log(`✅ تم إضافة العمود: ${column.name}`);
        columnsAdded++;
      } catch (error) {
        console.error(`❌ خطأ في إضافة العمود ${column.name}:`, error.message);
      }
    } else {
      console.log(`✅ العمود ${column.name} موجود`);
    }
  }

  console.log(`📊 تم إضافة ${columnsAdded} عمود جديد`);

  // التحقق من وجود بيانات في الخزينة
  const cashboxData = db.prepare('SELECT * FROM cashbox LIMIT 1').get();

  if (!cashboxData) {
    console.log('⚠️  لا توجد بيانات في جدول الخزينة');
    console.log('💡 يمكنك إنشاء خزينة جديدة من واجهة التطبيق');
  } else {
    console.log('✅ تم العثور على بيانات الخزينة:');
    console.log('   - الرصيد الافتتاحي:', cashboxData.initial_balance || 0);
    console.log('   - الرصيد الحالي:', cashboxData.current_balance || 0);
    console.log('   - إجمالي الأرباح:', cashboxData.profit_total || 0);
    console.log('   - إجمالي المبيعات:', cashboxData.sales_total || 0);
    console.log('   - إجمالي المشتريات:', cashboxData.purchases_total || 0);
    console.log('   - إجمالي المرتجعات:', cashboxData.returns_total || 0);
    console.log('   - إجمالي مصاريف النقل:', cashboxData.transport_total || 0);

    // تحديث القيم الافتراضية للأعمدة الجديدة إذا كانت NULL
    console.log('🔄 تحديث القيم الافتراضية...');

    const updateQuery = `
      UPDATE cashbox
      SET
        profit_total = COALESCE(profit_total, 0),
        sales_total = COALESCE(sales_total, 0),
        purchases_total = COALESCE(purchases_total, 0),
        returns_total = COALESCE(returns_total, 0),
        transport_total = COALESCE(transport_total, 0),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;

    const updateResult = db.prepare(updateQuery).run(cashboxData.id);
    console.log(`✅ تم تحديث ${updateResult.changes} سجل`);

    // اختبار تحديث الرصيد الافتتاحي
    console.log('🧪 اختبار تحديث الرصيد الافتتاحي...');

    try {
      const testInitialBalance = 1000;
      const updateInitialBalanceQuery = `
        UPDATE cashbox
        SET
          initial_balance = ?,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;

      const testResult = db.prepare(updateInitialBalanceQuery).run(testInitialBalance, cashboxData.id);

      if (testResult.changes > 0) {
        console.log('✅ اختبار تحديث الرصيد الافتتاحي نجح');

        // إعادة الرصيد الافتتاحي إلى قيمته الأصلية
        db.prepare(updateInitialBalanceQuery).run(cashboxData.initial_balance, cashboxData.id);
        console.log('✅ تم إعادة الرصيد الافتتاحي إلى قيمته الأصلية');
      } else {
        console.log('❌ فشل اختبار تحديث الرصيد الافتتاحي');
      }
    } catch (testError) {
      console.error('❌ خطأ في اختبار تحديث الرصيد الافتتاحي:', testError.message);
    }
  }

  // عرض بنية الجدول النهائية
  console.log('📋 بنية الجدول النهائية:');
  const finalTableInfo = db.prepare("PRAGMA table_info(cashbox)").all();
  finalTableInfo.forEach(col => {
    console.log(`   - ${col.name}: ${col.type} ${col.notnull ? 'NOT NULL' : ''} ${col.dflt_value ? `DEFAULT ${col.dflt_value}` : ''}`);
  });

  console.log('✅ تم إكمال إصلاح بنية جدول الخزينة بنجاح');
  console.log('💡 يمكنك الآن تحديث الرصيد الافتتاحي من واجهة التطبيق');

} catch (error) {
  console.error('❌ خطأ في إصلاح بنية جدول الخزينة:', error);
  process.exit(1);
}
