import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useApp } from '../context/AppContext';

const Login = ({ onLogin }) => {
  const { settings } = useApp();
  // Variable no utilizada - considere eliminarla o usarla
  const [username, setUsername] = useState('');
  // Variable no utilizada - considere eliminarla o usarla
  const [password, setPassword] = useState('');
  // Variable no utilizada - considere eliminarla o usarla
  const [error, setError] = useState('');
  // Variable no utilizada - considere eliminarla o usarla
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // مرجع لتتبع ما إذا كان المكون لا يزال مثبتًا
  const isMounted = useRef(true);

  // مرجع للنموذج
  const formRef = useRef(null);

  // إعادة تعيين حقول الإدخال عند تحميل صفحة تسجيل الدخول
  useEffect(() => {
    // تعيين isMounted إلى true عند تحميل المكون
    isMounted.current = true;

    // إعادة تعيين قيم حقول الإدخال
    setUsername('');
    setPassword('');
    setError('');
    setLoading(false);

    // تنظيف عند إلغاء تحميل المكون
    return () => {
      isMounted.current = false;
    };
  }, []);

  // وظيفة لإعادة تمكين حقول النموذج
  const enableFormFields = () => {
    if (formRef.current && isMounted.current) {
      // الحصول على جميع حقول الإدخال في النموذج
      const inputs = formRef.current.querySelectorAll('input');

      // إعادة تمكين جميع الحقول
      inputs.forEach(input => {
        input.disabled = false;
      });

      // تركيز حقل اسم المستخدم
      const usernameInput = formRef.current.querySelector('input[name="username"]');
      if (usernameInput) {
        usernameInput.focus();
      }
    }
  };

  // استدعاء وظيفة إعادة تمكين الحقول عند تحميل الصفحة أو تغيير المسار
  useEffect(() => {
    const timer = setTimeout(() => {
      enableFormFields();
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  // إعادة تمكين الحقول عند تغيير المسار
  useEffect(() => {
    // تأخير قصير لضمان تهيئة الحقول بشكل صحيح
    const timer = setTimeout(() => {
      enableFormFields();
    }, 100);

    return () => clearTimeout(timer);
  }, [location.key]);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!username || !password) {
      if (isMounted.current) {
        setError('الرجاء إدخال اسم المستخدم وكلمة المرور');
      }
      return;
    }

    try {
      if (isMounted.current) {
        setError('');
        setLoading(true);
      }
      console.log('Attempting login with:', username);

      // التحقق من المستخدمين الافتراضيين
      // مدير النظام
      if (username === 'admin' && password === 'admin') {
        console.log('Using default admin login');

        // إنشاء كائن المستخدم الافتراضي
        const defaultUser = {
          id: 1,
          username: 'admin',
          full_name: 'مدير النظام',
          role: 'admin'
        };

        // تخزين معلومات المستخدم في التخزين المحلي
        localStorage.setItem('wms_user', JSON.stringify(defaultUser));

        // تخزين معرف المستخدم ودوره في التخزين المحلي للاستخدام في التحقق من الصلاحيات
        localStorage.setItem('currentUserId', defaultUser.id);
        localStorage.setItem('currentUserRole', defaultUser.role);
        localStorage.setItem('currentUserName', defaultUser.username);

        console.log('تم تخزين دور المستخدم في localStorage:', localStorage.getItem('currentUserRole'));

        console.log('تم تسجيل الدخول بالمستخدم الافتراضي:', {
          id: defaultUser.id,
          username: defaultUser.username,
          role: defaultUser.role
        });

        // إعادة تعيين حقول الإدخال
        if (isMounted.current) {
          setUsername('');
          setPassword('');
          setLoading(false);
        }

        // تسجيل الدخول باستخدام المستخدم الافتراضي
        onLogin(defaultUser);
        return;
      }

      // موظف
      if (username === 'employee' && password === 'employee') {
        console.log('Using default employee login');

        // إنشاء كائن المستخدم الافتراضي
        const defaultUser = {
          id: 2,
          username: 'employee',
          full_name: 'موظف',
          role: 'employee'
        };

        // تخزين معلومات المستخدم في التخزين المحلي
        localStorage.setItem('wms_user', JSON.stringify(defaultUser));

        // تخزين معرف المستخدم ودوره في التخزين المحلي للاستخدام في التحقق من الصلاحيات
        localStorage.setItem('currentUserId', defaultUser.id);
        localStorage.setItem('currentUserRole', defaultUser.role);
        localStorage.setItem('currentUserName', defaultUser.username);

        console.log('تم تسجيل الدخول بالمستخدم الافتراضي:', {
          id: defaultUser.id,
          username: defaultUser.username,
          role: defaultUser.role
        });

        // إعادة تعيين حقول الإدخال
        if (isMounted.current) {
          setUsername('');
          setPassword('');
          setLoading(false);
        }

        // تسجيل الدخول باستخدام المستخدم الافتراضي
        onLogin(defaultUser);
        return;
      }

      // مدير
      if (username === 'manager' && password === 'manager') {
        console.log('Using default manager login');

        // إنشاء كائن المستخدم الافتراضي
        const defaultUser = {
          id: 3,
          username: 'manager',
          full_name: 'مدير',
          role: 'manager'
        };

        // تخزين معلومات المستخدم في التخزين المحلي
        localStorage.setItem('wms_user', JSON.stringify(defaultUser));

        // تخزين معرف المستخدم ودوره في التخزين المحلي للاستخدام في التحقق من الصلاحيات
        localStorage.setItem('currentUserId', defaultUser.id);
        localStorage.setItem('currentUserRole', defaultUser.role);
        localStorage.setItem('currentUserName', defaultUser.username);

        console.log('تم تخزين دور المستخدم في localStorage:', localStorage.getItem('currentUserRole'));

        console.log('تم تسجيل الدخول بالمستخدم الافتراضي:', {
          id: defaultUser.id,
          username: defaultUser.username,
          role: defaultUser.role
        });

        // إعادة تعيين حقول الإدخال
        if (isMounted.current) {
          setUsername('');
          setPassword('');
          setLoading(false);
        }

        // تسجيل الدخول باستخدام المستخدم الافتراضي
        onLogin(defaultUser);
        return;
      }

      // مشاهد
      if (username === 'viewer' && password === 'viewer') {
        console.log('Using default viewer login');

        // إنشاء كائن المستخدم الافتراضي
        const defaultUser = {
          id: 4,
          username: 'viewer',
          full_name: 'مشاهد',
          role: 'viewer'
        };

        // تخزين معلومات المستخدم في التخزين المحلي
        localStorage.setItem('wms_user', JSON.stringify(defaultUser));

        // تخزين معرف المستخدم ودوره في التخزين المحلي للاستخدام في التحقق من الصلاحيات
        localStorage.setItem('currentUserId', defaultUser.id);
        localStorage.setItem('currentUserRole', defaultUser.role);
        localStorage.setItem('currentUserName', defaultUser.username);

        console.log('تم تسجيل الدخول بالمستخدم الافتراضي:', {
          id: defaultUser.id,
          username: defaultUser.username,
          role: defaultUser.role
        });

        // إعادة تعيين حقول الإدخال
        if (isMounted.current) {
          setUsername('');
          setPassword('');
          setLoading(false);
        }

        // تسجيل الدخول باستخدام المستخدم الافتراضي
        onLogin(defaultUser);
        return;
      }

      // محاولة تسجيل الدخول باستخدام واجهة API
      try {
        const result = await window.api.invoke('login', { username, password });
        console.log('Login result:', result);

        if (result && result.success) {
          // إعادة تعيين حقول الإدخال
          if (isMounted.current) {
            setUsername('');
            setPassword('');
            setLoading(false);
          }

          onLogin(result.user);
        } else {
          // التحقق مما إذا كان المكون لا يزال مثبتًا قبل تحديث الحالة
          if (isMounted.current) {
            setError(result?.message || 'فشل تسجيل الدخول');
          }
        }
      } catch (apiError) {
        console.error('API login error:', apiError);
        // التحقق مما إذا كان المكون لا يزال مثبتًا قبل تحديث الحالة
        if (isMounted.current) {
          setError('حدث خطأ أثناء تسجيل الدخول');
        }
      }
    } catch (err) {
      console.error('Login error:', err);
      // التحقق مما إذا كان المكون لا يزال مثبتًا قبل تحديث الحالة
      if (isMounted.current) {
        setError('حدث خطأ أثناء تسجيل الدخول');
      }
    } finally {
      // التحقق مما إذا كان المكون لا يزال مثبتًا قبل تحديث الحالة
      if (isMounted.current) {
        setLoading(false);
      }
    }
  };

  return (
    <div className="login-container">
      <div className="login-card">
        <div className="login-header">
          <div className="login-logo" style={{
            width: '100px',
            height: '100px',
            borderRadius: '50%',
            backgroundColor: settings.logoUrl ? 'transparent' : '#3498db',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            overflow: 'hidden',
            margin: '0 auto 16px auto'
          }}>
            {settings.logoUrl ? (
              <img
                src={settings.logoUrl}
                alt="شعار المنظومة"
                style={{
                  maxWidth: '100%',
                  maxHeight: '100%',
                  objectFit: 'contain'
                }}
              />
            ) : (
              <span style={{
                fontSize: '2rem',
                fontWeight: 'bold',
                color: 'white'
              }}>WMS</span>
            )}
          </div>
          <h2 className="login-title">{settings.systemName || 'نظام إدارة المخازن'}</h2>
          <p className="login-subtitle">قم بتسجيل الدخول للوصول إلى لوحة التحكم</p>
        </div>

        <div className="login-body">
          {error && (
            <div className="alert alert-danger">
              {error}
            </div>
          )}

          <form ref={formRef} onSubmit={handleSubmit}>
            <div className="form-group">
              <label className="form-label">اسم المستخدم</label>
              <input
                type="text"
                name="username"
                className="form-control"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                disabled={loading}
                placeholder="أدخل اسم المستخدم"
                autoComplete="username"
              />
            </div>

            <div className="form-group">
              <label className="form-label">كلمة المرور</label>
              <input
                type="password"
                name="password"
                className="form-control"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                disabled={loading}
                placeholder="أدخل كلمة المرور"
                autoComplete="current-password"
              />
            </div>

            <button
              type="submit"
              className="btn btn-primary"
              disabled={loading}
              style={{
                width: '100%',
                marginTop: '24px',
                padding: '12px',
                fontSize: '1rem',
                fontWeight: 'bold',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px'
              }}
            >
              {loading ? (
                <span className="spinner" style={{
                  width: '1.5rem',
                  height: '1.5rem',
                  border: '2px solid rgba(255, 255, 255, 0.3)',
                  borderTop: '2px solid white',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite'
                }}></span>
              ) : (
                'تسجيل الدخول'
              )}
            </button>
          </form>
        </div>

        <div className="login-footer">
          <div style={{ marginBottom: '10px' }}>
            <p style={{ fontWeight: 'bold', marginBottom: '5px' }}>المستخدمين الافتراضيين:</p>
            <p>مسؤول النظام: <strong>admin / admin</strong></p>
            <p>مدير: <strong>manager / manager</strong></p>
            <p>موظف: <strong>employee / employee</strong></p>
            <p>مشاهد: <strong>viewer / viewer</strong></p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
