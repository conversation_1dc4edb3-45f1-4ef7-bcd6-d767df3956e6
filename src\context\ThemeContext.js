import React, { createContext, useContext } from 'react';

const ThemeContext = createContext();

export const useTheme = () => useContext(ThemeContext);

export const ThemeProvider = ({ children }) => {
  // تم إزالة وظيفة الوضع الليلي/النهاري بناءً على طلب المستخدم
  // وتم الاعتماد على ملف modern-theme.css فقط

  const value = {
    // قيم فارغة للحفاظ على التوافق مع الكود الحالي
    darkMode: false,
    toggleDarkMode: () => {}
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
