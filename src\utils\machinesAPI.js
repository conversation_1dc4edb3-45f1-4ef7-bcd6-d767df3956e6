// وظائف التعامل مع الآلات والمعدات

// استيراد وظائف قاعدة البيانات
let ipcRenderer;

// التحقق من وجود window
if (typeof window !== 'undefined') {
  try {
    const electron = window.require('electron');
    ipcRenderer = electron.ipcRenderer;
  } catch (error) {
    console.error('خطأ في استيراد electron:', error);
  }
}

// التحقق من وجود window.api
const checkApiAvailability = () => {
  if (typeof window === 'undefined') {
    console.error('window غير متوفر');
    return false;
  }

  if (!window.api) {
    console.error('window.api غير متوفر');
    return false;
  }

  return true;
};

/**
 * الحصول على جميع الآلات
 * @returns {Promise<Array>} مصفوفة من الآلات
 */
export const getAllMachines = async () => {
  try {
    console.log('جاري الحصول على جميع الآلات...');

    // محاولة استخدام ipcRenderer أولاً
    if (ipcRenderer) {
      try {
        const machines = await ipcRenderer.invoke('get-machines');
        console.log('تم الحصول على الآلات بنجاح (ipcRenderer):', machines);

        if (Array.isArray(machines)) {
          // التأكد من أن القيم العددية هي أرقام
          return machines.map(machine => ({
            ...machine,
            id: machine._id || machine.id,
            purchase_price: Number(machine.purchase_price || 0),
            current_value: Number(machine.current_value || 0)
          }));
        }
      } catch (ipcError) {
        console.error('خطأ في استخدام ipcRenderer:', ipcError);
      }
    }

    // محاولة استخدام window.api كبديل
    if (checkApiAvailability()) {
      try {
        const machines = await window.api.invoke('get-machines');
        console.log('تم الحصول على الآلات بنجاح (window.api):', machines);

        if (Array.isArray(machines)) {
          // التأكد من أن القيم العددية هي أرقام
          return machines.map(machine => ({
            ...machine,
            id: machine._id || machine.id,
            purchase_price: Number(machine.purchase_price || 0),
            current_value: Number(machine.current_value || 0)
          }));
        } else {
          console.error('بيانات الآلات ليست مصفوفة:', machines);
          return [];
        }
      } catch (apiError) {
        console.error('خطأ في استخدام window.api:', apiError);
      }
    }

    console.error('لا يمكن الوصول إلى قاعدة البيانات');
    return [];
  } catch (error) {
    console.error('خطأ عام في الحصول على الآلات:', error);
    return [];
  }
};

/**
 * إضافة آلة جديدة
 * @param {Object} machine بيانات الآلة
 * @returns {Promise<Object>} نتيجة الإضافة
 */
export const addMachine = async (machine) => {
  try {
    console.log('جاري إضافة آلة جديدة:', machine);

    // تنظيف البيانات
    const cleanedMachine = {
      name: String(machine.name || '').trim(),
      purchase_date: String(machine.purchase_date || new Date().toISOString().split('T')[0]),
      purchase_price: Number(machine.purchase_price || 0),
      current_value: Number(machine.current_value || 0),
      notes: String(machine.notes || '').trim(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    console.log('بيانات الآلة بعد التنظيف:', cleanedMachine);

    // محاولة استخدام ipcRenderer أولاً
    if (ipcRenderer) {
      try {
        const result = await ipcRenderer.invoke('add-machine', cleanedMachine);
        console.log('تمت إضافة الآلة بنجاح (ipcRenderer):', result);
        return {
          ...result,
          id: result._id || result.id
        };
      } catch (ipcError) {
        console.error('خطأ في استخدام ipcRenderer لإضافة الآلة:', ipcError);
      }
    }

    // محاولة استخدام window.api كبديل
    if (checkApiAvailability()) {
      try {
        const result = await window.api.invoke('add-machine', cleanedMachine);
        console.log('تمت إضافة الآلة بنجاح (window.api):', result);
        return {
          ...result,
          id: result._id || result.id
        };
      } catch (apiError) {
        console.error('خطأ في استخدام window.api لإضافة الآلة:', apiError);
        throw apiError;
      }
    }

    throw new Error('لا يمكن الوصول إلى قاعدة البيانات');
  } catch (error) {
    console.error('خطأ عام في إضافة الآلة:', error);
    throw error;
  }
};

/**
 * تحديث آلة موجودة
 * @param {Object} machine بيانات الآلة
 * @returns {Promise<Object>} نتيجة التحديث
 */
export const updateMachine = async (machine) => {
  try {
    // التأكد من أن البيانات تحتوي على معرف صحيح
    if (!machine._id && !machine.id) {
      throw new Error('معرف الآلة مطلوب للتحديث');
    }

    // نسخة من البيانات مع التأكد من وجود كلا المعرفين
    const machineData = {
      ...machine,
      _id: machine._id || machine.id,
      id: machine.id || machine._id,
      updated_at: new Date().toISOString()
    };

    console.log('جاري تحديث الآلة:', machineData);

    // محاولة استخدام ipcRenderer أولاً
    if (ipcRenderer) {
      try {
        const result = await ipcRenderer.invoke('update-machine', machineData);
        console.log('تم تحديث الآلة بنجاح (ipcRenderer):', result);
        return {
          ...result,
          id: result._id || result.id
        };
      } catch (ipcError) {
        console.error('خطأ في استخدام ipcRenderer لتحديث الآلة:', ipcError);
      }
    }

    // محاولة استخدام window.api كبديل
    if (checkApiAvailability()) {
      try {
        const result = await window.api.invoke('update-machine', machineData);
        console.log('تم تحديث الآلة بنجاح (window.api):', result);
        return {
          ...result,
          id: result._id || result.id
        };
      } catch (apiError) {
        console.error('خطأ في استخدام window.api لتحديث الآلة:', apiError);
        throw apiError;
      }
    }

    throw new Error('لا يمكن الوصول إلى قاعدة البيانات');
  } catch (error) {
    console.error('خطأ عام في تحديث الآلة:', error);
    throw error;
  }
};

/**
 * حذف آلة
 * @param {number} id معرف الآلة
 * @returns {Promise<Object>} نتيجة الحذف
 */
export const deleteMachine = async (id) => {
  try {
    if (!id) {
      throw new Error('معرف الآلة مطلوب للحذف');
    }

    console.log('جاري حذف الآلة مع المعرف:', id);

    // محاولة استخدام ipcRenderer أولاً
    if (ipcRenderer) {
      try {
        const result = await ipcRenderer.invoke('delete-machine', id);
        console.log('تم حذف الآلة بنجاح (ipcRenderer):', result);
        return result;
      } catch (ipcError) {
        console.error('خطأ في استخدام ipcRenderer لحذف الآلة:', ipcError);
      }
    }

    // محاولة استخدام window.api كبديل
    if (checkApiAvailability()) {
      try {
        const result = await window.api.invoke('delete-machine', id);
        console.log('تم حذف الآلة بنجاح (window.api):', result);
        return result;
      } catch (apiError) {
        console.error('خطأ في استخدام window.api لحذف الآلة:', apiError);
        throw apiError;
      }
    }

    throw new Error('لا يمكن الوصول إلى قاعدة البيانات');
  } catch (error) {
    console.error('خطأ عام في حذف الآلة:', error);
    throw error;
  }
};
