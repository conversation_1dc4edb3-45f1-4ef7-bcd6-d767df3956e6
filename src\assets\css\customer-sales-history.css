/* تنسيقات محسنة لنافذة سجل مبيعات العميل - تم تحديثها لتتناسب مع التصميم الجديد */

/* إخفاء خانة الربح */
.data-table th:nth-child(6),
.data-table td:nth-child(6),
.data-table tfoot tr td:nth-child(2) {
  display: none;
}

/* إخفاء بطاقة إجمالي الربح */
.profit-card {
  display: none;
}

/* تنسيقات التحريك */
@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out forwards;
}

/* تنسيقات الفواتير الفرعية */
.sub-invoice-row {
  background-color: rgba(52, 152, 219, 0.05) !important;
  border-right: 3px solid #3498db !important;
}

/* تنسيقات خاصة للفواتير الفرعية */
.invoice-header.sub-invoice {
  border-right: 4px solid #3498db !important;
  background-color: rgba(52, 152, 219, 0.05) !important;
}

.invoice-header.sub-invoice .invoice-icon {
  color: #3498db !important;
  background-color: rgba(52, 152, 219, 0.1) !important;
}

.sub-invoice-badge {
  display: inline-block !important;
  margin-left: 5px !important;
  padding: 2px 5px !important;
  font-size: 10px !important;
  background-color: #3498db !important;
  color: white !important;
  border-radius: 3px !important;
  vertical-align: middle !important;
}

.parent-invoice-info {
  display: inline-block !important;
  margin-right: 10px !important;
  font-size: 14px !important;
  color: #3498db !important;
}

/* تنسيق الحالات الخاصة */
.loading-state, .error-state, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 20px;
  text-align: center;
  min-height: 300px;
}

.loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid var(--primary-color, #1a3a5f);
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-title, .error-title, .empty-title {
  font-size: 1.5rem;
  margin: 15px 0;
  color: var(--text-dark, #333);
}

.loading-description, .error-description, .empty-description {
  color: var(--text-light, #666);
  margin-bottom: 20px;
}

.error-icon, .empty-icon {
  font-size: 3rem;
  color: var(--danger-color, #e74c3c);
  margin-bottom: 15px;
}

.empty-icon {
  color: var(--text-light, #718096);
}

.retry-button {
  padding: 10px 20px;
  background-color: var(--primary-color, #1a3a5f);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s;
}

.retry-button:hover {
  background-color: var(--primary-dark, #122a45);
}

/* تنسيق النافذة المنبثقة */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  overflow-y: auto;
  padding: 20px;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
}

.modal-container {
  background-color: var(--card-bg, #fff);
  border-radius: 16px;
  width: 90%;
  max-width: 1100px;
  max-height: 90vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  animation: modalFadeIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25), 0 5px 15px rgba(0, 0, 0, 0.1);
}

.sales-history-container {
  background-color: #f8f9fa;
  position: relative;
}

.modal-header {
  padding: 20px 28px;
  background: linear-gradient(to right, #ffffff, #f8f9fa);
  color: #333;
  border-radius: 16px 16px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
  position: sticky;
  top: 0;
  z-index: 100;
}

.modal-header h3 {
  margin: 0;
  font-size: 22px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  background-color: rgba(243, 156, 18, 0.1);
  width: 42px;
  height: 42px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #f39c12;
  box-shadow: 0 2px 8px rgba(243, 156, 18, 0.2);
  transition: all 0.3s ease;
}

.header-text {
  background: linear-gradient(135deg, #1a3a5f, #2c5282);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.close-btn {
  background: rgba(231, 76, 60, 0.1);
  border: none;
  color: #e74c3c;
  font-size: 18px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 42px;
  height: 42px;
  border-radius: 50%;
  transition: all 0.3s;
}

.close-btn:hover {
  background-color: rgba(231, 76, 60, 0.2);
  transform: rotate(90deg);
}

.modal-body {
  padding: 25px;
  overflow-y: auto;
  flex: 1;
  background-color: #f8f9fa;
  border-radius: 0 0 16px 16px;
}

/* تنسيقات ملخص المبيعات */
.sales-summary {
  margin-bottom: 30px;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -15px;
}

.col-md-4 {
  flex: 0 0 33.333333%;
  max-width: 33.333333%;
  padding: 0 15px;
  box-sizing: border-box;
}

.summary-card {
  border-radius: 16px;
  padding: 25px;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
  height: 100%;
  position: relative;
  overflow: hidden;
  background-color: white;
  border: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 6px;
  opacity: 1;
}

.items-card::before {
  background: linear-gradient(to right, #3498db, #2980b9);
}

.sales-card::before {
  background: linear-gradient(to right, #f39c12, #e67e22);
}

.profit-card::before {
  background: linear-gradient(to right, #2ecc71, #27ae60);
}

.summary-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.summary-card:hover .card-icon {
  transform: scale(1.1) rotate(10deg);
}

.card-body {
  text-align: center;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 1;
}

.card-icon {
  font-size: 28px;
  margin-bottom: 20px;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  transition: all 0.4s ease;
  position: relative;
}

.card-icon::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: currentColor;
  opacity: 0.1;
  z-index: -1;
  transform: scale(1);
  transition: transform 0.4s ease;
}

.summary-card:hover .card-icon::after {
  transform: scale(1.2);
}

.items-card .card-icon {
  color: #3498db;
}

.sales-card .card-icon {
  color: #f39c12;
}

.profit-card .card-icon {
  color: #2ecc71;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #555;
  position: relative;
  display: inline-block;
}

.card-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 3px;
  background-color: currentColor;
  opacity: 0.3;
  border-radius: 3px;
}

.card-text {
  font-size: 36px;
  font-weight: 700;
  margin: 15px 0 0;
  background: linear-gradient(135deg, #1a3a5f, #2c5282);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

/* تنسيقات شريط التحكم */
.control-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  background-color: white;
  padding: 20px 25px;
  border-radius: 16px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.control-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 5px;
  height: 100%;
  background: linear-gradient(to bottom, #f39c12, #e67e22);
}

.customer-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-left: 15px;
}

.section-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1a3a5f;
  display: flex;
  align-items: center;
  gap: 12px;
}

.section-icon {
  color: #f39c12;
  font-size: 22px;
  background-color: rgba(243, 156, 18, 0.1);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.customer-details {
  display: flex;
  align-items: center;
  gap: 20px;
  font-size: 15px;
  color: #666;
  margin-top: 5px;
}

.customer-name {
  font-weight: 600;
  color: #1a3a5f;
  position: relative;
  padding-right: 20px;
}

.customer-name::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  background-color: #f39c12;
  border-radius: 50%;
}

.customer-phone {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: rgba(52, 152, 219, 0.1);
  padding: 5px 12px;
  border-radius: 20px;
  color: #3498db;
}

/* تنسيقات عنوان القسم */
.section-header {
  background-color: white;
  padding: 20px 25px;
  border-radius: 16px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
  margin-bottom: 25px;
  position: relative;
  overflow: hidden;
}

.section-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 5px;
  height: 100%;
  background: linear-gradient(to bottom, #3498db, #2980b9);
}

.action-buttons {
  display: flex;
  gap: 15px;
}

.btn-print, .btn-close {
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.btn-print::after, .btn-close::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.btn-print:hover::after, .btn-close:hover::after {
  opacity: 1;
}

.button-icon {
  font-size: 16px;
  transition: transform 0.3s ease;
}

.btn-print {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  min-width: 160px;
  justify-content: center;
}

.btn-print:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(52, 152, 219, 0.3);
}

.btn-print:hover .button-icon {
  transform: rotate(15deg);
}

.btn-close {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
}

.btn-close:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(231, 76, 60, 0.3);
}

.btn-close:hover .button-icon {
  transform: rotate(90deg);
}



/* تنسيقات أدوات الفلترة والبحث */
.filter-tools {
  margin-bottom: 25px;
  background-color: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.search-box, .filter-box {
  position: relative;
  margin-bottom: 10px;
}

.search-icon, .filter-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #aaa;
  font-size: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.search-input, .filter-select {
  width: 100%;
  padding: 12px 15px 12px 40px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s;
  background-color: #fff;
  color: #333;
  height: 45px;
}

.search-input:focus, .filter-select:focus {
  border-color: #1a3a5f;
  box-shadow: 0 0 0 3px rgba(26, 58, 95, 0.1);
  outline: none;
}

/* تنسيقات جدول البيانات */
.table-container {
  background-color: var(--card-bg, white);
  border-radius: var(--border-radius-lg, 12px);
  overflow: hidden;
  box-shadow: var(--shadow-md, 0 4px 8px rgba(0, 0, 0, 0.1));
  margin-top: 20px;
  border: 1px solid var(--border-color, rgba(0, 0, 0, 0.05));
  padding: 20px;
  transition: all 0.3s ease;
}









.table-container {
  background-color: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  margin-top: 25px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  padding: 0;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
}

.table-container:hover {
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
  transform: translateY(-5px);
}

/* رأس الفاتورة */
.invoice-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px 30px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background: linear-gradient(to right, #ffffff, #f8f9fa);
  position: relative;
}

.invoice-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to right, #f39c12, #e67e22);
}

.invoice-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.invoice-title {
  font-size: 22px;
  font-weight: 600;
  color: #1a3a5f;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.invoice-icon {
  color: #f39c12;
  background-color: rgba(243, 156, 18, 0.1);
  width: 42px;
  height: 42px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(243, 156, 18, 0.2);
}

.invoice-date {
  font-size: 15px;
  color: #666;
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: rgba(52, 152, 219, 0.1);
  padding: 5px 12px;
  border-radius: 20px;
  width: fit-content;
}

.date-label {
  font-weight: 600;
  color: #3498db;
}

.date-value {
  color: #333;
}

.data-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin: 0;
}

.data-table th, .data-table td {
  padding: 18px 15px;
  text-align: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  vertical-align: middle;
  transition: all 0.3s ease;
}

.data-table th {
  background: linear-gradient(to bottom, #f8f9fa, #edf2f7);
  font-weight: 600;
  color: #1a3a5f;
  position: sticky;
  top: 0;
  z-index: 10;
  padding: 20px 15px;
  text-transform: uppercase;
  font-size: 13px;
  letter-spacing: 0.5px;
}

.data-table tbody tr {
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  cursor: pointer;
  position: relative;
}

.data-table tbody tr::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 0;
  background-color: #f39c12;
  transition: width 0.3s ease;
  opacity: 0;
}

.data-table tbody tr:hover::after {
  width: 4px;
  opacity: 1;
}

.data-table tbody tr:hover {
  background-color: rgba(243, 156, 18, 0.05);
  transform: translateX(5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

/* تنسيق الصفوف عند النقر عليها */
.data-table tbody tr:active {
  transform: translateX(0);
  background-color: rgba(243, 156, 18, 0.1);
}

/* تنسيق قيم الربح */
.profit-value {
  color: #2ecc71;
  font-weight: 600;
  position: relative;
  display: inline-block;
  padding: 5px 10px;
  border-radius: 15px;
  background-color: rgba(46, 204, 113, 0.1);
}

.profit-value.negative {
  color: #e74c3c;
  background-color: rgba(231, 76, 60, 0.1);
}

/* تنسيق الصفوف المتناوبة */
.data-table tbody tr:nth-child(odd) {
  background-color: rgba(0, 0, 0, 0.01);
}

.data-table tbody tr:last-child td {
  border-bottom: none;
}

/* تنسيقات الإجمالي */
.data-table tfoot {
  background: linear-gradient(to bottom, #f8f9fa, #edf2f7);
  font-weight: 600;
}

.data-table tfoot .total-row {
  border-top: 2px solid rgba(0, 0, 0, 0.1);
}

.data-table tfoot .total-row td {
  padding: 20px 15px;
  color: #1a3a5f;
  font-size: 18px;
}

.text-center {
  text-align: center;
}

.th-icon {
  margin-left: 8px;
  color: #f39c12;
  font-size: 16px;
  vertical-align: middle;
}

.action-button {
  background-color: #1a3a5f;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 15px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-width: 100px;
}

.action-button:hover {
  background-color: #122a45;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.button-icon {
  font-size: 14px;
}

.profit-value {
  color: #2ecc71;
  font-weight: 600;
}

.profit-value.negative {
  color: #e74c3c;
}

/* تنسيقات حالة عدم وجود بيانات */
.empty-state {
  text-align: center;
  padding: 50px 20px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
  margin-top: 20px;
  animation: fadeIn 0.5s ease-in-out;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.empty-icon {
  font-size: 50px;
  color: #b0bec5;
  margin-bottom: 20px;
  background-color: #f5f5f5;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  animation: pulse 2s infinite;
}

.empty-title {
  font-size: 24px;
  font-weight: 600;
  color: #455a64;
  margin-bottom: 10px;
}

.empty-description {
  font-size: 16px;
  color: #78909c;
  max-width: 400px;
  margin: 0 auto;
}

/* تنسيقات حالة التحميل */
.loading-state {
  text-align: center;
  padding: 50px 20px;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #1a3a5f;
  border-radius: 50%;
  margin: 0 auto 20px;
  animation: spin 1s linear infinite;
}

.loading-title {
  font-size: 24px;
  font-weight: 600;
  color: #455a64;
  margin-bottom: 10px;
}

.loading-description {
  font-size: 16px;
  color: #78909c;
}

/* تنسيقات حالة الخطأ */
.error-state {
  text-align: center;
  padding: 50px 20px;
}

.error-icon {
  font-size: 50px;
  color: #e74c3c;
  margin-bottom: 20px;
}

.error-title {
  font-size: 24px;
  font-weight: 600;
  color: #455a64;
  margin-bottom: 10px;
}

.error-description {
  font-size: 16px;
  color: #78909c;
  max-width: 400px;
  margin: 0 auto 20px;
}

.retry-button {
  background-color: #1a3a5f;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.retry-button:hover {
  background-color: #122a45;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* تنسيقات للشاشات الصغيرة */
@media (max-width: 768px) {
  .modal-container {
    width: 95%;
    max-height: 95vh;
  }

  .modal-header {
    padding: 15px;
  }

  .modal-header h3 {
    font-size: 18px;
  }

  .modal-body {
    padding: 15px;
  }

  .sales-summary .col-md-4 {
    margin-bottom: 15px;
  }

  .control-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .action-buttons {
    width: 100%;
  }

  .btn-print, .btn-close {
    flex: 1;
  }

  .table-container {
    overflow-x: auto;
  }

  .data-table th, .data-table td {
    padding: 10px;
    font-size: 13px;
  }

  .search-input, .filter-select {
    padding: 10px 15px 10px 40px;
  }
}

/* الرسوم المتحركة */
@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}
