// هذا الملف تم استبداله بوظائف مباشرة في ملف CustomerSalesHistory.js
// يتم الاحتفاظ به فقط لتجنب أخطاء الاستيراد في الملفات الأخرى

import React from 'react';
import { FaPrint, FaFilePdf } from 'react-icons/fa';

/**
 * مكون لطباعة فاتورة المبيعات بتصميم مخصص للشركات
 * تم استبداله بوظائف مباشرة في ملف CustomerSalesHistory.js
 */
const BusinessInvoicePrinter = () => {

  return (
    <div className="invoice-actions">
      <button className="btn btn-sm btn-info ml-2" title="طباعة الفاتورة">
        <FaPrint className="ml-1" />
        طباعة الفاتورة
      </button>
      <button className="btn btn-sm btn-primary" title="تصدير كملف PDF">
        <FaFilePdf className="ml-1" />
        PDF
      </button>
    </div>
  );
};

export default BusinessInvoicePrinter;
