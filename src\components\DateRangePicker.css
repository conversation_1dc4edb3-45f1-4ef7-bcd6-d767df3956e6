/* تنسيقات مكون اختيار نطاق التاريخ */
.date-range-picker {
  position: relative;
  display: inline-block;
  font-family: 'Cairo', sans-serif;
  direction: rtl;
}

.date-range-display {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
  cursor: pointer;
  min-width: 200px;
  transition: all 0.2s ease;
}

.date-range-display:hover {
  border-color: #aaa;
  background-color: #f9f9f9;
}

.calendar-icon {
  color: #3498db;
}

.date-range-text {
  font-size: 0.9rem;
  color: #333;
}

.date-range-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 5px;
  width: 300px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 15px;
}

.date-range-presets {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-bottom: 15px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.date-range-presets button {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 3px;
  padding: 5px 10px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.date-range-presets button:hover {
  background-color: #e9e9e9;
  border-color: #ccc;
}

.date-range-inputs {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 15px;
}

.date-input-group {
  display: flex;
  align-items: center;
}

.date-input-group label {
  width: 40px;
  font-size: 0.9rem;
  color: #666;
}

.date-input-group input {
  flex: 1;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 3px;
  font-size: 0.9rem;
}

.date-range-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.apply-btn, .cancel-btn {
  padding: 6px 12px;
  border-radius: 3px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.apply-btn {
  background-color: #3498db;
  color: white;
  border: 1px solid #2980b9;
}

.apply-btn:hover {
  background-color: #2980b9;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.cancel-btn:hover {
  background-color: #e9e9e9;
}

/* تنسيقات للشاشات الصغيرة */
@media (max-width: 480px) {
  .date-range-dropdown {
    width: 280px;
    right: -50px;
  }
  
  .date-range-presets {
    justify-content: center;
  }
}
