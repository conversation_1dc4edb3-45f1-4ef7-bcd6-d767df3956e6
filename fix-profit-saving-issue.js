/**
 * إصلاح مشكلة حفظ إجمالي الأرباح في الخزينة
 * المشكلة: النظام يحسب الأرباح بشكل صحيح لكن لا يحفظها في قاعدة البيانات
 */

const { logError, logSystem } = require('./error-handler');
const DatabaseManager = require('./database-singleton');

/**
 * تشخيص مشكلة حفظ الأرباح
 */
function diagnoseProfitSavingIssue() {
  console.log('🔍 تشخيص مشكلة حفظ الأرباح...');
  
  try {
    const db = DatabaseManager.getDatabase();
    if (!db) {
      throw new Error('قاعدة البيانات غير متصلة');
    }

    // 1. فحص معاملات البيع والأرباح
    console.log('\n1️⃣ فحص معاملات البيع والأرباح:');
    const salesQuery = db.prepare(`
      SELECT 
        id, 
        item_id, 
        quantity, 
        selling_price, 
        profit, 
        transaction_date
      FROM transactions 
      WHERE transaction_type = 'sale' 
      ORDER BY transaction_date DESC 
      LIMIT 10
    `);
    
    const salesTransactions = salesQuery.all();
    console.log(`📊 عدد معاملات البيع: ${salesTransactions.length}`);
    
    let calculatedTotalProfit = 0;
    salesTransactions.forEach((transaction, index) => {
      const profit = Number(transaction.profit) || 0;
      calculatedTotalProfit += profit;
      console.log(`   ${index + 1}. المعاملة ${transaction.id}: الربح = ${profit}`);
    });
    
    console.log(`💰 إجمالي الأرباح المحسوب: ${calculatedTotalProfit}`);

    // 2. فحص قيمة الأرباح في الخزينة
    console.log('\n2️⃣ فحص قيمة الأرباح في الخزينة:');
    const cashboxQuery = db.prepare('SELECT profit_total FROM cashbox WHERE id = 1');
    const cashboxResult = cashboxQuery.get();
    
    if (cashboxResult) {
      const savedProfit = Number(cashboxResult.profit_total) || 0;
      console.log(`💾 الأرباح المحفوظة في الخزينة: ${savedProfit}`);
      
      if (Math.abs(savedProfit - calculatedTotalProfit) > 0.01) {
        console.log('❌ هناك تباين بين الأرباح المحسوبة والمحفوظة!');
        return {
          issue: true,
          calculatedProfit: calculatedTotalProfit,
          savedProfit: savedProfit,
          difference: calculatedTotalProfit - savedProfit
        };
      } else {
        console.log('✅ الأرباح المحسوبة والمحفوظة متطابقة');
        return {
          issue: false,
          calculatedProfit: calculatedTotalProfit,
          savedProfit: savedProfit
        };
      }
    } else {
      console.log('❌ لم يتم العثور على بيانات الخزينة');
      return {
        issue: true,
        error: 'لم يتم العثور على بيانات الخزينة'
      };
    }

  } catch (error) {
    console.error('❌ خطأ في تشخيص مشكلة حفظ الأرباح:', error);
    return {
      issue: true,
      error: error.message
    };
  }
}

/**
 * إصلاح مشكلة حفظ الأرباح
 */
function fixProfitSavingIssue() {
  console.log('🔧 إصلاح مشكلة حفظ الأرباح...');
  
  try {
    const db = DatabaseManager.getDatabase();
    if (!db) {
      throw new Error('قاعدة البيانات غير متصلة');
    }

    // 1. حساب إجمالي الأرباح الصحيح
    console.log('\n1️⃣ حساب إجمالي الأرباح الصحيح...');
    const profitQuery = db.prepare(`
      SELECT COALESCE(SUM(profit), 0) as total_profit
      FROM transactions
      WHERE transaction_type = 'sale'
    `);
    
    const result = profitQuery.get();
    const correctTotalProfit = Number(result.total_profit) || 0;
    console.log(`💰 إجمالي الأرباح الصحيح: ${correctTotalProfit}`);

    // 2. تحديث الأرباح في الخزينة باستخدام معاملة محسنة
    console.log('\n2️⃣ تحديث الأرباح في الخزينة...');
    
    const updateTransaction = db.transaction(() => {
      // تحديث القيمة
      const updateStmt = db.prepare(`
        UPDATE cashbox
        SET profit_total = ?,
            updated_at = ?
        WHERE id = 1
      `);

      const updateResult = updateStmt.run(correctTotalProfit, new Date().toISOString());

      if (updateResult.changes === 0) {
        throw new Error('لم يتم تحديث أي صف في جدول cashbox');
      }

      console.log(`✅ تم تحديث profit_total. الصفوف المتأثرة: ${updateResult.changes}`);

      // التحقق الفوري من حفظ القيمة
      const verifyStmt = db.prepare('SELECT profit_total FROM cashbox WHERE id = 1');
      const verifyResult = verifyStmt.get();

      if (!verifyResult) {
        throw new Error('فشل في استرجاع البيانات للتحقق');
      }

      const savedValue = Number(verifyResult.profit_total);
      console.log(`💾 القيمة المحفوظة: ${savedValue}`);

      if (Math.abs(savedValue - correctTotalProfit) > 0.01) {
        throw new Error(`فشل في حفظ القيمة: متوقع ${correctTotalProfit} لكن تم حفظ ${savedValue}`);
      }

      console.log(`✅ تم حفظ القيمة بنجاح: ${savedValue}`);

      return {
        success: true,
        savedValue: savedValue,
        changes: updateResult.changes
      };
    });

    const updateResult = updateTransaction();
    
    if (updateResult.success) {
      console.log('🎉 تم إصلاح مشكلة حفظ الأرباح بنجاح!');
      logSystem(`تم إصلاح مشكلة حفظ الأرباح: ${updateResult.savedValue}`, 'info');
      
      return {
        success: true,
        correctedProfit: updateResult.savedValue,
        message: 'تم إصلاح مشكلة حفظ الأرباح بنجاح'
      };
    } else {
      throw new Error('فشل في تحديث الأرباح');
    }

  } catch (error) {
    console.error('❌ خطأ في إصلاح مشكلة حفظ الأرباح:', error);
    logError(error, 'fixProfitSavingIssue');
    
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * اختبار حفظ الأرباح
 */
function testProfitSaving() {
  console.log('🧪 اختبار حفظ الأرباح...');
  
  try {
    const db = DatabaseManager.getDatabase();
    if (!db) {
      throw new Error('قاعدة البيانات غير متصلة');
    }

    // الحصول على القيمة الحالية
    const currentQuery = db.prepare('SELECT profit_total FROM cashbox WHERE id = 1');
    const currentResult = currentQuery.get();
    const originalValue = currentResult ? Number(currentResult.profit_total) : 0;
    
    console.log(`📊 القيمة الأصلية: ${originalValue}`);

    // اختبار تحديث وحفظ قيمة جديدة
    const testValue = originalValue + 0.01;
    console.log(`🔄 اختبار حفظ القيمة: ${testValue}`);
    
    const testTransaction = db.transaction(() => {
      const updateStmt = db.prepare(`
        UPDATE cashbox
        SET profit_total = ?,
            updated_at = ?
        WHERE id = 1
      `);

      const updateResult = updateStmt.run(testValue, new Date().toISOString());

      if (updateResult.changes === 0) {
        throw new Error('لم يتم تحديث أي صف');
      }

      // التحقق من الحفظ
      const verifyStmt = db.prepare('SELECT profit_total FROM cashbox WHERE id = 1');
      const verifyResult = verifyStmt.get();
      const savedValue = Number(verifyResult.profit_total);

      if (Math.abs(savedValue - testValue) > 0.01) {
        throw new Error(`فشل في حفظ القيمة التجريبية`);
      }

      return { success: true, savedValue: savedValue };
    });

    const testResult = testTransaction();
    
    if (testResult.success) {
      console.log(`✅ تم حفظ القيمة التجريبية بنجاح: ${testResult.savedValue}`);
      
      // إعادة القيمة الأصلية
      const restoreTransaction = db.transaction(() => {
        const restoreStmt = db.prepare(`
          UPDATE cashbox
          SET profit_total = ?,
              updated_at = ?
          WHERE id = 1
        `);

        restoreStmt.run(originalValue, new Date().toISOString());
        return { success: true };
      });

      restoreTransaction();
      console.log(`🔄 تم إعادة القيمة الأصلية: ${originalValue}`);
      
      return {
        success: true,
        message: 'اختبار حفظ الأرباح نجح'
      };
    } else {
      throw new Error('فشل اختبار حفظ الأرباح');
    }

  } catch (error) {
    console.error('❌ خطأ في اختبار حفظ الأرباح:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * إصلاح شامل لمشكلة حفظ الأرباح
 */
function comprehensiveProfitFix() {
  console.log('🚀 بدء الإصلاح الشامل لمشكلة حفظ الأرباح...');
  console.log('='.repeat(60));
  
  const results = {
    diagnosis: null,
    fix: null,
    test: null,
    success: false
  };

  try {
    // 1. التشخيص
    console.log('\n📋 المرحلة 1: التشخيص');
    results.diagnosis = diagnoseProfitSavingIssue();
    
    if (results.diagnosis.issue) {
      console.log('⚠️ تم اكتشاف مشكلة في حفظ الأرباح');
      
      // 2. الإصلاح
      console.log('\n🔧 المرحلة 2: الإصلاح');
      results.fix = fixProfitSavingIssue();
      
      if (results.fix.success) {
        console.log('✅ تم إصلاح المشكلة بنجاح');
        
        // 3. الاختبار
        console.log('\n🧪 المرحلة 3: الاختبار');
        results.test = testProfitSaving();
        
        if (results.test.success) {
          console.log('✅ اختبار الإصلاح نجح');
          results.success = true;
        } else {
          console.log('❌ فشل اختبار الإصلاح');
        }
      } else {
        console.log('❌ فشل في إصلاح المشكلة');
      }
    } else {
      console.log('✅ لا توجد مشكلة في حفظ الأرباح');
      results.success = true;
    }

    // النتيجة النهائية
    console.log('\n' + '='.repeat(60));
    console.log('📋 ملخص الإصلاح الشامل:');
    console.log('='.repeat(60));
    
    if (results.success) {
      console.log('🎉 تم إكمال الإصلاح الشامل بنجاح!');
      console.log('💡 يمكنك الآن التحقق من عرض الأرباح في واجهة النظام');
    } else {
      console.log('⚠️ الإصلاح لم يكتمل بنجاح');
      console.log('💡 راجع الأخطاء أعلاه وقم بالإصلاح اليدوي إذا لزم الأمر');
    }

    return results;

  } catch (error) {
    console.error('❌ خطأ في الإصلاح الشامل:', error);
    logError(error, 'comprehensiveProfitFix');
    
    results.success = false;
    results.error = error.message;
    
    return results;
  }
}

module.exports = {
  diagnoseProfitSavingIssue,
  fixProfitSavingIssue,
  testProfitSaving,
  comprehensiveProfitFix
};

// تشغيل الإصلاح إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  console.log('🚀 بدء إصلاح مشكلة حفظ الأرباح...');
  
  const results = comprehensiveProfitFix();
  
  console.log('\n📋 النتائج النهائية:', {
    success: results.success,
    diagnosis: results.diagnosis ? 'مكتمل' : 'فشل',
    fix: results.fix ? (results.fix.success ? 'نجح' : 'فشل') : 'لم يتم',
    test: results.test ? (results.test.success ? 'نجح' : 'فشل') : 'لم يتم'
  });
  
  console.log('\n🏁 انتهى إصلاح مشكلة حفظ الأرباح');
  process.exit(results.success ? 0 : 1);
}
