import React, { useState, useEffect, useRef } from 'react';
import { FaSearch, FaTimes, FaBoxOpen, FaMoneyBillWave, FaRulerHorizontal } from 'react-icons/fa';
import '../../assets/css/item-search-popup.css';

/**
 * مكون نافذة منبثقة للبحث عن الأصناف
 * @param {Object} props - خصائص المكون
 * @param {Array} props.items - قائمة الأصناف
 * @param {Function} props.onSelect - دالة تُستدعى عند اختيار صنف
 * @param {Function} props.onClose - دالة تُستدعى عند إغلاق النافذة
 * @returns {JSX.Element}
 */
const ItemSearchPopup = ({ items, onSelect, onClose }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredItems, setFilteredItems] = useState([]);
  const [selectedItem, setSelectedItem] = useState(null);
  const searchInputRef = useRef(null);

  // تركيز حقل البحث عند فتح النافذة
  useEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);

  // تصفية الأصناف بناءً على مصطلح البحث
  useEffect(() => {
    if (!items || items.length === 0) {
      setFilteredItems([]);
      return;
    }

    if (searchTerm.trim() === '') {
      setFilteredItems(items);
    } else {
      const filtered = items.filter(item =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (item.id && item.id.toString().includes(searchTerm))
      );
      setFilteredItems(filtered);
    }
  }, [items, searchTerm]);

  // معالجة تغيير مصطلح البحث
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  // معالجة اختيار صنف
  const handleItemClick = (item) => {
    setSelectedItem(item);
  };

  // معالجة تأكيد الاختيار
  const handleConfirm = async () => {
    if (selectedItem) {
      try {
        // محاولة الحصول على أحدث معلومات الصنف من قاعدة البيانات مع تجاوز التخزين المؤقت
        console.log('محاولة الحصول على أحدث معلومات الصنف من قاعدة البيانات:', selectedItem.id);
        const dbItem = await window.api.invoke('get-inventory-item', selectedItem.id, true); // تجاوز التخزين المؤقت للحصول على أحدث البيانات

        if (dbItem) {
          console.log('تم استلام بيانات الصنف من قاعدة البيانات:', dbItem);

          // دمج بيانات الصنف من قاعدة البيانات مع البيانات المحلية
          const updatedItem = {
            ...selectedItem,
            id: selectedItem.id, // التأكد من وجود المعرف
            name: selectedItem.name, // الحفاظ على اسم الصنف من البيانات المحلية
            unit: selectedItem.unit, // الحفاظ على وحدة الصنف من البيانات المحلية
            // استخدام البيانات من قاعدة البيانات مع التحقق من نوع البيانات
            selling_price: typeof dbItem.selling_price === 'number' ? dbItem.selling_price :
                          (typeof selectedItem.selling_price === 'number' ? selectedItem.selling_price : 0),
            current_quantity: typeof dbItem.current_quantity === 'number' ? dbItem.current_quantity :
                             (typeof selectedItem.current_quantity === 'number' ? selectedItem.current_quantity : 0),
            minimum_quantity: typeof dbItem.minimum_quantity === 'number' ? dbItem.minimum_quantity :
                             (typeof selectedItem.minimum_quantity === 'number' ? selectedItem.minimum_quantity : 0)
          };

          console.log('تم تحديث بيانات الصنف قبل الإرسال:', updatedItem);

          // التحقق من وجود المعرف والاسم قبل الإرسال
          if (!updatedItem.id) {
            console.error('الصنف المحدث لا يحتوي على معرف:', updatedItem);
            // استخدام البيانات المحلية في حالة عدم وجود معرف
            onSelect(selectedItem);
            return;
          }

          onSelect(updatedItem);
          return;
        }
      } catch (error) {
        console.error('خطأ في الحصول على معلومات الصنف من قاعدة البيانات:', error);
      }

      // في حالة فشل الحصول على البيانات من قاعدة البيانات، نستخدم البيانات المحلية
      // التحقق من وجود المعرف والاسم قبل الإرسال
      if (!selectedItem.id) {
        console.error('الصنف المحدد لا يحتوي على معرف:', selectedItem);
        return;
      }

      onSelect(selectedItem);
    }
  };

  // معالجة الضغط على مفتاح
  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      onClose();
    } else if (e.key === 'Enter' && selectedItem) {
      handleConfirm();
    }
  };

  // تحديد لون الكمية بناءً على قيمتها
  const getQuantityColorClass = (quantity, minimumQuantity = 0) => {
    if (quantity <= 0) return 'out';
    if (minimumQuantity > 0 && quantity <= minimumQuantity) return 'low';
    return '';
  };

  return (
    <div className="item-search-popup" onKeyDown={handleKeyDown}>
      <div className="item-search-container">
        <div className="item-search-header">
          <h3>البحث عن صنف</h3>
          <button className="item-search-close" onClick={onClose}>
            <FaTimes />
          </button>
        </div>

        <div className="item-search-body">
          <div className="item-search-input">
            <FaSearch className="search-icon" />
            <input
              ref={searchInputRef}
              type="text"
              placeholder="اكتب اسم الصنف أو الرقم..."
              value={searchTerm}
              onChange={handleSearchChange}
            />
          </div>

          {filteredItems.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '20px', color: 'var(--text-light, #777)' }}>
              {items.length === 0 ? 'لا توجد أصناف متاحة' : 'لا توجد نتائج مطابقة للبحث'}
            </div>
          ) : (
            <div className="item-search-results">
              {filteredItems.map(item => (
                <div
                  key={item.id || item._id}
                  className={`item-card ${selectedItem && (selectedItem.id === item.id || selectedItem._id === item._id) ? 'selected' : ''}`}
                  onClick={() => handleItemClick(item)}
                >
                  <div className="item-name">{item.name}</div>
                  <div className="item-details">
                    <div className="item-detail">
                      <span className="item-detail-label">
                        <FaRulerHorizontal style={{ marginLeft: '5px' }} />
                        الوحدة:
                      </span>
                      <span className="item-detail-value">{item.unit || 'غير محدد'}</span>
                    </div>
                    <div className="item-detail">
                      <span className="item-detail-label">
                        <FaBoxOpen style={{ marginLeft: '5px' }} />
                        الكمية المتوفرة:
                      </span>
                      <span className={`item-detail-value item-quantity ${getQuantityColorClass(item.current_quantity, item.minimum_quantity)}`}>
                        {item.current_quantity || 0}
                      </span>
                    </div>
                    <div className="item-detail">
                      <span className="item-detail-label">
                        <FaMoneyBillWave style={{ marginLeft: '5px' }} />
                        سعر البيع:
                      </span>
                      <span className="item-detail-value">
                        {item.selling_price ? item.selling_price.toFixed(2) : '0.00'}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="item-search-footer">
          <button
            className="item-search-btn item-search-btn-secondary"
            onClick={onClose}
          >
            إلغاء
          </button>
          <button
            className="item-search-btn item-search-btn-primary"
            onClick={handleConfirm}
            disabled={!selectedItem}
          >
            اختيار
          </button>
        </div>
      </div>
    </div>
  );
};

export default ItemSearchPopup;
