/**
 * معالجات قاعدة البيانات
 * توفر وظائف للتعامل مع قاعدة البيانات من خلال IPC
 */

const { ipcMain } = require('electron');
const DatabaseAdapter = require('./database-adapter');

/**
 * تعريف معالجات IPC لقاعدة البيانات
 */
function setupDatabaseHandlers() {
  console.log('تعريف معالجات IPC لقاعدة البيانات...');

  // معالجات التحميل التدريجي
  ipcMain.handle('get-items-paginated', handleGetItemsPaginated);
  ipcMain.handle('get-transactions-paginated', handleGetTransactionsPaginated);
  ipcMain.handle('get-customers-paginated', handleGetCustomersPaginated);
  ipcMain.handle('get-machines-paginated', handleGetMachinesPaginated);

  // معالجات النسخ الاحتياطي
  ipcMain.handle('backup-database', handleBackupDatabase);
  ipcMain.handle('restore-database', handleRestoreDatabase);

  console.log('تم تعريف معالجات IPC لقاعدة البيانات بنجاح');
}

/**
 * إنشاء نسخة احتياطية من قاعدة البيانات
 */
async function handleBackupDatabase() {
  try {
    return await DatabaseAdapter.backupDatabase();
  } catch (error) {
    console.error('خطأ في إنشاء نسخة احتياطية من قاعدة البيانات:', error);
    throw error;
  }
}

/**
 * استعادة قاعدة البيانات من نسخة احتياطية
 */
async function handleRestoreDatabase(event, backupPath) {
  try {
    return await DatabaseAdapter.restoreDatabase(backupPath);
  } catch (error) {
    console.error('خطأ في استعادة قاعدة البيانات:', error);
    throw error;
  }
}

/**
 * الحصول على الأصناف مع التحميل التدريجي
 */
async function handleGetItemsPaginated(event, { page, pageSize, filters }) {
  try {
    return await DatabaseAdapter.getItemsPaginated(page, pageSize, filters);
  } catch (error) {
    console.error('خطأ في الحصول على الأصناف مع التحميل التدريجي:', error);
    throw error;
  }
}

/**
 * الحصول على المعاملات مع التحميل التدريجي
 */
async function handleGetTransactionsPaginated(event, queryParams) {
  try {
    const { page, pageSize, filters } = queryParams;
    return await DatabaseAdapter.getTransactionsPaginated(page, pageSize, filters);
  } catch (error) {
    console.error('خطأ في الحصول على المعاملات مع التحميل التدريجي:', error);
    throw error;
  }
}

/**
 * الحصول على العملاء مع التحميل التدريجي
 */
async function handleGetCustomersPaginated(event, { page, pageSize, filters }) {
  try {
    return await DatabaseAdapter.getCustomersPaginated(page, pageSize, filters);
  } catch (error) {
    console.error('خطأ في الحصول على العملاء مع التحميل التدريجي:', error);
    throw error;
  }
}

/**
 * الحصول على الآلات مع التحميل التدريجي
 */
async function handleGetMachinesPaginated(event, { page, pageSize, filters }) {
  try {
    return await DatabaseAdapter.getMachinesPaginated(page, pageSize, filters);
  } catch (error) {
    console.error('خطأ في الحصول على الآلات مع التحميل التدريجي:', error);
    throw error;
  }
}

// تصدير الوظائف
const handlers = {
  setupDatabaseHandlers,
  handleGetItemsPaginated,
  handleGetTransactionsPaginated,
  handleGetCustomersPaginated,
  handleGetMachinesPaginated,
  handleBackupDatabase,
  handleRestoreDatabase
};

module.exports = handlers;
