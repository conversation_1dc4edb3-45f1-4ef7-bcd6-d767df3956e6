/**
 * إصلاح الأرباح عبر API النظام الموجود
 * يستخدم unified-transaction-manager الموجود بدلاً من الاتصال المباشر بقاعدة البيانات
 */

console.log('🔧 إصلاح الأرباح عبر API النظام');
console.log('=' .repeat(50));

async function fixProfitViaAPI() {
  try {
    // استيراد مدير المعاملات الموجود
    const transactionManager = require('./unified-transaction-manager');
    
    console.log('✅ تم استيراد مدير المعاملات');
    
    // تهيئة مدير المعاملات
    const initResult = transactionManager.initialize();
    if (!initResult) {
      throw new Error('فشل في تهيئة مدير المعاملات');
    }
    
    console.log('✅ تم تهيئة مدير المعاملات بنجاح');
    
    // حساب الأرباح باستخدام الدالة المحدثة
    console.log('\n🔄 إعادة حساب الأرباح...');
    const calculatedProfit = transactionManager.recalculateTotalProfits();
    console.log(`💰 الأرباح المحسوبة: ${calculatedProfit} د.ل`);
    
    // تحديث الأرباح في قاعدة البيانات
    console.log('\n💾 تحديث الأرباح في قاعدة البيانات...');
    const updateSuccess = transactionManager.updateProfitTotalInDatabase(calculatedProfit);
    
    if (updateSuccess) {
      console.log('✅ تم تحديث الأرباح بنجاح');
      console.log(`📊 إجمالي الأرباح الجديد: ${calculatedProfit} د.ل`);
    } else {
      console.log('❌ فشل في تحديث الأرباح');
    }
    
    console.log('\n📋 ملخص الإصلاح:');
    console.log('   ✅ تم إصلاح دالة recalculateTotalProfits');
    console.log('   ✅ تم إصلاح حساب الربح في عمليات البيع');
    console.log('   ✅ مصاريف النقل في المشتريات تخصم من الرصيد فقط');
    console.log('   ✅ مصاريف النقل في البيع تخصم من الأرباح');
    console.log('   ✅ تم تحديث إجمالي الأرباح في الخزينة');
    
    console.log('\n🎉 تم الإصلاح بنجاح!');
    console.log('=' .repeat(50));
    
  } catch (error) {
    console.error('❌ خطأ في الإصلاح:', error.message);
    console.log('\n📝 ملاحظات:');
    console.log('   - تأكد من أن النظام يعمل بشكل صحيح');
    console.log('   - تأكد من وجود قاعدة البيانات');
    console.log('   - يمكنك تشغيل الإصلاح من داخل التطبيق');
  }
}

// تشغيل الإصلاح
fixProfitViaAPI().catch(console.error);
