import React, { createContext, useState, useEffect } from 'react';
import { initBrightness } from '../../utils/brightness';

// إنشاء سياق الإعدادات
export const SettingsContext = createContext();

/**
 * مزود سياق إعدادات النظام
 *
 * يوفر هذا المكون إعدادات النظام ووظائف تحديثها
 *
 * @param {Object} props - خصائص المكون
 * @param {React.ReactNode} props.children - المكونات الفرعية
 * @returns {React.ReactElement} مزود سياق الإعدادات
 */
export const SettingsProvider = ({ children }) => {
  // حالة إعدادات النظام
  const [settings, setSettings] = useState({
    currency: 'د ل',
    lowStockNotification: true,
    defaultMinimumQuantity: 5,
    systemName: 'شركة أثاث غروب',
    address: 'للتصميم و تصنيع الأثاث والديكورات',
    phone: '+218 92-3000151',
    logoUrl: '',
    integrationEnabled: false,
    integrationUrl: '',
    integrationApiKey: '',
    syncInterval: 30,
    // إعدادات التحديث
    appVersion: '1.1.0',
    autoUpdateEnabled: false,
    lastUpdateCheck: null
  });

  // تحميل الإعدادات من قاعدة البيانات عند بدء التطبيق
  useEffect(() => {
    const loadSettings = async () => {
      try {
        console.log('جاري تحميل الإعدادات من قاعدة البيانات...');

        // استخدام واجهة الإعدادات الجديدة للحصول على الإعدادات
        if (window.api && window.api.settings) {
          const loadedSettings = await window.api.settings.get();
          console.log('تم تحميل الإعدادات من قاعدة البيانات');

          // معالجة خاصة لصورة الشعار
          if (loadedSettings.logoUrl) {
            console.log('تم العثور على إعداد صورة الشعار، طول البيانات:',
              loadedSettings.logoUrl.length);

            // التحقق من صحة البيانات
            if (typeof loadedSettings.logoUrl === 'string' && loadedSettings.logoUrl.startsWith('data:image/')) {
              console.log('صورة الشعار صالحة، طول البيانات:', loadedSettings.logoUrl.length);

              // تحميل الصورة مسبقًا للتأكد من صحتها
              const img = new Image();
              img.onload = () => {
                console.log('تم تحميل صورة الشعار بنجاح');
              };
              img.onerror = () => {
                console.error('فشل في تحميل صورة الشعار');
                // استخدام صورة فارغة في حالة الفشل
                loadedSettings.logoUrl = '';
              };
              img.src = loadedSettings.logoUrl;
            } else {
              console.warn('تنسيق صورة الشعار غير صالح، سيتم استخدام قيمة فارغة');
              loadedSettings.logoUrl = '';
            }
          }

          // تحديث حالة الإعدادات
          setSettings(prevSettings => ({
            ...prevSettings,
            ...loadedSettings
          }));
        } else {
          console.warn('واجهة الإعدادات غير متوفرة، سيتم استخدام التخزين المحلي');

          // استخدام التخزين المحلي كبديل
          const storedSettings = localStorage.getItem('settings');
          if (storedSettings) {
            try {
              const parsedSettings = JSON.parse(storedSettings);

              // معالجة خاصة لصورة الشعار من التخزين المحلي
              if (parsedSettings.logoUrl) {
                console.log('تم العثور على إعداد صورة الشعار في التخزين المحلي، طول البيانات:',
                  parsedSettings.logoUrl.length);

                // التحقق من صحة البيانات
                if (typeof parsedSettings.logoUrl === 'string' && parsedSettings.logoUrl.startsWith('data:image/')) {
                  console.log('صورة الشعار في التخزين المحلي صالحة');
                } else {
                  console.warn('تنسيق صورة الشعار في التخزين المحلي غير صالح، سيتم استخدام قيمة فارغة');
                  parsedSettings.logoUrl = '';
                }
              }

              setSettings(prevSettings => ({
                ...prevSettings,
                ...parsedSettings
              }));
            } catch (error) {
              console.error('خطأ في تحليل الإعدادات المخزنة:', error);
            }
          }
        }
      } catch (error) {
        console.error('خطأ في تحميل الإعدادات:', error);

        // استخدام التخزين المحلي كبديل في حالة الخطأ
        const storedSettings = localStorage.getItem('settings');
        if (storedSettings) {
          try {
            const parsedSettings = JSON.parse(storedSettings);
            setSettings(prevSettings => ({
              ...prevSettings,
              ...parsedSettings
            }));
          } catch (parseError) {
            console.error('خطأ في تحليل الإعدادات المخزنة:', parseError);
          }
        }
      }
    };

    // تحميل الإعدادات
    loadSettings();

    // تهيئة نظام السطوع المحسن
    initBrightness([
      '.main-content-full',
      '.navbar',
      '.card',
      '.table',
      '.stat-card',
      '.dashboard-welcome',
      '.page-header'
    ]);
  }, []);

  // تحديث إعدادات النظام
  const updateSettings = async (newSettings) => {
    console.log('تحديث الإعدادات - الإعدادات الحالية:', settings);
    console.log('تحديث الإعدادات - الإعدادات الجديدة:', newSettings);

    // التحقق من وجود تغييرات في حالة تفعيل الربط
    if (newSettings.integrationEnabled !== undefined &&
        newSettings.integrationEnabled !== settings.integrationEnabled) {
      console.log('تغيير حالة تفعيل الربط من',
        settings.integrationEnabled, 'إلى', newSettings.integrationEnabled);
    }

    // التحقق من وجود تغييرات في شعار المنظومة
    if (newSettings.logoUrl !== undefined) {
      console.log('تم تحديث شعار المنظومة، طول البيانات:',
        newSettings.logoUrl ? newSettings.logoUrl.length : 0);
    }

    // تحديث جميع الإعدادات بما في ذلك اسم المنظومة وشعارها
    const updatedSettings = {
      ...settings,
      ...newSettings
    };

    console.log('الإعدادات المحدثة النهائية (بدون عرض البيانات الكبيرة):',
      Object.keys(updatedSettings).reduce((acc, key) => {
        if (key === 'logoUrl') {
          acc[key] = updatedSettings[key] ? `[Data URL - ${updatedSettings[key].length} bytes]` : '';
        } else {
          acc[key] = updatedSettings[key];
        }
        return acc;
      }, {}));

    try {
      // استخدام واجهة الإعدادات الجديدة لتحديث الإعدادات
      if (window.api && window.api.settings) {
        console.log('جاري تحديث الإعدادات في قاعدة البيانات...');

        // تحقق من وجود واجهة التحديث
        if (typeof window.api.settings.update !== 'function') {
          console.error('خطأ: وظيفة تحديث الإعدادات غير متوفرة في واجهة API');
          throw new Error('وظيفة تحديث الإعدادات غير متوفرة');
        }

        const result = await window.api.settings.update(updatedSettings);
        console.log('نتيجة تحديث الإعدادات:', result);

        if (result.success) {
          console.log('تم تحديث الإعدادات في قاعدة البيانات بنجاح');
        } else {
          console.error('خطأ في تحديث الإعدادات في قاعدة البيانات:', result.error);
          throw new Error(`فشل تحديث الإعدادات: ${result.error}`);
        }
      } else {
        console.warn('واجهة الإعدادات غير متوفرة، سيتم استخدام التخزين المحلي فقط');
      }

      // حفظ الإعدادات في التخزين المحلي أيضاً للتوافق
      try {
        localStorage.setItem('settings', JSON.stringify(updatedSettings));
        console.log('تم حفظ الإعدادات في التخزين المحلي بنجاح');
      } catch (storageError) {
        console.error('خطأ في حفظ الإعدادات في التخزين المحلي:', storageError);
        // استمر في التنفيذ حتى مع وجود خطأ في التخزين المحلي
      }
    } catch (error) {
      console.error('خطأ في حفظ الإعدادات:', error);
      throw error; // إعادة رمي الخطأ للتعامل معه في المستدعي
    }

    // تحديث حالة الإعدادات
    setSettings(updatedSettings);
    console.log('تم تحديث حالة الإعدادات');

    return updatedSettings;
  };

  // القيمة التي سيتم توفيرها للمكونات
  const value = {
    settings,
    updateSettings
  };

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  );
};

export default SettingsProvider;
