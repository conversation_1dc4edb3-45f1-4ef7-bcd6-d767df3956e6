/**
 * ملف اختبار لتشخيص مشكلة حساب وتسجيل الأرباح
 */

// استيراد الوحدات المطلوبة
const { calculateProfit, calculateProfitWithTransport } = require('./utils/profitCalculator');

// استخدام نفس آلية قاعدة البيانات المستخدمة في التطبيق
const dbManager = require('./database-singleton').getInstance();
const db = dbManager.getConnection();

console.log('=== اختبار تشخيص مشكلة الأرباح ===\n');

// 1. فحص بنية جدول transactions
console.log('1. فحص بنية جدول transactions:');
try {
  const tableInfo = db.prepare("PRAGMA table_info(transactions)").all();
  console.log('أعمدة جدول transactions:');
  tableInfo.forEach(column => {
    console.log(`  - ${column.name}: ${column.type} (${column.notnull ? 'مطلوب' : 'اختياري'})`);
  });

  // التحقق من وجود حقل profit
  const profitColumn = tableInfo.find(col => col.name === 'profit');
  if (profitColumn) {
    console.log('✅ حقل profit موجود في الجدول');
  } else {
    console.log('❌ حقل profit غير موجود في الجدول');
  }
} catch (error) {
  console.error('خطأ في فحص بنية الجدول:', error.message);
}

console.log('\n2. فحص بنية جدول cashbox:');
try {
  const cashboxInfo = db.prepare("PRAGMA table_info(cashbox)").all();
  console.log('أعمدة جدول cashbox:');
  cashboxInfo.forEach(column => {
    console.log(`  - ${column.name}: ${column.type} (${column.notnull ? 'مطلوب' : 'اختياري'})`);
  });

  // التحقق من وجود حقل profit_total
  const profitTotalColumn = cashboxInfo.find(col => col.name === 'profit_total');
  if (profitTotalColumn) {
    console.log('✅ حقل profit_total موجود في الجدول');
  } else {
    console.log('❌ حقل profit_total غير موجود في الجدول');
  }
} catch (error) {
  console.error('خطأ في فحص بنية جدول cashbox:', error.message);
}

console.log('\n3. فحص البيانات الحالية:');

// فحص معاملات البيع الحالية
try {
  const salesTransactions = db.prepare(`
    SELECT id, transaction_id, item_id, quantity, price, selling_price, total_price, profit, transaction_date
    FROM transactions
    WHERE transaction_type = 'sale'
    ORDER BY transaction_date DESC
    LIMIT 5
  `).all();

  console.log('آخر 5 معاملات بيع:');
  if (salesTransactions.length === 0) {
    console.log('  لا توجد معاملات بيع');
  } else {
    salesTransactions.forEach(transaction => {
      console.log(`  - المعاملة ${transaction.transaction_id}:`);
      console.log(`    الكمية: ${transaction.quantity}, سعر البيع: ${transaction.selling_price}, سعر الشراء: ${transaction.price}`);
      console.log(`    المبلغ الإجمالي: ${transaction.total_price}, الربح المسجل: ${transaction.profit}`);

      // حساب الربح المتوقع
      const expectedProfit = calculateProfit(transaction.selling_price, transaction.price, transaction.quantity);
      console.log(`    الربح المتوقع: ${expectedProfit}`);

      if (Math.abs(transaction.profit - expectedProfit) > 0.01) {
        console.log(`    ⚠️  هناك اختلاف في حساب الربح!`);
      } else {
        console.log(`    ✅ الربح محسوب بشكل صحيح`);
      }
    });
  }
} catch (error) {
  console.error('خطأ في فحص معاملات البيع:', error.message);
}

// فحص بيانات الخزينة
try {
  const cashbox = db.prepare('SELECT * FROM cashbox LIMIT 1').get();

  console.log('\nبيانات الخزينة الحالية:');
  if (cashbox) {
    console.log(`  الرصيد الافتتاحي: ${cashbox.initial_balance}`);
    console.log(`  الرصيد الحالي: ${cashbox.current_balance}`);
    console.log(`  إجمالي المبيعات: ${cashbox.sales_total}`);
    console.log(`  إجمالي المشتريات: ${cashbox.purchases_total}`);
    console.log(`  إجمالي الأرباح: ${cashbox.profit_total}`);

    // حساب إجمالي الأرباح من المعاملات
    const calculatedProfit = db.prepare(`
      SELECT COALESCE(SUM(profit), 0) as total_profit
      FROM transactions
      WHERE transaction_type = 'sale'
    `).get();

    console.log(`  إجمالي الأرباح المحسوب من المعاملات: ${calculatedProfit.total_profit}`);

    if (Math.abs(cashbox.profit_total - calculatedProfit.total_profit) > 0.01) {
      console.log(`  ⚠️  هناك اختلاف بين إجمالي الأرباح في الخزينة والمحسوب من المعاملات!`);
    } else {
      console.log(`  ✅ إجمالي الأرباح متطابق`);
    }
  } else {
    console.log('  لا توجد بيانات خزينة');
  }
} catch (error) {
  console.error('خطأ في فحص بيانات الخزينة:', error.message);
}

console.log('\n4. اختبار دوال حساب الأرباح:');

// اختبار دالة calculateProfit
const testCases = [
  { sellingPrice: 1200, costPrice: 1000, quantity: 1, expected: 200 },
  { sellingPrice: 150, costPrice: 100, quantity: 5, expected: 250 },
  { sellingPrice: 50, costPrice: 60, quantity: 2, expected: 0 }, // خسارة تصبح 0
];

testCases.forEach((testCase, index) => {
  const result = calculateProfit(testCase.sellingPrice, testCase.costPrice, testCase.quantity);
  console.log(`اختبار ${index + 1}: (${testCase.sellingPrice} - ${testCase.costPrice}) × ${testCase.quantity} = ${result}`);
  console.log(`المتوقع: ${testCase.expected}, النتيجة: ${result}`);

  if (Math.abs(result - testCase.expected) < 0.01) {
    console.log('✅ النتيجة صحيحة');
  } else {
    console.log('❌ النتيجة خاطئة');
  }
  console.log('');
});

// اختبار دالة calculateProfitWithTransport
console.log('اختبار حساب الأرباح مع مصاريف النقل:');
const transportTestCases = [
  { sellingPrice: 1200, costPrice: 1000, quantity: 1, transportCost: 50, expected: 150 },
  { sellingPrice: 150, costPrice: 100, quantity: 5, transportCost: 10, expected: 200 },
];

transportTestCases.forEach((testCase, index) => {
  const result = calculateProfitWithTransport(
    testCase.sellingPrice,
    testCase.costPrice,
    testCase.quantity,
    testCase.transportCost
  );
  console.log(`اختبار ${index + 1}: (${testCase.sellingPrice} - ${testCase.costPrice} - ${testCase.transportCost}) × ${testCase.quantity} = ${result}`);
  console.log(`المتوقع: ${testCase.expected}, النتيجة: ${result}`);

  if (Math.abs(result - testCase.expected) < 0.01) {
    console.log('✅ النتيجة صحيحة');
  } else {
    console.log('❌ النتيجة خاطئة');
  }
  console.log('');
});

console.log('=== انتهاء الاختبار ===');

// لا نحتاج لإغلاق قاعدة البيانات لأننا نستخدم database-singleton
