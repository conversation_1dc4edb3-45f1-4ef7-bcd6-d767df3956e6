/* أنماط الرسوم البيانية */
.dashboard-charts {
  margin-bottom: var(--spacing-xl);
}

.charts-row {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.chart-wrapper {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: transform 0.3s, box-shadow 0.3s;
  flex: 1;
}

.chart-wrapper:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.chart-content {
  padding: var(--spacing-md);
  position: relative;
}

/* تجاوب الرسوم البيانية مع أحجام الشاشات المختلفة */
@media (max-width: 1200px) {
  .charts-row {
    flex-direction: column;
  }
  
  .chart-wrapper {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .chart-content {
    height: 250px !important;
  }
}

@media (max-width: 576px) {
  .chart-content {
    height: 200px !important;
  }
}

/* أنماط الطباعة */
@media print {
  .dashboard-charts {
    page-break-inside: avoid;
  }
  
  .charts-row {
    display: block;
  }
  
  .chart-wrapper {
    width: 100%;
    margin-bottom: 20px;
    box-shadow: none;
    border: 1px solid #ddd;
  }
  
  .chart-content {
    height: 200px !important;
  }
}
