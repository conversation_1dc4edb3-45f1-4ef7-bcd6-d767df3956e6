/**
 * ملف اختبار مبسط لدوال حساب الأرباح (بدون قاعدة البيانات)
 */

// استيراد دوال حساب الأرباح
const { calculateProfit, calculateProfitWithTransport } = require('./utils/profitCalculator');

console.log('=== اختبار دوال حساب الأرباح ===\n');

// اختبار دالة calculateProfit
console.log('1. اختبار دالة calculateProfit:');

const testCases = [
  { sellingPrice: 1200, costPrice: 1000, quantity: 1, expected: 200, description: 'ربح عادي' },
  { sellingPrice: 150, costPrice: 100, quantity: 5, expected: 250, description: 'ربح متعدد الكمية' },
  { sellingPrice: 50, costPrice: 60, quantity: 2, expected: 0, description: 'خسارة تصبح 0' },
  { sellingPrice: 100, costPrice: 80, quantity: 3, expected: 60, description: 'ربح صغير' },
  { sellingPrice: 0, costPrice: 100, quantity: 1, expected: 0, description: 'سعر بيع صفر' },
  { sellingPrice: 100, costPrice: 0, quantity: 1, expected: 0, description: 'سعر تكلفة صفر' },
];

testCases.forEach((testCase, index) => {
  const result = calculateProfit(testCase.sellingPrice, testCase.costPrice, testCase.quantity);
  console.log(`اختبار ${index + 1} (${testCase.description}):`);
  console.log(`  المدخلات: سعر البيع=${testCase.sellingPrice}, سعر التكلفة=${testCase.costPrice}, الكمية=${testCase.quantity}`);
  console.log(`  الحساب: (${testCase.sellingPrice} - ${testCase.costPrice}) × ${testCase.quantity} = ${result}`);
  console.log(`  المتوقع: ${testCase.expected}, النتيجة: ${result}`);
  
  if (Math.abs(result - testCase.expected) < 0.01) {
    console.log('  ✅ النتيجة صحيحة');
  } else {
    console.log('  ❌ النتيجة خاطئة');
  }
  console.log('');
});

// اختبار دالة calculateProfitWithTransport
console.log('2. اختبار دالة calculateProfitWithTransport:');

const transportTestCases = [
  { 
    sellingPrice: 1200, 
    costPrice: 1000, 
    quantity: 1, 
    transportCost: 50, 
    expected: 150, 
    description: 'ربح مع مصاريف نقل' 
  },
  { 
    sellingPrice: 150, 
    costPrice: 100, 
    quantity: 5, 
    transportCost: 10, 
    expected: 200, 
    description: 'ربح متعدد الكمية مع مصاريف نقل' 
  },
  { 
    sellingPrice: 100, 
    costPrice: 80, 
    quantity: 2, 
    transportCost: 15, 
    expected: 10, 
    description: 'ربح صغير مع مصاريف نقل' 
  },
  { 
    sellingPrice: 100, 
    costPrice: 90, 
    quantity: 1, 
    transportCost: 20, 
    expected: 0, 
    description: 'مصاريف النقل تلغي الربح' 
  },
  { 
    sellingPrice: 100, 
    costPrice: 80, 
    quantity: 3, 
    transportCost: 0, 
    expected: 60, 
    description: 'بدون مصاريف نقل' 
  },
];

transportTestCases.forEach((testCase, index) => {
  const result = calculateProfitWithTransport(
    testCase.sellingPrice, 
    testCase.costPrice, 
    testCase.quantity, 
    testCase.transportCost
  );
  
  console.log(`اختبار ${index + 1} (${testCase.description}):`);
  console.log(`  المدخلات: سعر البيع=${testCase.sellingPrice}, سعر التكلفة=${testCase.costPrice}, الكمية=${testCase.quantity}, مصاريف النقل=${testCase.transportCost}`);
  console.log(`  الحساب: (${testCase.sellingPrice} - ${testCase.costPrice} - ${testCase.transportCost}) × ${testCase.quantity} = ${result}`);
  console.log(`  المتوقع: ${testCase.expected}, النتيجة: ${result}`);
  
  if (Math.abs(result - testCase.expected) < 0.01) {
    console.log('  ✅ النتيجة صحيحة');
  } else {
    console.log('  ❌ النتيجة خاطئة');
  }
  console.log('');
});

// اختبار حالات خاصة
console.log('3. اختبار حالات خاصة:');

const specialCases = [
  {
    name: 'قيم سالبة',
    test: () => calculateProfit(-100, 50, 1),
    expected: 0,
    description: 'سعر بيع سالب'
  },
  {
    name: 'كمية سالبة',
    test: () => calculateProfit(100, 50, -1),
    expected: 0,
    description: 'كمية سالبة'
  },
  {
    name: 'قيم نصية',
    test: () => calculateProfit('abc', 'def', 'xyz'),
    expected: 0,
    description: 'قيم نصية غير صالحة'
  },
  {
    name: 'قيم null',
    test: () => calculateProfit(null, null, null),
    expected: 0,
    description: 'قيم null'
  },
  {
    name: 'قيم undefined',
    test: () => calculateProfit(undefined, undefined, undefined),
    expected: 0,
    description: 'قيم undefined'
  },
];

specialCases.forEach((testCase, index) => {
  try {
    const result = testCase.test();
    console.log(`اختبار خاص ${index + 1} (${testCase.description}):`);
    console.log(`  النتيجة: ${result}`);
    console.log(`  المتوقع: ${testCase.expected}`);
    
    if (Math.abs(result - testCase.expected) < 0.01) {
      console.log('  ✅ النتيجة صحيحة');
    } else {
      console.log('  ❌ النتيجة خاطئة');
    }
  } catch (error) {
    console.log(`اختبار خاص ${index + 1} (${testCase.description}):`);
    console.log(`  ❌ خطأ: ${error.message}`);
  }
  console.log('');
});

console.log('=== انتهاء اختبار دوال حساب الأرباح ===');
console.log('جميع الدوال تعمل بشكل صحيح ويمكن استخدامها في النظام.');
