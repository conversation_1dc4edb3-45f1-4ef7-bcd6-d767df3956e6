/**
 * وحدة مساعدة لتصدير التقارير كملفات PDF باستخدام الصور
 * تقوم بتحويل التقرير إلى صورة ثم إضافتها إلى ملف PDF
 */

import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';
import { setupArabicSupport } from './arabicPdfSupport';

/**
 * تصدير عنصر HTML كملف PDF باستخدام الصور
 * @param {HTMLElement} element - عنصر HTML المراد تصديره
 * @param {string} filename - اسم الملف
 * @param {Object} options - خيارات التصدير
 */
export async function exportElementToPDF(element, filename, options = {}) {
  try {
    if (!element) {
      console.error('لم يتم تحديد عنصر HTML للتصدير');
      return;
    }

    const { orientation = 'portrait', format = 'a4', scale = 2 } = options;

    // إنشاء مستند PDF جديد
    const doc = new jsPDF({
      orientation,
      unit: 'mm',
      format,
      putOnlyUsedFonts: true,
      compress: true
    });

    // إعداد دعم اللغة العربية
    setupArabicSupport(doc);

    // تحويل عنصر HTML إلى صورة
    const canvas = await html2canvas(element, {
      scale,
      useCORS: true,
      allowTaint: true,
      logging: false
    });

    const imgData = canvas.toDataURL('image/png');
    const imgWidth = doc.internal.pageSize.getWidth();
    const imgHeight = (canvas.height * imgWidth) / canvas.width;

    // إضافة الصورة إلى المستند
    doc.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);

    // حفظ الملف
    doc.save(filename);

    return true;
  } catch (error) {
    console.error('خطأ في تصدير عنصر HTML كملف PDF:', error);
    alert('حدث خطأ أثناء تصدير الملف. يرجى المحاولة مرة أخرى.');
    return false;
  }
}

/**
 * طباعة عنصر HTML باستخدام الصور
 * @param {HTMLElement} element - عنصر HTML المراد طباعته
 * @param {Object} options - خيارات الطباعة
 */
export async function printElementAsImage(element, options = {}) {
  try {
    if (!element) {
      console.error('لم يتم تحديد عنصر HTML للطباعة');
      return;
    }

    const { orientation = 'portrait', format = 'a4', scale = 2 } = options;

    // إنشاء مستند PDF جديد
    const doc = new jsPDF({
      orientation,
      unit: 'mm',
      format,
      putOnlyUsedFonts: true,
      compress: true
    });

    // إعداد دعم اللغة العربية
    setupArabicSupport(doc);

    // تحويل عنصر HTML إلى صورة
    const canvas = await html2canvas(element, {
      scale,
      useCORS: true,
      allowTaint: true,
      logging: false
    });

    const imgData = canvas.toDataURL('image/png');
    const imgWidth = doc.internal.pageSize.getWidth();
    const imgHeight = (canvas.height * imgWidth) / canvas.width;

    // إضافة الصورة إلى المستند
    doc.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);

    // طباعة المستند
    doc.autoPrint();
    window.open(doc.output('bloburl'), '_blank');

    return true;
  } catch (error) {
    console.error('خطأ في طباعة عنصر HTML:', error);
    alert('حدث خطأ أثناء محاولة طباعة العنصر. يرجى المحاولة مرة أخرى.');
    return false;
  }
}

export default {
  exportElementToPDF,
  printElementAsImage
};
