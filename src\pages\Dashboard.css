/* أنماط صفحة لوحة التحكم */
.dashboard-page {
  padding: 20px;
}

.dashboard-welcome {
  margin-bottom: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.dashboard-welcome h1 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 10px;
}

.dashboard-welcome p {
  font-size: 1.1rem;
  color: var(--text-light);
}

.system-actions {
  display: flex;
  gap: 10px;
}

.alert {
  padding: 12px 15px;
  margin-bottom: 20px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.alert-warning {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeeba;
}

/* شبكة الإحصائيات */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card-wrapper {
  height: 100%;
}

.stat-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  height: 100%;
  transition: transform 0.2s;
  text-align: center;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-bottom: 15px;
  background-color: rgba(var(--primary-rgb), 0.1);
  color: var(--primary-color);
  transition: transform 0.3s;
}

.stat-card:hover .stat-card-icon {
  transform: scale(1.1);
}

.stat-card-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 5px;
}

.stat-card-title {
  font-size: 1rem;
  color: var(--text-light);
  margin-bottom: 5px;
}

.stat-card-subtitle {
  font-size: 0.85rem;
  color: var(--text-muted);
  margin-bottom: 10px;
}

/* الرسوم البيانية */
.dashboard-charts {
  margin-bottom: 30px;
}

.charts-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.chart-card {
  height: 100%;
}

.chart-content {
  width: 100%;
  position: relative;
}

/* بطاقة إحصائيات المخزون */
.inventory-stats-card {
  margin-bottom: 30px;
}

/* بطاقة آخر المعاملات */
.recent-transactions-card {
  margin-bottom: 30px;
}

.badge {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.badge-primary {
  background-color: rgba(var(--primary-rgb), 0.1);
  color: var(--primary-color);
}

.badge-success {
  background-color: rgba(var(--success-rgb), 0.1);
  color: var(--success-color);
}

.badge-secondary {
  background-color: rgba(108, 117, 125, 0.1);
  color: #6c757d;
}

.customer-name {
  font-size: 0.9rem;
  color: var(--text-light);
}

/* تنسيقات للشاشات الصغيرة */
@media (max-width: 992px) {
  .charts-row {
    flex-direction: column;
  }

  .chart-wrapper {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .dashboard-welcome h1 {
    font-size: 1.5rem;
  }

  .dashboard-welcome p {
    font-size: 1rem;
  }

  .stat-card-value {
    font-size: 1.5rem;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }
}
