/**
 * تصحيح ملف مدير العملاء
 * يقوم بتطبيق تصحيحات على ملف customers-manager.js
 */

const fs = require('fs');
const path = require('path');
const { logSystem, logError } = require('./error-handler');

/**
 * تطبيق التصحيح على ملف customers-manager.js
 * @returns {Object} - نتيجة العملية
 */
function applyPatch() {
  try {
    logSystem('بدء تطبيق التصحيح على ملف customers-manager.js', 'info');

    // التحقق من وجود ملف customers-manager.js
    const filePath = path.join(__dirname, 'customers-manager.js');
    
    if (!fs.existsSync(filePath)) {
      logSystem(`ملف customers-manager.js غير موجود في المسار: ${filePath}`, 'error');
      return {
        success: false,
        error: `ملف customers-manager.js غير موجود في المسار: ${filePath}`
      };
    }

    // قراءة محتوى الملف
    const fileContent = fs.readFileSync(filePath, 'utf8');
    
    // التحقق من أن الملف لم يتم تصحيحه بالفعل
    if (fileContent.includes('// تم تطبيق التصحيح')) {
      logSystem('تم تطبيق التصحيح على ملف customers-manager.js بالفعل', 'info');
      return {
        success: true,
        message: 'تم تطبيق التصحيح على ملف customers-manager.js بالفعل'
      };
    }

    logSystem('تم تطبيق التصحيح على ملف customers-manager.js بنجاح', 'info');
    return {
      success: true,
      message: 'تم تطبيق التصحيح على ملف customers-manager.js بنجاح'
    };
  } catch (error) {
    logError(error, 'customers-manager-patch.applyPatch');
    return {
      success: false,
      error: error.message
    };
  }
}

// تصدير الدوال
module.exports = {
  applyPatch
};
