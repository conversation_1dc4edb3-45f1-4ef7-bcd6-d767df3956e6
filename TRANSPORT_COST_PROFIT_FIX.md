# إصلاح مشكلة مصاريف النقل والأرباح

## المشكلة
كانت هناك مشكلة في النظام حيث أن مصاريف النقل في عمليات الشراء تؤثر على حساب الأرباح، بينما المطلوب هو:

1. **في عمليات البيع**: مصاريف النقل يجب أن تخصم من الأرباح
2. **في عمليات الشراء**: مصاريف النقل يجب أن تخصم من الرصيد الحالي فقط ولا تؤثر على الأرباح

## الحل المطبق

### 1. تحديث unified-transaction-manager.js

#### أ. إصلاح حساب الربح في عمليات البيع
```javascript
if (transaction_type === 'sale' && selling_price > 0) {
  // حساب الربح في حالة البيع مع خصم مصاريف النقل
  const inventoryStmt = db.prepare('SELECT avg_price FROM inventory WHERE item_id = ?');
  const inventory = inventoryStmt.get(item_id);
  const avgPrice = inventory ? inventory.avg_price : 0;

  // حساب مصاريف النقل المخصصة لهذا الصنف
  let transportCostPerUnit = 0;
  try {
    // الحصول على جميع معاملات الشراء للصنف التي تحتوي على مصاريف نقل
    const purchaseStmt = db.prepare(`
      SELECT quantity, transport_cost
      FROM transactions
      WHERE item_id = ? AND transaction_type = 'purchase' AND transport_cost > 0
    `);

    const purchases = purchaseStmt.all(item_id);
    if (purchases && purchases.length > 0) {
      let totalPurchasedQuantity = 0;
      let totalTransportCost = 0;

      purchases.forEach(purchase => {
        totalPurchasedQuantity += purchase.quantity;
        totalTransportCost += purchase.transport_cost;
      });

      if (totalPurchasedQuantity > 0) {
        transportCostPerUnit = totalTransportCost / totalPurchasedQuantity;
      }
    }
  } catch (error) {
    console.error('خطأ في حساب مصاريف النقل:', error);
    transportCostPerUnit = 0;
  }

  // استخدام وظيفة حساب الربح مع مصاريف النقل
  profit = calculateProfitWithTransport(
    selling_price,
    avgPrice,
    quantity,
    transportCostPerUnit
  );
}
```

#### ب. تحديث التعليقات في recalculateTotalProfits()
```javascript
function recalculateTotalProfits() {
  try {
    console.log('[PROFIT-RECALC] بدء إعادة حساب إجمالي الأرباح...');
    console.log('[PROFIT-RECALC] حساب الأرباح من معاملات البيع فقط (مصاريف النقل مخصومة من الأرباح في البيع)');

    // حساب إجمالي الأرباح من معاملات البيع فقط
    // الربح في معاملات البيع يتضمن بالفعل خصم مصاريف النقل
    // مصاريف النقل في المشتريات تخصم من الرصيد الحالي فقط ولا تؤثر على الأرباح
    const totalProfitStmt = db.prepare(`
      SELECT COALESCE(SUM(profit), 0) as total_profit
      FROM transactions
      WHERE transaction_type = 'sale'
    `);

    const result = totalProfitStmt.get();
    const totalProfit = Number(result ? result.total_profit : 0);

    console.log(`[PROFIT-RECALC] إجمالي الأرباح المحسوب من معاملات البيع: ${totalProfit}`);
    console.log(`[PROFIT-RECALC] ملاحظة: في البيع - مصاريف النقل تخصم من الأرباح، في الشراء - تخصم من الرصيد الحالي فقط`);

    return totalProfit;
  } catch (error) {
    console.error('[PROFIT-RECALC] خطأ في إعادة حساب الأرباح:', error);
    return 0;
  }
}
```

#### ج. تحديث التعليقات في معالجة عمليات الشراء
```javascript
// تحديث إجمالي الأرباح تلقائياً بعد معاملة الشراء
// ملاحظة: عمليات الشراء لا تؤثر على الأرباح مباشرة، لكن قد تؤثر على حساب متوسط التكلفة
// مما قد يؤثر على حساب الأرباح في المبيعات المستقبلية
// مصاريف النقل في المشتريات تخصم من الرصيد الحالي فقط ولا تؤثر على الأرباح
try {
  console.log(`[PROFIT-FIX] تحديث إجمالي الأرباح تلقائياً بعد معاملة الشراء...`);
  console.log(`[PROFIT-FIX] ملاحظة: مصاريف النقل في المشتريات تخصم من الرصيد الحالي فقط ولا تؤثر على الأرباح`);
  // ...
}
```

## السلوك الجديد

### عمليات الشراء
- **الرصيد الحالي**: يخصم منه (مبلغ الشراء + مصاريف النقل)
- **إجمالي المشتريات**: يضاف إليه مبلغ الشراء فقط
- **إجمالي مصاريف النقل**: يضاف إليه مصاريف النقل
- **الأرباح**: لا تتأثر مطلقاً

### عمليات البيع
- **الرصيد الحالي**: يضاف إليه مبلغ البيع
- **إجمالي المبيعات**: يضاف إليه مبلغ البيع
- **الأرباح**: تحسب كالتالي: (سعر البيع - سعر الشراء - مصاريف النقل لكل وحدة) × الكمية

## مثال عملي

### شراء صنف
- الكمية: 10 وحدات
- سعر الوحدة: 100
- مصاريف النقل: 50
- **التأثير على الخزينة**:
  - الرصيد الحالي: -1050 (1000 + 50)
  - إجمالي المشتريات: +1000
  - إجمالي مصاريف النقل: +50
  - الأرباح: لا تتأثر

### بيع نفس الصنف
- الكمية: 5 وحدات
- سعر البيع: 150 للوحدة
- مصاريف النقل لكل وحدة: 50/10 = 5
- **حساب الربح**: (150 - 100 - 5) × 5 = 225
- **التأثير على الخزينة**:
  - الرصيد الحالي: +750 (5 × 150)
  - إجمالي المبيعات: +750
  - الأرباح: +225

## الفوائد
1. **دقة في حساب الأرباح**: الأرباح تعكس التكلفة الحقيقية للمنتج شاملة مصاريف النقل
2. **وضوح في المحاسبة**: مصاريف النقل في الشراء تؤثر على السيولة فقط، لا على الربحية
3. **تتبع دقيق للتكاليف**: مصاريف النقل تتوزع على الأصناف المشتراة وتخصم من أرباح البيع

## الاختبارات
تم إنشاء ملف `test-transport-cost-fix.js` يحتوي على اختبارات شاملة للتأكد من:
- صحة حساب الأرباح في عمليات البيع مع خصم مصاريف النقل
- عدم تأثير مصاريف النقل على الأرباح في عمليات الشراء
- دقة حساب الأرباح الإجمالية من معاملات البيع فقط
