import React, { Component } from 'react';
import { FaExclamationTriangle } from 'react-icons/fa';
import '../../assets/css/error-boundary.css';

/**
 * مكون حدود الخطأ
 * يلتقط الأخطاء في شجرة المكونات ويعرض واجهة خطأ بديلة
 */
class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error) {
    // تحديث الحالة لعرض واجهة الخطأ البديلة
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // تسجيل تفاصيل الخطأ
    this.setState({
      error: error,
      errorInfo: errorInfo
    });
    
    // إذا كان window.api متاحًا، استخدمه لتسجيل الخطأ
    if (window.api && window.api.logger) {
      window.api.logger.error('React Error Boundary caught an error', {
        error: error.toString(),
        componentStack: errorInfo.componentStack
      });
    } else {
      console.error('React Error:', error);
      console.error('Component Stack:', errorInfo.componentStack);
    }
  }

  handleReset = () => {
    this.setState({ hasError: false, error: null, errorInfo: null });
    
    // محاولة إعادة تحميل المكون
    if (this.props.onReset) {
      this.props.onReset();
    }
  }

  handleReportError = () => {
    // إنشاء تقرير خطأ
    const errorReport = {
      error: this.state.error ? this.state.error.toString() : 'Unknown error',
      componentStack: this.state.errorInfo ? this.state.errorInfo.componentStack : '',
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString()
    };
    
    // إرسال تقرير الخطأ إلى الخادم أو حفظه محليًا
    if (window.api && window.api.reportError) {
      window.api.reportError(errorReport)
        .then(() => {
          alert('تم إرسال تقرير الخطأ بنجاح. شكرًا لمساعدتك في تحسين التطبيق!');
        })
        .catch(err => {
          console.error('فشل في إرسال تقرير الخطأ:', err);
          alert('فشل في إرسال تقرير الخطأ. يرجى المحاولة مرة أخرى لاحقًا.');
        });
    } else {
      console.log('تقرير الخطأ:', errorReport);
      alert('تم تسجيل تقرير الخطأ محليًا.');
    }
  }

  render() {
    if (this.state.hasError) {
      // عرض واجهة الخطأ البديلة
      return (
        <div className="error-boundary">
          <div className="error-container">
            <div className="error-icon">
              <FaExclamationTriangle />
            </div>
            <h2>عذرًا، حدث خطأ غير متوقع</h2>
            <p>نعتذر عن هذا الخطأ. يمكنك محاولة إعادة تحميل هذا الجزء أو إرسال تقرير بالخطأ.</p>
            
            {this.props.showDetails && this.state.error && (
              <div className="error-details">
                <h3>تفاصيل الخطأ:</h3>
                <p>{this.state.error.toString()}</p>
                {this.state.errorInfo && (
                  <pre>{this.state.errorInfo.componentStack}</pre>
                )}
              </div>
            )}
            
            <div className="error-actions">
              <button className="btn-primary" onClick={this.handleReset}>
                إعادة تحميل
              </button>
              <button className="btn-secondary" onClick={this.handleReportError}>
                إرسال تقرير
              </button>
            </div>
          </div>
        </div>
      );
    }

    // إذا لم يكن هناك خطأ، عرض المكونات الفرعية بشكل طبيعي
    return this.props.children;
  }
}

export default ErrorBoundary;
