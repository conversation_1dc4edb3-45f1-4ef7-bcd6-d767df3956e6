/* أنماط صفحة الأصناف */

/* تنبيه الصنف المكرر */
.duplicate-item-error {
  display: flex;
  align-items: center;
  background-color: #fff3cd;
  border: 1px solid #ffeeba;
  border-right: 4px solid #e74c3c;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 20px;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
  }
}

.duplicate-item-error-icon {
  font-size: 24px;
  color: #e74c3c;
  margin-left: 12px;
  display: flex;
  align-items: center;
}

.duplicate-item-error-message {
  flex: 1;
  color: #856404;
  font-size: 14px;
  line-height: 1.5;
}

.duplicate-item-error-message strong {
  font-weight: bold;
  color: #e74c3c;
}

/* حقل الإدخال مع خطأ */
.error-input {
  border-color: #e74c3c !important;
  background-color: #fff8f8 !important;
}

.error-input:focus {
  box-shadow: 0 0 0 0.2rem rgba(231, 76, 60, 0.25) !important;
}

/* حاوية الإكمال التلقائي */
.autocomplete-container {
  position: relative;
  width: 100%;
}

.error-container .item-autocomplete-input {
  border-color: #e74c3c !important;
  background-color: #fff8f8 !important;
}

.error-container .item-autocomplete-input:focus {
  box-shadow: 0 0 0 0.2rem rgba(231, 76, 60, 0.25) !important;
}

/* زر التبديل بين الإكمال التلقائي والإدخال اليدوي */
.switch-input-button {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #777;
  font-size: 16px;
  cursor: pointer;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s;
  z-index: 10;
}

.switch-input-button:hover {
  color: #3498db;
}

/* حاوية الإدخال اليدوي */
.manual-input-container {
  position: relative;
  width: 100%;
}

/* تخصيص مكون الإكمال التلقائي في نموذج الأصناف */
.item-form-autocomplete .item-autocomplete-input {
  height: 38px;
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 4px;
}

.item-form-autocomplete .item-autocomplete-suggestions-container-open {
  z-index: 1100;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* أنماط أزرار التحديث التلقائي */
.auto-refresh-control {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.auto-refresh-button, .manual-refresh-button {
  padding: 6px 12px;
  border-radius: 4px;
  border: 1px solid #ccc;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.auto-refresh-button.enabled {
  background-color: #4caf50;
  color: white;
  border-color: #388e3c;
}

.auto-refresh-button.disabled {
  background-color: #f44336;
  color: white;
  border-color: #d32f2f;
}

.manual-refresh-button {
  background-color: #2196f3;
  color: white;
  border-color: #1976d2;
}

.auto-refresh-button:hover, .manual-refresh-button:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}
