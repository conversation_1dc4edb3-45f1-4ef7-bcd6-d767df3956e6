/**
 * أداة تحسين قاعدة البيانات
 * توفر وظائف لتحسين أداء قاعدة البيانات SQLite
 */

const logger = require('./logger');

/**
 * تحسين قاعدة البيانات SQLite
 * @param {Object} db - كائن قاعدة البيانات SQLite
 * @returns {boolean} - نجاح العملية
 */
function optimizeDatabase(db) {
  if (!db || typeof db.prepare !== 'function') {
    logger.error('خطأ في تحسين قاعدة البيانات: كائن قاعدة البيانات غير صالح');
    return false;
  }

  try {
    logger.info('بدء تحسين قاعدة البيانات...');

    // تشغيل VACUUM لإعادة تنظيم قاعدة البيانات وتقليل حجمها
    db.exec('VACUUM');
    logger.info('تم تنفيذ VACUUM بنجاح');

    // تحليل قاعدة البيانات لتحسين المخطط الإحصائي
    db.exec('ANALYZE');
    logger.info('تم تنفيذ ANALYZE بنجاح');

    // تحسين الفهارس
    optimizeIndexes(db);

    // تحسين إعدادات قاعدة البيانات
    optimizeSettings(db);

    logger.info('تم تحسين قاعدة البيانات بنجاح');
    return true;
  } catch (error) {
    logger.error('خطأ أثناء تحسين قاعدة البيانات:', error);
    return false;
  }
}

/**
 * تحسين الفهارس في قاعدة البيانات
 * @param {Object} db - كائن قاعدة البيانات SQLite
 */
function optimizeIndexes(db) {
  try {
    logger.info('تحسين الفهارس...');

    // إعادة بناء الفهارس
    db.exec('REINDEX');
    logger.info('تم إعادة بناء الفهارس بنجاح');

    // التحقق من الفهارس المفقودة وإضافتها
    checkAndAddMissingIndexes(db);
  } catch (error) {
    logger.error('خطأ أثناء تحسين الفهارس:', error);
  }
}

/**
 * التحقق من الفهارس المفقودة وإضافتها
 * @param {Object} db - كائن قاعدة البيانات SQLite
 */
function checkAndAddMissingIndexes(db) {
  try {
    // الحصول على قائمة الفهارس الحالية
    const indexesStmt = db.prepare(`
      SELECT name, tbl_name, sql 
      FROM sqlite_master 
      WHERE type = 'index' AND name NOT LIKE 'sqlite_%'
    `);
    const existingIndexes = indexesStmt.all();
    
    // تخزين أسماء الفهارس الموجودة للبحث السريع
    const indexNames = new Set(existingIndexes.map(idx => idx.name));
    
    // قائمة الفهارس المهمة التي يجب أن تكون موجودة
    const criticalIndexes = [
      { name: 'idx_items_name', table: 'items', column: 'name' },
      { name: 'idx_inventory_item_id', table: 'inventory', column: 'item_id' },
      { name: 'idx_transactions_transaction_id', table: 'transactions', column: 'transaction_id' },
      { name: 'idx_transactions_item_id', table: 'transactions', column: 'item_id' },
      { name: 'idx_transactions_type', table: 'transactions', column: 'transaction_type' },
      { name: 'idx_transactions_date', table: 'transactions', column: 'transaction_date' },
      { name: 'idx_transactions_customer', table: 'transactions', column: 'customer' },
      { name: 'idx_customers_name', table: 'customers', column: 'name' },
      { name: 'idx_customers_type', table: 'customers', column: 'customer_type' },
      { name: 'idx_customers_parent', table: 'customers', column: 'parent_id' },
      { name: 'idx_machines_name', table: 'machines', column: 'name' },
      { name: 'idx_settings_key', table: 'settings', column: 'key' }
    ];
    
    // إضافة الفهارس المفقودة
    for (const index of criticalIndexes) {
      if (!indexNames.has(index.name)) {
        try {
          const sql = `CREATE INDEX IF NOT EXISTS ${index.name} ON ${index.table}(${index.column})`;
          db.exec(sql);
          logger.info(`تم إنشاء الفهرس المفقود: ${index.name}`);
        } catch (err) {
          logger.error(`خطأ في إنشاء الفهرس ${index.name}:`, err);
        }
      }
    }
    
    logger.info('تم التحقق من الفهارس المفقودة وإضافتها');
  } catch (error) {
    logger.error('خطأ أثناء التحقق من الفهارس المفقودة:', error);
  }
}

/**
 * تحسين إعدادات قاعدة البيانات
 * @param {Object} db - كائن قاعدة البيانات SQLite
 */
function optimizeSettings(db) {
  try {
    logger.info('تحسين إعدادات قاعدة البيانات...');

    // تمكين المفاتيح الأجنبية
    db.pragma('foreign_keys = ON');
    
    // تحسين الأداء
    db.pragma('synchronous = NORMAL');  // توازن بين الأداء والأمان
    db.pragma('journal_mode = WAL');    // وضع سجل المعاملات WAL لتحسين الأداء
    db.pragma('temp_store = MEMORY');   // تخزين الجداول المؤقتة في الذاكرة
    
    // تحسين ذاكرة التخزين المؤقت
    db.pragma('cache_size = 10000');    // زيادة حجم ذاكرة التخزين المؤقت (بالكيلوبايت)
    
    logger.info('تم تحسين إعدادات قاعدة البيانات بنجاح');
  } catch (error) {
    logger.error('خطأ أثناء تحسين إعدادات قاعدة البيانات:', error);
  }
}

/**
 * تنظيف البيانات القديمة أو غير المستخدمة
 * @param {Object} db - كائن قاعدة البيانات SQLite
 * @param {Object} options - خيارات التنظيف
 * @returns {Object} - إحصائيات التنظيف
 */
function cleanupDatabase(db, options = {}) {
  const stats = {
    orphanedRecords: 0,
    duplicateRecords: 0,
    emptyRecords: 0
  };
  
  if (!db || typeof db.prepare !== 'function') {
    logger.error('خطأ في تنظيف قاعدة البيانات: كائن قاعدة البيانات غير صالح');
    return stats;
  }
  
  try {
    logger.info('بدء تنظيف قاعدة البيانات...');
    
    // تنظيف السجلات اليتيمة في المخزون (بدون صنف مرتبط)
    if (options.cleanOrphanedInventory !== false) {
      const orphanedInventoryStmt = db.prepare(`
        DELETE FROM inventory 
        WHERE item_id NOT IN (SELECT id FROM items)
      `);
      const result = orphanedInventoryStmt.run();
      stats.orphanedRecords += result.changes;
      logger.info(`تم حذف ${result.changes} سجل يتيم من المخزون`);
    }
    
    // تنظيف المعاملات اليتيمة (بدون صنف مرتبط)
    if (options.cleanOrphanedTransactions !== false) {
      const orphanedTransactionsStmt = db.prepare(`
        DELETE FROM transactions 
        WHERE item_id IS NOT NULL AND item_id NOT IN (SELECT id FROM items)
      `);
      const result = orphanedTransactionsStmt.run();
      stats.orphanedRecords += result.changes;
      logger.info(`تم حذف ${result.changes} معاملة يتيمة`);
    }
    
    // تنظيف العملاء الفرعيين اليتامى (بدون عميل رئيسي)
    if (options.cleanOrphanedCustomers !== false) {
      const orphanedCustomersStmt = db.prepare(`
        DELETE FROM customers 
        WHERE customer_type = 'sub' AND parent_id IS NOT NULL 
        AND parent_id NOT IN (SELECT id FROM customers WHERE customer_type = 'regular')
      `);
      const result = orphanedCustomersStmt.run();
      stats.orphanedRecords += result.changes;
      logger.info(`تم حذف ${result.changes} عميل فرعي يتيم`);
    }
    
    logger.info('تم تنظيف قاعدة البيانات بنجاح');
    return stats;
  } catch (error) {
    logger.error('خطأ أثناء تنظيف قاعدة البيانات:', error);
    return stats;
  }
}

module.exports = {
  optimizeDatabase,
  cleanupDatabase,
  optimizeIndexes,
  optimizeSettings
};
