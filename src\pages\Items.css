/* أنماط صفحة الأصناف */
.items-page {
  padding: 20px;
}

.items-header {
  margin-bottom: 20px;
}

.items-header h1 {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 10px;
}

.items-header p {
  font-size: 1rem;
  color: var(--text-light);
}

.items-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.search-container {
  position: relative;
  margin-bottom: 20px;
}

.search-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-light);
}

.search-input {
  width: 100%;
  padding: 10px 15px 10px 40px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  font-size: 0.95rem;
}

.search-input:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.items-table-container {
  margin-bottom: 30px;
}

.item-name {
  font-weight: 600;
  color: var(--text-dark);
}

.item-unit {
  color: var(--text-light);
  font-size: 0.9rem;
}

.item-price {
  font-weight: 500;
}

.item-min-quantity {
  font-size: 0.9rem;
  color: var(--text-dark);
}

.item-min-quantity.low {
  color: var(--danger-color);
  font-weight: 500;
}

/* أنماط عرض الكمية */
.quantity-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
  font-size: 0.9rem;
}

.quantity-badge.available {
  background-color: rgba(46, 204, 113, 0.1);
  color: #27ae60;
}

.quantity-badge.low {
  background-color: rgba(243, 156, 18, 0.1);
  color: #f39c12;
}

.quantity-badge.out {
  background-color: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.quantity-badge .ml-1 {
  margin-left: 5px;
}

.item-actions {
  display: flex;
  gap: 5px;
}

/* أنماط نافذة إضافة/تعديل الصنف */
.item-form {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group.full-width {
  grid-column: span 2;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-dark);
}

.form-control {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: 0.95rem;
}

.form-control:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.form-text {
  font-size: 0.8rem;
  color: var(--text-light);
  margin-top: 5px;
}

.form-actions {
  grid-column: span 2;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 10px;
}

/* أنماط أزرار وحدة القياس */
.unit-buttons-container {
  display: flex;
  gap: 10px;
  margin-top: 5px;
}

.unit-button {
  flex: 1;
  padding: 10px 15px;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  background-color: #fff;
  color: var(--text-dark);
  font-weight: 500;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.unit-button:hover {
  border-color: var(--primary-color);
  background-color: rgba(var(--primary-rgb), 0.05);
}

.unit-button.active {
  border-color: var(--primary-color);
  background-color: var(--primary-color);
  color: white;
}

/* أنماط قسم الاستيراد/التصدير */
.importer-container {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  background-color: rgba(0, 0, 0, 0.01);
}

.importer-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--text-dark);
}

/* تنسيقات للشاشات الصغيرة */
@media (max-width: 768px) {
  .item-form {
    grid-template-columns: 1fr;
  }

  .form-group.full-width {
    grid-column: span 1;
  }

  .form-actions {
    grid-column: span 1;
  }
}
