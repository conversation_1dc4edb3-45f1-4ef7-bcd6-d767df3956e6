/**
 * وحدة تحسين الأداء
 * تحتوي على وظائف لتحسين أداء التطبيق وإدارة الموارد
 */

/**
 * تحسين أداء الذاكرة باستخدام تقنية التخزين المؤقت مع حد أقصى للعناصر
 * @param {Number} maxItems - الحد الأقصى لعدد العناصر في التخزين المؤقت
 * @param {Number} expiryTime - وقت انتهاء صلاحية العناصر بالمللي ثانية (افتراضي: 5 دقائق)
 * @returns {Object} - كائن التخزين المؤقت مع وظائف الإدارة
 */
export const createLimitedCache = (maxItems = 100, expiryTime = 5 * 60 * 1000) => {
  const cache = new Map();
  const timestamps = new Map();

  return {
    /**
     * إضافة عنصر إلى التخزين المؤقت
     * @param {String} key - مفتاح العنصر
     * @param {*} value - قيمة العنصر
     */
    set: (key, value) => {
      // إذا وصل التخزين المؤقت إلى الحد الأقصى، قم بإزالة أقدم عنصر
      if (cache.size >= maxItems && !cache.has(key)) {
        let oldestKey = null;
        let oldestTime = Date.now();

        for (const [k, time] of timestamps.entries()) {
          if (time < oldestTime) {
            oldestTime = time;
            oldestKey = k;
          }
        }

        if (oldestKey) {
          cache.delete(oldestKey);
          timestamps.delete(oldestKey);
        }
      }

      // إضافة العنصر الجديد
      cache.set(key, value);
      timestamps.set(key, Date.now());
    },

    /**
     * الحصول على عنصر من التخزين المؤقت
     * @param {String} key - مفتاح العنصر
     * @returns {*} - قيمة العنصر أو undefined إذا لم يكن موجودًا أو منتهي الصلاحية
     */
    get: (key) => {
      if (cache.has(key)) {
        const timestamp = timestamps.get(key);
        const now = Date.now();

        // التحقق من انتهاء صلاحية العنصر
        if (now - timestamp > expiryTime) {
          // إزالة العنصر منتهي الصلاحية
          cache.delete(key);
          timestamps.delete(key);
          return undefined;
        }

        // تحديث وقت الوصول
        timestamps.set(key, now);
        return cache.get(key);
      }
      return undefined;
    },

    /**
     * التحقق من وجود عنصر في التخزين المؤقت
     * @param {String} key - مفتاح العنصر
     * @returns {Boolean} - true إذا كان العنصر موجودًا وغير منتهي الصلاحية، false إذا لم يكن موجودًا أو منتهي الصلاحية
     */
    has: (key) => {
      if (cache.has(key)) {
        const timestamp = timestamps.get(key);
        const now = Date.now();

        // التحقق من انتهاء صلاحية العنصر
        if (now - timestamp > expiryTime) {
          // إزالة العنصر منتهي الصلاحية
          cache.delete(key);
          timestamps.delete(key);
          return false;
        }

        return true;
      }
      return false;
    },

    /**
     * إزالة عنصر من التخزين المؤقت
     * @param {String} key - مفتاح العنصر
     * @returns {Boolean} - true إذا تم إزالة العنصر، false إذا لم يكن موجودًا
     */
    delete: (key) => {
      timestamps.delete(key);
      return cache.delete(key);
    },

    /**
     * مسح التخزين المؤقت بالكامل
     */
    clear: () => {
      cache.clear();
      timestamps.clear();
    },

    /**
     * الحصول على حجم التخزين المؤقت الحالي
     * @returns {Number} - عدد العناصر في التخزين المؤقت
     */
    size: () => {
      return cache.size;
    },

    /**
     * الحصول على جميع مفاتيح التخزين المؤقت
     * @returns {Array} - مصفوفة المفاتيح
     */
    keys: () => {
      return Array.from(cache.keys());
    }
  };
};

/**
 * تحسين أداء العمليات الثقيلة باستخدام تقنية التنفيذ المتأخر
 * @param {Function} heavyFunction - الوظيفة الثقيلة
 * @param {Number} delay - التأخير بالمللي ثانية (الافتراضي: 300 مللي ثانية)
 * @returns {Function} - وظيفة محسنة مع تنفيذ متأخر
 */
export const debounce = (heavyFunction, delay = 300) => {
  let timeoutId;

  return (...args) => {
    // إلغاء المؤقت السابق
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    // إنشاء مؤقت جديد
    return new Promise((resolve) => {
      timeoutId = setTimeout(() => {
        const result = heavyFunction(...args);
        resolve(result);
      }, delay);
    });
  };
};

/**
 * تحسين أداء العمليات المتكررة باستخدام تقنية التنفيذ المحدود
 * @param {Function} repeatedFunction - الوظيفة المتكررة
 * @param {Number} limit - الحد الأقصى لعدد مرات التنفيذ في الفترة الزمنية
 * @param {Number} interval - الفترة الزمنية بالمللي ثانية (الافتراضي: 1000 مللي ثانية)
 * @returns {Function} - وظيفة محسنة مع تنفيذ محدود
 */
export const throttle = (repeatedFunction, limit = 1, interval = 1000) => {
  let inThrottle = false;
  let callCount = 0;
  let lastCallTime = 0;

  return (...args) => {
    const now = Date.now();

    // إعادة تعيين العداد إذا انتهت الفترة الزمنية
    if (now - lastCallTime >= interval) {
      callCount = 0;
      lastCallTime = now;
    }

    // التحقق من عدم تجاوز الحد الأقصى
    if (callCount < limit) {
      callCount++;
      return repeatedFunction(...args);
    }

    // إذا تم تجاوز الحد الأقصى، قم بتأخير التنفيذ
    if (!inThrottle) {
      inThrottle = true;

      setTimeout(() => {
        inThrottle = false;
        callCount = 0;
        lastCallTime = Date.now();
      }, interval - (now - lastCallTime));
    }

    return null;
  };
};

/**
 * تحسين أداء العمليات المتزامنة باستخدام تقنية التنفيذ المتوازي
 * @param {Array<Function>} functions - مصفوفة من الوظائف المتزامنة
 * @param {Number} concurrency - عدد الوظائف التي يمكن تنفيذها في وقت واحد (الافتراضي: 3)
 * @returns {Promise<Array>} - وعد بمصفوفة من نتائج الوظائف
 */
export const parallelExecute = async (functions, concurrency = 3) => {
  const results = [];
  let currentIndex = 0;

  // وظيفة لتنفيذ الوظائف بشكل متوازي
  const executeNext = async () => {
    const index = currentIndex++;

    if (index >= functions.length) {
      return;
    }

    try {
      results[index] = await functions[index]();
    } catch (error) {
      results[index] = { error };
    }

    // تنفيذ الوظيفة التالية
    await executeNext();
  };

  // بدء تنفيذ الوظائف بشكل متوازي
  const executors = [];
  for (let i = 0; i < Math.min(concurrency, functions.length); i++) {
    executors.push(executeNext());
  }

  // انتظار انتهاء جميع الوظائف
  await Promise.all(executors);

  return results;
};

/**
 * تحسين أداء التطبيق باستخدام تقنية تحرير الذاكرة
 * @param {Array} data - البيانات المراد تحسينها
 * @param {Array} fieldsToKeep - الحقول المراد الاحتفاظ بها
 * @returns {Array} - البيانات المحسنة
 */
export const optimizeMemoryUsage = (data, fieldsToKeep) => {
  // تحرير الذاكرة غير المستخدمة
  if (global.gc) {
    global.gc();
  }

  // تحرير الذاكرة المؤقتة
  if (window.performance && window.performance.memory) {
    console.log('استخدام الذاكرة قبل التحسين:', window.performance.memory.usedJSHeapSize / 1024 / 1024, 'ميجابايت');
  }

  // إزالة المراجع غير المستخدمة
  const cleanupUnusedReferences = () => {
    // تنظيف التخزين المؤقت للصور
    const images = document.querySelectorAll('img');
    for (let i = 0; i < images.length; i++) {
      const img = images[i];
      if (!isElementVisible(img)) {
        img.src = '';
      }
    }

    // تنظيف مستمعي الأحداث غير المستخدمة
    // (هذا مثال فقط، يجب تنفيذه بشكل صحيح في التطبيق الفعلي)
  };

  // التحقق من رؤية العنصر
  const isElementVisible = (element) => {
    const rect = element.getBoundingClientRect();
    return (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
  };

  // تنفيذ التنظيف
  cleanupUnusedReferences();

  if (window.performance && window.performance.memory) {
    console.log('استخدام الذاكرة بعد التحسين:', window.performance.memory.usedJSHeapSize / 1024 / 1024, 'ميجابايت');
  }

  // تحسين البيانات
  if (data && Array.isArray(data) && fieldsToKeep && Array.isArray(fieldsToKeep) && fieldsToKeep.length > 0) {
    return data.map(item => {
      const optimizedItem = {};

      for (const field of fieldsToKeep) {
        if (item.hasOwnProperty(field)) {
          optimizedItem[field] = item[field];
        }
      }

      return optimizedItem;
    });
  }

  return data;
};

/**
 * تقسيم المهام الثقيلة إلى أجزاء صغيرة لتحسين استجابة واجهة المستخدم
 * @param {Array} items - العناصر المراد معالجتها
 * @param {Function} processItem - دالة معالجة العنصر
 * @param {Number} chunkSize - حجم الجزء
 * @param {Number} delay - التأخير بين الأجزاء بالمللي ثانية
 * @returns {Promise} - وعد بانتهاء المعالجة
 */
export const processInChunks = (items, processItem, chunkSize = 10, delay = 10) => {
  return new Promise((resolve, reject) => {
    if (!items || !items.length) {
      resolve([]);
      return;
    }

    const result = [];
    let index = 0;

    function processNextChunk() {
      const chunk = items.slice(index, index + chunkSize);
      index += chunkSize;

      try {
        // معالجة الجزء الحالي
        for (const item of chunk) {
          result.push(processItem(item));
        }

        // التحقق من انتهاء المعالجة
        if (index >= items.length) {
          resolve(result);
          return;
        }

        // جدولة معالجة الجزء التالي
        setTimeout(processNextChunk, delay);
      } catch (error) {
        reject(error);
      }
    }

    // بدء المعالجة
    processNextChunk();
  });
};

/**
 * تحميل البيانات بشكل تدريجي
 * @param {Function} fetchFunction - دالة جلب البيانات
 * @param {Object} options - خيارات التحميل
 * @returns {Object} - واجهة التحميل التدريجي
 */
export const createInfiniteLoader = (fetchFunction, options = {}) => {
  const {
    pageSize = 20,
    initialPage = 1,
    totalItems = null,
    cacheKey = 'infinite_loader'
  } = options;

  // إنشاء تخزين مؤقت للصفحات
  const pagesCache = createLimitedCache(50);

  let currentPage = initialPage;
  let hasMore = true;
  let isLoading = false;
  let total = totalItems;

  return {
    /**
     * تحميل الصفحة التالية
     * @param {Object} filters - فلاتر التصفية
     * @returns {Promise<Object>} - وعد بالعناصر المحملة
     */
    async loadNextPage(filters = {}) {
      if (isLoading || !hasMore) {
        return { items: [], hasMore, total };
      }

      // التحقق من وجود الصفحة في التخزين المؤقت
      const cacheKeyWithFilters = `${cacheKey}_page_${currentPage}_${JSON.stringify(filters)}`;
      const cachedPage = pagesCache.get(cacheKeyWithFilters);

      if (cachedPage) {
        currentPage++;
        return {
          items: cachedPage.items,
          hasMore: cachedPage.hasMore,
          total: cachedPage.total
        };
      }

      // تحميل الصفحة من الخادم
      isLoading = true;

      try {
        const result = await fetchFunction(currentPage, pageSize, filters);

        // تحديث الحالة
        hasMore = result.items && result.items.length === pageSize && (!total || currentPage * pageSize < total);
        total = result.total || total;
        currentPage++;
        isLoading = false;

        // تخزين الصفحة في التخزين المؤقت
        pagesCache.set(cacheKeyWithFilters, {
          items: result.items || [],
          hasMore,
          total
        });

        return {
          items: result.items || [],
          hasMore,
          total
        };
      } catch (error) {
        isLoading = false;
        console.error('خطأ في تحميل البيانات:', error);
        return { items: [], hasMore: false, total, error };
      }
    },

    /**
     * إعادة تعيين التحميل التدريجي
     */
    reset() {
      currentPage = initialPage;
      hasMore = true;
      isLoading = false;
      pagesCache.clear();
    },

    /**
     * الحصول على حالة التحميل
     * @returns {Object} - حالة التحميل
     */
    getState() {
      return {
        currentPage: currentPage - 1,
        hasMore,
        isLoading,
        total
      };
    }
  };
};

/**
 * تحسين أداء الاستعلامات المتكررة
 * @param {Function} queryFunction - دالة الاستعلام
 * @param {Object} options - خيارات التحسين
 * @returns {Function} - دالة الاستعلام المحسنة
 */
export const memoizeQuery = (queryFunction, options = {}) => {
  const {
    maxCacheSize = 100,
    expiryTime = 5 * 60 * 1000,
    cacheKeyGenerator = JSON.stringify
  } = options;

  // إنشاء تخزين مؤقت للاستعلامات
  const queryCache = createLimitedCache(maxCacheSize, expiryTime);

  return async function memoizedQuery(...args) {
    // إنشاء مفتاح التخزين المؤقت
    const cacheKey = cacheKeyGenerator(args);

    // التحقق من وجود النتيجة في التخزين المؤقت
    const cachedResult = queryCache.get(cacheKey);
    if (cachedResult !== undefined) {
      return cachedResult;
    }

    // تنفيذ الاستعلام
    const result = await queryFunction(...args);

    // تخزين النتيجة في التخزين المؤقت
    queryCache.set(cacheKey, result);

    return result;
  };
};
