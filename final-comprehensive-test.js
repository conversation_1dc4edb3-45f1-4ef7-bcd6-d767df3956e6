/**
 * اختبار شامل نهائي للتحديث الفوري لبطاقات الأرباح
 * 
 * هذا الملف يجمع جميع الاختبارات ويقدم تقريراً شاملاً
 * عن حالة نظام التحديث الفوري
 */

const { runAllTests } = require('./run-profit-test.js');
const { runAllUITests } = require('./test-ui-instant-update.js');

// اختبار التكامل بين المكونات
async function testIntegration() {
  console.log('🔗 اختبار التكامل بين المكونات...');
  
  try {
    // اختبار استيراد المكونات الأساسية
    const { calculateQuarterlyProfits } = require('./src/utils/profitCalculator.js');
    
    // بيانات اختبار متنوعة
    const testData = [
      // معاملات الربع الأول
      {
        transaction_type: 'sale',
        profit: 200,
        transaction_date: new Date(2025, 0, 10).toISOString()
      },
      {
        transaction_type: 'return',
        profit: -50,
        transaction_date: new Date(2025, 1, 15).toISOString()
      },
      // معاملات الربع الثاني
      {
        transaction_type: 'sale',
        profit: 300,
        transaction_date: new Date(2025, 3, 10).toISOString()
      },
      {
        transaction_type: 'return',
        profit: -75,
        transaction_date: new Date(2025, 4, 20).toISOString()
      },
      // معاملات الربع الثالث
      {
        transaction_type: 'sale',
        profit: 150,
        transaction_date: new Date(2025, 6, 5).toISOString()
      },
      // معاملات الربع الرابع
      {
        transaction_type: 'sale',
        profit: 400,
        transaction_date: new Date(2025, 10, 15).toISOString()
      }
    ];
    
    // حساب الأرباح
    const results = calculateQuarterlyProfits(testData);
    
    // التحقق من النتائج المتوقعة
    const expectedResults = {
      quarterly: 150,    // Q1: 200 - 50 = 150
      halfYearly: 375,   // Q1 + Q2: 150 + 225 = 375
      threeQuarters: 525, // Q1 + Q2 + Q3: 375 + 150 = 525
      yearly: 925        // Q1 + Q2 + Q3 + Q4: 525 + 400 = 925
    };
    
    console.log('📊 النتائج المحسوبة:', results);
    console.log('📋 النتائج المتوقعة:', expectedResults);
    
    // التحقق من دقة النتائج
    const isAccurate = 
      results.quarterly === expectedResults.quarterly &&
      results.halfYearly === expectedResults.halfYearly &&
      results.threeQuarters === expectedResults.threeQuarters &&
      results.yearly === expectedResults.yearly;
    
    if (isAccurate) {
      console.log('✅ اختبار التكامل نجح - جميع الحسابات دقيقة');
      return true;
    } else {
      console.log('❌ اختبار التكامل فشل - هناك خطأ في الحسابات');
      return false;
    }
    
  } catch (error) {
    console.error('❌ خطأ في اختبار التكامل:', error.message);
    return false;
  }
}

// اختبار سيناريوهات الاستخدام الحقيقي
async function testRealWorldScenarios() {
  console.log('🌍 اختبار سيناريوهات الاستخدام الحقيقي...');
  
  const scenarios = [
    {
      name: 'يوم عمل عادي',
      transactions: [
        { type: 'sale', profit: 50, count: 10 },
        { type: 'return', profit: -25, count: 2 }
      ],
      expectedProfit: 450 // (50 * 10) - (25 * 2) = 450
    },
    {
      name: 'يوم مبيعات مرتفعة',
      transactions: [
        { type: 'sale', profit: 100, count: 20 },
        { type: 'return', profit: -50, count: 1 }
      ],
      expectedProfit: 1950 // (100 * 20) - (50 * 1) = 1950
    },
    {
      name: 'يوم إرجاعات كثيرة',
      transactions: [
        { type: 'sale', profit: 75, count: 5 },
        { type: 'return', profit: -30, count: 8 }
      ],
      expectedProfit: 135 // (75 * 5) - (30 * 8) = 135
    }
  ];
  
  let passedScenarios = 0;
  
  for (const scenario of scenarios) {
    console.log(`\n📝 سيناريو: ${scenario.name}`);
    
    // إنشاء معاملات الاختبار
    const testTransactions = [];
    let transactionId = 1;
    
    scenario.transactions.forEach(transGroup => {
      for (let i = 0; i < transGroup.count; i++) {
        testTransactions.push({
          id: transactionId++,
          transaction_type: transGroup.type,
          profit: transGroup.profit,
          transaction_date: new Date(2025, 0, Math.floor(Math.random() * 28) + 1).toISOString()
        });
      }
    });
    
    // حساب الأرباح
    const { calculateQuarterlyProfits } = require('./src/utils/profitCalculator.js');
    const results = calculateQuarterlyProfits(testTransactions);
    
    console.log(`   💰 الربح المحسوب: ${results.quarterly}`);
    console.log(`   🎯 الربح المتوقع: ${scenario.expectedProfit}`);
    
    if (results.quarterly === scenario.expectedProfit) {
      console.log(`   ✅ ${scenario.name} نجح`);
      passedScenarios++;
    } else {
      console.log(`   ❌ ${scenario.name} فشل`);
    }
  }
  
  console.log(`\n📊 نتائج السيناريوهات: ${passedScenarios}/${scenarios.length} نجح`);
  return passedScenarios === scenarios.length;
}

// تقرير شامل عن حالة النظام
async function generateSystemReport() {
  console.log('\n📋 تقرير شامل عن حالة نظام التحديث الفوري');
  console.log('=' .repeat(70));
  
  const testResults = {
    basicTests: false,
    uiTests: false,
    integrationTest: false,
    realWorldTests: false
  };
  
  // تشغيل الاختبارات الأساسية
  console.log('\n1️⃣ الاختبارات الأساسية:');
  try {
    testResults.basicTests = runAllTests();
  } catch (error) {
    console.error('خطأ في الاختبارات الأساسية:', error.message);
  }
  
  // تشغيل اختبارات واجهة المستخدم
  console.log('\n2️⃣ اختبارات واجهة المستخدم:');
  try {
    testResults.uiTests = await runAllUITests();
  } catch (error) {
    console.error('خطأ في اختبارات واجهة المستخدم:', error.message);
  }
  
  // تشغيل اختبار التكامل
  console.log('\n3️⃣ اختبار التكامل:');
  try {
    testResults.integrationTest = await testIntegration();
  } catch (error) {
    console.error('خطأ في اختبار التكامل:', error.message);
  }
  
  // تشغيل اختبارات السيناريوهات الحقيقية
  console.log('\n4️⃣ اختبارات السيناريوهات الحقيقية:');
  try {
    testResults.realWorldTests = await testRealWorldScenarios();
  } catch (error) {
    console.error('خطأ في اختبارات السيناريوهات:', error.message);
  }
  
  // حساب النتيجة الإجمالية
  const passedTests = Object.values(testResults).filter(result => result === true).length;
  const totalTests = Object.keys(testResults).length;
  const successRate = (passedTests / totalTests) * 100;
  
  // عرض التقرير النهائي
  console.log('\n' + '=' .repeat(70));
  console.log('🎯 التقرير النهائي:');
  console.log('=' .repeat(70));
  
  console.log(`📊 معدل النجاح الإجمالي: ${successRate.toFixed(1)}% (${passedTests}/${totalTests})`);
  console.log('\n📋 تفاصيل النتائج:');
  console.log(`   ${testResults.basicTests ? '✅' : '❌'} الاختبارات الأساسية`);
  console.log(`   ${testResults.uiTests ? '✅' : '❌'} اختبارات واجهة المستخدم`);
  console.log(`   ${testResults.integrationTest ? '✅' : '❌'} اختبار التكامل`);
  console.log(`   ${testResults.realWorldTests ? '✅' : '❌'} اختبارات السيناريوهات الحقيقية`);
  
  if (successRate === 100) {
    console.log('\n🎉 ممتاز! جميع الاختبارات نجحت');
    console.log('✨ نظام التحديث الفوري لبطاقات الأرباح يعمل بشكل مثالي');
    console.log('🚀 النظام جاهز للاستخدام في الإنتاج');
  } else if (successRate >= 75) {
    console.log('\n👍 جيد! معظم الاختبارات نجحت');
    console.log('⚠️  هناك بعض المشاكل البسيطة التي تحتاج إلى إصلاح');
  } else {
    console.log('\n⚠️  تحذير! هناك مشاكل كبيرة في النظام');
    console.log('🔧 يحتاج النظام إلى مراجعة وإصلاح شامل');
  }
  
  console.log('\n📝 التوصيات:');
  if (!testResults.basicTests) {
    console.log('   🔧 راجع دوال حساب الأرباح الأساسية');
  }
  if (!testResults.uiTests) {
    console.log('   🎨 راجع نظام الأحداث في واجهة المستخدم');
  }
  if (!testResults.integrationTest) {
    console.log('   🔗 راجع التكامل بين المكونات المختلفة');
  }
  if (!testResults.realWorldTests) {
    console.log('   🌍 راجع معالجة السيناريوهات المعقدة');
  }
  
  if (successRate === 100) {
    console.log('   🎯 لا توجد توصيات - النظام يعمل بشكل مثالي!');
  }
  
  console.log('\n' + '=' .repeat(70));
  
  return successRate === 100;
}

// تشغيل التقرير الشامل
if (require.main === module) {
  generateSystemReport().then(success => {
    console.log(`\n🏁 انتهى الاختبار الشامل - النتيجة: ${success ? 'نجح' : 'فشل'}`);
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('❌ خطأ في تشغيل الاختبار الشامل:', error);
    process.exit(1);
  });
}

module.exports = {
  testIntegration,
  testRealWorldScenarios,
  generateSystemReport
};
