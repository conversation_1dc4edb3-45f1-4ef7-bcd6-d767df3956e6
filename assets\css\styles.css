/* Global Styles */
:root {
  --primary-color: #3498db;
  --secondary-color: #2ecc71;
  --danger-color: #e74c3c;
  --warning-color: #f39c12;
  --dark-color: #34495e;
  --light-color: #ecf0f1;
  --text-color: #333;
  --text-light: #f8f9fa;
  --border-color: #ddd;
  --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Dark Mode Variables */
.dark-mode {
  --primary-color: #2980b9;
  --secondary-color: #27ae60;
  --danger-color: #c0392b;
  --warning-color: #d35400;
  --dark-color: #2c3e50;
  --light-color: #2c3e50;
  --text-color: #ecf0f1;
  --text-light: #ecf0f1;
  --border-color: #4a5568;
  --shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Cairo', 'Arabic UI', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--light-color);
  color: var(--text-color);
  line-height: 1.6;
  direction: rtl;
}

a {
  text-decoration: none;
  color: var(--primary-color);
}

ul {
  list-style: none;
}

/* Layout */
.container {
  display: flex;
  min-height: 100vh;
}

.sidebar {
  width: 250px;
  background-color: var(--dark-color);
  color: var(--text-light);
  padding: 1rem;
  position: fixed;
  height: 100vh;
  overflow-y: auto;
  transition: all 0.3s;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h3 {
  font-size: 1.5rem;
}

.sidebar-menu {
  margin-top: 2rem;
}

.sidebar-menu-item {
  padding: 0.8rem 1rem;
  margin-bottom: 0.5rem;
  border-radius: 5px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s;
}

.sidebar-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-menu-item.active {
  background-color: var(--primary-color);
}

.sidebar-menu-item svg {
  margin-left: 0.8rem;
}

.main-content {
  flex: 1;
  margin-right: 250px;
  padding: 2rem;
  transition: all 0.3s;
}

/* Dashboard */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: var(--shadow);
  display: flex;
  flex-direction: column;
}

.stat-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.stat-card-icon {
  font-size: 2rem;
  color: var(--primary-color);
}

.stat-card-value {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.stat-card-title {
  color: #777;
  font-size: 0.9rem;
}

/* Charts */
.chart-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: var(--shadow);
  margin-bottom: 2rem;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

/* Tables */
.table-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: var(--shadow);
  margin-bottom: 2rem;
  overflow-x: auto;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 0.75rem 1rem;
  text-align: right;
}

.table th {
  background-color: var(--light-color);
  font-weight: 600;
}

.table tr {
  border-bottom: 1px solid var(--border-color);
}

.table tr:last-child {
  border-bottom: none;
}

.table tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

/* Forms */
.form-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: var(--shadow);
  max-width: 800px;
  margin: 0 auto;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.form-control {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.3s;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
}

.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.form-row .form-group {
  flex: 1;
  margin-bottom: 0;
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 0.6rem 1.2rem;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: #2980b9;
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-secondary:hover {
  background-color: #27ae60;
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
}

.btn-danger:hover {
  background-color: #c0392b;
}

.btn-warning {
  background-color: var(--warning-color);
  color: white;
}

.btn-warning:hover {
  background-color: #d35400;
}

.btn-sm {
  padding: 0.4rem 0.8rem;
  font-size: 0.875rem;
}

.btn-lg {
  padding: 0.8rem 1.5rem;
  font-size: 1.1rem;
}

/* Alerts */
.alert {
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1.5rem;
}

.alert-success {
  background-color: rgba(46, 204, 113, 0.2);
  border: 1px solid var(--secondary-color);
  color: #27ae60;
}

.alert-danger {
  background-color: rgba(231, 76, 60, 0.2);
  border: 1px solid var(--danger-color);
  color: #c0392b;
}

.alert-warning {
  background-color: rgba(243, 156, 18, 0.2);
  border: 1px solid var(--warning-color);
  color: #d35400;
}

.alert-info {
  background-color: rgba(52, 152, 219, 0.2);
  border: 1px solid var(--primary-color);
  color: #2980b9;
}

/* Modal */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: var(--shadow);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #777;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  padding: 1.5rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

/* Utilities */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.mt-1 {
  margin-top: 0.5rem;
}

.mt-2 {
  margin-top: 1rem;
}

.mt-3 {
  margin-top: 1.5rem;
}

.mt-4 {
  margin-top: 2rem;
}

.mb-1 {
  margin-bottom: 0.5rem;
}

.mb-2 {
  margin-bottom: 1rem;
}

.mb-3 {
  margin-bottom: 1.5rem;
}

.mb-4 {
  margin-bottom: 2rem;
}

.mr-1 {
  margin-right: 0.5rem;
}

.mr-2 {
  margin-right: 1rem;
}

.ml-1 {
  margin-left: 0.5rem;
}

.ml-2 {
  margin-left: 1rem;
}

.p-1 {
  padding: 0.5rem;
}

.p-2 {
  padding: 1rem;
}

.p-3 {
  padding: 1.5rem;
}

.p-4 {
  padding: 2rem;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    width: 80px;
    padding: 0.5rem;
  }

  .sidebar-header h3,
  .sidebar-menu-item span {
    display: none;
  }

  .sidebar-menu-item {
    justify-content: center;
    padding: 0.8rem;
  }

  .sidebar-menu-item svg {
    margin: 0;
    font-size: 1.5rem;
  }

  .main-content {
    margin-right: 80px;
  }

  .form-row {
    flex-direction: column;
    gap: 0;
  }

  .dashboard-stats {
    grid-template-columns: 1fr;
  }
}

/* Login Page */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: var(--light-color);
}

.login-form {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: var(--shadow);
  padding: 2rem;
  width: 100%;
  max-width: 400px;
}

.login-logo {
  text-align: center;
  margin-bottom: 2rem;
}

.login-logo h1 {
  font-size: 1.8rem;
  color: var(--primary-color);
}

/* Loading Spinner */
.spinner {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* Badge */
.badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 600;
}

.badge-primary {
  background-color: var(--primary-color);
  color: white;
}

.badge-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.badge-danger {
  background-color: var(--danger-color);
  color: white;
}

.badge-warning {
  background-color: var(--warning-color);
  color: white;
}

.badge-info {
  background-color: #3498db;
  color: white;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

.pagination-item {
  margin: 0 0.25rem;
}

.pagination-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 4px;
  background-color: #fff;
  border: 1px solid var(--border-color);
  color: var(--text-color);
  transition: all 0.3s;
}

.pagination-link:hover {
  background-color: var(--light-color);
}

.pagination-link.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Transaction ID */
.transaction-id {
  font-family: monospace;
  font-size: 0.9rem;
  font-weight: bold;
  padding: 0.25rem 0.5rem;
  background-color: rgba(52, 152, 219, 0.1);
  border-radius: 4px;
  color: var(--primary-color);
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}
