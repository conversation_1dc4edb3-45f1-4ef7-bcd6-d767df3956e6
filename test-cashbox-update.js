/**
 * اختبار تحديث الرصيد الابتدائي للخزينة
 * 
 * هذا السكريبت يقوم باختبار دالة تحديث الرصيد الابتدائي للخزينة
 */

const path = require('path');
const Database = require('better-sqlite3');

// مسار قاعدة البيانات
const dbPath = path.join(process.env.APPDATA || process.env.HOME, 'warehouse-management-system', 'wms-database', 'warehouse.db');

console.log('🧪 بدء اختبار تحديث الرصيد الابتدائي للخزينة...');
console.log(`📁 مسار قاعدة البيانات: ${dbPath}`);

try {
  // فتح قاعدة البيانات
  const db = new Database(dbPath);
  console.log('✅ تم فتح قاعدة البيانات بنجاح');

  // التحقق من وجود جدول الخزينة
  const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='cashbox'").get();
  
  if (!tableExists) {
    console.log('❌ جدول الخزينة غير موجود');
    process.exit(1);
  }

  // الحصول على بيانات الخزينة الحالية
  console.log('📊 الحصول على بيانات الخزينة الحالية...');
  const currentCashbox = db.prepare('SELECT * FROM cashbox LIMIT 1').get();
  
  if (!currentCashbox) {
    console.log('❌ لا توجد بيانات في جدول الخزينة');
    
    // إنشاء خزينة جديدة للاختبار
    console.log('🆕 إنشاء خزينة جديدة للاختبار...');
    const now = new Date().toISOString();
    const createStmt = db.prepare(`
      INSERT INTO cashbox (
        initial_balance, current_balance, profit_total, sales_total, purchases_total, returns_total, transport_total,
        created_at, updated_at
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    createStmt.run(0, 0, 0, 0, 0, 0, 0, now, now);
    console.log('✅ تم إنشاء خزينة جديدة');
    
    // إعادة قراءة بيانات الخزينة
    const newCashbox = db.prepare('SELECT * FROM cashbox LIMIT 1').get();
    console.log('📊 بيانات الخزينة الجديدة:', newCashbox);
  } else {
    console.log('✅ تم العثور على بيانات الخزينة:');
    console.log(`   - الرصيد الافتتاحي: ${currentCashbox.initial_balance}`);
    console.log(`   - الرصيد الحالي: ${currentCashbox.current_balance}`);
    console.log(`   - إجمالي المبيعات: ${currentCashbox.sales_total || 0}`);
    console.log(`   - إجمالي المشتريات: ${currentCashbox.purchases_total || 0}`);
    console.log(`   - إجمالي مصاريف النقل: ${currentCashbox.transport_total || 0}`);
    console.log(`   - إجمالي الأرباح: ${currentCashbox.profit_total || 0}`);
  }

  // اختبار تحديث الرصيد الابتدائي
  console.log('🔄 اختبار تحديث الرصيد الابتدائي...');
  
  const testInitialBalance = 1000;
  console.log(`📝 تحديث الرصيد الابتدائي إلى: ${testInitialBalance}`);

  // بدء معاملة قاعدة البيانات
  const result = db.transaction(() => {
    // التحقق من وجود الخزينة
    const cashbox = db.prepare('SELECT * FROM cashbox LIMIT 1').get();

    if (!cashbox) {
      throw new Error('الخزينة غير موجودة');
    }

    // الرصيد الحالي يساوي دائما الرصيد الافتتاحي الجديد
    const newCurrentBalance = testInitialBalance;

    // الأرباح = المبيعات - المشتريات - النقل
    const newProfit = (cashbox.sales_total || 0) - (cashbox.purchases_total || 0) - (cashbox.transport_total || 0);

    // تحديث الخزينة
    const now = new Date().toISOString();
    const updateStmt = db.prepare(`
      UPDATE cashbox
      SET initial_balance = ?,
          current_balance = ?,
          profit_total = ?,
          updated_at = ?
      WHERE id = ?
    `);

    const updateResult = updateStmt.run(
      testInitialBalance,
      newCurrentBalance,
      newProfit,
      now,
      cashbox.id
    );

    console.log(`✅ تم تحديث ${updateResult.changes} سجل`);

    // الحصول على الخزينة المحدثة
    const updatedCashbox = db.prepare('SELECT * FROM cashbox LIMIT 1').get();

    // إنشاء بيانات الخزينة للإرجاع
    const cashboxData = {
      exists: true,
      id: updatedCashbox.id,
      initial_balance: updatedCashbox.initial_balance,
      current_balance: updatedCashbox.current_balance,
      profit_total: updatedCashbox.profit_total,
      sales_total: updatedCashbox.sales_total,
      purchases_total: updatedCashbox.purchases_total,
      returns_total: updatedCashbox.returns_total || 0,
      transport_total: updatedCashbox.transport_total || 0,
      created_at: updatedCashbox.created_at,
      updated_at: updatedCashbox.updated_at
    };

    return {
      success: true,
      cashbox: cashboxData
    };
  })();

  console.log('📊 نتيجة التحديث:', result);

  if (result.success) {
    console.log('✅ تم تحديث الرصيد الابتدائي بنجاح');
    console.log('📊 بيانات الخزينة المحدثة:');
    console.log(`   - الرصيد الافتتاحي: ${result.cashbox.initial_balance}`);
    console.log(`   - الرصيد الحالي: ${result.cashbox.current_balance}`);
    console.log(`   - إجمالي المبيعات: ${result.cashbox.sales_total}`);
    console.log(`   - إجمالي المشتريات: ${result.cashbox.purchases_total}`);
    console.log(`   - إجمالي مصاريف النقل: ${result.cashbox.transport_total}`);
    console.log(`   - إجمالي الأرباح: ${result.cashbox.profit_total}`);

    // التحقق من وجود جميع الحقول المطلوبة
    const requiredFields = ['id', 'initial_balance', 'current_balance', 'profit_total', 'sales_total', 'purchases_total'];
    const missingFields = requiredFields.filter(field => result.cashbox[field] === undefined);
    
    if (missingFields.length > 0) {
      console.warn('⚠️ حقول مفقودة في بيانات الخزينة:', missingFields);
    } else {
      console.log('✅ جميع الحقول المطلوبة موجودة');
    }

    // اختبار تحويل البيانات إلى JSON
    try {
      const jsonString = JSON.stringify(result);
      console.log('✅ تم تحويل النتيجة إلى JSON بنجاح');
      console.log(`📝 حجم JSON: ${jsonString.length} حرف`);
    } catch (jsonError) {
      console.error('❌ خطأ في تحويل النتيجة إلى JSON:', jsonError.message);
    }

  } else {
    console.error('❌ فشل في تحديث الرصيد الابتدائي');
  }

  // إغلاق قاعدة البيانات
  db.close();

  console.log('✅ تم إكمال الاختبار بنجاح');

} catch (error) {
  console.error('❌ خطأ في اختبار تحديث الرصيد الابتدائي:', error);
  process.exit(1);
}
